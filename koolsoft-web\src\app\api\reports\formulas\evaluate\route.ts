/**
 * Formula Evaluation API Route
 * 
 * Provides endpoints for evaluating formulas with test data,
 * validating formulas, and testing formula expressions.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { FormulaEngine, FormulaContext } from '@/lib/formula-engine';

// Request schemas
const evaluateFormulaSchema = z.object({
  formula: z.string().min(1),
  context: z.record(z.any()).optional(),
  testCases: z.array(z.object({
    name: z.string().optional(),
    context: z.record(z.any()),
    expected: z.any().optional(),
  })).optional(),
});

const validateFormulaSchema = z.object({
  formula: z.string().min(1),
  context: z.record(z.any()).optional(),
});

/**
 * POST /api/reports/formulas/evaluate
 * Evaluate a formula with given context
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { formula, context, testCases } = evaluateFormulaSchema.parse(body);

    const formulaEngine = new FormulaEngine();

    // Single evaluation
    if (context && !testCases) {
      const result = formulaEngine.evaluate(formula, context);
      
      return NextResponse.json({
        success: true,
        data: {
          result: result.value,
          success: result.success,
          error: result.error,
          warnings: result.warnings,
        },
      });
    }

    // Multiple test cases
    if (testCases) {
      const results = formulaEngine.test(formula, testCases.map(tc => ({
        context: tc.context,
        expected: tc.expected,
      })));

      const summary = {
        totalTests: results.length,
        passed: results.filter(r => r.passed).length,
        failed: results.filter(r => !r.passed).length,
        successRate: results.length > 0 
          ? Math.round((results.filter(r => r.passed).length / results.length) * 100)
          : 0,
      };

      return NextResponse.json({
        success: true,
        data: {
          results: results.map((result, index) => ({
            name: testCases[index]?.name || `Test ${index + 1}`,
            context: result.context,
            result: result.result.value,
            expected: result.expected,
            passed: result.passed,
            success: result.result.success,
            error: result.result.error,
          })),
          summary,
        },
      });
    }

    // Simple evaluation without context
    const result = formulaEngine.evaluate(formula);
    
    return NextResponse.json({
      success: true,
      data: {
        result: result.value,
        success: result.success,
        error: result.error,
        warnings: result.warnings,
      },
    });

  } catch (error) {
    console.error('Error evaluating formula:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to evaluate formula' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports/formulas/validate
 * Validate a formula and return detailed information
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { formula, context } = validateFormulaSchema.parse(body);

    const formulaEngine = new FormulaEngine();
    const validation = formulaEngine.validate(formula, context);

    // Get additional information
    const availableFunctions = formulaEngine.getFunctionsByCategory();
    const complexityScore = formulaEngine.getComplexityScore(formula);

    return NextResponse.json({
      success: true,
      data: {
        formula: validation.formula,
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        variables: validation.variables,
        functions: validation.functions,
        complexity: complexityScore,
        availableFunctions,
        formattedFormula: formulaEngine.formatFormula(formula),
      },
    });

  } catch (error) {
    console.error('Error validating formula:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to validate formula' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/reports/formulas/functions
 * Get available functions and their documentation
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const functionName = searchParams.get('function');

    const formulaEngine = new FormulaEngine();

    // Get documentation for specific function
    if (functionName) {
      const doc = formulaEngine.getFunctionDoc(functionName);
      if (!doc) {
        return NextResponse.json(
          { error: 'Function not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          name: functionName,
          documentation: doc,
        },
      });
    }

    // Get functions by category or all functions
    const functionsByCategory = formulaEngine.getFunctionsByCategory();
    
    if (category) {
      const categoryFunctions = functionsByCategory[category];
      if (!categoryFunctions) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          category,
          functions: categoryFunctions,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        categories: Object.keys(functionsByCategory),
        functions: functionsByCategory,
        totalFunctions: formulaEngine.getAvailableFunctions().length,
      },
    });

  } catch (error) {
    console.error('Error fetching functions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch functions' },
      { status: 500 }
    );
  }
}
