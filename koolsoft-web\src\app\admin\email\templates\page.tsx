'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Plus, Edit, Eye, Trash2 } from 'lucide-react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Email Template interface
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  isActive: boolean;
  updatedAt: string;
  createdAt: string;
  body?: string;
}

/**
 * Email Templates List Page
 *
 * This page displays a list of all email templates and allows administrators
 * to manage them.
 */
export default function EmailTemplatesPage() {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const router = useRouter();

  // Fetch templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Fetch all email templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);

      // First try the admin-specific endpoint
      let response = await fetch('/api/admin/email/templates');

      // If that fails, try the regular endpoint
      if (!response.ok) {
        console.warn(`Admin API error (${response.status}), trying regular endpoint`);
        response = await fetch('/api/email/templates');
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}):`, errorText);
        showErrorToast(`Failed to fetch templates: ${response.status} ${response.statusText}`);
        return;
      }

      const data = await response.json();
      console.log('Templates API response:', data);

      if (data.templates && data.templates.length > 0) {
        setTemplates(data.templates);
      } else {
        console.warn('No email templates found in the database');
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      showErrorToast(`Failed to fetch templates: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // Navigate to template edit page
  const handleEditTemplate = (id: string) => {
    router.push(`/admin/email/templates/edit/${id}`);
  };

  // Navigate to template preview page
  const handlePreviewTemplate = () => {
    router.push('/admin/email/preview');
  };

  // Navigate to template create page
  const handleCreateTemplate = () => {
    router.push('/admin/email/templates/create');
  };

  // Delete template
  const handleDeleteTemplate = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/email/templates/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error deleting template:', errorData);
        showErrorToast(errorData.error || 'Failed to delete template');
        return;
      }

      showSuccessToast('Template deleted successfully');
      fetchTemplates();
    } catch (error) {
      console.error('Error deleting template:', error);
      showErrorToast(`Failed to delete template: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-2xl font-bold text-black">Email Templates</CardTitle>
            <CardDescription>
              Manage email templates for your application
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handlePreviewTemplate} variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button onClick={handleCreateTemplate} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : templates.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Name</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead className="w-[100px]">Status</TableHead>
                  <TableHead className="w-[150px]">Last Updated</TableHead>
                  <TableHead className="text-right w-[150px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium text-black">
                      {template.name}
                    </TableCell>
                    <TableCell className="text-black">
                      {template.subject}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          template.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {template.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </TableCell>
                    <TableCell className="text-black">
                      {formatDate(template.updatedAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTemplate(template.id)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">No email templates found</p>
              <Button onClick={handleCreateTemplate} className="mt-4">
                Create Your First Template
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
