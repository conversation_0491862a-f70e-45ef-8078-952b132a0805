"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, {\n  checkForDefaultPrevented = true\n} = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxTQUFTQSxvQkFBb0JBLENBQUNDLG9CQUFvQixFQUFFQyxlQUFlLEVBQUU7RUFBRUMsd0JBQXdCLEdBQUc7QUFBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUU7RUFDN0csT0FBTyxTQUFTQyxXQUFXQSxDQUFDQyxLQUFLLEVBQUU7SUFDakNKLG9CQUFvQixHQUFHSSxLQUFLLENBQUM7SUFDN0IsSUFBSUYsd0JBQXdCLEtBQUssS0FBSyxJQUFJLENBQUNFLEtBQUssQ0FBQ0MsZ0JBQWdCLEVBQUU7TUFDakUsT0FBT0osZUFBZSxHQUFHRyxLQUFLLENBQUM7SUFDakM7RUFDRixDQUFDO0FBQ0giLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHByaW1pdGl2ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbImNvbXBvc2VFdmVudEhhbmRsZXJzIiwib3JpZ2luYWxFdmVudEhhbmRsZXIiLCJvdXJFdmVudEhhbmRsZXIiLCJjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQiLCJoYW5kbGVFdmVudCIsImV2ZW50IiwiZGVmYXVsdFByZXZlbnRlZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection.CollectionItemSlot.useEffect\": ()=>{\n                context.itemMap.set(ref, {\n                    ref,\n                    ...itemData\n                });\n                return ({\n                    \"createCollection.CollectionItemSlot.useEffect\": ()=>void context.itemMap.delete(ref)\n                })[\"createCollection.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection.CollectionItemSlot.useEffect\"]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"createCollection.useCollection.useCallback[getItems]\": ()=>{\n                const collectionNode = context.collectionRef.current;\n                if (!collectionNode) return [];\n                const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n                const items = Array.from(context.itemMap.values());\n                const orderedItems = items.sort({\n                    \"createCollection.useCollection.useCallback[getItems].orderedItems\": (a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n                }[\"createCollection.useCollection.useCallback[getItems].orderedItems\"]);\n                return orderedItems;\n            }\n        }[\"createCollection.useCollection.useCallback[getItems]\"], [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection2.CollectionProviderImpl.useEffect\": ()=>{\n                if (!collectionElement) return;\n                const observer = getChildListObserver({\n                    \"createCollection2.CollectionProviderImpl.useEffect.observer\": ()=>{}\n                }[\"createCollection2.CollectionProviderImpl.useEffect.observer\"]);\n                observer.observe(collectionElement, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"createCollection2.CollectionProviderImpl.useEffect\": ()=>{\n                        observer.disconnect();\n                    }\n                })[\"createCollection2.CollectionProviderImpl.useEffect\"];\n            }\n        }[\"createCollection2.CollectionProviderImpl.useEffect\"], [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection2.CollectionItemSlot.useEffect\": ()=>{\n                const itemData2 = memoizedItemData;\n                setItemMap({\n                    \"createCollection2.CollectionItemSlot.useEffect\": (map)=>{\n                        if (!element) {\n                            return map;\n                        }\n                        if (!map.has(element)) {\n                            map.set(element, {\n                                ...itemData2,\n                                element\n                            });\n                            return map.toSorted(sortByDocumentPosition);\n                        }\n                        return map.set(element, {\n                            ...itemData2,\n                            element\n                        }).toSorted(sortByDocumentPosition);\n                    }\n                }[\"createCollection2.CollectionItemSlot.useEffect\"]);\n                return ({\n                    \"createCollection2.CollectionItemSlot.useEffect\": ()=>{\n                        setItemMap({\n                            \"createCollection2.CollectionItemSlot.useEffect\": (map)=>{\n                                if (!element || !map.has(element)) {\n                                    return map;\n                                }\n                                map.delete(element);\n                                return new OrderedDict(map);\n                            }\n                        }[\"createCollection2.CollectionItemSlot.useEffect\"]);\n                    }\n                })[\"createCollection2.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection2.CollectionItemSlot.useEffect\"], [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return node => {\n    let hasCleanup = false;\n    const cleanups = refs.map(ref => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = props => {\n    const {\n      children,\n      ...context\n    } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n      value,\n      children\n    });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = props => {\n      const {\n        scope,\n        children,\n        ...context\n      } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n        value,\n        children\n      });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map(defaultContext => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        [`__scope${scopeName}`]: {\n          ...scope,\n          [scopeName]: contexts\n        }\n      }), [scope, contexts]);\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map(createScope2 => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, {\n        useScope,\n        scopeName\n      }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return {\n          ...nextScopes2,\n          ...currentScope\n        };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        [`__scope${baseScope.scopeName}`]: nextScopes\n      }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXBvcnRhbC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O2lFQUVBO0FBQzhCO0FBQ0U7QUFDcUI7QUFDYztBQUM1QjtBQUN2QyxJQUFJSyxXQUFXLEdBQUcsUUFBUTtBQUMxQixJQUFJQyxNQUFNLGlCQUFHTiw2Q0FBZ0IsQ0FBQyxDQUFDUSxLQUFLLEVBQUVDLFlBQVk7SUFDaEQsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLGFBQWEsRUFBRSxHQUFHQyxhQUFhLEdBQUdKLEtBQUs7SUFDMUQsTUFBTSxDQUFDSyxPQUFPLEVBQUVDLFVBQVUsQ0FBQyxHQUFHZCwyQ0FBYyxDQUFDLEtBQUssQ0FBQztJQUNuREcsa0ZBQWU7a0NBQUMsSUFBTVcsVUFBVSxDQUFDLElBQUksQ0FBQztpQ0FBRSxFQUFFLENBQUM7SUFDM0MsTUFBTUosU0FBUyxHQUFHQyxhQUFhLElBQUlFLE9BQU8sSUFBSUcsVUFBVSxFQUFFQyxRQUFRLEVBQUVDLElBQUk7SUFDeEUsT0FBT1IsU0FBUyxpQkFBR1QsbURBQXFCLENBQUMsZ0JBQWdCRyxzREFBRyxDQUFDRixnRUFBUyxDQUFDa0IsR0FBRyxFQUFFO1FBQUUsR0FBR1IsV0FBVztRQUFFUyxHQUFHLEVBQUVaO0lBQWEsQ0FBQyxDQUFDLEVBQUVDLFNBQVMsQ0FBQyxHQUFHLElBQUk7QUFDdkksQ0FBQyxDQUFDO0FBQ0ZKLE1BQU0sQ0FBQ2dCLFdBQVcsR0FBR2pCLFdBQVc7QUFDaEMsSUFBSWtCLElBQUksR0FBR2pCLE1BQU07QUFHWCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtcG9ydGFsXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy9wb3J0YWwudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgUE9SVEFMX05BTUUgPSBcIlBvcnRhbFwiO1xudmFyIFBvcnRhbCA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgY29uc3QgeyBjb250YWluZXI6IGNvbnRhaW5lclByb3AsIC4uLnBvcnRhbFByb3BzIH0gPSBwcm9wcztcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4gc2V0TW91bnRlZCh0cnVlKSwgW10pO1xuICBjb25zdCBjb250YWluZXIgPSBjb250YWluZXJQcm9wIHx8IG1vdW50ZWQgJiYgZ2xvYmFsVGhpcz8uZG9jdW1lbnQ/LmJvZHk7XG4gIHJldHVybiBjb250YWluZXIgPyBSZWFjdERPTS5jcmVhdGVQb3J0YWwoLyogQF9fUFVSRV9fICovIGpzeChQcmltaXRpdmUuZGl2LCB7IC4uLnBvcnRhbFByb3BzLCByZWY6IGZvcndhcmRlZFJlZiB9KSwgY29udGFpbmVyKSA6IG51bGw7XG59KTtcblBvcnRhbC5kaXNwbGF5TmFtZSA9IFBPUlRBTF9OQU1FO1xudmFyIFJvb3QgPSBQb3J0YWw7XG5leHBvcnQge1xuICBQb3J0YWwsXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJSZWFjdERPTSIsIlByaW1pdGl2ZSIsInVzZUxheW91dEVmZmVjdCIsImpzeCIsIlBPUlRBTF9OQU1FIiwiUG9ydGFsIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwiY29udGFpbmVyIiwiY29udGFpbmVyUHJvcCIsInBvcnRhbFByb3BzIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJ1c2VTdGF0ZSIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsImJvZHkiLCJjcmVhdGVQb3J0YWwiLCJkaXYiLCJyZWYiLCJkaXNwbGF5TmFtZSIsIlJvb3QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\"a\", \"button\", \"div\", \"form\", \"h2\", \"h3\", \"img\", \"input\", \"label\", \"li\", \"nav\", \"ol\", \"p\", \"select\", \"span\", \"svg\", \"ul\"];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const {\n      asChild,\n      ...primitiveProps\n    } = props;\n    const Comp = asChild ? Slot : node;\n    if (false) {}\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n      ...primitiveProps,\n      ref: forwardedRef\n    });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return {\n    ...primitive,\n    [node]: Node\n  };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Slot,Slottable,createSlot,createSlottable auto */ // src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children) ? getElementRef(children) : void 0;\n        const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(childrenRef, forwardedRef);\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = ref;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount + 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"ToastProvider.useCallback\": ()=>setToastCount({\n                        \"ToastProvider.useCallback\": (prevCount)=>prevCount - 1\n                    }[\"ToastProvider.useCallback\"])\n            }[\"ToastProvider.useCallback\"], []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                    const isHotkeyPressed = hotkey.length !== 0 && hotkey.every({\n                        \"ToastViewport.useEffect.handleKeyDown\": (key)=>event[key] || event.code === key\n                    }[\"ToastViewport.useEffect.handleKeyDown\"]);\n                    if (isHotkeyPressed) ref.current?.focus();\n                }\n            }[\"ToastViewport.useEffect.handleKeyDown\"];\n            document.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"ToastViewport.useEffect\": ()=>document.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"ToastViewport.useEffect\"];\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const wrapper = wrapperRef.current;\n            const viewport = ref.current;\n            if (hasToasts && wrapper && viewport) {\n                const handlePause = {\n                    \"ToastViewport.useEffect.handlePause\": ()=>{\n                        if (!context.isClosePausedRef.current) {\n                            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                            viewport.dispatchEvent(pauseEvent);\n                            context.isClosePausedRef.current = true;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handlePause\"];\n                const handleResume = {\n                    \"ToastViewport.useEffect.handleResume\": ()=>{\n                        if (context.isClosePausedRef.current) {\n                            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                            viewport.dispatchEvent(resumeEvent);\n                            context.isClosePausedRef.current = false;\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleResume\"];\n                const handleFocusOutResume = {\n                    \"ToastViewport.useEffect.handleFocusOutResume\": (event)=>{\n                        const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                        if (isFocusMovingOutside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handleFocusOutResume\"];\n                const handlePointerLeaveResume = {\n                    \"ToastViewport.useEffect.handlePointerLeaveResume\": ()=>{\n                        const isFocusInside = wrapper.contains(document.activeElement);\n                        if (!isFocusInside) handleResume();\n                    }\n                }[\"ToastViewport.useEffect.handlePointerLeaveResume\"];\n                wrapper.addEventListener(\"focusin\", handlePause);\n                wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.addEventListener(\"pointermove\", handlePause);\n                wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.addEventListener(\"blur\", handlePause);\n                window.addEventListener(\"focus\", handleResume);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>{\n                        wrapper.removeEventListener(\"focusin\", handlePause);\n                        wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                        wrapper.removeEventListener(\"pointermove\", handlePause);\n                        wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                        window.removeEventListener(\"blur\", handlePause);\n                        window.removeEventListener(\"focus\", handleResume);\n                    }\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastViewport.useCallback[getSortedTabbableCandidates]\": ({ tabbingDirection })=>{\n            const toastItems = getItems();\n            const tabbableCandidates = toastItems.map({\n                \"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\": (toastItem)=>{\n                    const toastNode = toastItem.ref.current;\n                    const toastTabbableCandidates = [\n                        toastNode,\n                        ...getTabbableCandidates(toastNode)\n                    ];\n                    return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n                }\n            }[\"ToastViewport.useCallback[getSortedTabbableCandidates].tabbableCandidates\"]);\n            return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n        }\n    }[\"ToastViewport.useCallback[getSortedTabbableCandidates]\"], [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastViewport.useEffect\": ()=>{\n            const viewport = ref.current;\n            if (viewport) {\n                const handleKeyDown = {\n                    \"ToastViewport.useEffect.handleKeyDown\": (event)=>{\n                        const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                        const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                        if (isTabKey) {\n                            const focusedElement = document.activeElement;\n                            const isTabbingBackwards = event.shiftKey;\n                            const targetIsViewport = event.target === viewport;\n                            if (targetIsViewport && isTabbingBackwards) {\n                                headFocusProxyRef.current?.focus();\n                                return;\n                            }\n                            const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                            const sortedCandidates = getSortedTabbableCandidates({\n                                tabbingDirection\n                            });\n                            const index = sortedCandidates.findIndex({\n                                \"ToastViewport.useEffect.handleKeyDown.index\": (candidate)=>candidate === focusedElement\n                            }[\"ToastViewport.useEffect.handleKeyDown.index\"]);\n                            if (focusFirst(sortedCandidates.slice(index + 1))) {\n                                event.preventDefault();\n                            } else {\n                                isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                            }\n                        }\n                    }\n                }[\"ToastViewport.useEffect.handleKeyDown\"];\n                viewport.addEventListener(\"keydown\", handleKeyDown);\n                return ({\n                    \"ToastViewport.useEffect\": ()=>viewport.removeEventListener(\"keydown\", handleKeyDown)\n                })[\"ToastViewport.useEffect\"];\n            }\n        }\n    }[\"ToastViewport.useEffect\"], [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, {\n        \"ToastImpl.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"ToastImpl.useComposedRefs[composedRefs]\"]);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)({\n        \"ToastImpl.useCallbackRef[handleClose]\": ()=>{\n            const isFocusInToast = node?.contains(document.activeElement);\n            if (isFocusInToast) context.viewport?.focus();\n            onClose();\n        }\n    }[\"ToastImpl.useCallbackRef[handleClose]\"]);\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ToastImpl.useCallback[startTimer]\": (duration2)=>{\n            if (!duration2 || duration2 === Infinity) return;\n            window.clearTimeout(closeTimerRef.current);\n            closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n            closeTimerRef.current = window.setTimeout(handleClose, duration2);\n        }\n    }[\"ToastImpl.useCallback[startTimer]\"], [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            const viewport = context.viewport;\n            if (viewport) {\n                const handleResume = {\n                    \"ToastImpl.useEffect.handleResume\": ()=>{\n                        startTimer(closeTimerRemainingTimeRef.current);\n                        onResume?.();\n                    }\n                }[\"ToastImpl.useEffect.handleResume\"];\n                const handlePause = {\n                    \"ToastImpl.useEffect.handlePause\": ()=>{\n                        const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                        closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                        window.clearTimeout(closeTimerRef.current);\n                        onPause?.();\n                    }\n                }[\"ToastImpl.useEffect.handlePause\"];\n                viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n                return ({\n                    \"ToastImpl.useEffect\": ()=>{\n                        viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                        viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n                    }\n                })[\"ToastImpl.useEffect\"];\n            }\n        }\n    }[\"ToastImpl.useEffect\"], [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            if (open && !context.isClosePausedRef.current) startTimer(duration);\n        }\n    }[\"ToastImpl.useEffect\"], [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastImpl.useEffect\": ()=>{\n            onToastAdd();\n            return ({\n                \"ToastImpl.useEffect\": ()=>onToastRemove()\n            })[\"ToastImpl.useEffect\"];\n        }\n    }[\"ToastImpl.useEffect\"], [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ToastImpl.useMemo[announceTextContent]\": ()=>{\n            return node ? getAnnounceTextContent(node) : null;\n        }\n    }[\"ToastImpl.useMemo[announceTextContent]\"], [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame({\n        \"ToastAnnounce.useNextFrame\": ()=>setRenderAnnounceText(true)\n    }[\"ToastAnnounce.useNextFrame\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ToastAnnounce.useEffect\": ()=>{\n            const timer = window.setTimeout({\n                \"ToastAnnounce.useEffect.timer\": ()=>setIsAnnounced(true)\n            }[\"ToastAnnounce.useEffect.timer\"], 1e3);\n            return ({\n                \"ToastAnnounce.useEffect\": ()=>window.clearTimeout(timer)\n            })[\"ToastAnnounce.useEffect\"];\n        }\n    }[\"ToastAnnounce.useEffect\"], []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)({\n        \"useNextFrame.useLayoutEffect\": ()=>{\n            let raf1 = 0;\n            let raf2 = 0;\n            raf1 = window.requestAnimationFrame({\n                \"useNextFrame.useLayoutEffect\": ()=>raf2 = window.requestAnimationFrame(fn)\n            }[\"useNextFrame.useLayoutEffect\"]);\n            return ({\n                \"useNextFrame.useLayoutEffect\": ()=>{\n                    window.cancelAnimationFrame(raf1);\n                    window.cancelAnimationFrame(raf2);\n                }\n            })[\"useNextFrame.useLayoutEffect\"];\n        }\n    }[\"useNextFrame.useLayoutEffect\"], [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXRvYXN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztpTkFFQTtBQUM4QjtBQUNPO0FBQ3FCO0FBQ0k7QUFDRDtBQUNEO0FBQ1M7QUFDdEI7QUFDSTtBQUMrQjtBQUNqQjtBQUNZO0FBQ1Y7QUFDSDtBQUNUO0FBQ3ZELElBQUlrQixhQUFhLEdBQUcsZUFBZTtBQUNuQyxJQUFJLENBQUNDLFVBQVUsRUFBRUMsYUFBYSxFQUFFQyxxQkFBcUIsQ0FBQyxHQUFHakIsNEVBQWdCLENBQUMsT0FBTyxDQUFDO0FBQ2xGLElBQUksQ0FBQ2tCLGtCQUFrQixFQUFFQyxnQkFBZ0IsQ0FBQyxHQUFHbEIsMkVBQWtCLENBQUMsT0FBTyxFQUFFO0lBQUNnQixxQkFBcUI7Q0FBQyxDQUFDO0FBQ2pHLElBQUksQ0FBQ0cscUJBQXFCLEVBQUVDLHVCQUF1QixDQUFDLEdBQUdILGtCQUFrQixDQUFDSixhQUFhLENBQUM7QUFDeEYsSUFBSVEsYUFBYSxJQUFJQyxLQUFLLElBQUs7SUFDN0IsTUFBTSxFQUNKQyxZQUFZLEVBQ1pDLEtBQUssR0FBRyxjQUFjLEVBQ3RCQyxRQUFRLEdBQUcsR0FBRyxFQUNkQyxjQUFjLEdBQUcsT0FBTyxFQUN4QkMsY0FBYyxHQUFHLEVBQUUsRUFDbkJDLFFBQUFBLEVBQ0QsR0FBR04sS0FBSztJQUNULE1BQU0sQ0FBQ08sUUFBUSxFQUFFQyxXQUFXLENBQUMsR0FBR25DLDJDQUFjLENBQUMsSUFBSSxDQUFDO0lBQ3BELE1BQU0sQ0FBQ3FDLFVBQVUsRUFBRUMsYUFBYSxDQUFDLEdBQUd0QywyQ0FBYyxDQUFDLENBQUMsQ0FBQztJQUNyRCxNQUFNdUMsOEJBQThCLEdBQUd2Qyx5Q0FBWSxDQUFDLEtBQUssQ0FBQztJQUMxRCxNQUFNeUMsZ0JBQWdCLEdBQUd6Qyx5Q0FBWSxDQUFDLEtBQUssQ0FBQztJQUM1QyxJQUFJLENBQUM2QixLQUFLLENBQUNhLElBQUksQ0FBQyxDQUFDLEVBQUU7UUFDakJDLE9BQU8sQ0FBQ0MsS0FBSyxDQUNWLHdDQUF1QzFCLGFBQWMsb0NBQ3hELENBQUM7SUFDSDtJQUNBLE9BQU8sZ0JBQWdCRixzREFBRyxDQUFDRyxVQUFVLENBQUMwQixRQUFRLEVBQUU7UUFBRUMsS0FBSyxFQUFFbEIsWUFBWTtRQUFFSyxRQUFRLEVBQUUsZ0JBQWdCakIsc0RBQUcsQ0FDbEdRLHFCQUFxQixFQUNyQjtZQUNFc0IsS0FBSyxFQUFFbEIsWUFBWTtZQUNuQkMsS0FBSztZQUNMQyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEMsY0FBYztZQUNkSyxVQUFVO1lBQ1ZILFFBQVE7WUFDUmEsZ0JBQWdCLEVBQUVaLFdBQVc7WUFDN0JhLFVBQVUsRUFBRWhELDhDQUFpQjs2Q0FBQyxJQUFNc0MsYUFBYTtzREFBRVksU0FBUyxHQUFLQSxTQUFTLEdBQUcsQ0FBQzs7NENBQUcsRUFBRSxDQUFDO1lBQ3BGQyxhQUFhLEVBQUVuRCw4Q0FBaUI7NkNBQUMsSUFBTXNDLGFBQWE7c0RBQUVZLFNBQVMsR0FBS0EsU0FBUyxHQUFHLENBQUM7OzRDQUFHLEVBQUUsQ0FBQztZQUN2RlgsOEJBQThCO1lBQzlCRSxnQkFBZ0I7WUFDaEJSO1FBQ0YsQ0FDRjtJQUFFLENBQUMsQ0FBQztBQUNOLENBQUM7QUFDRFAsYUFBYSxDQUFDMEIsV0FBVyxHQUFHbEMsYUFBYTtBQUN6QyxJQUFJbUMsYUFBYSxHQUFHLGVBQWU7QUFDbkMsSUFBSUMsdUJBQXVCLEdBQUc7SUFBQyxJQUFJO0NBQUM7QUFDcEMsSUFBSUMsY0FBYyxHQUFHLHFCQUFxQjtBQUMxQyxJQUFJQyxlQUFlLEdBQUcsc0JBQXNCO0FBQzVDLElBQUlDLGFBQWEsaUJBQUd6RCw2Q0FBZ0IsQ0FDbEMsQ0FBQzJCLEtBQUssRUFBRWdDLFlBQVk7SUFDbEIsTUFBTSxFQUNKL0IsWUFBWSxFQUNaZ0MsTUFBTSxHQUFHTix1QkFBdUIsRUFDaEN6QixLQUFLLEdBQUcsMEJBQTBCLEVBQ2xDLEdBQUdnQyxlQUNKLEdBQUdsQyxLQUFLO0lBQ1QsTUFBTW1DLE9BQU8sR0FBR3JDLHVCQUF1QixDQUFDNEIsYUFBYSxFQUFFekIsWUFBWSxDQUFDO0lBQ3BFLE1BQU1tQyxRQUFRLEdBQUczQyxhQUFhLENBQUNRLFlBQVksQ0FBQztJQUM1QyxNQUFNb0MsVUFBVSxHQUFHaEUseUNBQVksQ0FBQyxJQUFJLENBQUM7SUFDckMsTUFBTWlFLGlCQUFpQixHQUFHakUseUNBQVksQ0FBQyxJQUFJLENBQUM7SUFDNUMsTUFBTWtFLGlCQUFpQixHQUFHbEUseUNBQVksQ0FBQyxJQUFJLENBQUM7SUFDNUMsTUFBTW1FLEdBQUcsR0FBR25FLHlDQUFZLENBQUMsSUFBSSxDQUFDO0lBQzlCLE1BQU1vRSxZQUFZLEdBQUdqRSw2RUFBZSxDQUFDd0QsWUFBWSxFQUFFUSxHQUFHLEVBQUVMLE9BQU8sQ0FBQ2YsZ0JBQWdCLENBQUM7SUFDakYsTUFBTXNCLFdBQVcsR0FBR1QsTUFBTSxDQUFDVSxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUNDLE9BQU8sQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLENBQUNBLE9BQU8sQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDO0lBQzlFLE1BQU1DLFNBQVMsR0FBR1YsT0FBTyxDQUFDekIsVUFBVSxHQUFHLENBQUM7SUFDeENyQyw0Q0FBZTttQ0FBQztZQUNkLE1BQU0wRSxhQUFhOzBEQUFJQyxLQUFLLElBQUs7b0JBQy9CLE1BQU1DLGVBQWUsR0FBR2hCLE1BQU0sQ0FBQ2lCLE1BQU0sS0FBSyxDQUFDLElBQUlqQixNQUFNLENBQUNrQixLQUFLO2tFQUFFQyxHQUFHLEdBQUtKLEtBQUssQ0FBQ0ksR0FBRyxDQUFDLElBQUlKLEtBQUssQ0FBQ0ssSUFBSSxLQUFLRCxHQUFHOztvQkFDckcsSUFBSUgsZUFBZSxFQUFFVCxHQUFHLENBQUNjLE9BQU8sRUFBRUMsS0FBSyxDQUFDLENBQUM7Z0JBQzNDLENBQUM7O1lBQ0RDLFFBQVEsQ0FBQ0MsZ0JBQWdCLENBQUMsU0FBUyxFQUFFVixhQUFhLENBQUM7WUFDbkQ7MkNBQU8sSUFBTVMsUUFBUSxDQUFDRSxtQkFBbUIsQ0FBQyxTQUFTLEVBQUVYLGFBQWEsQ0FBQzs7UUFDckUsQ0FBQztrQ0FBRTtRQUFDZCxNQUFNO0tBQUMsQ0FBQztJQUNaNUQsNENBQWU7bUNBQUM7WUFDZCxNQUFNc0YsT0FBTyxHQUFHdEIsVUFBVSxDQUFDaUIsT0FBTztZQUNsQyxNQUFNL0MsUUFBUSxHQUFHaUMsR0FBRyxDQUFDYyxPQUFPO1lBQzVCLElBQUlULFNBQVMsSUFBSWMsT0FBTyxJQUFJcEQsUUFBUSxFQUFFO2dCQUNwQyxNQUFNcUQsV0FBVzsyREFBR0EsQ0FBQTt3QkFDbEIsSUFBSSxDQUFDekIsT0FBTyxDQUFDckIsZ0JBQWdCLENBQUN3QyxPQUFPLEVBQUU7NEJBQ3JDLE1BQU1PLFVBQVUsR0FBRyxJQUFJQyxXQUFXLENBQUNsQyxjQUFjLENBQUM7NEJBQ2xEckIsUUFBUSxDQUFDd0QsYUFBYSxDQUFDRixVQUFVLENBQUM7NEJBQ2xDMUIsT0FBTyxDQUFDckIsZ0JBQWdCLENBQUN3QyxPQUFPLEdBQUcsSUFBSTt3QkFDekM7b0JBQ0YsQ0FBQzs7Z0JBQ0QsTUFBTVUsWUFBWTs0REFBR0EsQ0FBQTt3QkFDbkIsSUFBSTdCLE9BQU8sQ0FBQ3JCLGdCQUFnQixDQUFDd0MsT0FBTyxFQUFFOzRCQUNwQyxNQUFNVyxXQUFXLEdBQUcsSUFBSUgsV0FBVyxDQUFDakMsZUFBZSxDQUFDOzRCQUNwRHRCLFFBQVEsQ0FBQ3dELGFBQWEsQ0FBQ0UsV0FBVyxDQUFDOzRCQUNuQzlCLE9BQU8sQ0FBQ3JCLGdCQUFnQixDQUFDd0MsT0FBTyxHQUFHLEtBQUs7d0JBQzFDO29CQUNGLENBQUM7O2dCQUNELE1BQU1ZLG9CQUFvQjtxRUFBSWxCLEtBQUssSUFBSzt3QkFDdEMsTUFBTW1CLG9CQUFvQixHQUFHLENBQUNSLE9BQU8sQ0FBQ1MsUUFBUSxDQUFDcEIsS0FBSyxDQUFDcUIsYUFBYSxDQUFDO3dCQUNuRSxJQUFJRixvQkFBb0IsRUFBRUgsWUFBWSxDQUFDLENBQUM7b0JBQzFDLENBQUM7O2dCQUNELE1BQU1NLHdCQUF3Qjt3RUFBR0EsQ0FBQTt3QkFDL0IsTUFBTUMsYUFBYSxHQUFHWixPQUFPLENBQUNTLFFBQVEsQ0FBQ1osUUFBUSxDQUFDZ0IsYUFBYSxDQUFDO3dCQUM5RCxJQUFJLENBQUNELGFBQWEsRUFBRVAsWUFBWSxDQUFDLENBQUM7b0JBQ3BDLENBQUM7O2dCQUNETCxPQUFPLENBQUNGLGdCQUFnQixDQUFDLFNBQVMsRUFBRUcsV0FBVyxDQUFDO2dCQUNoREQsT0FBTyxDQUFDRixnQkFBZ0IsQ0FBQyxVQUFVLEVBQUVTLG9CQUFvQixDQUFDO2dCQUMxRFAsT0FBTyxDQUFDRixnQkFBZ0IsQ0FBQyxhQUFhLEVBQUVHLFdBQVcsQ0FBQztnQkFDcERELE9BQU8sQ0FBQ0YsZ0JBQWdCLENBQUMsY0FBYyxFQUFFYSx3QkFBd0IsQ0FBQztnQkFDbEVHLE1BQU0sQ0FBQ2hCLGdCQUFnQixDQUFDLE1BQU0sRUFBRUcsV0FBVyxDQUFDO2dCQUM1Q2EsTUFBTSxDQUFDaEIsZ0JBQWdCLENBQUMsT0FBTyxFQUFFTyxZQUFZLENBQUM7Z0JBQzlDOytDQUFPO3dCQUNMTCxPQUFPLENBQUNELG1CQUFtQixDQUFDLFNBQVMsRUFBRUUsV0FBVyxDQUFDO3dCQUNuREQsT0FBTyxDQUFDRCxtQkFBbUIsQ0FBQyxVQUFVLEVBQUVRLG9CQUFvQixDQUFDO3dCQUM3RFAsT0FBTyxDQUFDRCxtQkFBbUIsQ0FBQyxhQUFhLEVBQUVFLFdBQVcsQ0FBQzt3QkFDdkRELE9BQU8sQ0FBQ0QsbUJBQW1CLENBQUMsY0FBYyxFQUFFWSx3QkFBd0IsQ0FBQzt3QkFDckVHLE1BQU0sQ0FBQ2YsbUJBQW1CLENBQUMsTUFBTSxFQUFFRSxXQUFXLENBQUM7d0JBQy9DYSxNQUFNLENBQUNmLG1CQUFtQixDQUFDLE9BQU8sRUFBRU0sWUFBWSxDQUFDO29CQUNuRCxDQUFDOztZQUNIO1FBQ0YsQ0FBQztrQ0FBRTtRQUFDbkIsU0FBUztRQUFFVixPQUFPLENBQUNyQixnQkFBZ0I7S0FBQyxDQUFDO0lBQ3pDLE1BQU00RCwyQkFBMkIsR0FBR3JHLDhDQUFpQjtrRUFDbkQsQ0FBQyxFQUFFc0csZ0JBQUFBLEVBQWtCO1lBQ25CLE1BQU1DLFVBQVUsR0FBR3hDLFFBQVEsQ0FBQyxDQUFDO1lBQzdCLE1BQU15QyxrQkFBa0IsR0FBR0QsVUFBVSxDQUFDRSxHQUFHOzhGQUFFQyxTQUFTLElBQUs7b0JBQ3ZELE1BQU1DLFNBQVMsR0FBR0QsU0FBUyxDQUFDdkMsR0FBRyxDQUFDYyxPQUFPO29CQUN2QyxNQUFNMkIsdUJBQXVCLEdBQUc7d0JBQUNELFNBQVM7MkJBQUtFLHFCQUFxQixDQUFDRixTQUFTLENBQUM7cUJBQUM7b0JBQ2hGLE9BQU9MLGdCQUFnQixLQUFLLFVBQVUsR0FBR00sdUJBQXVCLEdBQUdBLHVCQUF1QixDQUFDRSxPQUFPLENBQUMsQ0FBQztnQkFDdEcsQ0FBQzs7WUFDRCxPQUFPLENBQUNSLGdCQUFnQixLQUFLLFVBQVUsR0FBR0Usa0JBQWtCLENBQUNNLE9BQU8sQ0FBQyxDQUFDLEdBQUdOLGtCQUFBQSxDQUFrQixDQUFFTyxJQUFJLENBQUMsQ0FBQztRQUNyRyxDQUFDO2lFQUNEO1FBQUNoRCxRQUFRO0tBQ1gsQ0FBQztJQUNEL0QsNENBQWU7bUNBQUM7WUFDZCxNQUFNa0MsUUFBUSxHQUFHaUMsR0FBRyxDQUFDYyxPQUFPO1lBQzVCLElBQUkvQyxRQUFRLEVBQUU7Z0JBQ1osTUFBTXdDLGFBQWE7OERBQUlDLEtBQUssSUFBSzt3QkFDL0IsTUFBTXFDLFNBQVMsR0FBR3JDLEtBQUssQ0FBQ3NDLE1BQU0sSUFBSXRDLEtBQUssQ0FBQ3VDLE9BQU8sSUFBSXZDLEtBQUssQ0FBQ3dDLE9BQU87d0JBQ2hFLE1BQU1DLFFBQVEsR0FBR3pDLEtBQUssQ0FBQ0ksR0FBRyxLQUFLLEtBQUssSUFBSSxDQUFDaUMsU0FBUzt3QkFDbEQsSUFBSUksUUFBUSxFQUFFOzRCQUNaLE1BQU1DLGNBQWMsR0FBR2xDLFFBQVEsQ0FBQ2dCLGFBQWE7NEJBQzdDLE1BQU1tQixrQkFBa0IsR0FBRzNDLEtBQUssQ0FBQzRDLFFBQVE7NEJBQ3pDLE1BQU1DLGdCQUFnQixHQUFHN0MsS0FBSyxDQUFDOEMsTUFBTSxLQUFLdkYsUUFBUTs0QkFDbEQsSUFBSXNGLGdCQUFnQixJQUFJRixrQkFBa0IsRUFBRTtnQ0FDMUNyRCxpQkFBaUIsQ0FBQ2dCLE9BQU8sRUFBRUMsS0FBSyxDQUFDLENBQUM7Z0NBQ2xDOzRCQUNGOzRCQUNBLE1BQU1vQixnQkFBZ0IsR0FBR2dCLGtCQUFrQixHQUFHLFdBQVcsR0FBRyxVQUFVOzRCQUN0RSxNQUFNSSxnQkFBZ0IsR0FBR3JCLDJCQUEyQixDQUFDO2dDQUFFQzs0QkFBaUIsQ0FBQyxDQUFDOzRCQUMxRSxNQUFNcUIsS0FBSyxHQUFHRCxnQkFBZ0IsQ0FBQ0UsU0FBUztnRkFBRUMsU0FBUyxHQUFLQSxTQUFTLEtBQUtSLGNBQWM7OzRCQUNwRixJQUFJUyxVQUFVLENBQUNKLGdCQUFnQixDQUFDSyxLQUFLLENBQUNKLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFO2dDQUNqRGhELEtBQUssQ0FBQ3FELGNBQWMsQ0FBQyxDQUFDOzRCQUN4QixDQUFDLE1BQU07Z0NBQ0xWLGtCQUFrQixHQUFHckQsaUJBQWlCLENBQUNnQixPQUFPLEVBQUVDLEtBQUssQ0FBQyxDQUFDLEdBQUdoQixpQkFBaUIsQ0FBQ2UsT0FBTyxFQUFFQyxLQUFLLENBQUMsQ0FBQzs0QkFDOUY7d0JBQ0Y7b0JBQ0YsQ0FBQzs7Z0JBQ0RoRCxRQUFRLENBQUNrRCxnQkFBZ0IsQ0FBQyxTQUFTLEVBQUVWLGFBQWEsQ0FBQztnQkFDbkQ7K0NBQU8sSUFBTXhDLFFBQVEsQ0FBQ21ELG1CQUFtQixDQUFDLFNBQVMsRUFBRVgsYUFBYSxDQUFDOztZQUNyRTtRQUNGLENBQUM7a0NBQUU7UUFBQ1gsUUFBUTtRQUFFc0MsMkJBQTJCO0tBQUMsQ0FBQztJQUMzQyxPQUFPLGdCQUFnQnBGLHVEQUFJLENBQ3pCWCxxRUFBdUIsRUFDdkI7UUFDRTZELEdBQUcsRUFBRUgsVUFBVTtRQUNma0UsSUFBSSxFQUFFLFFBQVE7UUFDZCxZQUFZLEVBQUVyRyxLQUFLLENBQUMwQyxPQUFPLENBQUMsVUFBVSxFQUFFRixXQUFXLENBQUM7UUFDcEQ4RCxRQUFRLEVBQUUsQ0FBQyxDQUFDO1FBQ1pDLEtBQUssRUFBRTtZQUFFQyxhQUFhLEVBQUU3RCxTQUFTLEdBQUcsS0FBSyxDQUFDLEdBQUc7UUFBTyxDQUFDO1FBQ3JEdkMsUUFBUSxFQUFFO1lBQ1J1QyxTQUFTLElBQUksZ0JBQWdCeEQsc0RBQUcsQ0FDOUJzSCxVQUFVLEVBQ1Y7Z0JBQ0VuRSxHQUFHLEVBQUVGLGlCQUFpQjtnQkFDdEJzRSwwQkFBMEIsRUFBRUEsQ0FBQTtvQkFDMUIsTUFBTS9CLGtCQUFrQixHQUFHSCwyQkFBMkIsQ0FBQzt3QkFDckRDLGdCQUFnQixFQUFFO29CQUNwQixDQUFDLENBQUM7b0JBQ0Z3QixVQUFVLENBQUN0QixrQkFBa0IsQ0FBQztnQkFDaEM7WUFDRixDQUNGLENBQUM7WUFDRCxnQkFBZ0J4RixzREFBRyxDQUFDRyxVQUFVLENBQUNxSCxJQUFJLEVBQUU7Z0JBQUUxRixLQUFLLEVBQUVsQixZQUFZO2dCQUFFSyxRQUFRLEVBQUUsZ0JBQWdCakIsc0RBQUcsQ0FBQ1AsZ0VBQVMsQ0FBQ2dJLEVBQUUsRUFBRTtvQkFBRU4sUUFBUSxFQUFFLENBQUMsQ0FBQztvQkFBRSxHQUFHdEUsYUFBYTtvQkFBRU0sR0FBRyxFQUFFQztnQkFBYSxDQUFDO1lBQUUsQ0FBQyxDQUFDO1lBQ2pLSSxTQUFTLElBQUksZ0JBQWdCeEQsc0RBQUcsQ0FDOUJzSCxVQUFVLEVBQ1Y7Z0JBQ0VuRSxHQUFHLEVBQUVELGlCQUFpQjtnQkFDdEJxRSwwQkFBMEIsRUFBRUEsQ0FBQTtvQkFDMUIsTUFBTS9CLGtCQUFrQixHQUFHSCwyQkFBMkIsQ0FBQzt3QkFDckRDLGdCQUFnQixFQUFFO29CQUNwQixDQUFDLENBQUM7b0JBQ0Z3QixVQUFVLENBQUN0QixrQkFBa0IsQ0FBQztnQkFDaEM7WUFDRixDQUNGLENBQUM7U0FBQTtJQUVMLENBQ0YsQ0FBQztBQUNILENBQ0YsQ0FBQztBQUNEL0MsYUFBYSxDQUFDTCxXQUFXLEdBQUdDLGFBQWE7QUFDekMsSUFBSXFGLGdCQUFnQixHQUFHLGlCQUFpQjtBQUN4QyxJQUFJSixVQUFVLGlCQUFHdEksNkNBQWdCLENBQy9CLENBQUMyQixLQUFLLEVBQUVnQyxZQUFZO0lBQ2xCLE1BQU0sRUFBRS9CLFlBQVksRUFBRTJHLDBCQUEwQixFQUFFLEdBQUdJLFlBQVksR0FBR2hILEtBQUs7SUFDekUsTUFBTW1DLE9BQU8sR0FBR3JDLHVCQUF1QixDQUFDaUgsZ0JBQWdCLEVBQUU5RyxZQUFZLENBQUM7SUFDdkUsT0FBTyxnQkFBZ0JaLHNEQUFHLENBQ3hCRiwyRUFBYyxFQUNkO1FBQ0UsYUFBYSxFQUFFLElBQUk7UUFDbkJxSCxRQUFRLEVBQUUsQ0FBQztRQUNYLEdBQUdRLFVBQVU7UUFDYnhFLEdBQUcsRUFBRVIsWUFBWTtRQUNqQnlFLEtBQUssRUFBRTtZQUFFUSxRQUFRLEVBQUU7UUFBUSxDQUFDO1FBQzVCQyxPQUFPLEdBQUdsRSxLQUFLLElBQUs7WUFDbEIsTUFBTW1FLGtCQUFrQixHQUFHbkUsS0FBSyxDQUFDcUIsYUFBYTtZQUM5QyxNQUFNK0MsMEJBQTBCLEdBQUcsQ0FBQ2pGLE9BQU8sQ0FBQzVCLFFBQVEsRUFBRTZELFFBQVEsQ0FBQytDLGtCQUFrQixDQUFDO1lBQ2xGLElBQUlDLDBCQUEwQixFQUFFUiwwQkFBMEIsQ0FBQyxDQUFDO1FBQzlEO0lBQ0YsQ0FDRixDQUFDO0FBQ0gsQ0FDRixDQUFDO0FBQ0RELFVBQVUsQ0FBQ2xGLFdBQVcsR0FBR3NGLGdCQUFnQjtBQUN6QyxJQUFJTSxVQUFVLEdBQUcsT0FBTztBQUN4QixJQUFJQyxpQkFBaUIsR0FBRyxrQkFBa0I7QUFDMUMsSUFBSUMsZ0JBQWdCLEdBQUcsaUJBQWlCO0FBQ3hDLElBQUlDLGtCQUFrQixHQUFHLG1CQUFtQjtBQUM1QyxJQUFJQyxlQUFlLEdBQUcsZ0JBQWdCO0FBQ3RDLElBQUlDLEtBQUssaUJBQUdySiw2Q0FBZ0IsQ0FDMUIsQ0FBQzJCLEtBQUssRUFBRWdDLFlBQVk7SUFDbEIsTUFBTSxFQUFFMkYsVUFBVSxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxZQUFZLEVBQUUsR0FBR0MsWUFBWSxHQUFHaEksS0FBSztJQUN0RixNQUFNLENBQUM0SCxJQUFJLEVBQUVLLE9BQU8sQ0FBQyxHQUFHaEosNEZBQW9CLENBQUM7UUFDM0NpSixJQUFJLEVBQUVMLFFBQVE7UUFDZE0sV0FBVyxFQUFFTCxXQUFXLElBQUksSUFBSTtRQUNoQ00sUUFBUSxFQUFFTCxZQUFZO1FBQ3RCTSxNQUFNLEVBQUVoQjtJQUNWLENBQUMsQ0FBQztJQUNGLE9BQU8sZ0JBQWdCaEksc0RBQUcsQ0FBQ1IsK0RBQVEsRUFBRTtRQUFFeUosT0FBTyxFQUFFWCxVQUFVLElBQUlDLElBQUk7UUFBRXRILFFBQVEsRUFBRSxnQkFBZ0JqQixzREFBRyxDQUMvRmtKLFNBQVMsRUFDVDtZQUNFWCxJQUFJO1lBQ0osR0FBR0ksVUFBVTtZQUNieEYsR0FBRyxFQUFFUixZQUFZO1lBQ2pCd0csT0FBTyxFQUFFQSxDQUFBLEdBQU1QLE9BQU8sQ0FBQyxLQUFLLENBQUM7WUFDN0JRLE9BQU8sRUFBRXpKLGlGQUFjLENBQUNnQixLQUFLLENBQUN5SSxPQUFPLENBQUM7WUFDdENDLFFBQVEsRUFBRTFKLGlGQUFjLENBQUNnQixLQUFLLENBQUMwSSxRQUFRLENBQUM7WUFDeENDLFlBQVksRUFBRXBLLDBFQUFvQixDQUFDeUIsS0FBSyxDQUFDMkksWUFBWSxHQUFHM0YsS0FBSyxJQUFLO2dCQUNoRUEsS0FBSyxDQUFDNEYsYUFBYSxDQUFDQyxZQUFZLENBQUMsWUFBWSxFQUFFLE9BQU8sQ0FBQztZQUN6RCxDQUFDLENBQUM7WUFDRkMsV0FBVyxFQUFFdkssMEVBQW9CLENBQUN5QixLQUFLLENBQUM4SSxXQUFXLEdBQUc5RixLQUFLLElBQUs7Z0JBQzlELE1BQU0sRUFBRStGLENBQUMsRUFBRUMsQ0FBQUEsRUFBRyxHQUFHaEcsS0FBSyxDQUFDaUcsTUFBTSxDQUFDQyxLQUFLO2dCQUNuQ2xHLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ0MsWUFBWSxDQUFDLFlBQVksRUFBRSxNQUFNLENBQUM7Z0JBQ3REN0YsS0FBSyxDQUFDNEYsYUFBYSxDQUFDbkMsS0FBSyxDQUFDMEMsV0FBVyxDQUFDLDRCQUE0QixFQUFHLEdBQUVKLENBQUUsSUFBRyxDQUFDO2dCQUM3RS9GLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ25DLEtBQUssQ0FBQzBDLFdBQVcsQ0FBQyw0QkFBNEIsRUFBRyxHQUFFSCxDQUFFLElBQUcsQ0FBQztZQUMvRSxDQUFDLENBQUM7WUFDRkksYUFBYSxFQUFFN0ssMEVBQW9CLENBQUN5QixLQUFLLENBQUNvSixhQUFhLEdBQUdwRyxLQUFLLElBQUs7Z0JBQ2xFQSxLQUFLLENBQUM0RixhQUFhLENBQUNDLFlBQVksQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDO2dCQUN4RDdGLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ25DLEtBQUssQ0FBQzRDLGNBQWMsQ0FBQyw0QkFBNEIsQ0FBQztnQkFDdEVyRyxLQUFLLENBQUM0RixhQUFhLENBQUNuQyxLQUFLLENBQUM0QyxjQUFjLENBQUMsNEJBQTRCLENBQUM7Z0JBQ3RFckcsS0FBSyxDQUFDNEYsYUFBYSxDQUFDbkMsS0FBSyxDQUFDNEMsY0FBYyxDQUFDLDJCQUEyQixDQUFDO2dCQUNyRXJHLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ25DLEtBQUssQ0FBQzRDLGNBQWMsQ0FBQywyQkFBMkIsQ0FBQztZQUN2RSxDQUFDLENBQUM7WUFDRkMsVUFBVSxFQUFFL0ssMEVBQW9CLENBQUN5QixLQUFLLENBQUNzSixVQUFVLEdBQUd0RyxLQUFLLElBQUs7Z0JBQzVELE1BQU0sRUFBRStGLENBQUMsRUFBRUMsQ0FBQUEsRUFBRyxHQUFHaEcsS0FBSyxDQUFDaUcsTUFBTSxDQUFDQyxLQUFLO2dCQUNuQ2xHLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ0MsWUFBWSxDQUFDLFlBQVksRUFBRSxLQUFLLENBQUM7Z0JBQ3JEN0YsS0FBSyxDQUFDNEYsYUFBYSxDQUFDbkMsS0FBSyxDQUFDNEMsY0FBYyxDQUFDLDRCQUE0QixDQUFDO2dCQUN0RXJHLEtBQUssQ0FBQzRGLGFBQWEsQ0FBQ25DLEtBQUssQ0FBQzRDLGNBQWMsQ0FBQyw0QkFBNEIsQ0FBQztnQkFDdEVyRyxLQUFLLENBQUM0RixhQUFhLENBQUNuQyxLQUFLLENBQUMwQyxXQUFXLENBQUMsMkJBQTJCLEVBQUcsR0FBRUosQ0FBRSxJQUFHLENBQUM7Z0JBQzVFL0YsS0FBSyxDQUFDNEYsYUFBYSxDQUFDbkMsS0FBSyxDQUFDMEMsV0FBVyxDQUFDLDJCQUEyQixFQUFHLEdBQUVILENBQUUsSUFBRyxDQUFDO2dCQUM1RWYsT0FBTyxDQUFDLEtBQUssQ0FBQztZQUNoQixDQUFDO1FBQ0gsQ0FDRjtJQUFFLENBQUMsQ0FBQztBQUNOLENBQ0YsQ0FBQztBQUNEUCxLQUFLLENBQUNqRyxXQUFXLEdBQUc0RixVQUFVO0FBQzlCLElBQUksQ0FBQ2tDLHdCQUF3QixFQUFFQywwQkFBMEIsQ0FBQyxHQUFHN0osa0JBQWtCLENBQUMwSCxVQUFVLEVBQUU7SUFDMUZtQixPQUFPQSxDQUFBLEVBQUcsRUFDVjtBQUNGLENBQUMsQ0FBQztBQUNGLElBQUlELFNBQVMsaUJBQUdsSyw2Q0FBZ0IsQ0FDOUIsQ0FBQzJCLEtBQUssRUFBRWdDLFlBQVk7SUFDbEIsTUFBTSxFQUNKL0IsWUFBWSxFQUNad0osSUFBSSxHQUFHLFlBQVksRUFDbkJ0SixRQUFRLEVBQUV1SixZQUFZLEVBQ3RCOUIsSUFBSSxFQUNKWSxPQUFPLEVBQ1BtQixlQUFlLEVBQ2ZsQixPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsWUFBWSxFQUNaRyxXQUFXLEVBQ1hNLGFBQWEsRUFDYkUsVUFBVSxFQUNWLEdBQUd0QixZQUNKLEdBQUdoSSxLQUFLO0lBQ1QsTUFBTW1DLE9BQU8sR0FBR3JDLHVCQUF1QixDQUFDdUgsVUFBVSxFQUFFcEgsWUFBWSxDQUFDO0lBQ2pFLE1BQU0sQ0FBQzJKLElBQUksRUFBRUMsT0FBTyxDQUFDLEdBQUd4TCwyQ0FBYyxDQUFDLElBQUksQ0FBQztJQUM1QyxNQUFNb0UsWUFBWSxHQUFHakUsNkVBQWUsQ0FBQ3dELFlBQVk7b0RBQUc4SCxLQUFLLEdBQUtELE9BQU8sQ0FBQ0MsS0FBSyxDQUFDOztJQUM1RSxNQUFNQyxlQUFlLEdBQUcxTCx5Q0FBWSxDQUFDLElBQUksQ0FBQztJQUMxQyxNQUFNMkwsYUFBYSxHQUFHM0wseUNBQVksQ0FBQyxJQUFJLENBQUM7SUFDeEMsTUFBTThCLFFBQVEsR0FBR3VKLFlBQVksSUFBSXZILE9BQU8sQ0FBQ2hDLFFBQVE7SUFDakQsTUFBTThKLHNCQUFzQixHQUFHNUwseUNBQVksQ0FBQyxDQUFDLENBQUM7SUFDOUMsTUFBTTZMLDBCQUEwQixHQUFHN0wseUNBQVksQ0FBQzhCLFFBQVEsQ0FBQztJQUN6RCxNQUFNZ0ssYUFBYSxHQUFHOUwseUNBQVksQ0FBQyxDQUFDLENBQUM7SUFDckMsTUFBTSxFQUFFZ0QsVUFBVSxFQUFFRyxhQUFBQSxFQUFlLEdBQUdXLE9BQU87SUFDN0MsTUFBTWlJLFdBQVcsR0FBR3BMLGlGQUFjO2lEQUFDO1lBQ2pDLE1BQU1xTCxjQUFjLEdBQUdULElBQUksRUFBRXhGLFFBQVEsQ0FBQ1osUUFBUSxDQUFDZ0IsYUFBYSxDQUFDO1lBQzdELElBQUk2RixjQUFjLEVBQUVsSSxPQUFPLENBQUM1QixRQUFRLEVBQUVnRCxLQUFLLENBQUMsQ0FBQztZQUM3Q2lGLE9BQU8sQ0FBQyxDQUFDO1FBQ1gsQ0FBQzs7SUFDRCxNQUFNOEIsVUFBVSxHQUFHak0sOENBQWlCOzhDQUNqQ2tNLFNBQVMsSUFBSztZQUNiLElBQUksQ0FBQ0EsU0FBUyxJQUFJQSxTQUFTLEtBQUtDLFFBQVEsRUFBRTtZQUMxQy9GLE1BQU0sQ0FBQ2dHLFlBQVksQ0FBQ04sYUFBYSxDQUFDN0csT0FBTyxDQUFDO1lBQzFDMkcsc0JBQXNCLENBQUMzRyxPQUFPLEdBQUksZ0JBQWdCLElBQUlvSCxJQUFJLENBQUMsQ0FBQyxDQUFFQyxPQUFPLENBQUMsQ0FBQztZQUN2RVIsYUFBYSxDQUFDN0csT0FBTyxHQUFHbUIsTUFBTSxDQUFDbUcsVUFBVSxDQUFDUixXQUFXLEVBQUVHLFNBQVMsQ0FBQztRQUNuRSxDQUFDOzRDQUNEO1FBQUNILFdBQVc7S0FDZCxDQUFDO0lBQ0QvTCw0Q0FBZTsrQkFBQztZQUNkLE1BQU1rQyxRQUFRLEdBQUc0QixPQUFPLENBQUM1QixRQUFRO1lBQ2pDLElBQUlBLFFBQVEsRUFBRTtnQkFDWixNQUFNeUQsWUFBWTt3REFBR0EsQ0FBQTt3QkFDbkJzRyxVQUFVLENBQUNKLDBCQUEwQixDQUFDNUcsT0FBTyxDQUFDO3dCQUM5Q29GLFFBQVEsR0FBRyxDQUFDO29CQUNkLENBQUM7O2dCQUNELE1BQU05RSxXQUFXO3VEQUFHQSxDQUFBO3dCQUNsQixNQUFNaUgsV0FBVyxHQUFJLGdCQUFnQixJQUFJSCxJQUFJLENBQUMsQ0FBQyxDQUFFQyxPQUFPLENBQUMsQ0FBQyxHQUFHVixzQkFBc0IsQ0FBQzNHLE9BQU87d0JBQzNGNEcsMEJBQTBCLENBQUM1RyxPQUFPLEdBQUc0RywwQkFBMEIsQ0FBQzVHLE9BQU8sR0FBR3VILFdBQVc7d0JBQ3JGcEcsTUFBTSxDQUFDZ0csWUFBWSxDQUFDTixhQUFhLENBQUM3RyxPQUFPLENBQUM7d0JBQzFDbUYsT0FBTyxHQUFHLENBQUM7b0JBQ2IsQ0FBQzs7Z0JBQ0RsSSxRQUFRLENBQUNrRCxnQkFBZ0IsQ0FBQzdCLGNBQWMsRUFBRWdDLFdBQVcsQ0FBQztnQkFDdERyRCxRQUFRLENBQUNrRCxnQkFBZ0IsQ0FBQzVCLGVBQWUsRUFBRW1DLFlBQVksQ0FBQztnQkFDeEQ7MkNBQU87d0JBQ0x6RCxRQUFRLENBQUNtRCxtQkFBbUIsQ0FBQzlCLGNBQWMsRUFBRWdDLFdBQVcsQ0FBQzt3QkFDekRyRCxRQUFRLENBQUNtRCxtQkFBbUIsQ0FBQzdCLGVBQWUsRUFBRW1DLFlBQVksQ0FBQztvQkFDN0QsQ0FBQzs7WUFDSDtRQUNGLENBQUM7OEJBQUU7UUFBQzdCLE9BQU8sQ0FBQzVCLFFBQVE7UUFBRUosUUFBUTtRQUFFc0ksT0FBTztRQUFFQyxRQUFRO1FBQUU0QixVQUFVO0tBQUMsQ0FBQztJQUMvRGpNLDRDQUFlOytCQUFDO1lBQ2QsSUFBSXVKLElBQUksSUFBSSxDQUFDekYsT0FBTyxDQUFDckIsZ0JBQWdCLENBQUN3QyxPQUFPLEVBQUVnSCxVQUFVLENBQUNuSyxRQUFRLENBQUM7UUFDckUsQ0FBQzs4QkFBRTtRQUFDeUgsSUFBSTtRQUFFekgsUUFBUTtRQUFFZ0MsT0FBTyxDQUFDckIsZ0JBQWdCO1FBQUV3SixVQUFVO0tBQUMsQ0FBQztJQUMxRGpNLDRDQUFlOytCQUFDO1lBQ2RnRCxVQUFVLENBQUMsQ0FBQztZQUNaO3VDQUFPLElBQU1HLGFBQWEsQ0FBQyxDQUFDOztRQUM5QixDQUFDOzhCQUFFO1FBQUNILFVBQVU7UUFBRUcsYUFBYTtLQUFDLENBQUM7SUFDL0IsTUFBTXNKLG1CQUFtQixHQUFHek0sMENBQWE7a0RBQUM7WUFDeEMsT0FBT3VMLElBQUksR0FBR29CLHNCQUFzQixDQUFDcEIsSUFBSSxDQUFDLEdBQUcsSUFBSTtRQUNuRCxDQUFDO2lEQUFFO1FBQUNBLElBQUk7S0FBQyxDQUFDO0lBQ1YsSUFBSSxDQUFDekgsT0FBTyxDQUFDNUIsUUFBUSxFQUFFLE9BQU8sSUFBSTtJQUNsQyxPQUFPLGdCQUFnQmpCLHVEQUFJLENBQUNGLHVEQUFRLEVBQUU7UUFBRWtCLFFBQVEsRUFBRTtZQUNoRHdLLG1CQUFtQixJQUFJLGdCQUFnQnpMLHNEQUFHLENBQ3hDNEwsYUFBYSxFQUNiO2dCQUNFaEwsWUFBWTtnQkFDWnNHLElBQUksRUFBRSxRQUFRO2dCQUNkLFdBQVcsRUFBRWtELElBQUksS0FBSyxZQUFZLEdBQUcsV0FBVyxHQUFHLFFBQVE7Z0JBQzNELGFBQWEsRUFBRSxJQUFJO2dCQUNuQm5KLFFBQVEsRUFBRXdLO1lBQ1osQ0FDRixDQUFDO1lBQ0QsZ0JBQWdCekwsc0RBQUcsQ0FBQ2tLLHdCQUF3QixFQUFFO2dCQUFFcEksS0FBSyxFQUFFbEIsWUFBWTtnQkFBRXVJLE9BQU8sRUFBRTRCLFdBQVc7Z0JBQUU5SixRQUFRLGdCQUFFaEMsbURBQXFCLENBQ3hILGdCQUFnQmUsc0RBQUcsQ0FBQ0csVUFBVSxDQUFDMkwsUUFBUSxFQUFFO29CQUFFaEssS0FBSyxFQUFFbEIsWUFBWTtvQkFBRUssUUFBUSxFQUFFLGdCQUFnQmpCLHNEQUFHLENBQzNGVixtRUFBcUIsRUFDckI7d0JBQ0UwTSxPQUFPLEVBQUUsSUFBSTt3QkFDYjFCLGVBQWUsRUFBRXBMLDBFQUFvQixDQUFDb0wsZUFBZSxFQUFFOzRCQUNyRCxJQUFJLENBQUN4SCxPQUFPLENBQUN2Qiw4QkFBOEIsQ0FBQzBDLE9BQU8sRUFBRThHLFdBQVcsQ0FBQyxDQUFDOzRCQUNsRWpJLE9BQU8sQ0FBQ3ZCLDhCQUE4QixDQUFDMEMsT0FBTyxHQUFHLEtBQUs7d0JBQ3hELENBQUMsQ0FBQzt3QkFDRmhELFFBQVEsRUFBRSxnQkFBZ0JqQixzREFBRyxDQUMzQlAsZ0VBQVMsQ0FBQ3dNLEVBQUUsRUFDWjs0QkFDRS9FLElBQUksRUFBRSxRQUFROzRCQUNkLFdBQVcsRUFBRSxLQUFLOzRCQUNsQixhQUFhLEVBQUUsSUFBSTs0QkFDbkJDLFFBQVEsRUFBRSxDQUFDOzRCQUNYLFlBQVksRUFBRW9CLElBQUksR0FBRyxNQUFNLEdBQUcsUUFBUTs0QkFDdEMsc0JBQXNCLEVBQUV6RixPQUFPLENBQUMvQixjQUFjOzRCQUM5QyxHQUFHNEgsVUFBVTs0QkFDYnhGLEdBQUcsRUFBRUMsWUFBWTs0QkFDakJnRSxLQUFLLEVBQUU7Z0NBQUU4RSxVQUFVLEVBQUUsTUFBTTtnQ0FBRUMsV0FBVyxFQUFFLE1BQU07Z0NBQUUsR0FBR3hMLEtBQUssQ0FBQ3lHLEtBQUFBOzRCQUFNLENBQUM7NEJBQ2xFZ0YsU0FBUyxFQUFFbE4sMEVBQW9CLENBQUN5QixLQUFLLENBQUN5TCxTQUFTLEdBQUd6SSxLQUFLLElBQUs7Z0NBQzFELElBQUlBLEtBQUssQ0FBQ0ksR0FBRyxLQUFLLFFBQVEsRUFBRTtnQ0FDNUJ1RyxlQUFlLEdBQUczRyxLQUFLLENBQUMwSSxXQUFXLENBQUM7Z0NBQ3BDLElBQUksQ0FBQzFJLEtBQUssQ0FBQzBJLFdBQVcsQ0FBQ0MsZ0JBQWdCLEVBQUU7b0NBQ3ZDeEosT0FBTyxDQUFDdkIsOEJBQThCLENBQUMwQyxPQUFPLEdBQUcsSUFBSTtvQ0FDckQ4RyxXQUFXLENBQUMsQ0FBQztnQ0FDZjs0QkFDRixDQUFDLENBQUM7NEJBQ0Z3QixhQUFhLEVBQUVyTiwwRUFBb0IsQ0FBQ3lCLEtBQUssQ0FBQzRMLGFBQWEsR0FBRzVJLEtBQUssSUFBSztnQ0FDbEUsSUFBSUEsS0FBSyxDQUFDNkksTUFBTSxLQUFLLENBQUMsRUFBRTtnQ0FDeEI5QixlQUFlLENBQUN6RyxPQUFPLEdBQUc7b0NBQUV5RixDQUFDLEVBQUUvRixLQUFLLENBQUM4SSxPQUFPO29DQUFFOUMsQ0FBQyxFQUFFaEcsS0FBSyxDQUFDK0ksT0FBQUE7Z0NBQVEsQ0FBQzs0QkFDbEUsQ0FBQyxDQUFDOzRCQUNGQyxhQUFhLEVBQUV6TiwwRUFBb0IsQ0FBQ3lCLEtBQUssQ0FBQ2dNLGFBQWEsR0FBR2hKLEtBQUssSUFBSztnQ0FDbEUsSUFBSSxDQUFDK0csZUFBZSxDQUFDekcsT0FBTyxFQUFFO2dDQUM5QixNQUFNeUYsQ0FBQyxHQUFHL0YsS0FBSyxDQUFDOEksT0FBTyxHQUFHL0IsZUFBZSxDQUFDekcsT0FBTyxDQUFDeUYsQ0FBQztnQ0FDbkQsTUFBTUMsQ0FBQyxHQUFHaEcsS0FBSyxDQUFDK0ksT0FBTyxHQUFHaEMsZUFBZSxDQUFDekcsT0FBTyxDQUFDMEYsQ0FBQztnQ0FDbkQsTUFBTWlELG1CQUFtQixHQUFHQyxPQUFPLENBQUNsQyxhQUFhLENBQUMxRyxPQUFPLENBQUM7Z0NBQzFELE1BQU02SSxpQkFBaUIsR0FBRztvQ0FBQyxNQUFNO29DQUFFLE9BQU87aUNBQUMsQ0FBQ0MsUUFBUSxDQUFDakssT0FBTyxDQUFDL0IsY0FBYyxDQUFDO2dDQUM1RSxNQUFNaU0sS0FBSyxHQUFHO29DQUFDLE1BQU07b0NBQUUsSUFBSTtpQ0FBQyxDQUFDRCxRQUFRLENBQUNqSyxPQUFPLENBQUMvQixjQUFjLENBQUMsR0FBR2tNLElBQUksQ0FBQ0MsR0FBRyxHQUFHRCxJQUFJLENBQUNFLEdBQUc7Z0NBQ25GLE1BQU1DLFFBQVEsR0FBR04saUJBQWlCLEdBQUdFLEtBQUssQ0FBQyxDQUFDLEVBQUV0RCxDQUFDLENBQUMsR0FBRyxDQUFDO2dDQUNwRCxNQUFNMkQsUUFBUSxHQUFHLENBQUNQLGlCQUFpQixHQUFHRSxLQUFLLENBQUMsQ0FBQyxFQUFFckQsQ0FBQyxDQUFDLEdBQUcsQ0FBQztnQ0FDckQsTUFBTTJELGVBQWUsR0FBRzNKLEtBQUssQ0FBQzRKLFdBQVcsS0FBSyxPQUFPLEdBQUcsRUFBRSxHQUFHLENBQUM7Z0NBQzlELE1BQU0xRCxLQUFLLEdBQUc7b0NBQUVILENBQUMsRUFBRTBELFFBQVE7b0NBQUV6RCxDQUFDLEVBQUUwRDtnQ0FBUyxDQUFDO2dDQUMxQyxNQUFNRyxXQUFXLEdBQUc7b0NBQUVDLGFBQWEsRUFBRTlKLEtBQUs7b0NBQUVrRztnQ0FBTSxDQUFDO2dDQUNuRCxJQUFJK0MsbUJBQW1CLEVBQUU7b0NBQ3ZCakMsYUFBYSxDQUFDMUcsT0FBTyxHQUFHNEYsS0FBSztvQ0FDN0I2RCw0QkFBNEIsQ0FBQ3hGLGdCQUFnQixFQUFFdUIsV0FBVyxFQUFFK0QsV0FBVyxFQUFFO3dDQUN2RUcsUUFBUSxFQUFFO29DQUNaLENBQUMsQ0FBQztnQ0FDSixDQUFDLE1BQU0sSUFBSUMsa0JBQWtCLENBQUMvRCxLQUFLLEVBQUUvRyxPQUFPLENBQUMvQixjQUFjLEVBQUV1TSxlQUFlLENBQUMsRUFBRTtvQ0FDN0UzQyxhQUFhLENBQUMxRyxPQUFPLEdBQUc0RixLQUFLO29DQUM3QjZELDRCQUE0QixDQUFDekYsaUJBQWlCLEVBQUVxQixZQUFZLEVBQUVrRSxXQUFXLEVBQUU7d0NBQ3pFRyxRQUFRLEVBQUU7b0NBQ1osQ0FBQyxDQUFDO29DQUNGaEssS0FBSyxDQUFDOEMsTUFBTSxDQUFDb0gsaUJBQWlCLENBQUNsSyxLQUFLLENBQUNtSyxTQUFTLENBQUM7Z0NBQ2pELENBQUMsTUFBTSxJQUFJYixJQUFJLENBQUNjLEdBQUcsQ0FBQ3JFLENBQUMsQ0FBQyxHQUFHNEQsZUFBZSxJQUFJTCxJQUFJLENBQUNjLEdBQUcsQ0FBQ3BFLENBQUMsQ0FBQyxHQUFHMkQsZUFBZSxFQUFFO29DQUN6RTVDLGVBQWUsQ0FBQ3pHLE9BQU8sR0FBRyxJQUFJO2dDQUNoQzs0QkFDRixDQUFDLENBQUM7NEJBQ0YrSixXQUFXLEVBQUU5TywwRUFBb0IsQ0FBQ3lCLEtBQUssQ0FBQ3FOLFdBQVcsR0FBR3JLLEtBQUssSUFBSztnQ0FDOUQsTUFBTWtHLEtBQUssR0FBR2MsYUFBYSxDQUFDMUcsT0FBTztnQ0FDbkMsTUFBTXdDLE1BQU0sR0FBRzlDLEtBQUssQ0FBQzhDLE1BQU07Z0NBQzNCLElBQUlBLE1BQU0sQ0FBQ3dILGlCQUFpQixDQUFDdEssS0FBSyxDQUFDbUssU0FBUyxDQUFDLEVBQUU7b0NBQzdDckgsTUFBTSxDQUFDeUgscUJBQXFCLENBQUN2SyxLQUFLLENBQUNtSyxTQUFTLENBQUM7Z0NBQy9DO2dDQUNBbkQsYUFBYSxDQUFDMUcsT0FBTyxHQUFHLElBQUk7Z0NBQzVCeUcsZUFBZSxDQUFDekcsT0FBTyxHQUFHLElBQUk7Z0NBQzlCLElBQUk0RixLQUFLLEVBQUU7b0NBQ1QsTUFBTXNFLEtBQUssR0FBR3hLLEtBQUssQ0FBQzRGLGFBQWE7b0NBQ2pDLE1BQU1pRSxXQUFXLEdBQUc7d0NBQUVDLGFBQWEsRUFBRTlKLEtBQUs7d0NBQUVrRztvQ0FBTSxDQUFDO29DQUNuRCxJQUFJK0Qsa0JBQWtCLENBQUMvRCxLQUFLLEVBQUUvRyxPQUFPLENBQUMvQixjQUFjLEVBQUUrQixPQUFPLENBQUM5QixjQUFjLENBQUMsRUFBRTt3Q0FDN0UwTSw0QkFBNEIsQ0FBQ3RGLGVBQWUsRUFBRTZCLFVBQVUsRUFBRXVELFdBQVcsRUFBRTs0Q0FDckVHLFFBQVEsRUFBRTt3Q0FDWixDQUFDLENBQUM7b0NBQ0osQ0FBQyxNQUFNO3dDQUNMRCw0QkFBNEIsQ0FDMUJ2RixrQkFBa0IsRUFDbEI0QixhQUFhLEVBQ2J5RCxXQUFXLEVBQ1g7NENBQ0VHLFFBQVEsRUFBRTt3Q0FDWixDQUNGLENBQUM7b0NBQ0g7b0NBQ0FRLEtBQUssQ0FBQy9KLGdCQUFnQixDQUFDLE9BQU8sR0FBR2dLLE1BQU0sR0FBS0EsTUFBTSxDQUFDcEgsY0FBYyxDQUFDLENBQUMsRUFBRTt3Q0FDbkVxSCxJQUFJLEVBQUU7b0NBQ1IsQ0FBQyxDQUFDO2dDQUNKOzRCQUNGLENBQUM7d0JBQ0gsQ0FDRjtvQkFDRixDQUNGO2dCQUFFLENBQUMsQ0FBQyxFQUNKdkwsT0FBTyxDQUFDNUIsUUFDVjtZQUFFLENBQUMsQ0FBQztTQUFBO0lBQ0osQ0FBQyxDQUFDO0FBQ04sQ0FDRixDQUFDO0FBQ0QsSUFBSTBLLGFBQWEsSUFBSWpMLEtBQUssSUFBSztJQUM3QixNQUFNLEVBQUVDLFlBQVksRUFBRUssUUFBUSxFQUFFLEdBQUdxTixlQUFlLEdBQUczTixLQUFLO0lBQzFELE1BQU1tQyxPQUFPLEdBQUdyQyx1QkFBdUIsQ0FBQ3VILFVBQVUsRUFBRXBILFlBQVksQ0FBQztJQUNqRSxNQUFNLENBQUMyTixrQkFBa0IsRUFBRUMscUJBQXFCLENBQUMsR0FBR3hQLDJDQUFjLENBQUMsS0FBSyxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ3lQLFdBQVcsRUFBRUMsY0FBYyxDQUFDLEdBQUcxUCwyQ0FBYyxDQUFDLEtBQUssQ0FBQztJQUMzRDJQLFlBQVk7c0NBQUMsSUFBTUgscUJBQXFCLENBQUMsSUFBSSxDQUFDOztJQUM5Q3hQLDRDQUFlO21DQUFDO1lBQ2QsTUFBTTRQLEtBQUssR0FBR3hKLE1BQU0sQ0FBQ21HLFVBQVU7aURBQUMsSUFBTW1ELGNBQWMsQ0FBQyxJQUFJLENBQUM7Z0RBQUUsR0FBRyxDQUFDO1lBQ2hFOzJDQUFPLElBQU10SixNQUFNLENBQUNnRyxZQUFZLENBQUN3RCxLQUFLLENBQUM7O1FBQ3pDLENBQUM7a0NBQUUsRUFBRSxDQUFDO0lBQ04sT0FBT0gsV0FBVyxHQUFHLElBQUksR0FBRyxnQkFBZ0J6TyxzREFBRyxDQUFDVCwyREFBTSxFQUFFO1FBQUV5TSxPQUFPLEVBQUUsSUFBSTtRQUFFL0ssUUFBUSxFQUFFLGdCQUFnQmpCLHNEQUFHLENBQUNGLDJFQUFjLEVBQUU7WUFBRSxHQUFHd08sYUFBYTtZQUFFck4sUUFBUSxFQUFFc04sa0JBQWtCLElBQUksZ0JBQWdCdE8sdURBQUksQ0FBQ0YsdURBQVEsRUFBRTtnQkFBRWtCLFFBQVEsRUFBRTtvQkFDcE42QixPQUFPLENBQUNqQyxLQUFLO29CQUNiLEdBQUc7b0JBQ0hJLFFBQVE7aUJBQUE7WUFDUixDQUFDO1FBQUUsQ0FBQztJQUFFLENBQUMsQ0FBQztBQUNaLENBQUM7QUFDRCxJQUFJNE4sVUFBVSxHQUFHLFlBQVk7QUFDN0IsSUFBSUMsVUFBVSxpQkFBRzlQLDZDQUFnQixDQUMvQixDQUFDMkIsS0FBSyxFQUFFZ0MsWUFBWTtJQUNsQixNQUFNLEVBQUUvQixZQUFZLEVBQUUsR0FBR21PLFlBQVksR0FBR3BPLEtBQUs7SUFDN0MsT0FBTyxnQkFBZ0JYLHNEQUFHLENBQUNQLGdFQUFTLENBQUN1UCxHQUFHLEVBQUU7UUFBRSxHQUFHRCxVQUFVO1FBQUU1TCxHQUFHLEVBQUVSO0lBQWEsQ0FBQyxDQUFDO0FBQ2pGLENBQ0YsQ0FBQztBQUNEbU0sVUFBVSxDQUFDMU0sV0FBVyxHQUFHeU0sVUFBVTtBQUNuQyxJQUFJSSxnQkFBZ0IsR0FBRyxrQkFBa0I7QUFDekMsSUFBSUMsZ0JBQWdCLGlCQUFHbFEsNkNBQWdCLENBQ3JDLENBQUMyQixLQUFLLEVBQUVnQyxZQUFZO0lBQ2xCLE1BQU0sRUFBRS9CLFlBQVksRUFBRSxHQUFHdU8sa0JBQWtCLEdBQUd4TyxLQUFLO0lBQ25ELE9BQU8sZ0JBQWdCWCxzREFBRyxDQUFDUCxnRUFBUyxDQUFDdVAsR0FBRyxFQUFFO1FBQUUsR0FBR0csZ0JBQWdCO1FBQUVoTSxHQUFHLEVBQUVSO0lBQWEsQ0FBQyxDQUFDO0FBQ3ZGLENBQ0YsQ0FBQztBQUNEdU0sZ0JBQWdCLENBQUM5TSxXQUFXLEdBQUc2TSxnQkFBZ0I7QUFDL0MsSUFBSUcsV0FBVyxHQUFHLGFBQWE7QUFDL0IsSUFBSUMsV0FBVyxpQkFBR3JRLDZDQUFnQixDQUNoQyxDQUFDMkIsS0FBSyxFQUFFZ0MsWUFBWTtJQUNsQixNQUFNLEVBQUUyTSxPQUFPLEVBQUUsR0FBR0MsYUFBYSxHQUFHNU8sS0FBSztJQUN6QyxJQUFJLENBQUMyTyxPQUFPLENBQUM1TixJQUFJLENBQUMsQ0FBQyxFQUFFO1FBQ25CQyxPQUFPLENBQUNDLEtBQUssQ0FDViwwQ0FBeUN3TixXQUFZLG9DQUN4RCxDQUFDO1FBQ0QsT0FBTyxJQUFJO0lBQ2I7SUFDQSxPQUFPLGdCQUFnQnBQLHNEQUFHLENBQUN3UCxvQkFBb0IsRUFBRTtRQUFFRixPQUFPO1FBQUV0RCxPQUFPLEVBQUUsSUFBSTtRQUFFL0ssUUFBUSxFQUFFLGdCQUFnQmpCLHNEQUFHLENBQUN5UCxVQUFVLEVBQUU7WUFBRSxHQUFHRixXQUFXO1lBQUVwTSxHQUFHLEVBQUVSO1FBQWEsQ0FBQztJQUFFLENBQUMsQ0FBQztBQUNoSyxDQUNGLENBQUM7QUFDRDBNLFdBQVcsQ0FBQ2pOLFdBQVcsR0FBR2dOLFdBQVc7QUFDckMsSUFBSU0sVUFBVSxHQUFHLFlBQVk7QUFDN0IsSUFBSUQsVUFBVSxpQkFBR3pRLDZDQUFnQixDQUMvQixDQUFDMkIsS0FBSyxFQUFFZ0MsWUFBWTtJQUNsQixNQUFNLEVBQUUvQixZQUFZLEVBQUUsR0FBRytPLFlBQVksR0FBR2hQLEtBQUs7SUFDN0MsTUFBTWlQLGtCQUFrQixHQUFHekYsMEJBQTBCLENBQUN1RixVQUFVLEVBQUU5TyxZQUFZLENBQUM7SUFDL0UsT0FBTyxnQkFBZ0JaLHNEQUFHLENBQUN3UCxvQkFBb0IsRUFBRTtRQUFFeEQsT0FBTyxFQUFFLElBQUk7UUFBRS9LLFFBQVEsRUFBRSxnQkFBZ0JqQixzREFBRyxDQUM3RlAsZ0VBQVMsQ0FBQytNLE1BQU0sRUFDaEI7WUFDRXBDLElBQUksRUFBRSxRQUFRO1lBQ2QsR0FBR3VGLFVBQVU7WUFDYnhNLEdBQUcsRUFBRVIsWUFBWTtZQUNqQmtOLE9BQU8sRUFBRTNRLDBFQUFvQixDQUFDeUIsS0FBSyxDQUFDa1AsT0FBTyxFQUFFRCxrQkFBa0IsQ0FBQ3pHLE9BQU87UUFDekUsQ0FDRjtJQUFFLENBQUMsQ0FBQztBQUNOLENBQ0YsQ0FBQztBQUNEc0csVUFBVSxDQUFDck4sV0FBVyxHQUFHc04sVUFBVTtBQUNuQyxJQUFJRixvQkFBb0IsaUJBQUd4USw2Q0FBZ0IsQ0FBQyxDQUFDMkIsS0FBSyxFQUFFZ0MsWUFBWTtJQUM5RCxNQUFNLEVBQUUvQixZQUFZLEVBQUUwTyxPQUFPLEVBQUUsR0FBR1Esc0JBQXNCLEdBQUduUCxLQUFLO0lBQ2hFLE9BQU8sZ0JBQWdCWCxzREFBRyxDQUN4QlAsZ0VBQVMsQ0FBQ3VQLEdBQUcsRUFDYjtRQUNFLG1DQUFtQyxFQUFFLEVBQUU7UUFDdkMsK0JBQStCLEVBQUVNLE9BQU8sSUFBSSxLQUFLLENBQUM7UUFDbEQsR0FBR1Esb0JBQW9CO1FBQ3ZCM00sR0FBRyxFQUFFUjtJQUNQLENBQ0YsQ0FBQztBQUNILENBQUMsQ0FBQztBQUNGLFNBQVNnSixzQkFBc0JBLENBQUNvRSxTQUFTLEVBQUU7SUFDekMsTUFBTUMsV0FBVyxHQUFHLEVBQUU7SUFDdEIsTUFBTUMsVUFBVSxHQUFHQyxLQUFLLENBQUNDLElBQUksQ0FBQ0osU0FBUyxDQUFDRSxVQUFVLENBQUM7SUFDbkRBLFVBQVUsQ0FBQ0csT0FBTyxFQUFFN0YsSUFBSSxJQUFLO1FBQzNCLElBQUlBLElBQUksQ0FBQzhGLFFBQVEsS0FBSzlGLElBQUksQ0FBQytGLFNBQVMsSUFBSS9GLElBQUksQ0FBQ3lGLFdBQVcsRUFBRUEsV0FBVyxDQUFDTyxJQUFJLENBQUNoRyxJQUFJLENBQUN5RixXQUFXLENBQUM7UUFDNUYsSUFBSVEsYUFBYSxDQUFDakcsSUFBSSxDQUFDLEVBQUU7WUFDdkIsTUFBTWtHLFFBQVEsR0FBR2xHLElBQUksQ0FBQ21HLFVBQVUsSUFBSW5HLElBQUksQ0FBQ29HLE1BQU0sSUFBSXBHLElBQUksQ0FBQ25ELEtBQUssQ0FBQ3dKLE9BQU8sS0FBSyxNQUFNO1lBQ2hGLE1BQU1DLFVBQVUsR0FBR3RHLElBQUksQ0FBQ3VHLE9BQU8sQ0FBQ0MseUJBQXlCLEtBQUssRUFBRTtZQUNoRSxJQUFJLENBQUNOLFFBQVEsRUFBRTtnQkFDYixJQUFJSSxVQUFVLEVBQUU7b0JBQ2QsTUFBTXZCLE9BQU8sR0FBRy9FLElBQUksQ0FBQ3VHLE9BQU8sQ0FBQ0UscUJBQXFCO29CQUNsRCxJQUFJMUIsT0FBTyxFQUFFVSxXQUFXLENBQUNPLElBQUksQ0FBQ2pCLE9BQU8sQ0FBQztnQkFDeEMsQ0FBQyxNQUFNO29CQUNMVSxXQUFXLENBQUNPLElBQUksQ0FBQyxHQUFHNUUsc0JBQXNCLENBQUNwQixJQUFJLENBQUMsQ0FBQztnQkFDbkQ7WUFDRjtRQUNGO0lBQ0YsQ0FBQyxDQUFDO0lBQ0YsT0FBT3lGLFdBQVc7QUFDcEI7QUFDQSxTQUFTdEMsNEJBQTRCQSxDQUFDdUQsSUFBSSxFQUFFQyxPQUFPLEVBQUV0SCxNQUFNLEVBQUUsRUFBRStELFFBQUFBLEVBQVUsRUFBRTtJQUN6RSxNQUFNcEUsYUFBYSxHQUFHSyxNQUFNLENBQUM2RCxhQUFhLENBQUNsRSxhQUFhO0lBQ3hELE1BQU01RixLQUFLLEdBQUcsSUFBSWMsV0FBVyxDQUFDd00sSUFBSSxFQUFFO1FBQUVFLE9BQU8sRUFBRSxJQUFJO1FBQUVDLFVBQVUsRUFBRSxJQUFJO1FBQUV4SDtJQUFPLENBQUMsQ0FBQztJQUNoRixJQUFJc0gsT0FBTyxFQUFFM0gsYUFBYSxDQUFDbkYsZ0JBQWdCLENBQUM2TSxJQUFJLEVBQUVDLE9BQU8sRUFBRTtRQUFFN0MsSUFBSSxFQUFFO0lBQUssQ0FBQyxDQUFDO0lBQzFFLElBQUlWLFFBQVEsRUFBRTtRQUNaak8sc0ZBQTJCLENBQUM2SixhQUFhLEVBQUU1RixLQUFLLENBQUM7SUFDbkQsQ0FBQyxNQUFNO1FBQ0w0RixhQUFhLENBQUM3RSxhQUFhLENBQUNmLEtBQUssQ0FBQztJQUNwQztBQUNGO0FBQ0EsSUFBSWlLLGtCQUFrQixHQUFHQSxDQUFDL0QsS0FBSyxFQUFFd0gsU0FBUyxFQUFFQyxTQUFTLEdBQUcsQ0FBQztJQUN2RCxNQUFNQyxNQUFNLEdBQUd0RSxJQUFJLENBQUNjLEdBQUcsQ0FBQ2xFLEtBQUssQ0FBQ0gsQ0FBQyxDQUFDO0lBQ2hDLE1BQU04SCxNQUFNLEdBQUd2RSxJQUFJLENBQUNjLEdBQUcsQ0FBQ2xFLEtBQUssQ0FBQ0YsQ0FBQyxDQUFDO0lBQ2hDLE1BQU04SCxRQUFRLEdBQUdGLE1BQU0sR0FBR0MsTUFBTTtJQUNoQyxJQUFJSCxTQUFTLEtBQUssTUFBTSxJQUFJQSxTQUFTLEtBQUssT0FBTyxFQUFFO1FBQ2pELE9BQU9JLFFBQVEsSUFBSUYsTUFBTSxHQUFHRCxTQUFTO0lBQ3ZDLENBQUMsTUFBTTtRQUNMLE9BQU8sQ0FBQ0csUUFBUSxJQUFJRCxNQUFNLEdBQUdGLFNBQVM7SUFDeEM7QUFDRixDQUFDO0FBQ0QsU0FBUzNDLFlBQVlBLENBQUMrQyxRQUFRLEdBQUdBLENBQUEsSUFDakMsQ0FEdUMsRUFDcEM7SUFDRCxNQUFNQyxFQUFFLEdBQUdoUyxpRkFBYyxDQUFDK1IsUUFBUSxDQUFDO0lBQ25DN1IsbUZBQWU7d0NBQUM7WUFDZCxJQUFJK1IsSUFBSSxHQUFHLENBQUM7WUFDWixJQUFJQyxJQUFJLEdBQUcsQ0FBQztZQUNaRCxJQUFJLEdBQUd4TSxNQUFNLENBQUMwTSxxQkFBcUI7Z0RBQUMsSUFBTUQsSUFBSSxHQUFHek0sTUFBTSxDQUFDME0scUJBQXFCLENBQUNILEVBQUUsQ0FBQzs7WUFDakY7Z0RBQU87b0JBQ0x2TSxNQUFNLENBQUMyTSxvQkFBb0IsQ0FBQ0gsSUFBSSxDQUFDO29CQUNqQ3hNLE1BQU0sQ0FBQzJNLG9CQUFvQixDQUFDRixJQUFJLENBQUM7Z0JBQ25DLENBQUM7O1FBQ0gsQ0FBQzt1Q0FBRTtRQUFDRixFQUFFO0tBQUMsQ0FBQztBQUNWO0FBQ0EsU0FBU25CLGFBQWFBLENBQUNqRyxJQUFJLEVBQUU7SUFDM0IsT0FBT0EsSUFBSSxDQUFDOEYsUUFBUSxLQUFLOUYsSUFBSSxDQUFDeUgsWUFBWTtBQUM1QztBQUNBLFNBQVNuTSxxQkFBcUJBLENBQUNrSyxTQUFTLEVBQUU7SUFDeEMsTUFBTWtDLEtBQUssR0FBRyxFQUFFO0lBQ2hCLE1BQU1DLE1BQU0sR0FBRy9OLFFBQVEsQ0FBQ2dPLGdCQUFnQixDQUFDcEMsU0FBUyxFQUFFcUMsVUFBVSxDQUFDQyxZQUFZLEVBQUU7UUFDM0VDLFVBQVUsR0FBRy9ILElBQUksSUFBSztZQUNwQixNQUFNZ0ksYUFBYSxHQUFHaEksSUFBSSxDQUFDaUksT0FBTyxLQUFLLE9BQU8sSUFBSWpJLElBQUksQ0FBQ0gsSUFBSSxLQUFLLFFBQVE7WUFDeEUsSUFBSUcsSUFBSSxDQUFDa0ksUUFBUSxJQUFJbEksSUFBSSxDQUFDb0csTUFBTSxJQUFJNEIsYUFBYSxFQUFFLE9BQU9ILFVBQVUsQ0FBQ00sV0FBVztZQUNoRixPQUFPbkksSUFBSSxDQUFDcEQsUUFBUSxJQUFJLENBQUMsR0FBR2lMLFVBQVUsQ0FBQ08sYUFBYSxHQUFHUCxVQUFVLENBQUNNLFdBQVc7UUFDL0U7SUFDRixDQUFDLENBQUM7SUFDRixNQUFPUixNQUFNLENBQUNVLFFBQVEsQ0FBQyxDQUFDLENBQUVYLEtBQUssQ0FBQzFCLElBQUksQ0FBQzJCLE1BQU0sQ0FBQ1csV0FBVyxDQUFDO0lBQ3hELE9BQU9aLEtBQUs7QUFDZDtBQUNBLFNBQVNuTCxVQUFVQSxDQUFDZ00sVUFBVSxFQUFFO0lBQzlCLE1BQU1DLHdCQUF3QixHQUFHNU8sUUFBUSxDQUFDZ0IsYUFBYTtJQUN2RCxPQUFPMk4sVUFBVSxDQUFDRSxJQUFJLEVBQUVuTSxTQUFTLElBQUs7UUFDcEMsSUFBSUEsU0FBUyxLQUFLa00sd0JBQXdCLEVBQUUsT0FBTyxJQUFJO1FBQ3ZEbE0sU0FBUyxDQUFDM0MsS0FBSyxDQUFDLENBQUM7UUFDakIsT0FBT0MsUUFBUSxDQUFDZ0IsYUFBYSxLQUFLNE4sd0JBQXdCO0lBQzVELENBQUMsQ0FBQztBQUNKO0FBQ0EsSUFBSWxSLFFBQVEsR0FBR25CLGFBQWE7QUFDNUIsSUFBSXVTLFFBQVEsR0FBR3hRLGFBQWE7QUFDNUIsSUFBSXlRLEtBQUssR0FBRzdLLEtBQUs7QUFDakIsSUFBSThLLEtBQUssR0FBR3JFLFVBQVU7QUFDdEIsSUFBSXNFLFdBQVcsR0FBR2xFLGdCQUFnQjtBQUNsQyxJQUFJbUUsTUFBTSxHQUFHaEUsV0FBVztBQUN4QixJQUFJaUUsS0FBSyxHQUFHN0QsVUFBVTtBQWdCSiIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdG9hc3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL3RvYXN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gXCJAcmFkaXgtdWkvcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmc1wiO1xuaW1wb3J0IHsgY3JlYXRlQ29sbGVjdGlvbiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtY29sbGVjdGlvblwiO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1jb250ZXh0XCI7XG5pbXBvcnQgKiBhcyBEaXNtaXNzYWJsZUxheWVyIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtZGlzbWlzc2FibGUtbGF5ZXJcIjtcbmltcG9ydCB7IFBvcnRhbCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcG9ydGFsXCI7XG5pbXBvcnQgeyBQcmVzZW5jZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJlc2VuY2VcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSwgZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmXCI7XG5pbXBvcnQgeyB1c2VDb250cm9sbGFibGVTdGF0ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZVwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xuaW1wb3J0IHsgVmlzdWFsbHlIaWRkZW4gfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlblwiO1xuaW1wb3J0IHsgRnJhZ21lbnQsIGpzeCwganN4cyB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIFBST1ZJREVSX05BTUUgPSBcIlRvYXN0UHJvdmlkZXJcIjtcbnZhciBbQ29sbGVjdGlvbiwgdXNlQ29sbGVjdGlvbiwgY3JlYXRlQ29sbGVjdGlvblNjb3BlXSA9IGNyZWF0ZUNvbGxlY3Rpb24oXCJUb2FzdFwiKTtcbnZhciBbY3JlYXRlVG9hc3RDb250ZXh0LCBjcmVhdGVUb2FzdFNjb3BlXSA9IGNyZWF0ZUNvbnRleHRTY29wZShcIlRvYXN0XCIsIFtjcmVhdGVDb2xsZWN0aW9uU2NvcGVdKTtcbnZhciBbVG9hc3RQcm92aWRlclByb3ZpZGVyLCB1c2VUb2FzdFByb3ZpZGVyQ29udGV4dF0gPSBjcmVhdGVUb2FzdENvbnRleHQoUFJPVklERVJfTkFNRSk7XG52YXIgVG9hc3RQcm92aWRlciA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7XG4gICAgX19zY29wZVRvYXN0LFxuICAgIGxhYmVsID0gXCJOb3RpZmljYXRpb25cIixcbiAgICBkdXJhdGlvbiA9IDVlMyxcbiAgICBzd2lwZURpcmVjdGlvbiA9IFwicmlnaHRcIixcbiAgICBzd2lwZVRocmVzaG9sZCA9IDUwLFxuICAgIGNoaWxkcmVuXG4gIH0gPSBwcm9wcztcbiAgY29uc3QgW3ZpZXdwb3J0LCBzZXRWaWV3cG9ydF0gPSBSZWFjdC51c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3RvYXN0Q291bnQsIHNldFRvYXN0Q291bnRdID0gUmVhY3QudXNlU3RhdGUoMCk7XG4gIGNvbnN0IGlzRm9jdXNlZFRvYXN0RXNjYXBlS2V5RG93blJlZiA9IFJlYWN0LnVzZVJlZihmYWxzZSk7XG4gIGNvbnN0IGlzQ2xvc2VQYXVzZWRSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICBpZiAoIWxhYmVsLnRyaW0oKSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICBgSW52YWxpZCBwcm9wIFxcYGxhYmVsXFxgIHN1cHBsaWVkIHRvIFxcYCR7UFJPVklERVJfTkFNRX1cXGAuIEV4cGVjdGVkIG5vbi1lbXB0eSBcXGBzdHJpbmdcXGAuYFxuICAgICk7XG4gIH1cbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29sbGVjdGlvbi5Qcm92aWRlciwgeyBzY29wZTogX19zY29wZVRvYXN0LCBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovIGpzeChcbiAgICBUb2FzdFByb3ZpZGVyUHJvdmlkZXIsXG4gICAge1xuICAgICAgc2NvcGU6IF9fc2NvcGVUb2FzdCxcbiAgICAgIGxhYmVsLFxuICAgICAgZHVyYXRpb24sXG4gICAgICBzd2lwZURpcmVjdGlvbixcbiAgICAgIHN3aXBlVGhyZXNob2xkLFxuICAgICAgdG9hc3RDb3VudCxcbiAgICAgIHZpZXdwb3J0LFxuICAgICAgb25WaWV3cG9ydENoYW5nZTogc2V0Vmlld3BvcnQsXG4gICAgICBvblRvYXN0QWRkOiBSZWFjdC51c2VDYWxsYmFjaygoKSA9PiBzZXRUb2FzdENvdW50KChwcmV2Q291bnQpID0+IHByZXZDb3VudCArIDEpLCBbXSksXG4gICAgICBvblRvYXN0UmVtb3ZlOiBSZWFjdC51c2VDYWxsYmFjaygoKSA9PiBzZXRUb2FzdENvdW50KChwcmV2Q291bnQpID0+IHByZXZDb3VudCAtIDEpLCBbXSksXG4gICAgICBpc0ZvY3VzZWRUb2FzdEVzY2FwZUtleURvd25SZWYsXG4gICAgICBpc0Nsb3NlUGF1c2VkUmVmLFxuICAgICAgY2hpbGRyZW5cbiAgICB9XG4gICkgfSk7XG59O1xuVG9hc3RQcm92aWRlci5kaXNwbGF5TmFtZSA9IFBST1ZJREVSX05BTUU7XG52YXIgVklFV1BPUlRfTkFNRSA9IFwiVG9hc3RWaWV3cG9ydFwiO1xudmFyIFZJRVdQT1JUX0RFRkFVTFRfSE9US0VZID0gW1wiRjhcIl07XG52YXIgVklFV1BPUlRfUEFVU0UgPSBcInRvYXN0LnZpZXdwb3J0UGF1c2VcIjtcbnZhciBWSUVXUE9SVF9SRVNVTUUgPSBcInRvYXN0LnZpZXdwb3J0UmVzdW1lXCI7XG52YXIgVG9hc3RWaWV3cG9ydCA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZVRvYXN0LFxuICAgICAgaG90a2V5ID0gVklFV1BPUlRfREVGQVVMVF9IT1RLRVksXG4gICAgICBsYWJlbCA9IFwiTm90aWZpY2F0aW9ucyAoe2hvdGtleX0pXCIsXG4gICAgICAuLi52aWV3cG9ydFByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VUb2FzdFByb3ZpZGVyQ29udGV4dChWSUVXUE9SVF9OQU1FLCBfX3Njb3BlVG9hc3QpO1xuICAgIGNvbnN0IGdldEl0ZW1zID0gdXNlQ29sbGVjdGlvbihfX3Njb3BlVG9hc3QpO1xuICAgIGNvbnN0IHdyYXBwZXJSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gICAgY29uc3QgaGVhZEZvY3VzUHJveHlSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gICAgY29uc3QgdGFpbEZvY3VzUHJveHlSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gICAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZiwgY29udGV4dC5vblZpZXdwb3J0Q2hhbmdlKTtcbiAgICBjb25zdCBob3RrZXlMYWJlbCA9IGhvdGtleS5qb2luKFwiK1wiKS5yZXBsYWNlKC9LZXkvZywgXCJcIikucmVwbGFjZSgvRGlnaXQvZywgXCJcIik7XG4gICAgY29uc3QgaGFzVG9hc3RzID0gY29udGV4dC50b2FzdENvdW50ID4gMDtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgICBjb25zdCBpc0hvdGtleVByZXNzZWQgPSBob3RrZXkubGVuZ3RoICE9PSAwICYmIGhvdGtleS5ldmVyeSgoa2V5KSA9PiBldmVudFtrZXldIHx8IGV2ZW50LmNvZGUgPT09IGtleSk7XG4gICAgICAgIGlmIChpc0hvdGtleVByZXNzZWQpIHJlZi5jdXJyZW50Py5mb2N1cygpO1xuICAgICAgfTtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pO1xuICAgICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pO1xuICAgIH0sIFtob3RrZXldKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3Qgd3JhcHBlciA9IHdyYXBwZXJSZWYuY3VycmVudDtcbiAgICAgIGNvbnN0IHZpZXdwb3J0ID0gcmVmLmN1cnJlbnQ7XG4gICAgICBpZiAoaGFzVG9hc3RzICYmIHdyYXBwZXIgJiYgdmlld3BvcnQpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlUGF1c2UgPSAoKSA9PiB7XG4gICAgICAgICAgaWYgKCFjb250ZXh0LmlzQ2xvc2VQYXVzZWRSZWYuY3VycmVudCkge1xuICAgICAgICAgICAgY29uc3QgcGF1c2VFdmVudCA9IG5ldyBDdXN0b21FdmVudChWSUVXUE9SVF9QQVVTRSk7XG4gICAgICAgICAgICB2aWV3cG9ydC5kaXNwYXRjaEV2ZW50KHBhdXNlRXZlbnQpO1xuICAgICAgICAgICAgY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzdW1lID0gKCkgPT4ge1xuICAgICAgICAgIGlmIChjb250ZXh0LmlzQ2xvc2VQYXVzZWRSZWYuY3VycmVudCkge1xuICAgICAgICAgICAgY29uc3QgcmVzdW1lRXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoVklFV1BPUlRfUkVTVU1FKTtcbiAgICAgICAgICAgIHZpZXdwb3J0LmRpc3BhdGNoRXZlbnQocmVzdW1lRXZlbnQpO1xuICAgICAgICAgICAgY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGhhbmRsZUZvY3VzT3V0UmVzdW1lID0gKGV2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgaXNGb2N1c01vdmluZ091dHNpZGUgPSAhd3JhcHBlci5jb250YWlucyhldmVudC5yZWxhdGVkVGFyZ2V0KTtcbiAgICAgICAgICBpZiAoaXNGb2N1c01vdmluZ091dHNpZGUpIGhhbmRsZVJlc3VtZSgpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBoYW5kbGVQb2ludGVyTGVhdmVSZXN1bWUgPSAoKSA9PiB7XG4gICAgICAgICAgY29uc3QgaXNGb2N1c0luc2lkZSA9IHdyYXBwZXIuY29udGFpbnMoZG9jdW1lbnQuYWN0aXZlRWxlbWVudCk7XG4gICAgICAgICAgaWYgKCFpc0ZvY3VzSW5zaWRlKSBoYW5kbGVSZXN1bWUoKTtcbiAgICAgICAgfTtcbiAgICAgICAgd3JhcHBlci5hZGRFdmVudExpc3RlbmVyKFwiZm9jdXNpblwiLCBoYW5kbGVQYXVzZSk7XG4gICAgICAgIHdyYXBwZXIuYWRkRXZlbnRMaXN0ZW5lcihcImZvY3Vzb3V0XCIsIGhhbmRsZUZvY3VzT3V0UmVzdW1lKTtcbiAgICAgICAgd3JhcHBlci5hZGRFdmVudExpc3RlbmVyKFwicG9pbnRlcm1vdmVcIiwgaGFuZGxlUGF1c2UpO1xuICAgICAgICB3cmFwcGVyLmFkZEV2ZW50TGlzdGVuZXIoXCJwb2ludGVybGVhdmVcIiwgaGFuZGxlUG9pbnRlckxlYXZlUmVzdW1lKTtcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJibHVyXCIsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJmb2N1c1wiLCBoYW5kbGVSZXN1bWUpO1xuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgIHdyYXBwZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImZvY3VzaW5cIiwgaGFuZGxlUGF1c2UpO1xuICAgICAgICAgIHdyYXBwZXIucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImZvY3Vzb3V0XCIsIGhhbmRsZUZvY3VzT3V0UmVzdW1lKTtcbiAgICAgICAgICB3cmFwcGVyLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJwb2ludGVybW92ZVwiLCBoYW5kbGVQYXVzZSk7XG4gICAgICAgICAgd3JhcHBlci5yZW1vdmVFdmVudExpc3RlbmVyKFwicG9pbnRlcmxlYXZlXCIsIGhhbmRsZVBvaW50ZXJMZWF2ZVJlc3VtZSk7XG4gICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJibHVyXCIsIGhhbmRsZVBhdXNlKTtcbiAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImZvY3VzXCIsIGhhbmRsZVJlc3VtZSk7XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSwgW2hhc1RvYXN0cywgY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmXSk7XG4gICAgY29uc3QgZ2V0U29ydGVkVGFiYmFibGVDYW5kaWRhdGVzID0gUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAoeyB0YWJiaW5nRGlyZWN0aW9uIH0pID0+IHtcbiAgICAgICAgY29uc3QgdG9hc3RJdGVtcyA9IGdldEl0ZW1zKCk7XG4gICAgICAgIGNvbnN0IHRhYmJhYmxlQ2FuZGlkYXRlcyA9IHRvYXN0SXRlbXMubWFwKCh0b2FzdEl0ZW0pID0+IHtcbiAgICAgICAgICBjb25zdCB0b2FzdE5vZGUgPSB0b2FzdEl0ZW0ucmVmLmN1cnJlbnQ7XG4gICAgICAgICAgY29uc3QgdG9hc3RUYWJiYWJsZUNhbmRpZGF0ZXMgPSBbdG9hc3ROb2RlLCAuLi5nZXRUYWJiYWJsZUNhbmRpZGF0ZXModG9hc3ROb2RlKV07XG4gICAgICAgICAgcmV0dXJuIHRhYmJpbmdEaXJlY3Rpb24gPT09IFwiZm9yd2FyZHNcIiA/IHRvYXN0VGFiYmFibGVDYW5kaWRhdGVzIDogdG9hc3RUYWJiYWJsZUNhbmRpZGF0ZXMucmV2ZXJzZSgpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuICh0YWJiaW5nRGlyZWN0aW9uID09PSBcImZvcndhcmRzXCIgPyB0YWJiYWJsZUNhbmRpZGF0ZXMucmV2ZXJzZSgpIDogdGFiYmFibGVDYW5kaWRhdGVzKS5mbGF0KCk7XG4gICAgICB9LFxuICAgICAgW2dldEl0ZW1zXVxuICAgICk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGNvbnN0IHZpZXdwb3J0ID0gcmVmLmN1cnJlbnQ7XG4gICAgICBpZiAodmlld3BvcnQpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgICAgIGNvbnN0IGlzTWV0YUtleSA9IGV2ZW50LmFsdEtleSB8fCBldmVudC5jdHJsS2V5IHx8IGV2ZW50Lm1ldGFLZXk7XG4gICAgICAgICAgY29uc3QgaXNUYWJLZXkgPSBldmVudC5rZXkgPT09IFwiVGFiXCIgJiYgIWlzTWV0YUtleTtcbiAgICAgICAgICBpZiAoaXNUYWJLZXkpIHtcbiAgICAgICAgICAgIGNvbnN0IGZvY3VzZWRFbGVtZW50ID0gZG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgICAgICAgICAgIGNvbnN0IGlzVGFiYmluZ0JhY2t3YXJkcyA9IGV2ZW50LnNoaWZ0S2V5O1xuICAgICAgICAgICAgY29uc3QgdGFyZ2V0SXNWaWV3cG9ydCA9IGV2ZW50LnRhcmdldCA9PT0gdmlld3BvcnQ7XG4gICAgICAgICAgICBpZiAodGFyZ2V0SXNWaWV3cG9ydCAmJiBpc1RhYmJpbmdCYWNrd2FyZHMpIHtcbiAgICAgICAgICAgICAgaGVhZEZvY3VzUHJveHlSZWYuY3VycmVudD8uZm9jdXMoKTtcbiAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgdGFiYmluZ0RpcmVjdGlvbiA9IGlzVGFiYmluZ0JhY2t3YXJkcyA/IFwiYmFja3dhcmRzXCIgOiBcImZvcndhcmRzXCI7XG4gICAgICAgICAgICBjb25zdCBzb3J0ZWRDYW5kaWRhdGVzID0gZ2V0U29ydGVkVGFiYmFibGVDYW5kaWRhdGVzKHsgdGFiYmluZ0RpcmVjdGlvbiB9KTtcbiAgICAgICAgICAgIGNvbnN0IGluZGV4ID0gc29ydGVkQ2FuZGlkYXRlcy5maW5kSW5kZXgoKGNhbmRpZGF0ZSkgPT4gY2FuZGlkYXRlID09PSBmb2N1c2VkRWxlbWVudCk7XG4gICAgICAgICAgICBpZiAoZm9jdXNGaXJzdChzb3J0ZWRDYW5kaWRhdGVzLnNsaWNlKGluZGV4ICsgMSkpKSB7XG4gICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBpc1RhYmJpbmdCYWNrd2FyZHMgPyBoZWFkRm9jdXNQcm94eVJlZi5jdXJyZW50Py5mb2N1cygpIDogdGFpbEZvY3VzUHJveHlSZWYuY3VycmVudD8uZm9jdXMoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIHZpZXdwb3J0LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pO1xuICAgICAgICByZXR1cm4gKCkgPT4gdmlld3BvcnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93bik7XG4gICAgICB9XG4gICAgfSwgW2dldEl0ZW1zLCBnZXRTb3J0ZWRUYWJiYWJsZUNhbmRpZGF0ZXNdKTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeHMoXG4gICAgICBEaXNtaXNzYWJsZUxheWVyLkJyYW5jaCxcbiAgICAgIHtcbiAgICAgICAgcmVmOiB3cmFwcGVyUmVmLFxuICAgICAgICByb2xlOiBcInJlZ2lvblwiLFxuICAgICAgICBcImFyaWEtbGFiZWxcIjogbGFiZWwucmVwbGFjZShcIntob3RrZXl9XCIsIGhvdGtleUxhYmVsKSxcbiAgICAgICAgdGFiSW5kZXg6IC0xLFxuICAgICAgICBzdHlsZTogeyBwb2ludGVyRXZlbnRzOiBoYXNUb2FzdHMgPyB2b2lkIDAgOiBcIm5vbmVcIiB9LFxuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgIGhhc1RvYXN0cyAmJiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICAgICAgRm9jdXNQcm94eSxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgcmVmOiBoZWFkRm9jdXNQcm94eVJlZixcbiAgICAgICAgICAgICAgb25Gb2N1c0Zyb21PdXRzaWRlVmlld3BvcnQ6ICgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB0YWJiYWJsZUNhbmRpZGF0ZXMgPSBnZXRTb3J0ZWRUYWJiYWJsZUNhbmRpZGF0ZXMoe1xuICAgICAgICAgICAgICAgICAgdGFiYmluZ0RpcmVjdGlvbjogXCJmb3J3YXJkc1wiXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgZm9jdXNGaXJzdCh0YWJiYWJsZUNhbmRpZGF0ZXMpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgKSxcbiAgICAgICAgICAvKiBAX19QVVJFX18gKi8ganN4KENvbGxlY3Rpb24uU2xvdCwgeyBzY29wZTogX19zY29wZVRvYXN0LCBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovIGpzeChQcmltaXRpdmUub2wsIHsgdGFiSW5kZXg6IC0xLCAuLi52aWV3cG9ydFByb3BzLCByZWY6IGNvbXBvc2VkUmVmcyB9KSB9KSxcbiAgICAgICAgICBoYXNUb2FzdHMgJiYgLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgICAgIEZvY3VzUHJveHksXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIHJlZjogdGFpbEZvY3VzUHJveHlSZWYsXG4gICAgICAgICAgICAgIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGFiYmFibGVDYW5kaWRhdGVzID0gZ2V0U29ydGVkVGFiYmFibGVDYW5kaWRhdGVzKHtcbiAgICAgICAgICAgICAgICAgIHRhYmJpbmdEaXJlY3Rpb246IFwiYmFja3dhcmRzXCJcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBmb2N1c0ZpcnN0KHRhYmJhYmxlQ2FuZGlkYXRlcyk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICApXG4gICAgICAgIF1cbiAgICAgIH1cbiAgICApO1xuICB9XG4pO1xuVG9hc3RWaWV3cG9ydC5kaXNwbGF5TmFtZSA9IFZJRVdQT1JUX05BTUU7XG52YXIgRk9DVVNfUFJPWFlfTkFNRSA9IFwiVG9hc3RGb2N1c1Byb3h5XCI7XG52YXIgRm9jdXNQcm94eSA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlVG9hc3QsIG9uRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0LCAuLi5wcm94eVByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlVG9hc3RQcm92aWRlckNvbnRleHQoRk9DVVNfUFJPWFlfTkFNRSwgX19zY29wZVRvYXN0KTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgIFZpc3VhbGx5SGlkZGVuLFxuICAgICAge1xuICAgICAgICBcImFyaWEtaGlkZGVuXCI6IHRydWUsXG4gICAgICAgIHRhYkluZGV4OiAwLFxuICAgICAgICAuLi5wcm94eVByb3BzLFxuICAgICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgICAgc3R5bGU6IHsgcG9zaXRpb246IFwiZml4ZWRcIiB9LFxuICAgICAgICBvbkZvY3VzOiAoZXZlbnQpID0+IHtcbiAgICAgICAgICBjb25zdCBwcmV2Rm9jdXNlZEVsZW1lbnQgPSBldmVudC5yZWxhdGVkVGFyZ2V0O1xuICAgICAgICAgIGNvbnN0IGlzRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0ID0gIWNvbnRleHQudmlld3BvcnQ/LmNvbnRhaW5zKHByZXZGb2N1c2VkRWxlbWVudCk7XG4gICAgICAgICAgaWYgKGlzRm9jdXNGcm9tT3V0c2lkZVZpZXdwb3J0KSBvbkZvY3VzRnJvbU91dHNpZGVWaWV3cG9ydCgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcbkZvY3VzUHJveHkuZGlzcGxheU5hbWUgPSBGT0NVU19QUk9YWV9OQU1FO1xudmFyIFRPQVNUX05BTUUgPSBcIlRvYXN0XCI7XG52YXIgVE9BU1RfU1dJUEVfU1RBUlQgPSBcInRvYXN0LnN3aXBlU3RhcnRcIjtcbnZhciBUT0FTVF9TV0lQRV9NT1ZFID0gXCJ0b2FzdC5zd2lwZU1vdmVcIjtcbnZhciBUT0FTVF9TV0lQRV9DQU5DRUwgPSBcInRvYXN0LnN3aXBlQ2FuY2VsXCI7XG52YXIgVE9BU1RfU1dJUEVfRU5EID0gXCJ0b2FzdC5zd2lwZUVuZFwiO1xudmFyIFRvYXN0ID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IGZvcmNlTW91bnQsIG9wZW46IG9wZW5Qcm9wLCBkZWZhdWx0T3Blbiwgb25PcGVuQ2hhbmdlLCAuLi50b2FzdFByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBbb3Blbiwgc2V0T3Blbl0gPSB1c2VDb250cm9sbGFibGVTdGF0ZSh7XG4gICAgICBwcm9wOiBvcGVuUHJvcCxcbiAgICAgIGRlZmF1bHRQcm9wOiBkZWZhdWx0T3BlbiA/PyB0cnVlLFxuICAgICAgb25DaGFuZ2U6IG9uT3BlbkNoYW5nZSxcbiAgICAgIGNhbGxlcjogVE9BU1RfTkFNRVxuICAgIH0pO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFByZXNlbmNlLCB7IHByZXNlbnQ6IGZvcmNlTW91bnQgfHwgb3BlbiwgY2hpbGRyZW46IC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICBUb2FzdEltcGwsXG4gICAgICB7XG4gICAgICAgIG9wZW4sXG4gICAgICAgIC4uLnRvYXN0UHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgICBvbkNsb3NlOiAoKSA9PiBzZXRPcGVuKGZhbHNlKSxcbiAgICAgICAgb25QYXVzZTogdXNlQ2FsbGJhY2tSZWYocHJvcHMub25QYXVzZSksXG4gICAgICAgIG9uUmVzdW1lOiB1c2VDYWxsYmFja1JlZihwcm9wcy5vblJlc3VtZSksXG4gICAgICAgIG9uU3dpcGVTdGFydDogY29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Td2lwZVN0YXJ0LCAoZXZlbnQpID0+IHtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnNldEF0dHJpYnV0ZShcImRhdGEtc3dpcGVcIiwgXCJzdGFydFwiKTtcbiAgICAgICAgfSksXG4gICAgICAgIG9uU3dpcGVNb3ZlOiBjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblN3aXBlTW92ZSwgKGV2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgeyB4LCB5IH0gPSBldmVudC5kZXRhaWwuZGVsdGE7XG4gICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zZXRBdHRyaWJ1dGUoXCJkYXRhLXN3aXBlXCIsIFwibW92ZVwiKTtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnNldFByb3BlcnR5KFwiLS1yYWRpeC10b2FzdC1zd2lwZS1tb3ZlLXhcIiwgYCR7eH1weGApO1xuICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUuc2V0UHJvcGVydHkoXCItLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteVwiLCBgJHt5fXB4YCk7XG4gICAgICAgIH0pLFxuICAgICAgICBvblN3aXBlQ2FuY2VsOiBjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblN3aXBlQ2FuY2VsLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnNldEF0dHJpYnV0ZShcImRhdGEtc3dpcGVcIiwgXCJjYW5jZWxcIik7XG4gICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zdHlsZS5yZW1vdmVQcm9wZXJ0eShcIi0tcmFkaXgtdG9hc3Qtc3dpcGUtbW92ZS14XCIpO1xuICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUucmVtb3ZlUHJvcGVydHkoXCItLXJhZGl4LXRvYXN0LXN3aXBlLW1vdmUteVwiKTtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnJlbW92ZVByb3BlcnR5KFwiLS1yYWRpeC10b2FzdC1zd2lwZS1lbmQteFwiKTtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnJlbW92ZVByb3BlcnR5KFwiLS1yYWRpeC10b2FzdC1zd2lwZS1lbmQteVwiKTtcbiAgICAgICAgfSksXG4gICAgICAgIG9uU3dpcGVFbmQ6IGNvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uU3dpcGVFbmQsIChldmVudCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHsgeCwgeSB9ID0gZXZlbnQuZGV0YWlsLmRlbHRhO1xuICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc2V0QXR0cmlidXRlKFwiZGF0YS1zd2lwZVwiLCBcImVuZFwiKTtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnJlbW92ZVByb3BlcnR5KFwiLS1yYWRpeC10b2FzdC1zd2lwZS1tb3ZlLXhcIik7XG4gICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5zdHlsZS5yZW1vdmVQcm9wZXJ0eShcIi0tcmFkaXgtdG9hc3Qtc3dpcGUtbW92ZS15XCIpO1xuICAgICAgICAgIGV2ZW50LmN1cnJlbnRUYXJnZXQuc3R5bGUuc2V0UHJvcGVydHkoXCItLXJhZGl4LXRvYXN0LXN3aXBlLWVuZC14XCIsIGAke3h9cHhgKTtcbiAgICAgICAgICBldmVudC5jdXJyZW50VGFyZ2V0LnN0eWxlLnNldFByb3BlcnR5KFwiLS1yYWRpeC10b2FzdC1zd2lwZS1lbmQteVwiLCBgJHt5fXB4YCk7XG4gICAgICAgICAgc2V0T3BlbihmYWxzZSk7XG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgKSB9KTtcbiAgfVxuKTtcblRvYXN0LmRpc3BsYXlOYW1lID0gVE9BU1RfTkFNRTtcbnZhciBbVG9hc3RJbnRlcmFjdGl2ZVByb3ZpZGVyLCB1c2VUb2FzdEludGVyYWN0aXZlQ29udGV4dF0gPSBjcmVhdGVUb2FzdENvbnRleHQoVE9BU1RfTkFNRSwge1xuICBvbkNsb3NlKCkge1xuICB9XG59KTtcbnZhciBUb2FzdEltcGwgPSBSZWFjdC5mb3J3YXJkUmVmKFxuICAocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIF9fc2NvcGVUb2FzdCxcbiAgICAgIHR5cGUgPSBcImZvcmVncm91bmRcIixcbiAgICAgIGR1cmF0aW9uOiBkdXJhdGlvblByb3AsXG4gICAgICBvcGVuLFxuICAgICAgb25DbG9zZSxcbiAgICAgIG9uRXNjYXBlS2V5RG93bixcbiAgICAgIG9uUGF1c2UsXG4gICAgICBvblJlc3VtZSxcbiAgICAgIG9uU3dpcGVTdGFydCxcbiAgICAgIG9uU3dpcGVNb3ZlLFxuICAgICAgb25Td2lwZUNhbmNlbCxcbiAgICAgIG9uU3dpcGVFbmQsXG4gICAgICAuLi50b2FzdFByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VUb2FzdFByb3ZpZGVyQ29udGV4dChUT0FTVF9OQU1FLCBfX3Njb3BlVG9hc3QpO1xuICAgIGNvbnN0IFtub2RlLCBzZXROb2RlXSA9IFJlYWN0LnVzZVN0YXRlKG51bGwpO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlMikgPT4gc2V0Tm9kZShub2RlMikpO1xuICAgIGNvbnN0IHBvaW50ZXJTdGFydFJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgICBjb25zdCBzd2lwZURlbHRhUmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICAgIGNvbnN0IGR1cmF0aW9uID0gZHVyYXRpb25Qcm9wIHx8IGNvbnRleHQuZHVyYXRpb247XG4gICAgY29uc3QgY2xvc2VUaW1lclN0YXJ0VGltZVJlZiA9IFJlYWN0LnVzZVJlZigwKTtcbiAgICBjb25zdCBjbG9zZVRpbWVyUmVtYWluaW5nVGltZVJlZiA9IFJlYWN0LnVzZVJlZihkdXJhdGlvbik7XG4gICAgY29uc3QgY2xvc2VUaW1lclJlZiA9IFJlYWN0LnVzZVJlZigwKTtcbiAgICBjb25zdCB7IG9uVG9hc3RBZGQsIG9uVG9hc3RSZW1vdmUgfSA9IGNvbnRleHQ7XG4gICAgY29uc3QgaGFuZGxlQ2xvc2UgPSB1c2VDYWxsYmFja1JlZigoKSA9PiB7XG4gICAgICBjb25zdCBpc0ZvY3VzSW5Ub2FzdCA9IG5vZGU/LmNvbnRhaW5zKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQpO1xuICAgICAgaWYgKGlzRm9jdXNJblRvYXN0KSBjb250ZXh0LnZpZXdwb3J0Py5mb2N1cygpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH0pO1xuICAgIGNvbnN0IHN0YXJ0VGltZXIgPSBSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgIChkdXJhdGlvbjIpID0+IHtcbiAgICAgICAgaWYgKCFkdXJhdGlvbjIgfHwgZHVyYXRpb24yID09PSBJbmZpbml0eSkgcmV0dXJuO1xuICAgICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KGNsb3NlVGltZXJSZWYuY3VycmVudCk7XG4gICAgICAgIGNsb3NlVGltZXJTdGFydFRpbWVSZWYuY3VycmVudCA9ICgvKiBAX19QVVJFX18gKi8gbmV3IERhdGUoKSkuZ2V0VGltZSgpO1xuICAgICAgICBjbG9zZVRpbWVyUmVmLmN1cnJlbnQgPSB3aW5kb3cuc2V0VGltZW91dChoYW5kbGVDbG9zZSwgZHVyYXRpb24yKTtcbiAgICAgIH0sXG4gICAgICBbaGFuZGxlQ2xvc2VdXG4gICAgKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3Qgdmlld3BvcnQgPSBjb250ZXh0LnZpZXdwb3J0O1xuICAgICAgaWYgKHZpZXdwb3J0KSB7XG4gICAgICAgIGNvbnN0IGhhbmRsZVJlc3VtZSA9ICgpID0+IHtcbiAgICAgICAgICBzdGFydFRpbWVyKGNsb3NlVGltZXJSZW1haW5pbmdUaW1lUmVmLmN1cnJlbnQpO1xuICAgICAgICAgIG9uUmVzdW1lPy4oKTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaGFuZGxlUGF1c2UgPSAoKSA9PiB7XG4gICAgICAgICAgY29uc3QgZWxhcHNlZFRpbWUgPSAoLyogQF9fUFVSRV9fICovIG5ldyBEYXRlKCkpLmdldFRpbWUoKSAtIGNsb3NlVGltZXJTdGFydFRpbWVSZWYuY3VycmVudDtcbiAgICAgICAgICBjbG9zZVRpbWVyUmVtYWluaW5nVGltZVJlZi5jdXJyZW50ID0gY2xvc2VUaW1lclJlbWFpbmluZ1RpbWVSZWYuY3VycmVudCAtIGVsYXBzZWRUaW1lO1xuICAgICAgICAgIHdpbmRvdy5jbGVhclRpbWVvdXQoY2xvc2VUaW1lclJlZi5jdXJyZW50KTtcbiAgICAgICAgICBvblBhdXNlPy4oKTtcbiAgICAgICAgfTtcbiAgICAgICAgdmlld3BvcnQuYWRkRXZlbnRMaXN0ZW5lcihWSUVXUE9SVF9QQVVTRSwgaGFuZGxlUGF1c2UpO1xuICAgICAgICB2aWV3cG9ydC5hZGRFdmVudExpc3RlbmVyKFZJRVdQT1JUX1JFU1VNRSwgaGFuZGxlUmVzdW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICB2aWV3cG9ydC5yZW1vdmVFdmVudExpc3RlbmVyKFZJRVdQT1JUX1BBVVNFLCBoYW5kbGVQYXVzZSk7XG4gICAgICAgICAgdmlld3BvcnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihWSUVXUE9SVF9SRVNVTUUsIGhhbmRsZVJlc3VtZSk7XG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfSwgW2NvbnRleHQudmlld3BvcnQsIGR1cmF0aW9uLCBvblBhdXNlLCBvblJlc3VtZSwgc3RhcnRUaW1lcl0pO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAob3BlbiAmJiAhY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmLmN1cnJlbnQpIHN0YXJ0VGltZXIoZHVyYXRpb24pO1xuICAgIH0sIFtvcGVuLCBkdXJhdGlvbiwgY29udGV4dC5pc0Nsb3NlUGF1c2VkUmVmLCBzdGFydFRpbWVyXSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIG9uVG9hc3RBZGQoKTtcbiAgICAgIHJldHVybiAoKSA9PiBvblRvYXN0UmVtb3ZlKCk7XG4gICAgfSwgW29uVG9hc3RBZGQsIG9uVG9hc3RSZW1vdmVdKTtcbiAgICBjb25zdCBhbm5vdW5jZVRleHRDb250ZW50ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgICByZXR1cm4gbm9kZSA/IGdldEFubm91bmNlVGV4dENvbnRlbnQobm9kZSkgOiBudWxsO1xuICAgIH0sIFtub2RlXSk7XG4gICAgaWYgKCFjb250ZXh0LnZpZXdwb3J0KSByZXR1cm4gbnVsbDtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeHMoRnJhZ21lbnQsIHsgY2hpbGRyZW46IFtcbiAgICAgIGFubm91bmNlVGV4dENvbnRlbnQgJiYgLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgVG9hc3RBbm5vdW5jZSxcbiAgICAgICAge1xuICAgICAgICAgIF9fc2NvcGVUb2FzdCxcbiAgICAgICAgICByb2xlOiBcInN0YXR1c1wiLFxuICAgICAgICAgIFwiYXJpYS1saXZlXCI6IHR5cGUgPT09IFwiZm9yZWdyb3VuZFwiID8gXCJhc3NlcnRpdmVcIiA6IFwicG9saXRlXCIsXG4gICAgICAgICAgXCJhcmlhLWF0b21pY1wiOiB0cnVlLFxuICAgICAgICAgIGNoaWxkcmVuOiBhbm5vdW5jZVRleHRDb250ZW50XG4gICAgICAgIH1cbiAgICAgICksXG4gICAgICAvKiBAX19QVVJFX18gKi8ganN4KFRvYXN0SW50ZXJhY3RpdmVQcm92aWRlciwgeyBzY29wZTogX19zY29wZVRvYXN0LCBvbkNsb3NlOiBoYW5kbGVDbG9zZSwgY2hpbGRyZW46IFJlYWN0RE9NLmNyZWF0ZVBvcnRhbChcbiAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChDb2xsZWN0aW9uLkl0ZW1TbG90LCB7IHNjb3BlOiBfX3Njb3BlVG9hc3QsIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICAgIERpc21pc3NhYmxlTGF5ZXIuUm9vdCxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBhc0NoaWxkOiB0cnVlLFxuICAgICAgICAgICAgb25Fc2NhcGVLZXlEb3duOiBjb21wb3NlRXZlbnRIYW5kbGVycyhvbkVzY2FwZUtleURvd24sICgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKCFjb250ZXh0LmlzRm9jdXNlZFRvYXN0RXNjYXBlS2V5RG93blJlZi5jdXJyZW50KSBoYW5kbGVDbG9zZSgpO1xuICAgICAgICAgICAgICBjb250ZXh0LmlzRm9jdXNlZFRvYXN0RXNjYXBlS2V5RG93blJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICAgICAgICBQcmltaXRpdmUubGksXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICByb2xlOiBcInN0YXR1c1wiLFxuICAgICAgICAgICAgICAgIFwiYXJpYS1saXZlXCI6IFwib2ZmXCIsXG4gICAgICAgICAgICAgICAgXCJhcmlhLWF0b21pY1wiOiB0cnVlLFxuICAgICAgICAgICAgICAgIHRhYkluZGV4OiAwLFxuICAgICAgICAgICAgICAgIFwiZGF0YS1zdGF0ZVwiOiBvcGVuID8gXCJvcGVuXCIgOiBcImNsb3NlZFwiLFxuICAgICAgICAgICAgICAgIFwiZGF0YS1zd2lwZS1kaXJlY3Rpb25cIjogY29udGV4dC5zd2lwZURpcmVjdGlvbixcbiAgICAgICAgICAgICAgICAuLi50b2FzdFByb3BzLFxuICAgICAgICAgICAgICAgIHJlZjogY29tcG9zZWRSZWZzLFxuICAgICAgICAgICAgICAgIHN0eWxlOiB7IHVzZXJTZWxlY3Q6IFwibm9uZVwiLCB0b3VjaEFjdGlvbjogXCJub25lXCIsIC4uLnByb3BzLnN0eWxlIH0sXG4gICAgICAgICAgICAgICAgb25LZXlEb3duOiBjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbktleURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmtleSAhPT0gXCJFc2NhcGVcIikgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgb25Fc2NhcGVLZXlEb3duPy4oZXZlbnQubmF0aXZlRXZlbnQpO1xuICAgICAgICAgICAgICAgICAgaWYgKCFldmVudC5uYXRpdmVFdmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQuaXNGb2N1c2VkVG9hc3RFc2NhcGVLZXlEb3duUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBoYW5kbGVDbG9zZSgpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIG9uUG9pbnRlckRvd246IGNvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uUG9pbnRlckRvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmJ1dHRvbiAhPT0gMCkgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQgPSB7IHg6IGV2ZW50LmNsaWVudFgsIHk6IGV2ZW50LmNsaWVudFkgfTtcbiAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICBvblBvaW50ZXJNb3ZlOiBjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vblBvaW50ZXJNb3ZlLCAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgIGlmICghcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQpIHJldHVybjtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHggPSBldmVudC5jbGllbnRYIC0gcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQueDtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IHkgPSBldmVudC5jbGllbnRZIC0gcG9pbnRlclN0YXJ0UmVmLmN1cnJlbnQueTtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGhhc1N3aXBlTW92ZVN0YXJ0ZWQgPSBCb29sZWFuKHN3aXBlRGVsdGFSZWYuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBpc0hvcml6b250YWxTd2lwZSA9IFtcImxlZnRcIiwgXCJyaWdodFwiXS5pbmNsdWRlcyhjb250ZXh0LnN3aXBlRGlyZWN0aW9uKTtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGNsYW1wID0gW1wibGVmdFwiLCBcInVwXCJdLmluY2x1ZGVzKGNvbnRleHQuc3dpcGVEaXJlY3Rpb24pID8gTWF0aC5taW4gOiBNYXRoLm1heDtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGNsYW1wZWRYID0gaXNIb3Jpem9udGFsU3dpcGUgPyBjbGFtcCgwLCB4KSA6IDA7XG4gICAgICAgICAgICAgICAgICBjb25zdCBjbGFtcGVkWSA9ICFpc0hvcml6b250YWxTd2lwZSA/IGNsYW1wKDAsIHkpIDogMDtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG1vdmVTdGFydEJ1ZmZlciA9IGV2ZW50LnBvaW50ZXJUeXBlID09PSBcInRvdWNoXCIgPyAxMCA6IDI7XG4gICAgICAgICAgICAgICAgICBjb25zdCBkZWx0YSA9IHsgeDogY2xhbXBlZFgsIHk6IGNsYW1wZWRZIH07XG4gICAgICAgICAgICAgICAgICBjb25zdCBldmVudERldGFpbCA9IHsgb3JpZ2luYWxFdmVudDogZXZlbnQsIGRlbHRhIH07XG4gICAgICAgICAgICAgICAgICBpZiAoaGFzU3dpcGVNb3ZlU3RhcnRlZCkge1xuICAgICAgICAgICAgICAgICAgICBzd2lwZURlbHRhUmVmLmN1cnJlbnQgPSBkZWx0YTtcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChUT0FTVF9TV0lQRV9NT1ZFLCBvblN3aXBlTW92ZSwgZXZlbnREZXRhaWwsIHtcbiAgICAgICAgICAgICAgICAgICAgICBkaXNjcmV0ZTogZmFsc2VcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGlzRGVsdGFJbkRpcmVjdGlvbihkZWx0YSwgY29udGV4dC5zd2lwZURpcmVjdGlvbiwgbW92ZVN0YXJ0QnVmZmVyKSkge1xuICAgICAgICAgICAgICAgICAgICBzd2lwZURlbHRhUmVmLmN1cnJlbnQgPSBkZWx0YTtcbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChUT0FTVF9TV0lQRV9TVEFSVCwgb25Td2lwZVN0YXJ0LCBldmVudERldGFpbCwge1xuICAgICAgICAgICAgICAgICAgICAgIGRpc2NyZXRlOiBmYWxzZVxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgZXZlbnQudGFyZ2V0LnNldFBvaW50ZXJDYXB0dXJlKGV2ZW50LnBvaW50ZXJJZCk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKE1hdGguYWJzKHgpID4gbW92ZVN0YXJ0QnVmZmVyIHx8IE1hdGguYWJzKHkpID4gbW92ZVN0YXJ0QnVmZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHBvaW50ZXJTdGFydFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICBvblBvaW50ZXJVcDogY29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Qb2ludGVyVXAsIChldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgZGVsdGEgPSBzd2lwZURlbHRhUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQ7XG4gICAgICAgICAgICAgICAgICBpZiAodGFyZ2V0Lmhhc1BvaW50ZXJDYXB0dXJlKGV2ZW50LnBvaW50ZXJJZCkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LnJlbGVhc2VQb2ludGVyQ2FwdHVyZShldmVudC5wb2ludGVySWQpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgc3dpcGVEZWx0YVJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgIHBvaW50ZXJTdGFydFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgIGlmIChkZWx0YSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB0b2FzdCA9IGV2ZW50LmN1cnJlbnRUYXJnZXQ7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV2ZW50RGV0YWlsID0geyBvcmlnaW5hbEV2ZW50OiBldmVudCwgZGVsdGEgfTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzRGVsdGFJbkRpcmVjdGlvbihkZWx0YSwgY29udGV4dC5zd2lwZURpcmVjdGlvbiwgY29udGV4dC5zd2lwZVRocmVzaG9sZCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50KFRPQVNUX1NXSVBFX0VORCwgb25Td2lwZUVuZCwgZXZlbnREZXRhaWwsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2NyZXRlOiB0cnVlXG4gICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChcbiAgICAgICAgICAgICAgICAgICAgICAgIFRPQVNUX1NXSVBFX0NBTkNFTCxcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uU3dpcGVDYW5jZWwsXG4gICAgICAgICAgICAgICAgICAgICAgICBldmVudERldGFpbCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzY3JldGU6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRvYXN0LmFkZEV2ZW50TGlzdGVuZXIoXCJjbGlja1wiLCAoZXZlbnQyKSA9PiBldmVudDIucHJldmVudERlZmF1bHQoKSwge1xuICAgICAgICAgICAgICAgICAgICAgIG9uY2U6IHRydWVcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgKVxuICAgICAgICAgIH1cbiAgICAgICAgKSB9KSxcbiAgICAgICAgY29udGV4dC52aWV3cG9ydFxuICAgICAgKSB9KVxuICAgIF0gfSk7XG4gIH1cbik7XG52YXIgVG9hc3RBbm5vdW5jZSA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IF9fc2NvcGVUb2FzdCwgY2hpbGRyZW4sIC4uLmFubm91bmNlUHJvcHMgfSA9IHByb3BzO1xuICBjb25zdCBjb250ZXh0ID0gdXNlVG9hc3RQcm92aWRlckNvbnRleHQoVE9BU1RfTkFNRSwgX19zY29wZVRvYXN0KTtcbiAgY29uc3QgW3JlbmRlckFubm91bmNlVGV4dCwgc2V0UmVuZGVyQW5ub3VuY2VUZXh0XSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQW5ub3VuY2VkLCBzZXRJc0Fubm91bmNlZF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gIHVzZU5leHRGcmFtZSgoKSA9PiBzZXRSZW5kZXJBbm5vdW5jZVRleHQodHJ1ZSkpO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHRpbWVyID0gd2luZG93LnNldFRpbWVvdXQoKCkgPT4gc2V0SXNBbm5vdW5jZWQodHJ1ZSksIDFlMyk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5jbGVhclRpbWVvdXQodGltZXIpO1xuICB9LCBbXSk7XG4gIHJldHVybiBpc0Fubm91bmNlZCA/IG51bGwgOiAvKiBAX19QVVJFX18gKi8ganN4KFBvcnRhbCwgeyBhc0NoaWxkOiB0cnVlLCBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovIGpzeChWaXN1YWxseUhpZGRlbiwgeyAuLi5hbm5vdW5jZVByb3BzLCBjaGlsZHJlbjogcmVuZGVyQW5ub3VuY2VUZXh0ICYmIC8qIEBfX1BVUkVfXyAqLyBqc3hzKEZyYWdtZW50LCB7IGNoaWxkcmVuOiBbXG4gICAgY29udGV4dC5sYWJlbCxcbiAgICBcIiBcIixcbiAgICBjaGlsZHJlblxuICBdIH0pIH0pIH0pO1xufTtcbnZhciBUSVRMRV9OQU1FID0gXCJUb2FzdFRpdGxlXCI7XG52YXIgVG9hc3RUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlVG9hc3QsIC4uLnRpdGxlUHJvcHMgfSA9IHByb3BzO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFByaW1pdGl2ZS5kaXYsIHsgLi4udGl0bGVQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSk7XG4gIH1cbik7XG5Ub2FzdFRpdGxlLmRpc3BsYXlOYW1lID0gVElUTEVfTkFNRTtcbnZhciBERVNDUklQVElPTl9OQU1FID0gXCJUb2FzdERlc2NyaXB0aW9uXCI7XG52YXIgVG9hc3REZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlVG9hc3QsIC4uLmRlc2NyaXB0aW9uUHJvcHMgfSA9IHByb3BzO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFByaW1pdGl2ZS5kaXYsIHsgLi4uZGVzY3JpcHRpb25Qcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSk7XG4gIH1cbik7XG5Ub2FzdERlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gREVTQ1JJUFRJT05fTkFNRTtcbnZhciBBQ1RJT05fTkFNRSA9IFwiVG9hc3RBY3Rpb25cIjtcbnZhciBUb2FzdEFjdGlvbiA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBhbHRUZXh0LCAuLi5hY3Rpb25Qcm9wcyB9ID0gcHJvcHM7XG4gICAgaWYgKCFhbHRUZXh0LnRyaW0oKSkge1xuICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgYEludmFsaWQgcHJvcCBcXGBhbHRUZXh0XFxgIHN1cHBsaWVkIHRvIFxcYCR7QUNUSU9OX05BTUV9XFxgLiBFeHBlY3RlZCBub24tZW1wdHkgXFxgc3RyaW5nXFxgLmBcbiAgICAgICk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goVG9hc3RBbm5vdW5jZUV4Y2x1ZGUsIHsgYWx0VGV4dCwgYXNDaGlsZDogdHJ1ZSwgY2hpbGRyZW46IC8qIEBfX1BVUkVfXyAqLyBqc3goVG9hc3RDbG9zZSwgeyAuLi5hY3Rpb25Qcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSkgfSk7XG4gIH1cbik7XG5Ub2FzdEFjdGlvbi5kaXNwbGF5TmFtZSA9IEFDVElPTl9OQU1FO1xudmFyIENMT1NFX05BTUUgPSBcIlRvYXN0Q2xvc2VcIjtcbnZhciBUb2FzdENsb3NlID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVUb2FzdCwgLi4uY2xvc2VQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgaW50ZXJhY3RpdmVDb250ZXh0ID0gdXNlVG9hc3RJbnRlcmFjdGl2ZUNvbnRleHQoQ0xPU0VfTkFNRSwgX19zY29wZVRvYXN0KTtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChUb2FzdEFubm91bmNlRXhjbHVkZSwgeyBhc0NoaWxkOiB0cnVlLCBjaGlsZHJlbjogLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgIFByaW1pdGl2ZS5idXR0b24sXG4gICAgICB7XG4gICAgICAgIHR5cGU6IFwiYnV0dG9uXCIsXG4gICAgICAgIC4uLmNsb3NlUHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgICBvbkNsaWNrOiBjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkNsaWNrLCBpbnRlcmFjdGl2ZUNvbnRleHQub25DbG9zZSlcbiAgICAgIH1cbiAgICApIH0pO1xuICB9XG4pO1xuVG9hc3RDbG9zZS5kaXNwbGF5TmFtZSA9IENMT1NFX05BTUU7XG52YXIgVG9hc3RBbm5vdW5jZUV4Y2x1ZGUgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IHsgX19zY29wZVRvYXN0LCBhbHRUZXh0LCAuLi5hbm5vdW5jZUV4Y2x1ZGVQcm9wcyB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgIFByaW1pdGl2ZS5kaXYsXG4gICAge1xuICAgICAgXCJkYXRhLXJhZGl4LXRvYXN0LWFubm91bmNlLWV4Y2x1ZGVcIjogXCJcIixcbiAgICAgIFwiZGF0YS1yYWRpeC10b2FzdC1hbm5vdW5jZS1hbHRcIjogYWx0VGV4dCB8fCB2b2lkIDAsXG4gICAgICAuLi5hbm5vdW5jZUV4Y2x1ZGVQcm9wcyxcbiAgICAgIHJlZjogZm9yd2FyZGVkUmVmXG4gICAgfVxuICApO1xufSk7XG5mdW5jdGlvbiBnZXRBbm5vdW5jZVRleHRDb250ZW50KGNvbnRhaW5lcikge1xuICBjb25zdCB0ZXh0Q29udGVudCA9IFtdO1xuICBjb25zdCBjaGlsZE5vZGVzID0gQXJyYXkuZnJvbShjb250YWluZXIuY2hpbGROb2Rlcyk7XG4gIGNoaWxkTm9kZXMuZm9yRWFjaCgobm9kZSkgPT4ge1xuICAgIGlmIChub2RlLm5vZGVUeXBlID09PSBub2RlLlRFWFRfTk9ERSAmJiBub2RlLnRleHRDb250ZW50KSB0ZXh0Q29udGVudC5wdXNoKG5vZGUudGV4dENvbnRlbnQpO1xuICAgIGlmIChpc0hUTUxFbGVtZW50KG5vZGUpKSB7XG4gICAgICBjb25zdCBpc0hpZGRlbiA9IG5vZGUuYXJpYUhpZGRlbiB8fCBub2RlLmhpZGRlbiB8fCBub2RlLnN0eWxlLmRpc3BsYXkgPT09IFwibm9uZVwiO1xuICAgICAgY29uc3QgaXNFeGNsdWRlZCA9IG5vZGUuZGF0YXNldC5yYWRpeFRvYXN0QW5ub3VuY2VFeGNsdWRlID09PSBcIlwiO1xuICAgICAgaWYgKCFpc0hpZGRlbikge1xuICAgICAgICBpZiAoaXNFeGNsdWRlZCkge1xuICAgICAgICAgIGNvbnN0IGFsdFRleHQgPSBub2RlLmRhdGFzZXQucmFkaXhUb2FzdEFubm91bmNlQWx0O1xuICAgICAgICAgIGlmIChhbHRUZXh0KSB0ZXh0Q29udGVudC5wdXNoKGFsdFRleHQpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRleHRDb250ZW50LnB1c2goLi4uZ2V0QW5ub3VuY2VUZXh0Q29udGVudChub2RlKSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICByZXR1cm4gdGV4dENvbnRlbnQ7XG59XG5mdW5jdGlvbiBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50KG5hbWUsIGhhbmRsZXIsIGRldGFpbCwgeyBkaXNjcmV0ZSB9KSB7XG4gIGNvbnN0IGN1cnJlbnRUYXJnZXQgPSBkZXRhaWwub3JpZ2luYWxFdmVudC5jdXJyZW50VGFyZ2V0O1xuICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudChuYW1lLCB7IGJ1YmJsZXM6IHRydWUsIGNhbmNlbGFibGU6IHRydWUsIGRldGFpbCB9KTtcbiAgaWYgKGhhbmRsZXIpIGN1cnJlbnRUYXJnZXQuYWRkRXZlbnRMaXN0ZW5lcihuYW1lLCBoYW5kbGVyLCB7IG9uY2U6IHRydWUgfSk7XG4gIGlmIChkaXNjcmV0ZSkge1xuICAgIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudChjdXJyZW50VGFyZ2V0LCBldmVudCk7XG4gIH0gZWxzZSB7XG4gICAgY3VycmVudFRhcmdldC5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfVxufVxudmFyIGlzRGVsdGFJbkRpcmVjdGlvbiA9IChkZWx0YSwgZGlyZWN0aW9uLCB0aHJlc2hvbGQgPSAwKSA9PiB7XG4gIGNvbnN0IGRlbHRhWCA9IE1hdGguYWJzKGRlbHRhLngpO1xuICBjb25zdCBkZWx0YVkgPSBNYXRoLmFicyhkZWx0YS55KTtcbiAgY29uc3QgaXNEZWx0YVggPSBkZWx0YVggPiBkZWx0YVk7XG4gIGlmIChkaXJlY3Rpb24gPT09IFwibGVmdFwiIHx8IGRpcmVjdGlvbiA9PT0gXCJyaWdodFwiKSB7XG4gICAgcmV0dXJuIGlzRGVsdGFYICYmIGRlbHRhWCA+IHRocmVzaG9sZDtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gIWlzRGVsdGFYICYmIGRlbHRhWSA+IHRocmVzaG9sZDtcbiAgfVxufTtcbmZ1bmN0aW9uIHVzZU5leHRGcmFtZShjYWxsYmFjayA9ICgpID0+IHtcbn0pIHtcbiAgY29uc3QgZm4gPSB1c2VDYWxsYmFja1JlZihjYWxsYmFjayk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgbGV0IHJhZjEgPSAwO1xuICAgIGxldCByYWYyID0gMDtcbiAgICByYWYxID0gd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiByYWYyID0gd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZShmbikpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cuY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmMSk7XG4gICAgICB3aW5kb3cuY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmMik7XG4gICAgfTtcbiAgfSwgW2ZuXSk7XG59XG5mdW5jdGlvbiBpc0hUTUxFbGVtZW50KG5vZGUpIHtcbiAgcmV0dXJuIG5vZGUubm9kZVR5cGUgPT09IG5vZGUuRUxFTUVOVF9OT0RFO1xufVxuZnVuY3Rpb24gZ2V0VGFiYmFibGVDYW5kaWRhdGVzKGNvbnRhaW5lcikge1xuICBjb25zdCBub2RlcyA9IFtdO1xuICBjb25zdCB3YWxrZXIgPSBkb2N1bWVudC5jcmVhdGVUcmVlV2Fsa2VyKGNvbnRhaW5lciwgTm9kZUZpbHRlci5TSE9XX0VMRU1FTlQsIHtcbiAgICBhY2NlcHROb2RlOiAobm9kZSkgPT4ge1xuICAgICAgY29uc3QgaXNIaWRkZW5JbnB1dCA9IG5vZGUudGFnTmFtZSA9PT0gXCJJTlBVVFwiICYmIG5vZGUudHlwZSA9PT0gXCJoaWRkZW5cIjtcbiAgICAgIGlmIChub2RlLmRpc2FibGVkIHx8IG5vZGUuaGlkZGVuIHx8IGlzSGlkZGVuSW5wdXQpIHJldHVybiBOb2RlRmlsdGVyLkZJTFRFUl9TS0lQO1xuICAgICAgcmV0dXJuIG5vZGUudGFiSW5kZXggPj0gMCA/IE5vZGVGaWx0ZXIuRklMVEVSX0FDQ0VQVCA6IE5vZGVGaWx0ZXIuRklMVEVSX1NLSVA7XG4gICAgfVxuICB9KTtcbiAgd2hpbGUgKHdhbGtlci5uZXh0Tm9kZSgpKSBub2Rlcy5wdXNoKHdhbGtlci5jdXJyZW50Tm9kZSk7XG4gIHJldHVybiBub2Rlcztcbn1cbmZ1bmN0aW9uIGZvY3VzRmlyc3QoY2FuZGlkYXRlcykge1xuICBjb25zdCBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICByZXR1cm4gY2FuZGlkYXRlcy5zb21lKChjYW5kaWRhdGUpID0+IHtcbiAgICBpZiAoY2FuZGlkYXRlID09PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQpIHJldHVybiB0cnVlO1xuICAgIGNhbmRpZGF0ZS5mb2N1cygpO1xuICAgIHJldHVybiBkb2N1bWVudC5hY3RpdmVFbGVtZW50ICE9PSBwcmV2aW91c2x5Rm9jdXNlZEVsZW1lbnQ7XG4gIH0pO1xufVxudmFyIFByb3ZpZGVyID0gVG9hc3RQcm92aWRlcjtcbnZhciBWaWV3cG9ydCA9IFRvYXN0Vmlld3BvcnQ7XG52YXIgUm9vdDIgPSBUb2FzdDtcbnZhciBUaXRsZSA9IFRvYXN0VGl0bGU7XG52YXIgRGVzY3JpcHRpb24gPSBUb2FzdERlc2NyaXB0aW9uO1xudmFyIEFjdGlvbiA9IFRvYXN0QWN0aW9uO1xudmFyIENsb3NlID0gVG9hc3RDbG9zZTtcbmV4cG9ydCB7XG4gIEFjdGlvbixcbiAgQ2xvc2UsXG4gIERlc2NyaXB0aW9uLFxuICBQcm92aWRlcixcbiAgUm9vdDIgYXMgUm9vdCxcbiAgVGl0bGUsXG4gIFRvYXN0LFxuICBUb2FzdEFjdGlvbixcbiAgVG9hc3RDbG9zZSxcbiAgVG9hc3REZXNjcmlwdGlvbixcbiAgVG9hc3RQcm92aWRlcixcbiAgVG9hc3RUaXRsZSxcbiAgVG9hc3RWaWV3cG9ydCxcbiAgVmlld3BvcnQsXG4gIGNyZWF0ZVRvYXN0U2NvcGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJSZWFjdERPTSIsImNvbXBvc2VFdmVudEhhbmRsZXJzIiwidXNlQ29tcG9zZWRSZWZzIiwiY3JlYXRlQ29sbGVjdGlvbiIsImNyZWF0ZUNvbnRleHRTY29wZSIsIkRpc21pc3NhYmxlTGF5ZXIiLCJQb3J0YWwiLCJQcmVzZW5jZSIsIlByaW1pdGl2ZSIsImRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCIsInVzZUNhbGxiYWNrUmVmIiwidXNlQ29udHJvbGxhYmxlU3RhdGUiLCJ1c2VMYXlvdXRFZmZlY3QiLCJWaXN1YWxseUhpZGRlbiIsIkZyYWdtZW50IiwianN4IiwianN4cyIsIlBST1ZJREVSX05BTUUiLCJDb2xsZWN0aW9uIiwidXNlQ29sbGVjdGlvbiIsImNyZWF0ZUNvbGxlY3Rpb25TY29wZSIsImNyZWF0ZVRvYXN0Q29udGV4dCIsImNyZWF0ZVRvYXN0U2NvcGUiLCJUb2FzdFByb3ZpZGVyUHJvdmlkZXIiLCJ1c2VUb2FzdFByb3ZpZGVyQ29udGV4dCIsIlRvYXN0UHJvdmlkZXIiLCJwcm9wcyIsIl9fc2NvcGVUb2FzdCIsImxhYmVsIiwiZHVyYXRpb24iLCJzd2lwZURpcmVjdGlvbiIsInN3aXBlVGhyZXNob2xkIiwiY2hpbGRyZW4iLCJ2aWV3cG9ydCIsInNldFZpZXdwb3J0IiwidXNlU3RhdGUiLCJ0b2FzdENvdW50Iiwic2V0VG9hc3RDb3VudCIsImlzRm9jdXNlZFRvYXN0RXNjYXBlS2V5RG93blJlZiIsInVzZVJlZiIsImlzQ2xvc2VQYXVzZWRSZWYiLCJ0cmltIiwiY29uc29sZSIsImVycm9yIiwiUHJvdmlkZXIiLCJzY29wZSIsIm9uVmlld3BvcnRDaGFuZ2UiLCJvblRvYXN0QWRkIiwidXNlQ2FsbGJhY2siLCJwcmV2Q291bnQiLCJvblRvYXN0UmVtb3ZlIiwiZGlzcGxheU5hbWUiLCJWSUVXUE9SVF9OQU1FIiwiVklFV1BPUlRfREVGQVVMVF9IT1RLRVkiLCJWSUVXUE9SVF9QQVVTRSIsIlZJRVdQT1JUX1JFU1VNRSIsIlRvYXN0Vmlld3BvcnQiLCJmb3J3YXJkUmVmIiwiZm9yd2FyZGVkUmVmIiwiaG90a2V5Iiwidmlld3BvcnRQcm9wcyIsImNvbnRleHQiLCJnZXRJdGVtcyIsIndyYXBwZXJSZWYiLCJoZWFkRm9jdXNQcm94eVJlZiIsInRhaWxGb2N1c1Byb3h5UmVmIiwicmVmIiwiY29tcG9zZWRSZWZzIiwiaG90a2V5TGFiZWwiLCJqb2luIiwicmVwbGFjZSIsImhhc1RvYXN0cyIsInVzZUVmZmVjdCIsImhhbmRsZUtleURvd24iLCJldmVudCIsImlzSG90a2V5UHJlc3NlZCIsImxlbmd0aCIsImV2ZXJ5Iiwia2V5IiwiY29kZSIsImN1cnJlbnQiLCJmb2N1cyIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJ3cmFwcGVyIiwiaGFuZGxlUGF1c2UiLCJwYXVzZUV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkaXNwYXRjaEV2ZW50IiwiaGFuZGxlUmVzdW1lIiwicmVzdW1lRXZlbnQiLCJoYW5kbGVGb2N1c091dFJlc3VtZSIsImlzRm9jdXNNb3ZpbmdPdXRzaWRlIiwiY29udGFpbnMiLCJyZWxhdGVkVGFyZ2V0IiwiaGFuZGxlUG9pbnRlckxlYXZlUmVzdW1lIiwiaXNGb2N1c0luc2lkZSIsImFjdGl2ZUVsZW1lbnQiLCJ3aW5kb3ciLCJnZXRTb3J0ZWRUYWJiYWJsZUNhbmRpZGF0ZXMiLCJ0YWJiaW5nRGlyZWN0aW9uIiwidG9hc3RJdGVtcyIsInRhYmJhYmxlQ2FuZGlkYXRlcyIsIm1hcCIsInRvYXN0SXRlbSIsInRvYXN0Tm9kZSIsInRvYXN0VGFiYmFibGVDYW5kaWRhdGVzIiwiZ2V0VGFiYmFibGVDYW5kaWRhdGVzIiwicmV2ZXJzZSIsImZsYXQiLCJpc01ldGFLZXkiLCJhbHRLZXkiLCJjdHJsS2V5IiwibWV0YUtleSIsImlzVGFiS2V5IiwiZm9jdXNlZEVsZW1lbnQiLCJpc1RhYmJpbmdCYWNrd2FyZHMiLCJzaGlmdEtleSIsInRhcmdldElzVmlld3BvcnQiLCJ0YXJnZXQiLCJzb3J0ZWRDYW5kaWRhdGVzIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJjYW5kaWRhdGUiLCJmb2N1c0ZpcnN0Iiwic2xpY2UiLCJwcmV2ZW50RGVmYXVsdCIsIkJyYW5jaCIsInJvbGUiLCJ0YWJJbmRleCIsInN0eWxlIiwicG9pbnRlckV2ZW50cyIsIkZvY3VzUHJveHkiLCJvbkZvY3VzRnJvbU91dHNpZGVWaWV3cG9ydCIsIlNsb3QiLCJvbCIsIkZPQ1VTX1BST1hZX05BTUUiLCJwcm94eVByb3BzIiwicG9zaXRpb24iLCJvbkZvY3VzIiwicHJldkZvY3VzZWRFbGVtZW50IiwiaXNGb2N1c0Zyb21PdXRzaWRlVmlld3BvcnQiLCJUT0FTVF9OQU1FIiwiVE9BU1RfU1dJUEVfU1RBUlQiLCJUT0FTVF9TV0lQRV9NT1ZFIiwiVE9BU1RfU1dJUEVfQ0FOQ0VMIiwiVE9BU1RfU1dJUEVfRU5EIiwiVG9hc3QiLCJmb3JjZU1vdW50Iiwib3BlbiIsIm9wZW5Qcm9wIiwiZGVmYXVsdE9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJ0b2FzdFByb3BzIiwic2V0T3BlbiIsInByb3AiLCJkZWZhdWx0UHJvcCIsIm9uQ2hhbmdlIiwiY2FsbGVyIiwicHJlc2VudCIsIlRvYXN0SW1wbCIsIm9uQ2xvc2UiLCJvblBhdXNlIiwib25SZXN1bWUiLCJvblN3aXBlU3RhcnQiLCJjdXJyZW50VGFyZ2V0Iiwic2V0QXR0cmlidXRlIiwib25Td2lwZU1vdmUiLCJ4IiwieSIsImRldGFpbCIsImRlbHRhIiwic2V0UHJvcGVydHkiLCJvblN3aXBlQ2FuY2VsIiwicmVtb3ZlUHJvcGVydHkiLCJvblN3aXBlRW5kIiwiVG9hc3RJbnRlcmFjdGl2ZVByb3ZpZGVyIiwidXNlVG9hc3RJbnRlcmFjdGl2ZUNvbnRleHQiLCJ0eXBlIiwiZHVyYXRpb25Qcm9wIiwib25Fc2NhcGVLZXlEb3duIiwibm9kZSIsInNldE5vZGUiLCJub2RlMiIsInBvaW50ZXJTdGFydFJlZiIsInN3aXBlRGVsdGFSZWYiLCJjbG9zZVRpbWVyU3RhcnRUaW1lUmVmIiwiY2xvc2VUaW1lclJlbWFpbmluZ1RpbWVSZWYiLCJjbG9zZVRpbWVyUmVmIiwiaGFuZGxlQ2xvc2UiLCJpc0ZvY3VzSW5Ub2FzdCIsInN0YXJ0VGltZXIiLCJkdXJhdGlvbjIiLCJJbmZpbml0eSIsImNsZWFyVGltZW91dCIsIkRhdGUiLCJnZXRUaW1lIiwic2V0VGltZW91dCIsImVsYXBzZWRUaW1lIiwiYW5ub3VuY2VUZXh0Q29udGVudCIsInVzZU1lbW8iLCJnZXRBbm5vdW5jZVRleHRDb250ZW50IiwiVG9hc3RBbm5vdW5jZSIsImNyZWF0ZVBvcnRhbCIsIkl0ZW1TbG90IiwiUm9vdCIsImFzQ2hpbGQiLCJsaSIsInVzZXJTZWxlY3QiLCJ0b3VjaEFjdGlvbiIsIm9uS2V5RG93biIsIm5hdGl2ZUV2ZW50IiwiZGVmYXVsdFByZXZlbnRlZCIsIm9uUG9pbnRlckRvd24iLCJidXR0b24iLCJjbGllbnRYIiwiY2xpZW50WSIsIm9uUG9pbnRlck1vdmUiLCJoYXNTd2lwZU1vdmVTdGFydGVkIiwiQm9vbGVhbiIsImlzSG9yaXpvbnRhbFN3aXBlIiwiaW5jbHVkZXMiLCJjbGFtcCIsIk1hdGgiLCJtaW4iLCJtYXgiLCJjbGFtcGVkWCIsImNsYW1wZWRZIiwibW92ZVN0YXJ0QnVmZmVyIiwicG9pbnRlclR5cGUiLCJldmVudERldGFpbCIsIm9yaWdpbmFsRXZlbnQiLCJoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50IiwiZGlzY3JldGUiLCJpc0RlbHRhSW5EaXJlY3Rpb24iLCJzZXRQb2ludGVyQ2FwdHVyZSIsInBvaW50ZXJJZCIsImFicyIsIm9uUG9pbnRlclVwIiwiaGFzUG9pbnRlckNhcHR1cmUiLCJyZWxlYXNlUG9pbnRlckNhcHR1cmUiLCJ0b2FzdCIsImV2ZW50MiIsIm9uY2UiLCJhbm5vdW5jZVByb3BzIiwicmVuZGVyQW5ub3VuY2VUZXh0Iiwic2V0UmVuZGVyQW5ub3VuY2VUZXh0IiwiaXNBbm5vdW5jZWQiLCJzZXRJc0Fubm91bmNlZCIsInVzZU5leHRGcmFtZSIsInRpbWVyIiwiVElUTEVfTkFNRSIsIlRvYXN0VGl0bGUiLCJ0aXRsZVByb3BzIiwiZGl2IiwiREVTQ1JJUFRJT05fTkFNRSIsIlRvYXN0RGVzY3JpcHRpb24iLCJkZXNjcmlwdGlvblByb3BzIiwiQUNUSU9OX05BTUUiLCJUb2FzdEFjdGlvbiIsImFsdFRleHQiLCJhY3Rpb25Qcm9wcyIsIlRvYXN0QW5ub3VuY2VFeGNsdWRlIiwiVG9hc3RDbG9zZSIsIkNMT1NFX05BTUUiLCJjbG9zZVByb3BzIiwiaW50ZXJhY3RpdmVDb250ZXh0Iiwib25DbGljayIsImFubm91bmNlRXhjbHVkZVByb3BzIiwiY29udGFpbmVyIiwidGV4dENvbnRlbnQiLCJjaGlsZE5vZGVzIiwiQXJyYXkiLCJmcm9tIiwiZm9yRWFjaCIsIm5vZGVUeXBlIiwiVEVYVF9OT0RFIiwicHVzaCIsImlzSFRNTEVsZW1lbnQiLCJpc0hpZGRlbiIsImFyaWFIaWRkZW4iLCJoaWRkZW4iLCJkaXNwbGF5IiwiaXNFeGNsdWRlZCIsImRhdGFzZXQiLCJyYWRpeFRvYXN0QW5ub3VuY2VFeGNsdWRlIiwicmFkaXhUb2FzdEFubm91bmNlQWx0IiwibmFtZSIsImhhbmRsZXIiLCJidWJibGVzIiwiY2FuY2VsYWJsZSIsImRpcmVjdGlvbiIsInRocmVzaG9sZCIsImRlbHRhWCIsImRlbHRhWSIsImlzRGVsdGFYIiwiY2FsbGJhY2siLCJmbiIsInJhZjEiLCJyYWYyIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwiY2FuY2VsQW5pbWF0aW9uRnJhbWUiLCJFTEVNRU5UX05PREUiLCJub2RlcyIsIndhbGtlciIsImNyZWF0ZVRyZWVXYWxrZXIiLCJOb2RlRmlsdGVyIiwiU0hPV19FTEVNRU5UIiwiYWNjZXB0Tm9kZSIsImlzSGlkZGVuSW5wdXQiLCJ0YWdOYW1lIiwiZGlzYWJsZWQiLCJGSUxURVJfU0tJUCIsIkZJTFRFUl9BQ0NFUFQiLCJuZXh0Tm9kZSIsImN1cnJlbnROb2RlIiwiY2FuZGlkYXRlcyIsInByZXZpb3VzbHlGb2N1c2VkRWxlbWVudCIsInNvbWUiLCJWaWV3cG9ydCIsIlJvb3QyIiwiVGl0bGUiLCJEZXNjcmlwdGlvbiIsIkFjdGlvbiIsIkNsb3NlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQixTQUFTQyxjQUFjQSxDQUFDQyxRQUFRLEVBQUU7RUFDaEMsTUFBTUMsV0FBVyxHQUFHSCx5Q0FBWSxDQUFDRSxRQUFRLENBQUM7RUFDMUNGLDRDQUFlLENBQUMsTUFBTTtJQUNwQkcsV0FBVyxDQUFDRyxPQUFPLEdBQUdKLFFBQVE7RUFDaEMsQ0FBQyxDQUFDO0VBQ0YsT0FBT0YsMENBQWEsQ0FBQyxNQUFNLENBQUMsR0FBR1EsSUFBSSxLQUFLTCxXQUFXLENBQUNHLE9BQU8sR0FBRyxHQUFHRSxJQUFJLENBQUMsRUFBRSxFQUFFLENBQUM7QUFDN0UiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1jYWxsYmFjay1yZWZcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VDYWxsYmFja1JlZiIsImNhbGxiYWNrIiwiY2FsbGJhY2tSZWYiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwidXNlTWVtbyIsImFyZ3MiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {},\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(nextValue => {\n    if (isControlled) {\n      const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n      if (value2 !== prop) {\n        onChangeRef.current?.(value2);\n      }\n    } else {\n      setUncontrolledProp(nextValue);\n    }\n  }, [isControlled, prop, setUncontrolledProp, onChangeRef]);\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const {\n    prop: controlledState,\n    defaultProp,\n    onChange: onChangeProp,\n    caller\n  } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{\n    ...initialArg,\n    state: defaultProp\n  }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action) => {\n    if (action.type === SYNC_STATE) {\n      return {\n        ...state2,\n        state: action.state\n      };\n    }\n    const next = reducer(state2, action);\n    if (isControlled && !Object.is(next.state, state2.state)) {\n      onChange(next.state);\n    }\n    return next;\n  }, ...args);\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return {\n        ...internalState,\n        state: controlledState\n      };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({\n        type: SYNC_STATE,\n        state: controlledState\n      });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n      capture: true\n    });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n      capture: true\n    });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsSUFBSUMsZ0JBQWdCLEdBQUdDLFVBQVUsRUFBRUMsUUFBUSxHQUFHSCxrREFBcUIsR0FBRyxNQUFNLENBQzVFLENBQUMiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlTGF5b3V0RWZmZWN0MiIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsInVzZUxheW91dEVmZmVjdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n    ...props,\n    ref: forwardedRef,\n    style: {\n      ...VISUALLY_HIDDEN_STYLES,\n      ...props.style\n    }\n  });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;