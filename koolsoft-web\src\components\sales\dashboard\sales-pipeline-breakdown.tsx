'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Too<PERSON><PERSON>, Legend } from 'recharts';
import { BarChart3, Users, Target, Eye, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SalesPipelineBreakdownProps {
  data: Array<{
    status: string;
    count: number;
    color: string;
  }>;
  isLoading?: boolean;
  className?: string;
  showTitle?: boolean;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
}

function CustomTooltip({ active, payload }: CustomTooltipProps) {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-black">{data.status}</p>
        <p className="text-sm text-gray-600">Count: {data.count.toLocaleString()}</p>
      </div>
    );
  }
  return null;
}

function getStatusIcon(status: string) {
  switch (status.toLowerCase()) {
    case 'leads':
      return <Users className="h-4 w-4" />;
    case 'opportunities':
      return <Target className="h-4 w-4" />;
    case 'prospects':
      return <Eye className="h-4 w-4" />;
    case 'orders':
      return <ShoppingCart className="h-4 w-4" />;
    default:
      return <BarChart3 className="h-4 w-4" />;
  }
}

export function SalesPipelineBreakdown({ 
  data, 
  isLoading = false, 
  className, 
  showTitle = true 
}: SalesPipelineBreakdownProps) {
  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Pipeline Breakdown
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="h-64 bg-gray-200 rounded animate-pulse" />
            <div className="space-y-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const total = data.reduce((sum, item) => sum + item.count, 0);

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Pipeline Breakdown
          </CardTitle>
        </CardHeader>
      )}
      
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Pie Chart */}
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={2}
                  dataKey="count"
                >
                  {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Legend with Details */}
          <div className="space-y-3">
            {data.map((item, index) => {
              const percentage = total > 0 ? (item.count / total) * 100 : 0;
              
              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-4 h-4 rounded-full flex-shrink-0" 
                      style={{ backgroundColor: item.color }}
                    />
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(item.status)}
                      <span className="font-medium text-black">{item.status}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {percentage.toFixed(1)}%
                    </Badge>
                    <span className="font-bold text-black">
                      {item.count.toLocaleString()}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Total */}
          <div className="pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="font-medium text-black">Total Items</span>
              <span className="font-bold text-lg text-black">
                {total.toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
