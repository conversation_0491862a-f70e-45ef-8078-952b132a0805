import { PrismaClient, Prisma } from '@prisma/client';
import { ConversionReportFilter, ConversionStatistics, ConversionTrends } from '@/lib/validations/conversion.schema';
import { format, subDays, subMonths, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

/**
 * Conversion Report Repository
 * 
 * Handles conversion reporting and analytics operations
 */
export class ConversionReportRepository {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * Get conversion statistics
   */
  async getConversionStatistics(filters: Partial<ConversionReportFilter> = {}): Promise<ConversionStatistics> {
    const whereClause = this.buildWhereClause(filters);
    
    // Get total conversions
    const totalConversions = await this.prisma.history_cards.count({
      where: whereClause,
    });

    // Get conversions by type
    const conversionsByType = await this.prisma.history_cards.groupBy({
      by: ['source'],
      where: whereClause,
      _count: {
        id: true,
      },
    });

    // Calculate percentages for conversion types
    const conversionTypeStats = conversionsByType
      .filter(item => item.source && ['WARRANTY_TO_AMC', 'AMC_TO_OUT_WARRANTY', 'WARRANTY_TO_OUT_WARRANTY'].includes(item.source))
      .map(item => ({
        type: item.source as 'WARRANTY_TO_AMC' | 'AMC_TO_OUT_WARRANTY' | 'WARRANTY_TO_OUT_WARRANTY',
        count: item._count.id,
        percentage: totalConversions > 0 ? (item._count.id / totalConversions) * 100 : 0,
      }));

    // Get revenue data (from AMC contracts and out-warranty records)
    const amcConversions = await this.prisma.history_cards.findMany({
      where: {
        ...whereClause,
        source: 'WARRANTY_TO_AMC',
        amcId: { not: null },
      },
      include: {
        amcContract: {
          select: { amount: true },
        },
      },
    });

    const outWarrantyConversions = await this.prisma.history_cards.findMany({
      where: {
        ...whereClause,
        source: { in: ['AMC_TO_OUT_WARRANTY', 'WARRANTY_TO_OUT_WARRANTY'] },
        outWarrantyId: { not: null },
      },
      include: {
        outWarranty: {
          select: { amount: true },
        },
      },
    });

    const amcRevenue = amcConversions.reduce((sum, conv) => sum + (conv.amcContract?.amount || 0), 0);
    const outWarrantyRevenue = outWarrantyConversions.reduce((sum, conv) => sum + (Number(conv.outWarranty?.amount) || 0), 0);
    const totalRevenue = amcRevenue + outWarrantyRevenue;
    const averageConversionValue = totalConversions > 0 ? totalRevenue / totalConversions : 0;

    // Calculate success rate (assuming all conversions in history_cards are successful)
    const successRate = 100; // All records in history_cards represent successful conversions

    // Get previous period comparison
    const previousPeriodFilter = this.getPreviousPeriodFilter(filters);
    const previousPeriodTotal = await this.prisma.history_cards.count({
      where: this.buildWhereClause(previousPeriodFilter),
    });

    const growthPercentage = previousPeriodTotal > 0 
      ? ((totalConversions - previousPeriodTotal) / previousPeriodTotal) * 100 
      : totalConversions > 0 ? 100 : 0;

    return {
      totalConversions,
      conversionsByType: conversionTypeStats,
      conversionsByStatus: [
        { status: 'SUCCESS', count: totalConversions, percentage: 100 },
        { status: 'FAILED', count: 0, percentage: 0 },
        { status: 'PENDING', count: 0, percentage: 0 },
      ],
      totalRevenue,
      averageConversionValue,
      successRate,
      periodComparison: {
        previousPeriodTotal,
        growthPercentage,
      },
    };
  }

  /**
   * Get conversion trends over time
   */
  async getConversionTrends(
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' = 'monthly',
    filters: Partial<ConversionReportFilter> = {}
  ): Promise<ConversionTrends> {
    const whereClause = this.buildWhereClause(filters);
    
    // Determine date grouping based on period
    const dateFormat = this.getDateFormat(period);
    const dateGrouping = this.getDateGrouping(period);

    // Get conversions grouped by date and type
    const conversions = await this.prisma.history_cards.findMany({
      where: whereClause,
      include: {
        amcContract: {
          select: { amount: true },
        },
        outWarranty: {
          select: { amount: true },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    // Group conversions by date period
    const groupedData = new Map<string, {
      totalConversions: number;
      warrantyToAmc: number;
      amcToOutWarranty: number;
      warrantyToOutWarranty: number;
      totalRevenue: number;
    }>();

    conversions.forEach(conversion => {
      const dateKey = format(conversion.createdAt, dateFormat);
      const existing = groupedData.get(dateKey) || {
        totalConversions: 0,
        warrantyToAmc: 0,
        amcToOutWarranty: 0,
        warrantyToOutWarranty: 0,
        totalRevenue: 0,
      };

      existing.totalConversions++;
      
      switch (conversion.source) {
        case 'WARRANTY_TO_AMC':
          existing.warrantyToAmc++;
          existing.totalRevenue += conversion.amcContract?.amount || 0;
          break;
        case 'AMC_TO_OUT_WARRANTY':
          existing.amcToOutWarranty++;
          existing.totalRevenue += Number(conversion.outWarranty?.amount) || 0;
          break;
        case 'WARRANTY_TO_OUT_WARRANTY':
          existing.warrantyToOutWarranty++;
          existing.totalRevenue += Number(conversion.outWarranty?.amount) || 0;
          break;
      }

      groupedData.set(dateKey, existing);
    });

    // Convert to array and calculate success rates
    const data = Array.from(groupedData.entries()).map(([date, stats]) => ({
      date,
      ...stats,
      successRate: 100, // All conversions in history_cards are successful
    }));

    return {
      period,
      data,
    };
  }

  /**
   * Get detailed conversion records with filtering and pagination
   */
  async getDetailedConversions(filters: Partial<ConversionReportFilter> = {}) {
    const whereClause = this.buildWhereClause(filters);
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const orderBy = filters.orderBy || 'createdAt';
    const orderDirection = filters.orderDirection || 'desc';
    const skip = (page - 1) * limit;

    const [conversions, total] = await Promise.all([
      this.prisma.history_cards.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
            },
          },
          amcContract: {
            select: {
              id: true,

              amount: true,
              startDate: true,
              endDate: true,
              status: true,
            },
          },
          inWarranty: {
            select: {
              id: true,
              bslNo: true,
              installDate: true,
              warrantyDate: true,
            },
          },
          outWarranty: {
            select: {
              id: true,
              startDate: true,
              endDate: true,
              amount: true,
            },
          },
        },
        orderBy: {
          [orderBy]: orderDirection,
        },
        skip,
        take: limit,
      }),
      this.prisma.history_cards.count({ where: whereClause }),
    ]);

    return {
      conversions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Build where clause for filtering
   */
  private buildWhereClause(filters: Partial<ConversionReportFilter>): Prisma.history_cardsWhereInput {
    const where: Prisma.history_cardsWhereInput = {
      source: {
        in: ['WARRANTY_TO_AMC', 'AMC_TO_OUT_WARRANTY', 'WARRANTY_TO_OUT_WARRANTY'],
      },
    };

    if (filters.dateFrom || filters.dateTo) {
      where.createdAt = {};
      if (filters.dateFrom) {
        where.createdAt.gte = startOfDay(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.createdAt.lte = endOfDay(filters.dateTo);
      }
    }

    if (filters.conversionType) {
      where.source = filters.conversionType;
    }

    if (filters.customerId) {
      where.customerId = filters.customerId;
    }

    return where;
  }

  /**
   * Get previous period filter for comparison
   */
  private getPreviousPeriodFilter(filters: Partial<ConversionReportFilter>): Partial<ConversionReportFilter> {
    if (!filters.dateFrom || !filters.dateTo) {
      // Default to last 30 days comparison
      const now = new Date();
      const thirtyDaysAgo = subDays(now, 30);
      const sixtyDaysAgo = subDays(now, 60);
      
      return {
        ...filters,
        dateFrom: sixtyDaysAgo,
        dateTo: thirtyDaysAgo,
      };
    }

    const daysDiff = Math.ceil((filters.dateTo.getTime() - filters.dateFrom.getTime()) / (1000 * 60 * 60 * 24));
    const previousDateTo = subDays(filters.dateFrom, 1);
    const previousDateFrom = subDays(previousDateTo, daysDiff);

    return {
      ...filters,
      dateFrom: previousDateFrom,
      dateTo: previousDateTo,
    };
  }

  /**
   * Get date format for grouping
   */
  private getDateFormat(period: string): string {
    switch (period) {
      case 'daily':
        return 'yyyy-MM-dd';
      case 'weekly':
        return 'yyyy-\'W\'ww';
      case 'monthly':
        return 'yyyy-MM';
      case 'quarterly':
        return 'yyyy-\'Q\'Q';
      default:
        return 'yyyy-MM';
    }
  }

  /**
   * Get date grouping SQL for aggregation
   */
  private getDateGrouping(period: string): string {
    switch (period) {
      case 'daily':
        return 'DATE(created_at)';
      case 'weekly':
        return 'YEARWEEK(created_at)';
      case 'monthly':
        return 'DATE_FORMAT(created_at, "%Y-%m")';
      case 'quarterly':
        return 'CONCAT(YEAR(created_at), "-Q", QUARTER(created_at))';
      default:
        return 'DATE_FORMAT(created_at, "%Y-%m")';
    }
  }
}
