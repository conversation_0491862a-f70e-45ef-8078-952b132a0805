import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCContractRepository } from '@/lib/repositories';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

/**
 * GET /api/amc/contracts/count
 * Get count of AMC contracts by status
 * 
 * This endpoint provides counts of AMC contracts by various statuses.
 * It's useful for dashboard statistics and summary information.
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const amcContractRepository = getAMCContractRepository();
      // Use direct prisma client instead of repository's protected property
      
      // Get current date
      const today = new Date();
      const thirtyDaysFromNow = new Date(today);
      thirtyDaysFromNow.setDate(today.getDate() + 30);
      
      // Get counts by status
      const [
        totalCount,
        activeCount,
        expiredCount,
        pendingCount,
        cancelledCount,
        renewedCount,
        expiringCount
      ] = await Promise.all([
        prisma.amc_contracts.count(),
        prisma.amc_contracts.count({ where: { status: 'ACTIVE' } }),
        prisma.amc_contracts.count({ where: { status: 'EXPIRED' } }),
        prisma.amc_contracts.count({ where: { status: 'PENDING' } }),
        prisma.amc_contracts.count({ where: { status: 'CANCELLED' } }),
        prisma.amc_contracts.count({ where: { status: 'RENEWED' } }),
        prisma.amc_contracts.count({
          where: {
            status: 'ACTIVE',
            endDate: {
              gte: today,
              lte: thirtyDaysFromNow
            }
          }
        })
      ]);
      
      // Get counts by date ranges
      const currentYearStart = new Date(today.getFullYear(), 0, 1);
      const currentYearEnd = new Date(today.getFullYear(), 11, 31);
      
      const currentYearCount = await prisma.amc_contracts.count({
        where: {
          startDate: {
            gte: currentYearStart,
            lte: currentYearEnd
          }
        }
      });
      
      return NextResponse.json({
        total: totalCount,
        active: activeCount,
        expired: expiredCount,
        pending: pendingCount,
        cancelled: cancelledCount,
        renewed: renewedCount,
        expiring: expiringCount,
        currentYear: currentYearCount
      });
    } catch (error) {
      console.error('Error counting AMC contracts:', error);
      
      if (error instanceof PrismaClientKnownRequestError) {
        // Handle specific Prisma errors
        return NextResponse.json(
          { 
            error: 'Database error', 
            details: `Error code: ${error.code}`,
            code: 'DATABASE_ERROR'
          },
          { status: 500 }
        );
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to count AMC contracts',
          message: process.env.NODE_ENV === 'development' ? (error as Error).message : 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        },
        { status: 500 }
      );
    }
  }
);
