/**
 * KoolSoft Report Formula Engine
 * 
 * Main entry point for the formula engine providing a unified interface
 * for parsing, evaluating, and managing formulas in reports.
 */

import { FormulaParser, FormulaAST, FormulaParseError } from './parser';
import { FormulaEvaluator, FormulaContext, FormulaEvaluationError } from './evaluator';
import { FormulaFunctions, FunctionDefinition } from './functions';
import { FormulaValidator } from './validator';

export interface FormulaResult {
  success: boolean;
  value?: any;
  error?: string;
  warnings?: string[];
}

export interface FormulaInfo {
  formula: string;
  ast?: FormulaAST;
  variables: string[];
  functions: string[];
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class FormulaEngine {
  private parser: FormulaParser;
  private evaluator: FormulaEvaluator;
  private validator: FormulaValidator;
  private functions: FormulaFunctions;

  constructor(context: FormulaContext = {}) {
    this.parser = new FormulaParser();
    this.evaluator = new FormulaEvaluator(context);
    this.validator = new FormulaValidator();
    this.functions = new FormulaFunctions();
  }

  /**
   * Parse and evaluate a formula with the given context
   */
  evaluate(formula: string, context?: FormulaContext): FormulaResult {
    try {
      // Parse the formula
      const ast = this.parser.parse(formula);
      
      // Evaluate with context
      const value = this.evaluator.evaluate(ast, context);
      
      return {
        success: true,
        value,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Parse a formula and return AST
   */
  parse(formula: string): FormulaAST {
    return this.parser.parse(formula);
  }

  /**
   * Validate a formula and return detailed information
   */
  validate(formula: string, context?: FormulaContext): FormulaInfo {
    const info: FormulaInfo = {
      formula,
      variables: [],
      functions: [],
      isValid: false,
      errors: [],
      warnings: [],
    };

    try {
      // Parse the formula
      const ast = this.parser.parse(formula);
      info.ast = ast;

      // Extract variables and functions
      this.extractIdentifiers(ast, info);

      // Validate with context if provided
      if (context) {
        this.evaluator.setContext(context);
        const missingVars = this.evaluator.validateContext(ast);
        if (missingVars.length > 0) {
          info.errors.push(`Undefined variables: ${missingVars.join(', ')}`);
        }
      }

      // Validate functions
      for (const funcName of info.functions) {
        if (!this.functions.hasFunction(funcName)) {
          info.errors.push(`Unknown function: ${funcName}`);
        }
      }

      // Additional validation
      const validationResult = this.validator.validate(ast);
      info.errors.push(...validationResult.errors);
      info.warnings.push(...validationResult.warnings);

      info.isValid = info.errors.length === 0;
    } catch (error) {
      info.errors.push(error instanceof Error ? error.message : String(error));
      info.isValid = false;
    }

    return info;
  }

  /**
   * Get available functions
   */
  getAvailableFunctions(): FunctionDefinition[] {
    return this.functions.getAllFunctions();
  }

  /**
   * Get functions by category
   */
  getFunctionsByCategory(): Record<string, FunctionDefinition[]> {
    return this.functions.getFunctionsByCategory();
  }

  /**
   * Get function documentation
   */
  getFunctionDoc(name: string): string | undefined {
    return this.functions.getFunctionDoc(name);
  }

  /**
   * Update evaluation context
   */
  setContext(context: FormulaContext): void {
    this.evaluator.setContext(context);
  }

  /**
   * Add variables to context
   */
  addToContext(variables: FormulaContext): void {
    this.evaluator.addToContext(variables);
  }

  /**
   * Test a formula with sample data
   */
  test(formula: string, testCases: Array<{ context: FormulaContext; expected?: any }>): Array<{
    context: FormulaContext;
    result: FormulaResult;
    expected?: any;
    passed?: boolean;
  }> {
    return testCases.map(testCase => {
      const result = this.evaluate(formula, testCase.context);
      const passed = testCase.expected !== undefined 
        ? result.success && result.value === testCase.expected
        : result.success;

      return {
        context: testCase.context,
        result,
        expected: testCase.expected,
        passed,
      };
    });
  }

  /**
   * Extract identifiers (variables and functions) from AST
   */
  private extractIdentifiers(node: FormulaAST, info: FormulaInfo): void {
    switch (node.type) {
      case 'Identifier':
        if (node.name && !info.variables.includes(node.name)) {
          info.variables.push(node.name);
        }
        break;

      case 'FunctionCall':
        if (node.name && !info.functions.includes(node.name)) {
          info.functions.push(node.name);
        }
        if (node.arguments) {
          node.arguments.forEach(arg => this.extractIdentifiers(arg, info));
        }
        break;

      case 'BinaryExpression':
        if (node.left) this.extractIdentifiers(node.left, info);
        if (node.right) this.extractIdentifiers(node.right, info);
        break;

      case 'UnaryExpression':
        if (node.argument) this.extractIdentifiers(node.argument, info);
        break;
    }
  }

  /**
   * Format formula for display
   */
  formatFormula(formula: string): string {
    try {
      const ast = this.parser.parse(formula);
      return this.formatAST(ast);
    } catch {
      return formula; // Return original if parsing fails
    }
  }

  /**
   * Format AST back to formula string
   */
  private formatAST(node: FormulaAST): string {
    switch (node.type) {
      case 'Literal':
        return typeof node.value === 'string' ? `"${node.value}"` : String(node.value);

      case 'Identifier':
        return node.name || '';

      case 'BinaryExpression':
        const left = this.formatAST(node.left!);
        const right = this.formatAST(node.right!);
        return `(${left} ${node.operator} ${right})`;

      case 'UnaryExpression':
        const arg = this.formatAST(node.argument!);
        return `${node.operator}${arg}`;

      case 'FunctionCall':
        const args = node.arguments?.map(arg => this.formatAST(arg)).join(', ') || '';
        return `${node.name}(${args})`;

      default:
        return '';
    }
  }

  /**
   * Get formula complexity score (0-100)
   */
  getComplexityScore(formula: string): number {
    try {
      const ast = this.parser.parse(formula);
      return this.calculateComplexity(ast);
    } catch {
      return 0;
    }
  }

  /**
   * Calculate complexity score for AST
   */
  private calculateComplexity(node: FormulaAST): number {
    let score = 1; // Base score

    switch (node.type) {
      case 'BinaryExpression':
        score += 2;
        if (node.left) score += this.calculateComplexity(node.left);
        if (node.right) score += this.calculateComplexity(node.right);
        break;

      case 'UnaryExpression':
        score += 1;
        if (node.argument) score += this.calculateComplexity(node.argument);
        break;

      case 'FunctionCall':
        score += 3; // Functions add more complexity
        if (node.arguments) {
          node.arguments.forEach(arg => {
            score += this.calculateComplexity(arg);
          });
        }
        break;

      case 'Identifier':
        score += 1;
        break;

      case 'Literal':
        score += 0.5;
        break;
    }

    return Math.min(score, 100); // Cap at 100
  }
}

// Export all types and classes
export {
  FormulaParser,
  FormulaEvaluator,
  FormulaFunctions,
  FormulaValidator,
  FormulaParseError,
  FormulaEvaluationError,
};

export type {
  FormulaAST,
  FormulaContext,
  FunctionDefinition,
};

// Export default instance
export const defaultFormulaEngine = new FormulaEngine();
