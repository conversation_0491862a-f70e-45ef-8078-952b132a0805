import { PrismaClient, Prisma } from '@prisma/client';
import { PrismaRepository } from './prisma.repository';
import { BaseRepository } from './base.repository';

/**
 * Repository for managing out-of-warranty records
 */
export class OutWarrantyRepository extends PrismaRepository<
  Prisma.out_warrantiesGetPayload<{}>,
  string,
  Prisma.out_warrantiesCreateInput,
  Prisma.out_warrantiesUpdateInput
> {
  constructor(prisma: PrismaClient) {
    super('out_warranties');
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.out_warrantiesGetPayload<{}>,
    string,
    Prisma.out_warrantiesCreateInput,
    Prisma.out_warrantiesUpdateInput
  > {
    return new OutWarrantyRepository(tx);
  }

  /**
   * Find out-warranties with related data
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of out-warranties with related data
   */
  async findWithRelations(
    filter: Prisma.out_warrantiesWhereInput = {},
    skip?: number,
    take?: number,
    orderBy?: Prisma.out_warrantiesOrderByWithRelationInput
  ): Promise<any[]> {
    try {
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { startDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          machines: {
            include: {
              components: true,
              model: {
                include: {
                  product: {
                    include: {
                      brand: true,
                    }
                  }
                }
              },
            },
          },
          payments: true,
        },
      });

      return result;
    } catch (error) {
      console.error('OutWarrantyRepository.findWithRelations: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Find a single out-warranty with all related data
   * @param id Out-warranty ID
   * @returns Promise resolving to out-warranty with related data or null
   */
  async findWithAllRelations(id: string): Promise<any | null> {
    try {
      return await this.model.findUnique({
        where: { id },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
              address: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          machines: {
            include: {
              components: true,
              model: {
                include: {
                  product: {
                    include: {
                      brand: true,
                    }
                  }
                }
              },
            },
          },
          payments: {
            orderBy: { paymentDate: 'desc' },
          },
        },
      });
    } catch (error) {
      console.error('OutWarrantyRepository.findWithAllRelations: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Get out-warranties by customer
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to customer's out-warranties
   */
  async findByCustomer(
    customerId: string,
    skip?: number,
    take?: number
  ): Promise<any[]> {
    return this.findWithRelations(
      { customerId },
      skip,
      take,
      { startDate: 'desc' }
    );
  }

  /**
   * Get out-warranties by executive
   * @param executiveId Executive ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to executive's out-warranties
   */
  async findByExecutive(
    executiveId: string,
    skip?: number,
    take?: number
  ): Promise<any[]> {
    return this.findWithRelations(
      { executiveId },
      skip,
      take,
      { startDate: 'desc' }
    );
  }

  /**
   * Get out-warranties by source type
   * @param source Source type (WARRANTY_CONVERSION, AMC_CONVERSION, NEW)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to out-warranties by source
   */
  async findBySource(
    source: string,
    skip?: number,
    take?: number
  ): Promise<any[]> {
    return this.findWithRelations(
      { source },
      skip,
      take,
      { startDate: 'desc' }
    );
  }

  /**
   * Get active out-warranties
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to active out-warranties
   */
  async findActive(skip?: number, take?: number): Promise<any[]> {
    return this.findWithRelations(
      { isActive: true },
      skip,
      take,
      { startDate: 'desc' }
    );
  }

  /**
   * Search out-warranties by customer name or machine details
   * @param searchTerm Search term
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to matching out-warranties
   */
  async search(
    searchTerm: string,
    skip?: number,
    take?: number
  ): Promise<any[]> {
    const filter: Prisma.out_warrantiesWhereInput = {
      OR: [
        {
          customer: {
            name: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
        },
        {
          machines: {
            some: {
              OR: [
                {
                  serialNumber: {
                    contains: searchTerm,
                    mode: 'insensitive',
                  },
                },
                {
                  model: {
                    name: {
                      contains: searchTerm,
                      mode: 'insensitive',
                    },
                  },
                },
                {
                  model: {
                    product: {
                      name: {
                        contains: searchTerm,
                        mode: 'insensitive',
                      },
                    },
                  },
                },
                {
                  model: {
                    product: {
                      brand: {
                        name: {
                          contains: searchTerm,
                          mode: 'insensitive',
                        },
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    };

    return this.findWithRelations(filter, skip, take);
  }

  /**
   * Count out-warranties by filter
   * @param filter Filter criteria
   * @returns Promise resolving to count
   */
  async countByFilter(filter: Prisma.out_warrantiesWhereInput = {}): Promise<number> {
    try {
      return await this.model.count({ where: filter });
    } catch (error) {
      console.error('OutWarrantyRepository.countByFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Get out-warranty statistics
   * @returns Promise resolving to statistics object
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    bySource: Record<string, number>;
    totalAmount: number;
  }> {
    try {
      const [total, active, bySource, totalAmount] = await Promise.all([
        this.model.count(),
        this.model.count({ where: { isActive: true } }),
        this.model.groupBy({
          by: ['source'],
          _count: { source: true },
        }),
        this.model.aggregate({
          _sum: { amount: true },
        }),
      ]);

      const sourceStats = bySource.reduce((acc: any, item: any) => {
        acc[item.source] = item._count.source;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        active,
        bySource: sourceStats,
        totalAmount: Number(totalAmount._sum.amount || 0),
      };
    } catch (error) {
      console.error('OutWarrantyRepository.getStatistics: Error executing query:', error);
      throw error;
    }
  }
}
