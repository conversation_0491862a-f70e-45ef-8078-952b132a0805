"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mkdirp";
exports.ids = ["vendor-chunks/mkdirp"];
exports.modules = {

/***/ "(rsc)/./node_modules/mkdirp/index.js":
/*!**************************************!*\
  !*** ./node_modules/mkdirp/index.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar path = __webpack_require__(/*! path */ \"path\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar _0777 = parseInt('0777', 8);\nmodule.exports = mkdirP.mkdirp = mkdirP.mkdirP = mkdirP;\nfunction mkdirP(p, opts, f, made) {\n  if (typeof opts === 'function') {\n    f = opts;\n    opts = {};\n  } else if (!opts || typeof opts !== 'object') {\n    opts = {\n      mode: opts\n    };\n  }\n  var mode = opts.mode;\n  var xfs = opts.fs || fs;\n  if (mode === undefined) {\n    mode = _0777;\n  }\n  if (!made) made = null;\n  var cb = f || /* istanbul ignore next */function () {};\n  p = path.resolve(p);\n  xfs.mkdir(p, mode, function (er) {\n    if (!er) {\n      made = made || p;\n      return cb(null, made);\n    }\n    switch (er.code) {\n      case 'ENOENT':\n        /* istanbul ignore if */\n        if (path.dirname(p) === p) return cb(er);\n        mkdirP(path.dirname(p), opts, function (er, made) {\n          /* istanbul ignore if */\n          if (er) cb(er, made);else mkdirP(p, opts, cb, made);\n        });\n        break;\n\n      // In the case of any other error, just see if there's a dir\n      // there already.  If so, then hooray!  If not, then something\n      // is borked.\n      default:\n        xfs.stat(p, function (er2, stat) {\n          // if the stat fails, then that's super weird.\n          // let the original error be the failure reason.\n          if (er2 || !stat.isDirectory()) cb(er, made);else cb(null, made);\n        });\n        break;\n    }\n  });\n}\nmkdirP.sync = function sync(p, opts, made) {\n  if (!opts || typeof opts !== 'object') {\n    opts = {\n      mode: opts\n    };\n  }\n  var mode = opts.mode;\n  var xfs = opts.fs || fs;\n  if (mode === undefined) {\n    mode = _0777;\n  }\n  if (!made) made = null;\n  p = path.resolve(p);\n  try {\n    xfs.mkdirSync(p, mode);\n    made = made || p;\n  } catch (err0) {\n    switch (err0.code) {\n      case 'ENOENT':\n        made = sync(path.dirname(p), opts, made);\n        sync(p, opts, made);\n        break;\n\n      // In the case of any other error, just see if there's a dir\n      // there already.  If so, then hooray!  If not, then something\n      // is borked.\n      default:\n        var stat;\n        try {\n          stat = xfs.statSync(p);\n        } catch (err1) /* istanbul ignore next */{\n          throw err0;\n        }\n        /* istanbul ignore if */\n        if (!stat.isDirectory()) throw err0;\n        break;\n    }\n  }\n  return made;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/mkdirp/index.js\n");

/***/ })

};
;