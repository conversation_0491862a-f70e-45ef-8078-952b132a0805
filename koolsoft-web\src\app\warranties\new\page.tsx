'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Save, 
  X, 
  Plus, 
  AlertTriangle,
  Calendar,
  DollarSign,
  User,
  Building
} from 'lucide-react';
import Link from 'next/link';

interface Customer {
  id: string;
  name: string;
  city?: string;
}

interface Executive {
  id: string;
  name: string;
  email?: string;
}

/**
 * New Warranty Page
 *
 * This page provides a form for creating new warranties with support for
 * different warranty types (in-warranty, out-warranty, BLUESTAR-specific).
 */
export default function NewWarrantyPage() {
  const searchParams = useSearchParams();
  const warrantyType = searchParams?.get('type') || 'in-warranty';
  const vendor = searchParams?.get('vendor');

  const [formData, setFormData] = useState({
    customerId: '',
    executiveId: 'none',
    contactPersonId: '',
    bslNo: '',
    bslDate: '',
    bslAmount: '',
    frequency: '',
    numberOfMachines: '1',
    installDate: '',
    warrantyDate: '',
    warningDate: '',
    technicianId: '',
    amcId: '',
    status: 'ACTIVE',
    // BLUESTAR specific fields
    bluestarWarrantyCode: '',
    bluestarServiceCenter: '',
    bluestarContactPerson: '',
    bluestarContactPhone: '',
    specialTerms: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [serviceCenters, setServiceCenters] = useState<any[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Load customers, executives, and service centers
  useEffect(() => {
    const loadFormData = async () => {
      try {
        setIsLoadingData(true);

        const requests = [
          fetch('/api/customers?take=1000', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&take=1000', { credentials: 'include' })
        ];

        // Add service centers request if BLUESTAR vendor
        if (vendor === 'bluestar') {
          requests.push(fetch('/api/service-centers?vendor=BLUESTAR', { credentials: 'include' }));
        }

        const responses = await Promise.all(requests);

        if (responses[0].ok) {
          const customersData = await responses[0].json();
          setCustomers(customersData.success ? customersData.data : customersData.customers || []);
        }

        if (responses[1].ok) {
          const executivesData = await responses[1].json();
          setExecutives(executivesData.users || []);
        }

        if (vendor === 'bluestar' && responses[2] && responses[2].ok) {
          const serviceCentersData = await responses[2].json();
          setServiceCenters(serviceCentersData.serviceCenters || []);
        }
      } catch (error) {
        console.error('Error loading form data:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadFormData();
  }, [vendor]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerId) {
      newErrors.customerId = 'Customer is required';
    }

    if (!formData.bslNo) {
      newErrors.bslNo = 'BSL Number is required';
    }

    if (!formData.numberOfMachines || parseInt(formData.numberOfMachines) < 1) {
      newErrors.numberOfMachines = 'Number of machines must be at least 1';
    }

    if (formData.installDate && formData.warrantyDate) {
      const installDate = new Date(formData.installDate);
      const warrantyDate = new Date(formData.warrantyDate);
      if (warrantyDate <= installDate) {
        newErrors.warrantyDate = 'Warranty date must be after install date';
      }
    }

    if (vendor === 'bluestar' && !formData.bluestarWarrantyCode) {
      newErrors.bluestarWarrantyCode = 'BLUESTAR warranty code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare warranty data for API
      const warrantyData = {
        customerId: formData.customerId,
        executiveId: formData.executiveId && formData.executiveId !== 'none' ? formData.executiveId : null,
        contactPersonId: formData.contactPersonId || null,
        bslNo: formData.bslNo || null,
        bslDate: formData.bslDate ? new Date(formData.bslDate).toISOString() : null,
        bslAmount: formData.bslAmount ? parseFloat(formData.bslAmount) : 0,
        frequency: formData.frequency ? parseInt(formData.frequency) : 0,
        numberOfMachines: parseInt(formData.numberOfMachines),
        installDate: formData.installDate ? new Date(formData.installDate).toISOString() : null,
        warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,
        warningDate: formData.warningDate ? new Date(formData.warningDate).toISOString() : null,
        technicianId: formData.technicianId || null,
        amcId: formData.amcId || null,
        status: formData.status,
        // Add BLUESTAR specific data if applicable
        ...(vendor === 'bluestar' && {
          vendorSpecific: {
            bluestarWarrantyCode: formData.bluestarWarrantyCode,
            bluestarServiceCenter: formData.bluestarServiceCenter,
            bluestarContactPerson: formData.bluestarContactPerson,
            bluestarContactPhone: formData.bluestarContactPhone,
            specialTerms: formData.specialTerms
          }
        })
      };

      const response = await fetch('/api/warranties', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(warrantyData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('Warranty creation error:', response.status, response.statusText, errorData);

        if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        } else if (response.status === 400) {
          // Handle validation errors
          if (errorData.details && Array.isArray(errorData.details)) {
            const validationErrors = errorData.details.map((err: any) => err.message).join(', ');
            throw new Error(`Validation error: ${validationErrors}`);
          }
          throw new Error(errorData.error || 'Invalid data provided');
        } else if (response.status === 409) {
          throw new Error(errorData.error || 'Warranty with this BSL number already exists');
        } else {
          throw new Error(errorData.error || `Server error: ${response.statusText}`);
        }
      }

      const createdWarranty = await response.json();

      // Redirect to appropriate page after creation
      if (warrantyType === 'out-warranty') {
        window.location.href = '/warranties/out-warranty';
      } else if (vendor === 'bluestar') {
        window.location.href = '/warranties/bluestar';
      } else {
        window.location.href = '/warranties/in-warranty';
      }
    } catch (error: any) {
      console.error('Error creating warranty:', error);
      setErrors({ submit: error.message || 'Failed to create warranty. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPageTitle = () => {
    if (vendor === 'bluestar') return 'New BLUESTAR Warranty';
    if (warrantyType === 'out-warranty') return 'New Out-of-Warranty';
    return 'New In-Warranty';
  };

  const getBackUrl = () => {
    if (vendor === 'bluestar') return '/warranties/bluestar';
    if (warrantyType === 'out-warranty') return '/warranties/out-warranty';
    return '/warranties/in-warranty';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>{getPageTitle()}</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Create a new warranty record with machine and component details
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" asChild>
              <Link href={getBackUrl()}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit}>
            <Tabs defaultValue="basic" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="basic">Basic Information</TabsTrigger>
                <TabsTrigger value="dates">Dates & Terms</TabsTrigger>
                <TabsTrigger value="machines">Machines</TabsTrigger>
                {vendor === 'bluestar' && (
                  <TabsTrigger value="bluestar">BLUESTAR Details</TabsTrigger>
                )}
              </TabsList>

              <TabsContent value="basic" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="customerId" className="text-black">Customer *</Label>
                    <Select value={formData.customerId} onValueChange={(value) => handleInputChange('customerId', value)}>
                      <SelectTrigger id="customerId">
                        <SelectValue placeholder={isLoadingData ? "Loading customers..." : "Select customer"} />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.isArray(customers) && customers.length > 0 ? (
                          customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name} {customer.city && `(${customer.city})`}
                            </SelectItem>
                          ))
                        ) : !isLoadingData ? (
                          <SelectItem value="no-customers" disabled>
                            No customers available
                          </SelectItem>
                        ) : null}
                      </SelectContent>
                    </Select>
                    {errors.customerId && (
                      <p className="text-sm text-red-600">{errors.customerId}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="executiveId" className="text-black">Executive</Label>
                    <Select value={formData.executiveId} onValueChange={(value) => handleInputChange('executiveId', value)}>
                      <SelectTrigger id="executiveId">
                        <SelectValue placeholder={isLoadingData ? "Loading executives..." : "Select executive"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Executive</SelectItem>
                        {Array.isArray(executives) && executives.length > 0 ? (
                          executives.map((executive) => (
                            <SelectItem key={executive.id} value={executive.id}>
                              {executive.name} {executive.email && `(${executive.email})`}
                            </SelectItem>
                          ))
                        ) : !isLoadingData ? (
                          <SelectItem value="no-executives" disabled>
                            No executives available
                          </SelectItem>
                        ) : null}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bslNo" className="text-black">BSL Number *</Label>
                    <Input
                      id="bslNo"
                      value={formData.bslNo}
                      onChange={(e) => handleInputChange('bslNo', e.target.value)}
                      placeholder="Enter BSL number"
                    />
                    {errors.bslNo && (
                      <p className="text-sm text-red-600">{errors.bslNo}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bslAmount" className="text-black">BSL Amount</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="bslAmount"
                        type="number"
                        value={formData.bslAmount}
                        onChange={(e) => handleInputChange('bslAmount', e.target.value)}
                        placeholder="0"
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="numberOfMachines" className="text-black">Number of Machines *</Label>
                    <Input
                      id="numberOfMachines"
                      type="number"
                      min="1"
                      value={formData.numberOfMachines}
                      onChange={(e) => handleInputChange('numberOfMachines', e.target.value)}
                      placeholder="1"
                    />
                    {errors.numberOfMachines && (
                      <p className="text-sm text-red-600">{errors.numberOfMachines}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status" className="text-black">Status</Label>
                    <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="EXPIRED">Expired</SelectItem>
                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="dates" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="bslDate" className="text-black">BSL Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="bslDate"
                        type="date"
                        value={formData.bslDate}
                        onChange={(e) => handleInputChange('bslDate', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="installDate" className="text-black">Install Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="installDate"
                        type="date"
                        value={formData.installDate}
                        onChange={(e) => handleInputChange('installDate', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="warrantyDate" className="text-black">Warranty Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="warrantyDate"
                        type="date"
                        value={formData.warrantyDate}
                        onChange={(e) => handleInputChange('warrantyDate', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    {errors.warrantyDate && (
                      <p className="text-sm text-red-600">{errors.warrantyDate}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="warningDate" className="text-black">Warning Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="warningDate"
                        type="date"
                        value={formData.warningDate}
                        onChange={(e) => handleInputChange('warningDate', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="frequency" className="text-black">Frequency</Label>
                    <Input
                      id="frequency"
                      type="number"
                      value={formData.frequency}
                      onChange={(e) => handleInputChange('frequency', e.target.value)}
                      placeholder="0"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="machines" className="space-y-6">
                <div className="text-center py-8">
                  <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-black mb-2">Machine Assignment</h3>
                  <p className="text-gray-600 mb-4">
                    Machine and component assignment interface will be implemented here.
                  </p>
                  <Button type="button">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Machine
                  </Button>
                </div>
              </TabsContent>

              {vendor === 'bluestar' && (
                <TabsContent value="bluestar" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="bluestarWarrantyCode" className="text-black">BLUESTAR Warranty Code *</Label>
                      <Input
                        id="bluestarWarrantyCode"
                        value={formData.bluestarWarrantyCode}
                        onChange={(e) => handleInputChange('bluestarWarrantyCode', e.target.value)}
                        placeholder="Enter BLUESTAR warranty code"
                      />
                      {errors.bluestarWarrantyCode && (
                        <p className="text-sm text-red-600">{errors.bluestarWarrantyCode}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bluestarServiceCenter" className="text-black">Service Center</Label>
                      <Select value={formData.bluestarServiceCenter} onValueChange={(value) => handleInputChange('bluestarServiceCenter', value)}>
                        <SelectTrigger id="bluestarServiceCenter">
                          <SelectValue placeholder="Select service center" />
                        </SelectTrigger>
                        <SelectContent>
                          {serviceCenters.map((center) => (
                            <SelectItem key={center.id} value={center.name}>
                              {center.name}
                            </SelectItem>
                          ))}
                          {serviceCenters.length === 0 && !isLoadingData && (
                            <SelectItem value="no-centers" disabled>
                              No service centers available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bluestarContactPerson" className="text-black">Contact Person</Label>
                      <Input
                        id="bluestarContactPerson"
                        value={formData.bluestarContactPerson}
                        onChange={(e) => handleInputChange('bluestarContactPerson', e.target.value)}
                        placeholder="Enter contact person name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bluestarContactPhone" className="text-black">Contact Phone</Label>
                      <Input
                        id="bluestarContactPhone"
                        value={formData.bluestarContactPhone}
                        onChange={(e) => handleInputChange('bluestarContactPhone', e.target.value)}
                        placeholder="Enter contact phone number"
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="specialTerms" className="text-black">Special Terms</Label>
                      <Textarea
                        id="specialTerms"
                        value={formData.specialTerms}
                        onChange={(e) => handleInputChange('specialTerms', e.target.value)}
                        placeholder="Enter any special terms or conditions"
                        rows={3}
                      />
                    </div>
                  </div>
                </TabsContent>
              )}
            </Tabs>

            {/* Error Display */}
            {errors.submit && (
              <Alert className="mt-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-black">{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 mt-8">
              <Button type="button" variant="outline" asChild>
                <Link href={getBackUrl()}>
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Warranty
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
