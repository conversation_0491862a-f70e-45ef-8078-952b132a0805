import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { 
  getSalesLeadRepository, 
  getSalesOpportunityRepository, 
  getSalesProspectRepository, 
  getSalesOrderRepository 
} from '@/lib/repositories';
import { salesDashboardExportSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';
import { format } from 'date-fns';

/**
 * POST /api/sales/dashboard/export
 * Export sales dashboard data in CSV or Excel format
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedParams = salesDashboardExportSchema.parse(body);

      // Calculate date range based on period if not provided
      let dateRange = {
        startDate: validatedParams.startDate,
        endDate: validatedParams.endDate,
      };

      if (!dateRange.startDate || !dateRange.endDate) {
        const now = new Date();
        const periodDays = {
          '7d': 7,
          '30d': 30,
          '90d': 90,
          '6m': 180,
          '1y': 365,
        };

        const days = periodDays[validatedParams.period];
        dateRange = {
          startDate: new Date(now.getTime() - days * 24 * 60 * 60 * 1000).toISOString(),
          endDate: now.toISOString(),
        };
      }

      // Get repository instances
      const salesLeadRepository = getSalesLeadRepository();
      const salesOpportunityRepository = getSalesOpportunityRepository();
      const salesProspectRepository = getSalesProspectRepository();
      const salesOrderRepository = getSalesOrderRepository();

      // Prepare filters for data export
      const exportFilters = {
        customerId: validatedParams.customerId,
        executiveId: validatedParams.executiveId,
        startDate: dateRange.startDate ? new Date(dateRange.startDate) : undefined,
        endDate: dateRange.endDate ? new Date(dateRange.endDate) : undefined,
        skip: 0,
        take: 10000, // Large number to get all data
        sortBy: 'createdAt',
        sortOrder: 'desc' as const,
      };

      // Fetch all sales data in parallel
      const [
        leadsResult,
        opportunitiesResult,
        prospectsResult,
        ordersResult,
      ] = await Promise.all([
        salesLeadRepository.findWithFilters(exportFilters),
        salesOpportunityRepository.findWithFilters(exportFilters),
        salesProspectRepository.findWithFilters(exportFilters),
        salesOrderRepository.findWithFilters(exportFilters),
      ]);

      // Combine all data for export
      const exportData = [
        ...leadsResult.data.map((item: any) => ({
          type: 'Lead',
          id: item.id,
          customer: item.customer.name,
          executive: item.executive.name,
          date: format(new Date(item.leadDate), 'yyyy-MM-dd'),
          status: item.status,
          contactPerson: item.contactPerson || '',
          contactPhone: item.contactPhone || '',
          prospectPercentage: item.prospectPercentage || 0,
          amount: 0,
          followUpDate: item.followUpDate ? format(new Date(item.followUpDate), 'yyyy-MM-dd') : '',
          nextVisitDate: item.nextVisitDate ? format(new Date(item.nextVisitDate), 'yyyy-MM-dd') : '',
          remarks: item.remarks || '',
        })),
        ...opportunitiesResult.data.map((item: any) => ({
          type: 'Opportunity',
          id: item.id,
          customer: item.customer.name,
          executive: item.executive.name,
          date: format(new Date(item.opportunityDate), 'yyyy-MM-dd'),
          status: item.status,
          contactPerson: item.contactPerson || '',
          contactPhone: item.contactPhone || '',
          prospectPercentage: item.prospectPercentage || 0,
          amount: 0,
          followUpDate: item.followUpDate ? format(new Date(item.followUpDate), 'yyyy-MM-dd') : '',
          nextVisitDate: item.nextVisitDate ? format(new Date(item.nextVisitDate), 'yyyy-MM-dd') : '',
          remarks: item.remarks || '',
        })),
        ...prospectsResult.data.map((item: any) => ({
          type: 'Prospect',
          id: item.id,
          customer: item.customer.name,
          executive: item.executive.name,
          date: format(new Date(item.prospectDate), 'yyyy-MM-dd'),
          status: item.status,
          contactPerson: item.contactPerson || '',
          contactPhone: item.contactPhone || '',
          prospectPercentage: item.prospectPercentage || 0,
          amount: 0,
          followUpDate: item.followUpDate ? format(new Date(item.followUpDate), 'yyyy-MM-dd') : '',
          nextVisitDate: item.nextVisitDate ? format(new Date(item.nextVisitDate), 'yyyy-MM-dd') : '',
          remarks: item.remarks || '',
        })),
        ...ordersResult.data.map((item: any) => ({
          type: 'Order',
          id: item.id,
          customer: item.customer.name,
          executive: item.executive.name,
          date: format(new Date(item.orderDate), 'yyyy-MM-dd'),
          status: item.status,
          contactPerson: item.contactPerson || '',
          contactPhone: item.contactPhone || '',
          prospectPercentage: 0,
          amount: item.amount,
          followUpDate: '',
          nextVisitDate: '',
          remarks: item.remarks || '',
        })),
      ];

      // Sort by date (newest first)
      exportData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      // Generate export based on format
      switch (validatedParams.format) {
        case 'CSV':
          return generateCSVExport(exportData, validatedParams);
        case 'EXCEL':
          return generateExcelExport(exportData, validatedParams);
        default:
          return NextResponse.json(
            { error: 'Unsupported export format' },
            { status: 400 }
          );
      }
    } catch (error) {
      console.error('Error exporting sales dashboard data:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid export parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to export sales dashboard data',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * Generate CSV export
 */
function generateCSVExport(data: any[], params: any) {
  const headers = [
    'Type',
    'ID',
    'Customer',
    'Executive',
    'Date',
    'Status',
    'Contact Person',
    'Contact Phone',
    'Prospect %',
    'Amount',
    'Follow Up Date',
    'Next Visit Date',
    'Remarks',
  ];

  const csvRows = data.map(item => [
    item.type,
    item.id,
    `"${item.customer}"`,
    `"${item.executive}"`,
    item.date,
    item.status,
    `"${item.contactPerson}"`,
    `"${item.contactPhone}"`,
    item.prospectPercentage,
    item.amount,
    item.followUpDate,
    item.nextVisitDate,
    `"${item.remarks}"`,
  ]);

  const csvHeader = headers.join(',');
  const csvContent = csvRows.map(row => row.join(',')).join('\n');
  const csv = [csvHeader, csvContent].join('\n');

  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `sales_dashboard_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate Excel export (placeholder - returns CSV with Excel MIME type)
 */
function generateExcelExport(data: any[], params: any) {
  const csvResponse = generateCSVExport(data, params);
  
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `sales_dashboard_${now}.xlsx`;

  return new NextResponse(csvResponse.body, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}
