'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  BarChart, 
  Bar 
} from 'recharts';
import { TrendingUp, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SalesTrendsChartProps {
  data: Array<{
    month: string;
    leads: number;
    opportunities: number;
    prospects: number;
    orders: number;
    revenue: number;
  }>;
  isLoading?: boolean;
  className?: string;
  showTitle?: boolean;
  defaultChartType?: 'line' | 'bar';
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

function CustomTooltip({ active, payload, label }: CustomTooltipProps) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-black mb-2">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.name === 'Revenue' 
              ? `$${entry.value.toLocaleString()}` 
              : entry.value.toLocaleString()
            }
          </p>
        ))}
      </div>
    );
  }
  return null;
}

export function SalesTrendsChart({ 
  data, 
  isLoading = false, 
  className, 
  showTitle = true,
  defaultChartType = 'line'
}: SalesTrendsChartProps) {
  const [chartType, setChartType] = useState<'line' | 'bar'>(defaultChartType);

  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Sales Trends
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-8 w-24" />
            </div>
            <Skeleton className="h-80 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="bg-primary text-white">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Sales Trends
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Select value={chartType} onValueChange={(value: 'line' | 'bar') => setChartType(value)}>
                <SelectTrigger className="w-24 h-8 bg-white text-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Line
                    </div>
                  </SelectItem>
                  <SelectItem value="bar">
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Bar
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      )}
      
      <CardContent className="p-6">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'line' ? (
              <LineChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="leads" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  name="Leads"
                />
                <Line 
                  type="monotone" 
                  dataKey="opportunities" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  name="Opportunities"
                />
                <Line 
                  type="monotone" 
                  dataKey="prospects" 
                  stroke="#8B5CF6" 
                  strokeWidth={2}
                  name="Prospects"
                />
                <Line 
                  type="monotone" 
                  dataKey="orders" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                  name="Orders"
                />
              </LineChart>
            ) : (
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="leads" fill="#3B82F6" name="Leads" />
                <Bar dataKey="opportunities" fill="#10B981" name="Opportunities" />
                <Bar dataKey="prospects" fill="#8B5CF6" name="Prospects" />
                <Bar dataKey="orders" fill="#F59E0B" name="Orders" />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
