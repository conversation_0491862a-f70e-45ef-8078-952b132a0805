"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graceful-fs";
exports.ids = ["vendor-chunks/graceful-fs"];
exports.modules = {

/***/ "(rsc)/./node_modules/graceful-fs/clone.js":
/*!*******************************************!*\
  !*** ./node_modules/graceful-fs/clone.js ***!
  \*******************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = clone;\nvar getPrototypeOf = Object.getPrototypeOf || function (obj) {\n  return obj.__proto__;\n};\nfunction clone(obj) {\n  if (obj === null || typeof obj !== 'object') return obj;\n  if (obj instanceof Object) var copy = {\n    __proto__: getPrototypeOf(obj)\n  };else var copy = Object.create(null);\n  Object.getOwnPropertyNames(obj).forEach(function (key) {\n    Object.defineProperty(copy, key, Object.getOwnPropertyDescriptor(obj, key));\n  });\n  return copy;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graceful-fs/clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graceful-fs/graceful-fs.js":
/*!*************************************************!*\
  !*** ./node_modules/graceful-fs/graceful-fs.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar polyfills = __webpack_require__(/*! ./polyfills.js */ \"(rsc)/./node_modules/graceful-fs/polyfills.js\");\nvar legacy = __webpack_require__(/*! ./legacy-streams.js */ \"(rsc)/./node_modules/graceful-fs/legacy-streams.js\");\nvar clone = __webpack_require__(/*! ./clone.js */ \"(rsc)/./node_modules/graceful-fs/clone.js\");\nvar util = __webpack_require__(/*! util */ \"util\");\n\n/* istanbul ignore next - node 0.x polyfill */\nvar gracefulQueue;\nvar previousSymbol;\n\n/* istanbul ignore else - node 0.x polyfill */\nif (typeof Symbol === 'function' && typeof Symbol.for === 'function') {\n  gracefulQueue = Symbol.for('graceful-fs.queue');\n  // This is used in testing by future versions\n  previousSymbol = Symbol.for('graceful-fs.previous');\n} else {\n  gracefulQueue = '___graceful-fs.queue';\n  previousSymbol = '___graceful-fs.previous';\n}\nfunction noop() {}\nfunction publishQueue(context, queue) {\n  Object.defineProperty(context, gracefulQueue, {\n    get: function () {\n      return queue;\n    }\n  });\n}\nvar debug = noop;\nif (util.debuglog) debug = util.debuglog('gfs4');else if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || '')) debug = function () {\n  var m = util.format.apply(util, arguments);\n  m = 'GFS4: ' + m.split(/\\n/).join('\\nGFS4: ');\n  console.error(m);\n};\n\n// Once time initialization\nif (!fs[gracefulQueue]) {\n  // This queue can be shared by multiple loaded instances\n  var queue = global[gracefulQueue] || [];\n  publishQueue(fs, queue);\n\n  // Patch fs.close/closeSync to shared queue version, because we need\n  // to retry() whenever a close happens *anywhere* in the program.\n  // This is essential when multiple graceful-fs instances are\n  // in play at the same time.\n  fs.close = function (fs$close) {\n    function close(fd, cb) {\n      return fs$close.call(fs, fd, function (err) {\n        // This function uses the graceful-fs shared queue\n        if (!err) {\n          resetQueue();\n        }\n        if (typeof cb === 'function') cb.apply(this, arguments);\n      });\n    }\n    Object.defineProperty(close, previousSymbol, {\n      value: fs$close\n    });\n    return close;\n  }(fs.close);\n  fs.closeSync = function (fs$closeSync) {\n    function closeSync(fd) {\n      // This function uses the graceful-fs shared queue\n      fs$closeSync.apply(fs, arguments);\n      resetQueue();\n    }\n    Object.defineProperty(closeSync, previousSymbol, {\n      value: fs$closeSync\n    });\n    return closeSync;\n  }(fs.closeSync);\n  if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || '')) {\n    process.on('exit', function () {\n      debug(fs[gracefulQueue]);\n      (__webpack_require__(/*! assert */ \"assert\").equal)(fs[gracefulQueue].length, 0);\n    });\n  }\n}\nif (!global[gracefulQueue]) {\n  publishQueue(global, fs[gracefulQueue]);\n}\nmodule.exports = patch(clone(fs));\nif (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !fs.__patched) {\n  module.exports = patch(fs);\n  fs.__patched = true;\n}\nfunction patch(fs) {\n  // Everything that references the open() function needs to be in here\n  polyfills(fs);\n  fs.gracefulify = patch;\n  fs.createReadStream = createReadStream;\n  fs.createWriteStream = createWriteStream;\n  var fs$readFile = fs.readFile;\n  fs.readFile = readFile;\n  function readFile(path, options, cb) {\n    if (typeof options === 'function') cb = options, options = null;\n    return go$readFile(path, options, cb);\n    function go$readFile(path, options, cb, startTime) {\n      return fs$readFile(path, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$readFile, [path, options, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (typeof cb === 'function') cb.apply(this, arguments);\n        }\n      });\n    }\n  }\n  var fs$writeFile = fs.writeFile;\n  fs.writeFile = writeFile;\n  function writeFile(path, data, options, cb) {\n    if (typeof options === 'function') cb = options, options = null;\n    return go$writeFile(path, data, options, cb);\n    function go$writeFile(path, data, options, cb, startTime) {\n      return fs$writeFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$writeFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (typeof cb === 'function') cb.apply(this, arguments);\n        }\n      });\n    }\n  }\n  var fs$appendFile = fs.appendFile;\n  if (fs$appendFile) fs.appendFile = appendFile;\n  function appendFile(path, data, options, cb) {\n    if (typeof options === 'function') cb = options, options = null;\n    return go$appendFile(path, data, options, cb);\n    function go$appendFile(path, data, options, cb, startTime) {\n      return fs$appendFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$appendFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (typeof cb === 'function') cb.apply(this, arguments);\n        }\n      });\n    }\n  }\n  var fs$copyFile = fs.copyFile;\n  if (fs$copyFile) fs.copyFile = copyFile;\n  function copyFile(src, dest, flags, cb) {\n    if (typeof flags === 'function') {\n      cb = flags;\n      flags = 0;\n    }\n    return go$copyFile(src, dest, flags, cb);\n    function go$copyFile(src, dest, flags, cb, startTime) {\n      return fs$copyFile(src, dest, flags, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$copyFile, [src, dest, flags, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (typeof cb === 'function') cb.apply(this, arguments);\n        }\n      });\n    }\n  }\n  var fs$readdir = fs.readdir;\n  fs.readdir = readdir;\n  var noReaddirOptionVersions = /^v[0-5]\\./;\n  function readdir(path, options, cb) {\n    if (typeof options === 'function') cb = options, options = null;\n    var go$readdir = noReaddirOptionVersions.test(process.version) ? function go$readdir(path, options, cb, startTime) {\n      return fs$readdir(path, fs$readdirCallback(path, options, cb, startTime));\n    } : function go$readdir(path, options, cb, startTime) {\n      return fs$readdir(path, options, fs$readdirCallback(path, options, cb, startTime));\n    };\n    return go$readdir(path, options, cb);\n    function fs$readdirCallback(path, options, cb, startTime) {\n      return function (err, files) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$readdir, [path, options, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (files && files.sort) files.sort();\n          if (typeof cb === 'function') cb.call(this, err, files);\n        }\n      };\n    }\n  }\n  if (process.version.substr(0, 4) === 'v0.8') {\n    var legStreams = legacy(fs);\n    ReadStream = legStreams.ReadStream;\n    WriteStream = legStreams.WriteStream;\n  }\n  var fs$ReadStream = fs.ReadStream;\n  if (fs$ReadStream) {\n    ReadStream.prototype = Object.create(fs$ReadStream.prototype);\n    ReadStream.prototype.open = ReadStream$open;\n  }\n  var fs$WriteStream = fs.WriteStream;\n  if (fs$WriteStream) {\n    WriteStream.prototype = Object.create(fs$WriteStream.prototype);\n    WriteStream.prototype.open = WriteStream$open;\n  }\n  Object.defineProperty(fs, 'ReadStream', {\n    get: function () {\n      return ReadStream;\n    },\n    set: function (val) {\n      ReadStream = val;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(fs, 'WriteStream', {\n    get: function () {\n      return WriteStream;\n    },\n    set: function (val) {\n      WriteStream = val;\n    },\n    enumerable: true,\n    configurable: true\n  });\n\n  // legacy names\n  var FileReadStream = ReadStream;\n  Object.defineProperty(fs, 'FileReadStream', {\n    get: function () {\n      return FileReadStream;\n    },\n    set: function (val) {\n      FileReadStream = val;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  var FileWriteStream = WriteStream;\n  Object.defineProperty(fs, 'FileWriteStream', {\n    get: function () {\n      return FileWriteStream;\n    },\n    set: function (val) {\n      FileWriteStream = val;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  function ReadStream(path, options) {\n    if (this instanceof ReadStream) return fs$ReadStream.apply(this, arguments), this;else return ReadStream.apply(Object.create(ReadStream.prototype), arguments);\n  }\n  function ReadStream$open() {\n    var that = this;\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        if (that.autoClose) that.destroy();\n        that.emit('error', err);\n      } else {\n        that.fd = fd;\n        that.emit('open', fd);\n        that.read();\n      }\n    });\n  }\n  function WriteStream(path, options) {\n    if (this instanceof WriteStream) return fs$WriteStream.apply(this, arguments), this;else return WriteStream.apply(Object.create(WriteStream.prototype), arguments);\n  }\n  function WriteStream$open() {\n    var that = this;\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        that.destroy();\n        that.emit('error', err);\n      } else {\n        that.fd = fd;\n        that.emit('open', fd);\n      }\n    });\n  }\n  function createReadStream(path, options) {\n    return new fs.ReadStream(path, options);\n  }\n  function createWriteStream(path, options) {\n    return new fs.WriteStream(path, options);\n  }\n  var fs$open = fs.open;\n  fs.open = open;\n  function open(path, flags, mode, cb) {\n    if (typeof mode === 'function') cb = mode, mode = null;\n    return go$open(path, flags, mode, cb);\n    function go$open(path, flags, mode, cb, startTime) {\n      return fs$open(path, flags, mode, function (err, fd) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE')) enqueue([go$open, [path, flags, mode, cb], err, startTime || Date.now(), Date.now()]);else {\n          if (typeof cb === 'function') cb.apply(this, arguments);\n        }\n      });\n    }\n  }\n  return fs;\n}\nfunction enqueue(elem) {\n  debug('ENQUEUE', elem[0].name, elem[1]);\n  fs[gracefulQueue].push(elem);\n  retry();\n}\n\n// keep track of the timeout between retry() calls\nvar retryTimer;\n\n// reset the startTime and lastTime to now\n// this resets the start of the 60 second overall timeout as well as the\n// delay between attempts so that we'll retry these jobs sooner\nfunction resetQueue() {\n  var now = Date.now();\n  for (var i = 0; i < fs[gracefulQueue].length; ++i) {\n    // entries that are only a length of 2 are from an older version, don't\n    // bother modifying those since they'll be retried anyway.\n    if (fs[gracefulQueue][i].length > 2) {\n      fs[gracefulQueue][i][3] = now; // startTime\n      fs[gracefulQueue][i][4] = now; // lastTime\n    }\n  }\n  // call retry to make sure we're actively processing the queue\n  retry();\n}\nfunction retry() {\n  // clear the timer and remove it to help prevent unintended concurrency\n  clearTimeout(retryTimer);\n  retryTimer = undefined;\n  if (fs[gracefulQueue].length === 0) return;\n  var elem = fs[gracefulQueue].shift();\n  var fn = elem[0];\n  var args = elem[1];\n  // these items may be unset if they were added by an older graceful-fs\n  var err = elem[2];\n  var startTime = elem[3];\n  var lastTime = elem[4];\n\n  // if we don't have a startTime we have no way of knowing if we've waited\n  // long enough, so go ahead and retry this item now\n  if (startTime === undefined) {\n    debug('RETRY', fn.name, args);\n    fn.apply(null, args);\n  } else if (Date.now() - startTime >= 60000) {\n    // it's been more than 60 seconds total, bail now\n    debug('TIMEOUT', fn.name, args);\n    var cb = args.pop();\n    if (typeof cb === 'function') cb.call(null, err);\n  } else {\n    // the amount of time between the last attempt and right now\n    var sinceAttempt = Date.now() - lastTime;\n    // the amount of time between when we first tried, and when we last tried\n    // rounded up to at least 1\n    var sinceStart = Math.max(lastTime - startTime, 1);\n    // backoff. wait longer than the total time we've been retrying, but only\n    // up to a maximum of 100ms\n    var desiredDelay = Math.min(sinceStart * 1.2, 100);\n    // it's been long enough since the last retry, do it again\n    if (sinceAttempt >= desiredDelay) {\n      debug('RETRY', fn.name, args);\n      fn.apply(null, args.concat([startTime]));\n    } else {\n      // if we can't do this job yet, push it to the end of the queue\n      // and let the next iteration check again\n      fs[gracefulQueue].push(elem);\n    }\n  }\n\n  // schedule our next run if one isn't already scheduled\n  if (retryTimer === undefined) {\n    retryTimer = setTimeout(retry, 0);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graceful-fs/graceful-fs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graceful-fs/legacy-streams.js":
/*!****************************************************!*\
  !*** ./node_modules/graceful-fs/legacy-streams.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nmodule.exports = legacy;\nfunction legacy(fs) {\n  return {\n    ReadStream: ReadStream,\n    WriteStream: WriteStream\n  };\n  function ReadStream(path, options) {\n    if (!(this instanceof ReadStream)) return new ReadStream(path, options);\n    Stream.call(this);\n    var self = this;\n    this.path = path;\n    this.fd = null;\n    this.readable = true;\n    this.paused = false;\n    this.flags = 'r';\n    this.mode = 438; /*=0666*/\n    this.bufferSize = 64 * 1024;\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n    if (this.encoding) this.setEncoding(this.encoding);\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.end === undefined) {\n        this.end = Infinity;\n      } else if ('number' !== typeof this.end) {\n        throw TypeError('end must be a Number');\n      }\n      if (this.start > this.end) {\n        throw new Error('start must be <= end');\n      }\n      this.pos = this.start;\n    }\n    if (this.fd !== null) {\n      process.nextTick(function () {\n        self._read();\n      });\n      return;\n    }\n    fs.open(this.path, this.flags, this.mode, function (err, fd) {\n      if (err) {\n        self.emit('error', err);\n        self.readable = false;\n        return;\n      }\n      self.fd = fd;\n      self.emit('open', fd);\n      self._read();\n    });\n  }\n  function WriteStream(path, options) {\n    if (!(this instanceof WriteStream)) return new WriteStream(path, options);\n    Stream.call(this);\n    this.path = path;\n    this.fd = null;\n    this.writable = true;\n    this.flags = 'w';\n    this.encoding = 'binary';\n    this.mode = 438; /*=0666*/\n    this.bytesWritten = 0;\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.start < 0) {\n        throw new Error('start must be >= zero');\n      }\n      this.pos = this.start;\n    }\n    this.busy = false;\n    this._queue = [];\n    if (this.fd === null) {\n      this._open = fs.open;\n      this._queue.push([this._open, this.path, this.flags, this.mode, undefined]);\n      this.flush();\n    }\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graceful-fs/legacy-streams.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graceful-fs/polyfills.js":
/*!***********************************************!*\
  !*** ./node_modules/graceful-fs/polyfills.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar constants = __webpack_require__(/*! constants */ \"constants\");\nvar origCwd = process.cwd;\nvar cwd = null;\nvar platform = process.env.GRACEFUL_FS_PLATFORM || process.platform;\nprocess.cwd = function () {\n  if (!cwd) cwd = origCwd.call(process);\n  return cwd;\n};\ntry {\n  process.cwd();\n} catch (er) {}\n\n// This check is needed until node.js 12 is required\nif (typeof process.chdir === 'function') {\n  var chdir = process.chdir;\n  process.chdir = function (d) {\n    cwd = null;\n    chdir.call(process, d);\n  };\n  if (Object.setPrototypeOf) Object.setPrototypeOf(process.chdir, chdir);\n}\nmodule.exports = patch;\nfunction patch(fs) {\n  // (re-)implement some things that are known busted or missing.\n\n  // lchmod, broken prior to 0.6.2\n  // back-port the fix here.\n  if (constants.hasOwnProperty('O_SYMLINK') && process.version.match(/^v0\\.6\\.[0-2]|^v0\\.5\\./)) {\n    patchLchmod(fs);\n  }\n\n  // lutimes implementation, or no-op\n  if (!fs.lutimes) {\n    patchLutimes(fs);\n  }\n\n  // https://github.com/isaacs/node-graceful-fs/issues/4\n  // Chown should not fail on einval or eperm if non-root.\n  // It should not fail on enosys ever, as this just indicates\n  // that a fs doesn't support the intended operation.\n\n  fs.chown = chownFix(fs.chown);\n  fs.fchown = chownFix(fs.fchown);\n  fs.lchown = chownFix(fs.lchown);\n  fs.chmod = chmodFix(fs.chmod);\n  fs.fchmod = chmodFix(fs.fchmod);\n  fs.lchmod = chmodFix(fs.lchmod);\n  fs.chownSync = chownFixSync(fs.chownSync);\n  fs.fchownSync = chownFixSync(fs.fchownSync);\n  fs.lchownSync = chownFixSync(fs.lchownSync);\n  fs.chmodSync = chmodFixSync(fs.chmodSync);\n  fs.fchmodSync = chmodFixSync(fs.fchmodSync);\n  fs.lchmodSync = chmodFixSync(fs.lchmodSync);\n  fs.stat = statFix(fs.stat);\n  fs.fstat = statFix(fs.fstat);\n  fs.lstat = statFix(fs.lstat);\n  fs.statSync = statFixSync(fs.statSync);\n  fs.fstatSync = statFixSync(fs.fstatSync);\n  fs.lstatSync = statFixSync(fs.lstatSync);\n\n  // if lchmod/lchown do not exist, then make them no-ops\n  if (fs.chmod && !fs.lchmod) {\n    fs.lchmod = function (path, mode, cb) {\n      if (cb) process.nextTick(cb);\n    };\n    fs.lchmodSync = function () {};\n  }\n  if (fs.chown && !fs.lchown) {\n    fs.lchown = function (path, uid, gid, cb) {\n      if (cb) process.nextTick(cb);\n    };\n    fs.lchownSync = function () {};\n  }\n\n  // on Windows, A/V software can lock the directory, causing this\n  // to fail with an EACCES or EPERM if the directory contains newly\n  // created files.  Try again on failure, for up to 60 seconds.\n\n  // Set the timeout this long because some Windows Anti-Virus, such as Parity\n  // bit9, may lock files for up to a minute, causing npm package install\n  // failures. Also, take care to yield the scheduler. Windows scheduling gives\n  // CPU to a busy looping process, which can cause the program causing the lock\n  // contention to be starved of CPU by node, so the contention doesn't resolve.\n  if (platform === \"win32\") {\n    fs.rename = typeof fs.rename !== 'function' ? fs.rename : function (fs$rename) {\n      function rename(from, to, cb) {\n        var start = Date.now();\n        var backoff = 0;\n        fs$rename(from, to, function CB(er) {\n          if (er && (er.code === \"EACCES\" || er.code === \"EPERM\" || er.code === \"EBUSY\") && Date.now() - start < 60000) {\n            setTimeout(function () {\n              fs.stat(to, function (stater, st) {\n                if (stater && stater.code === \"ENOENT\") fs$rename(from, to, CB);else cb(er);\n              });\n            }, backoff);\n            if (backoff < 100) backoff += 10;\n            return;\n          }\n          if (cb) cb(er);\n        });\n      }\n      if (Object.setPrototypeOf) Object.setPrototypeOf(rename, fs$rename);\n      return rename;\n    }(fs.rename);\n  }\n\n  // if read() returns EAGAIN, then just try it again.\n  fs.read = typeof fs.read !== 'function' ? fs.read : function (fs$read) {\n    function read(fd, buffer, offset, length, position, callback_) {\n      var callback;\n      if (callback_ && typeof callback_ === 'function') {\n        var eagCounter = 0;\n        callback = function (er, _, __) {\n          if (er && er.code === 'EAGAIN' && eagCounter < 10) {\n            eagCounter++;\n            return fs$read.call(fs, fd, buffer, offset, length, position, callback);\n          }\n          callback_.apply(this, arguments);\n        };\n      }\n      return fs$read.call(fs, fd, buffer, offset, length, position, callback);\n    }\n\n    // This ensures `util.promisify` works as it does for native `fs.read`.\n    if (Object.setPrototypeOf) Object.setPrototypeOf(read, fs$read);\n    return read;\n  }(fs.read);\n  fs.readSync = typeof fs.readSync !== 'function' ? fs.readSync : function (fs$readSync) {\n    return function (fd, buffer, offset, length, position) {\n      var eagCounter = 0;\n      while (true) {\n        try {\n          return fs$readSync.call(fs, fd, buffer, offset, length, position);\n        } catch (er) {\n          if (er.code === 'EAGAIN' && eagCounter < 10) {\n            eagCounter++;\n            continue;\n          }\n          throw er;\n        }\n      }\n    };\n  }(fs.readSync);\n  function patchLchmod(fs) {\n    fs.lchmod = function (path, mode, callback) {\n      fs.open(path, constants.O_WRONLY | constants.O_SYMLINK, mode, function (err, fd) {\n        if (err) {\n          if (callback) callback(err);\n          return;\n        }\n        // prefer to return the chmod error, if one occurs,\n        // but still try to close, and report closing errors if they occur.\n        fs.fchmod(fd, mode, function (err) {\n          fs.close(fd, function (err2) {\n            if (callback) callback(err || err2);\n          });\n        });\n      });\n    };\n    fs.lchmodSync = function (path, mode) {\n      var fd = fs.openSync(path, constants.O_WRONLY | constants.O_SYMLINK, mode);\n\n      // prefer to return the chmod error, if one occurs,\n      // but still try to close, and report closing errors if they occur.\n      var threw = true;\n      var ret;\n      try {\n        ret = fs.fchmodSync(fd, mode);\n        threw = false;\n      } finally {\n        if (threw) {\n          try {\n            fs.closeSync(fd);\n          } catch (er) {}\n        } else {\n          fs.closeSync(fd);\n        }\n      }\n      return ret;\n    };\n  }\n  function patchLutimes(fs) {\n    if (constants.hasOwnProperty(\"O_SYMLINK\") && fs.futimes) {\n      fs.lutimes = function (path, at, mt, cb) {\n        fs.open(path, constants.O_SYMLINK, function (er, fd) {\n          if (er) {\n            if (cb) cb(er);\n            return;\n          }\n          fs.futimes(fd, at, mt, function (er) {\n            fs.close(fd, function (er2) {\n              if (cb) cb(er || er2);\n            });\n          });\n        });\n      };\n      fs.lutimesSync = function (path, at, mt) {\n        var fd = fs.openSync(path, constants.O_SYMLINK);\n        var ret;\n        var threw = true;\n        try {\n          ret = fs.futimesSync(fd, at, mt);\n          threw = false;\n        } finally {\n          if (threw) {\n            try {\n              fs.closeSync(fd);\n            } catch (er) {}\n          } else {\n            fs.closeSync(fd);\n          }\n        }\n        return ret;\n      };\n    } else if (fs.futimes) {\n      fs.lutimes = function (_a, _b, _c, cb) {\n        if (cb) process.nextTick(cb);\n      };\n      fs.lutimesSync = function () {};\n    }\n  }\n  function chmodFix(orig) {\n    if (!orig) return orig;\n    return function (target, mode, cb) {\n      return orig.call(fs, target, mode, function (er) {\n        if (chownErOk(er)) er = null;\n        if (cb) cb.apply(this, arguments);\n      });\n    };\n  }\n  function chmodFixSync(orig) {\n    if (!orig) return orig;\n    return function (target, mode) {\n      try {\n        return orig.call(fs, target, mode);\n      } catch (er) {\n        if (!chownErOk(er)) throw er;\n      }\n    };\n  }\n  function chownFix(orig) {\n    if (!orig) return orig;\n    return function (target, uid, gid, cb) {\n      return orig.call(fs, target, uid, gid, function (er) {\n        if (chownErOk(er)) er = null;\n        if (cb) cb.apply(this, arguments);\n      });\n    };\n  }\n  function chownFixSync(orig) {\n    if (!orig) return orig;\n    return function (target, uid, gid) {\n      try {\n        return orig.call(fs, target, uid, gid);\n      } catch (er) {\n        if (!chownErOk(er)) throw er;\n      }\n    };\n  }\n  function statFix(orig) {\n    if (!orig) return orig;\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options, cb) {\n      if (typeof options === 'function') {\n        cb = options;\n        options = null;\n      }\n      function callback(er, stats) {\n        if (stats) {\n          if (stats.uid < 0) stats.uid += 0x100000000;\n          if (stats.gid < 0) stats.gid += 0x100000000;\n        }\n        if (cb) cb.apply(this, arguments);\n      }\n      return options ? orig.call(fs, target, options, callback) : orig.call(fs, target, callback);\n    };\n  }\n  function statFixSync(orig) {\n    if (!orig) return orig;\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options) {\n      var stats = options ? orig.call(fs, target, options) : orig.call(fs, target);\n      if (stats) {\n        if (stats.uid < 0) stats.uid += 0x100000000;\n        if (stats.gid < 0) stats.gid += 0x100000000;\n      }\n      return stats;\n    };\n  }\n\n  // ENOSYS means that the fs doesn't support the op. Just ignore\n  // that, because it doesn't matter.\n  //\n  // if there's no getuid, or if getuid() is something other\n  // than 0, and the error is EINVAL or EPERM, then just ignore\n  // it.\n  //\n  // This specific case is a silent failure in cp, install, tar,\n  // and most other unix tools that manage permissions.\n  //\n  // When running as root, or if other types of errors are\n  // encountered, then it's strict.\n  function chownErOk(er) {\n    if (!er) return true;\n    if (er.code === \"ENOSYS\") return true;\n    var nonroot = !process.getuid || process.getuid() !== 0;\n    if (nonroot) {\n      if (er.code === \"EINVAL\" || er.code === \"EPERM\") return true;\n    }\n    return false;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graceful-fs/polyfills.js\n");

/***/ })

};
;