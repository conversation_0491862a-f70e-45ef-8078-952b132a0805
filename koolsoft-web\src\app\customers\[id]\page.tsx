'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Skeleton, SpanSkeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Home,
  Edit,
  Trash,
  RefreshCw
} from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

// Import customer detail components
import { CustomerOverview } from '@/components/customers/customer-overview';
import { CustomerContacts } from '@/components/customers/customer-contacts';
import { CustomerVisitCards } from '@/components/customers/customer-visit-cards';
import { CustomerAMCContracts } from '@/components/customers/customer-amc-contracts';
import { CustomerWarranties } from '@/components/customers/customer-warranties';
import { CustomerHistoryCards } from '@/components/customers/customer-history-cards';

/**
 * Customer Detail Page
 *
 * This page displays detailed information about a customer, including their
 * basic information, contacts, visit cards, AMC contracts, warranties, and history cards.
 * The page is organized into tabs for easy navigation between different types of customer data.
 */
export default function CustomerDetailPage() {
  const params = useParams();
  const router = useRouter();
  const customerId = params?.id as string;

  const [customerData, setCustomerData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Update document title, page title, and breadcrumb when customer data is loaded
  useEffect(() => {
    if (customerData?.customer?.name) {
      // Update browser tab title
      document.title = `${customerData.customer.name} - KoolSoft`;

      // Update the page header title
      const pageHeaderTitle = document.getElementById('page-title');
      if (pageHeaderTitle) {
        pageHeaderTitle.textContent = customerData.customer.name;
      }

      // Update the last breadcrumb item
      const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
      if (breadcrumbItems.length > 0) {
        const lastBreadcrumbItem = breadcrumbItems[breadcrumbItems.length - 1];
        const spanElement = lastBreadcrumbItem.querySelector('span');
        if (spanElement) {
          spanElement.textContent = customerData.customer.name;
        }
      }
    }
  }, [customerData]);

  // Fetch customer data
  useEffect(() => {
    const fetchCustomerDetails = async () => {
      try {
        setIsLoading(true);
        // Try the details endpoint first
        const response = await fetch(`/api/customers/${customerId}/details`, {
          credentials: 'include', // Include credentials for authentication
        });

        if (!response.ok) {
          throw new Error('Failed to fetch customer details');
        }

        const data = await response.json();

        // The data is already in the expected format
        setCustomerData(data);
      } catch (error: any) {
        console.error('Error fetching customer details:', error);
        setError(error.message || 'Failed to load customer details');
        toast({
          title: 'Error',
          description: 'Failed to load customer details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCustomerDetails();
  }, [customerId]);

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);
    fetch(`/api/customers/${customerId}/details`, {
      credentials: 'include', // Include credentials for authentication
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Failed to refresh customer details');
        }
        return response.json();
      })
      .then(data => {
        // The data is already in the expected format
        setCustomerData(data);
        toast({
          title: 'Success',
          description: 'Customer details refreshed successfully',
        });
      })
      .catch(error => {
        console.error('Error refreshing customer details:', error);
        setError(error.message || 'Failed to refresh customer details');
        toast({
          title: 'Error',
          description: 'Failed to refresh customer details. Please try again.',
          variant: 'destructive',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const response = await fetch(`/api/customers/${customerId}`, {
        method: 'DELETE',
        credentials: 'include', // Include credentials for authentication
      });

      if (!response.ok) {
        throw new Error('Failed to delete customer');
      }

      toast({
        title: 'Success',
        description: 'Customer deleted successfully',
      });

      // Redirect to customers list
      router.push('/customers');
    } catch (error: any) {
      console.error('Error deleting customer:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete customer. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>
              <Skeleton className="h-9 w-64" />
            </CardTitle>
            <CardDescription>
              <SpanSkeleton className="h-5 w-48" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <Skeleton className="h-12 w-full" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error || !customerData) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between">
            <div>
              <CardTitle>Customer Details</CardTitle>
              <CardDescription>
                Error loading customer information
              </CardDescription>
            </div>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error || 'Customer not found'}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { customer, contacts, visitCards, amcContracts, warranties, historyCards } = customerData;

  const customerActions = (
    <div className="flex space-x-2">
      <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
        Refresh
      </Button>
      <Button asChild variant="default">
        <Link href={`/customers/${customerId}/edit`}>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </Link>
      </Button>
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button variant="destructive">
            <Trash className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the customer
              and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              {customer.name}
              {customer.isActive ? (
                <Badge className="ml-3 bg-green-500 text-white">Active</Badge>
              ) : (
                <Badge className="ml-3 bg-gray-500 text-white">Inactive</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Customer details and information
            </CardDescription>
          </div>
          {customerActions}
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="contacts">Contacts</TabsTrigger>
              <TabsTrigger value="visitCards">Visit Cards</TabsTrigger>
              <TabsTrigger value="amcContracts">AMC Contracts</TabsTrigger>
              <TabsTrigger value="warranties">Warranties</TabsTrigger>
              <TabsTrigger value="historyCards">History Cards</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <CustomerOverview customer={customer} />
            </TabsContent>

            {/* Contacts Tab */}
            <TabsContent value="contacts" className="space-y-6">
              <CustomerContacts contacts={contacts} />
            </TabsContent>

            {/* Visit Cards Tab */}
            <TabsContent value="visitCards" className="space-y-6">
              <CustomerVisitCards visitCards={visitCards} customerId={customerId} />
            </TabsContent>

            {/* AMC Contracts Tab */}
            <TabsContent value="amcContracts" className="space-y-6">
              <CustomerAMCContracts amcContracts={amcContracts} />
            </TabsContent>

            {/* Warranties Tab */}
            <TabsContent value="warranties" className="space-y-6">
              <CustomerWarranties warranties={warranties} />
            </TabsContent>

            {/* History Cards Tab */}
            <TabsContent value="historyCards" className="space-y-6">
              <CustomerHistoryCards historyCards={customerData.historyCards || []} customerId={customerId} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
