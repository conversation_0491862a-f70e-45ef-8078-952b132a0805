/**
 * Report Export Utilities
 * 
 * This module provides comprehensive export functionality for KoolSoft reports
 * including PDF and Excel generation with professional formatting and branding.
 */

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as ExcelJS from 'exceljs';
import { formatReportDate, formatReportCurrency, formatReportNumber } from './report-utils';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

/**
 * KoolSoft branding configuration
 */
const KOOLSOFT_BRANDING = {
  companyName: 'KoolSoft Technologies',
  tagline: 'Air Conditioning & Refrigeration Solutions',
  email: '<EMAIL>',
  phone: '+91-XXXXXXXXXX',
  primaryColor: '#0F52BA',
  secondaryColor: '#666666',
  backgroundColor: '#f8f9fa',
};

/**
 * Report export parameters interface
 */
export interface ReportExportParams {
  reportType: string;
  reportName: string;
  data: any[];
  filters: Record<string, any>;
  format: 'PDF' | 'EXCEL';
  filename?: string;
  includeFilters?: boolean;
  includeTimestamp?: boolean;
}

/**
 * Table configuration interface
 */
export interface TableConfig {
  headers: string[];
  columns: Array<{
    key: string;
    type: 'text' | 'date' | 'currency' | 'number' | 'status';
    width?: number;
  }>;
}

/**
 * Generate professional PDF report with KoolSoft branding
 */
export async function generateReportPDF(params: ReportExportParams): Promise<Blob> {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  
  // Company header
  doc.setFontSize(20);
  doc.setTextColor(15, 82, 186); // Primary blue color
  doc.text(KOOLSOFT_BRANDING.companyName, 20, 25);
  
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text(KOOLSOFT_BRANDING.tagline, 20, 32);
  doc.text(`${KOOLSOFT_BRANDING.email} | ${KOOLSOFT_BRANDING.phone}`, 20, 38);
  
  // Report title
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text(params.reportName.toUpperCase(), 20, 55);
  
  // Report metadata
  let yPos = 70;
  doc.setFontSize(10);
  
  if (params.includeTimestamp !== false) {
    doc.text(`Generated: ${new Date().toLocaleString()}`, 20, yPos);
    yPos += 6;
  }
  
  doc.text(`Report Type: ${params.reportType}`, 20, yPos);
  yPos += 6;
  
  doc.text(`Total Records: ${params.data.length}`, 20, yPos);
  yPos += 10;
  
  // Applied filters section
  if (params.includeFilters && Object.keys(params.filters).length > 0) {
    doc.setFontSize(12);
    doc.text('Applied Filters:', 20, yPos);
    yPos += 8;
    
    doc.setFontSize(10);
    Object.entries(params.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const filterText = `${key}: ${String(value)}`;
        doc.text(filterText, 25, yPos);
        yPos += 5;
      }
    });
    yPos += 5;
  }
  
  // Generate table based on report type
  const tableConfig = getTableConfigForReportType(params.reportType);
  const tableData = prepareTableDataForPDF(params.data, tableConfig);
  
  doc.autoTable({
    head: [tableConfig.headers],
    body: tableData,
    startY: yPos,
    styles: {
      fontSize: 8,
      cellPadding: 3,
    },
    headStyles: {
      fillColor: [15, 82, 186], // Primary blue
      textColor: [255, 255, 255],
      fontStyle: 'bold',
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250], // Light gray
    },
    margin: { left: 20, right: 20 },
    tableWidth: 'auto',
    columnStyles: getColumnStylesForReportType(params.reportType),
  });
  
  // Footer
  const finalY = (doc as any).lastAutoTable.finalY || yPos + 50;
  if (finalY < pageHeight - 40) {
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Thank you for using KoolSoft Technologies!', 20, pageHeight - 25);
    doc.text(`Report generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, pageHeight - 20);
  }
  
  return doc.output('blob');
}

/**
 * Generate Excel report with professional formatting
 */
export async function generateReportExcel(params: ReportExportParams): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  
  // Set workbook properties
  workbook.creator = KOOLSOFT_BRANDING.companyName;
  workbook.lastModifiedBy = KOOLSOFT_BRANDING.companyName;
  workbook.created = new Date();
  workbook.modified = new Date();
  
  // Create main worksheet
  const worksheet = workbook.addWorksheet(params.reportType);
  
  // Company header
  worksheet.mergeCells('A1:F1');
  const titleCell = worksheet.getCell('A1');
  titleCell.value = KOOLSOFT_BRANDING.companyName;
  titleCell.font = { size: 16, bold: true, color: { argb: '0F52BA' } };
  titleCell.alignment = { horizontal: 'center' };
  
  worksheet.mergeCells('A2:F2');
  const taglineCell = worksheet.getCell('A2');
  taglineCell.value = KOOLSOFT_BRANDING.tagline;
  taglineCell.font = { size: 10, color: { argb: '666666' } };
  taglineCell.alignment = { horizontal: 'center' };
  
  // Report title and metadata
  let currentRow = 4;
  worksheet.mergeCells(`A${currentRow}:F${currentRow}`);
  const reportTitleCell = worksheet.getCell(`A${currentRow}`);
  reportTitleCell.value = params.reportName.toUpperCase();
  reportTitleCell.font = { size: 14, bold: true };
  reportTitleCell.alignment = { horizontal: 'center' };
  
  currentRow += 2;
  
  if (params.includeTimestamp !== false) {
    worksheet.getCell(`A${currentRow}`).value = 'Generated:';
    worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString();
    currentRow++;
  }
  
  worksheet.getCell(`A${currentRow}`).value = 'Report Type:';
  worksheet.getCell(`B${currentRow}`).value = params.reportType;
  currentRow++;
  
  worksheet.getCell(`A${currentRow}`).value = 'Total Records:';
  worksheet.getCell(`B${currentRow}`).value = params.data.length;
  currentRow += 2;
  
  // Applied filters
  if (params.includeFilters && Object.keys(params.filters).length > 0) {
    worksheet.getCell(`A${currentRow}`).value = 'Applied Filters:';
    worksheet.getCell(`A${currentRow}`).font = { bold: true };
    currentRow++;
    
    Object.entries(params.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        worksheet.getCell(`A${currentRow}`).value = `${key}:`;
        worksheet.getCell(`B${currentRow}`).value = String(value);
        currentRow++;
      }
    });
    currentRow++;
  }
  
  // Data table
  const tableConfig = getTableConfigForReportType(params.reportType);
  const headerRow = worksheet.getRow(currentRow);
  
  // Set headers
  tableConfig.headers.forEach((header, index) => {
    const cell = headerRow.getCell(index + 1);
    cell.value = header;
    cell.font = { bold: true, color: { argb: 'FFFFFF' } };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '0F52BA' }
    };
    cell.alignment = { horizontal: 'center' };
  });
  
  currentRow++;
  
  // Add data rows
  params.data.forEach((item, rowIndex) => {
    const dataRow = worksheet.getRow(currentRow + rowIndex);
    const rowData = prepareRowDataForExcel(item, tableConfig);
    
    rowData.forEach((value, colIndex) => {
      const cell = dataRow.getCell(colIndex + 1);
      cell.value = value;
      
      // Apply formatting based on data type
      if (tableConfig.columns[colIndex]?.type === 'currency') {
        cell.numFmt = '₹#,##0.00';
      } else if (tableConfig.columns[colIndex]?.type === 'date') {
        cell.numFmt = 'dd/mm/yyyy';
      } else if (tableConfig.columns[colIndex]?.type === 'number') {
        cell.numFmt = '#,##0';
      }
      
      // Alternate row colors
      if (rowIndex % 2 === 1) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F8F9FA' }
        };
      }
    });
  });
  
  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    if (column.eachCell) {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const columnLength = cell.value ? cell.value.toString().length : 10;
        if (columnLength > maxLength) {
          maxLength = columnLength;
        }
      });
      column.width = Math.min(maxLength + 2, 50);
    }
  });
  
  // Generate buffer
  const buffer = await workbook.xlsx.writeBuffer();
  return Buffer.from(buffer);
}

/**
 * Get table configuration for different report types
 */
export function getTableConfigForReportType(reportType: string): TableConfig {
  switch (reportType.toUpperCase()) {
    case 'AMC':
      return {
        headers: ['Customer', 'Start Date', 'End Date', 'Amount', 'Status', 'Executive'],
        columns: [
          { key: 'customer.name', type: 'text' },
          { key: 'startDate', type: 'date' },
          { key: 'endDate', type: 'date' },
          { key: 'amount', type: 'currency' },
          { key: 'status', type: 'status' },
          { key: 'users.name', type: 'text' },
        ],
      };

    case 'WARRANTY':
      return {
        headers: ['BSL No', 'Customer', 'Product', 'Purchase Date', 'Expiry Date', 'Status'],
        columns: [
          { key: 'bslNo', type: 'text' },
          { key: 'customer.name', type: 'text' },
          { key: 'productName', type: 'text' },
          { key: 'purchaseDate', type: 'date' },
          { key: 'expiryDate', type: 'date' },
          { key: 'status', type: 'status' },
        ],
      };

    case 'SERVICE':
      return {
        headers: ['Report Date', 'Customer', 'Nature of Service', 'Status', 'Executive', 'Completion Date'],
        columns: [
          { key: 'reportDate', type: 'date' },
          { key: 'customer.name', type: 'text' },
          { key: 'natureOfService', type: 'text' },
          { key: 'status', type: 'status' },
          { key: 'executive.name', type: 'text' },
          { key: 'completionDate', type: 'date' },
        ],
      };

    case 'SALES':
      return {
        headers: ['Order Date', 'Customer', 'Amount', 'Status', 'Executive', 'Delivery Date'],
        columns: [
          { key: 'orderDate', type: 'date' },
          { key: 'customer.name', type: 'text' },
          { key: 'totalAmount', type: 'currency' },
          { key: 'status', type: 'status' },
          { key: 'executive.name', type: 'text' },
          { key: 'deliveryDate', type: 'date' },
        ],
      };

    case 'CUSTOMER':
      return {
        headers: ['Name', 'City', 'Phone', 'Email', 'Registration Date', 'Status'],
        columns: [
          { key: 'name', type: 'text' },
          { key: 'city', type: 'text' },
          { key: 'phone', type: 'text' },
          { key: 'email', type: 'text' },
          { key: 'createdAt', type: 'date' },
          { key: 'status', type: 'status' },
        ],
      };

    default:
      return {
        headers: ['ID', 'Name', 'Date', 'Status'],
        columns: [
          { key: 'id', type: 'text' },
          { key: 'name', type: 'text' },
          { key: 'createdAt', type: 'date' },
          { key: 'status', type: 'status' },
        ],
      };
  }
}

/**
 * Prepare table data for PDF export
 */
export function prepareTableDataForPDF(data: any[], config: TableConfig): any[][] {
  return data.map(item =>
    config.columns.map(column => {
      const value = getNestedValue(item, column.key);
      return formatValueForDisplay(value, column.type);
    })
  );
}

/**
 * Prepare row data for Excel export
 */
export function prepareRowDataForExcel(item: any, config: TableConfig): any[] {
  return config.columns.map(column => {
    const value = getNestedValue(item, column.key);
    return formatValueForExcel(value, column.type);
  });
}

/**
 * Get column styles for PDF table based on report type
 */
export function getColumnStylesForReportType(reportType: string): Record<number, any> {
  const config = getTableConfigForReportType(reportType);
  const styles: Record<number, any> = {};

  config.columns.forEach((column, index) => {
    switch (column.type) {
      case 'currency':
        styles[index] = { halign: 'right' };
        break;
      case 'number':
        styles[index] = { halign: 'right' };
        break;
      case 'date':
        styles[index] = { halign: 'center' };
        break;
      case 'status':
        styles[index] = { halign: 'center' };
        break;
      default:
        styles[index] = { halign: 'left' };
    }
  });

  return styles;
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Format value for display in reports
 */
function formatValueForDisplay(value: any, type: string): string {
  if (value === null || value === undefined) return '';

  switch (type) {
    case 'date':
      return formatReportDate(value);
    case 'currency':
      return formatReportCurrency(Number(value));
    case 'number':
      return formatReportNumber(Number(value));
    case 'status':
      return String(value).toUpperCase();
    default:
      return String(value);
  }
}

/**
 * Format value for Excel export (preserving data types)
 */
function formatValueForExcel(value: any, type: string): any {
  if (value === null || value === undefined) return '';

  switch (type) {
    case 'date':
      return value instanceof Date ? value : new Date(value);
    case 'currency':
    case 'number':
      return Number(value) || 0;
    case 'status':
      return String(value).toUpperCase();
    default:
      return String(value);
  }
}
