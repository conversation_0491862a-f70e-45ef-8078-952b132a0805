'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ServiceReportForm } from '@/components/service/service-report-form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { updateServiceReportSchema } from '@/lib/validations/service.schema';
import { z } from 'zod';
import { Edit, ArrowLeft } from 'lucide-react';

type ServiceReportFormData = z.infer<typeof updateServiceReportSchema>;

interface ServiceReport {
  id: string;
  customerId: string;
  executiveId: string;
  reportDate: string;
  visitDate?: string;
  completionDate?: string;
  natureOfService: string;
  complaintType: string;
  actionTaken?: string;
  remarks?: string;
  status: string;
  details: Array<{
    id: string;
    machineType: string;
    serialNumber: string;
    problem: string;
    solution: string;
    partReplaced?: string;
  }>;
}

export default function EditServiceReportPage() {
  const router = useRouter();
  const params = useParams();
  const [serviceReport, setServiceReport] = useState<ServiceReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      loadServiceReport(params.id as string);
    }
  }, [params?.id]);

  const loadServiceReport = async (id: string) => {
    try {
      const response = await fetch(`/api/service/${id}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setServiceReport(data.serviceReport);
      } else {
        toast.error('Failed to load service report');
        router.push('/service');
      }
    } catch (error) {
      console.error('Error loading service report:', error);
      toast.error('Failed to load service report');
      router.push('/service');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (data: ServiceReportFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/service/${params?.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success('Service report updated successfully');
        router.push(`/service/${params?.id}`);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update service report');
      }
    } catch (error) {
      console.error('Error updating service report:', error);
      toast.error('Failed to update service report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/service/${params?.id}`);
  };

  const handleBack = () => {
    router.push('/service');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Edit Service Report
            </CardTitle>
            <CardDescription className="text-gray-100">
              Loading service report details...
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!serviceReport) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Edit Service Report
            </CardTitle>
            <CardDescription className="text-gray-100">
              Service report not found
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <p className="text-muted-foreground mb-4">
              The requested service report could not be found.
            </p>
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Service Reports
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Transform service report data for the form
  const initialData = {
    customerId: serviceReport.customerId,
    executiveId: serviceReport.executiveId,
    reportDate: new Date(serviceReport.reportDate),
    visitDate: serviceReport.visitDate ? new Date(serviceReport.visitDate) : undefined,
    completionDate: serviceReport.completionDate ? new Date(serviceReport.completionDate) : undefined,
    natureOfService: serviceReport.natureOfService,
    complaintType: serviceReport.complaintType as any,
    actionTaken: serviceReport.actionTaken || '',
    remarks: serviceReport.remarks || '',
    status: serviceReport.status as any,
    details: serviceReport.details.map(detail => ({
      id: detail.id,
      machineType: detail.machineType,
      serialNumber: detail.serialNumber,
      problem: detail.problem,
      solution: detail.solution,
      partReplaced: detail.partReplaced || '',
    })),
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Service Report
          </CardTitle>
          <CardDescription className="text-gray-100">
            Update the service report details below.
          </CardDescription>
        </CardHeader>
      </Card>

      <ServiceReportForm
        onSubmit={handleSubmit as any}
        onCancel={handleCancel}
        initialData={initialData}
        isLoading={isLoading}
      />
    </div>
  );
}
