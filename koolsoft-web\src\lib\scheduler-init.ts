import { getReportSchedulerService } from '@/lib/services/report-scheduler.service';

/**
 * Initialize the report scheduler service
 * This should be called when the application starts
 */
export async function initializeScheduler() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Initializing Report Scheduler Service...');
  }

  try {
    const schedulerService = getReportSchedulerService();
    await schedulerService.initialize();
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Report Scheduler Service initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize Report Scheduler Service:', error);
  }
}

/**
 * Shutdown the report scheduler service
 * This should be called when the application shuts down
 */
export function shutdownScheduler() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Shutting down Report Scheduler Service...');
  }

  try {
    const schedulerService = getReportSchedulerService();
    schedulerService.shutdown();
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Report Scheduler Service shutdown successfully');
    }
  } catch (error) {
    console.error('Failed to shutdown Report Scheduler Service:', error);
  }
}

// Handle process termination
if (typeof process !== 'undefined') {
  process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully...');
    shutdownScheduler();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    shutdownScheduler();
    process.exit(0);
  });
}
