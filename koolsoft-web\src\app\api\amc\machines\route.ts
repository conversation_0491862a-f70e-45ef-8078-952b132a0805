import { NextRequest, NextResponse } from 'next/server';
import { getAMCMachineRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * AMC Machine creation schema
 */
const createAMCMachineSchema = z.object({
  amcContractId: z.string().uuid(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  serialNumber: z.string().optional(),
  location: z.string().optional(),
  installationDate: z.coerce.date().optional(),
  tonnage: z.number().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  originalAmcId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
});

/**
 * GET /api/amc/machines
 * Get all AMC machines with optional pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const contractId = searchParams.get('contractId');
    const serialNumber = searchParams.get('serialNumber');
    
    const amcMachineRepository = getAMCMachineRepository();
    
    let machines = [];
    let total = 0;
    
    if (contractId) {
      // Get machines for a specific contract
      machines = await amcMachineRepository.findByContractId(contractId, skip, take);
      total = await amcMachineRepository.count({ amcContractId: contractId });
    } else if (serialNumber) {
      // Get machine by serial number
      const machine = await amcMachineRepository.findBySerialNumber(serialNumber);
      machines = machine ? [machine] : [];
      total = machine ? 1 : 0;
    } else {
      // Get all machines
      machines = await amcMachineRepository.findAll(skip, take);
      total = await amcMachineRepository.count();
    }
    
    return NextResponse.json({
      machines,
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching AMC machines:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AMC machines' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/amc/machines
 * Create a new AMC machine
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createAMCMachineSchema.parse(body);
    
    const amcMachineRepository = getAMCMachineRepository();
    
    // Transform data for database - connect the AMC contract and remove unsupported fields
    const { amcContractId, tonnage, installationDate, ...machineData } = validatedData;
    const createData = {
      ...machineData,
      amcContract: {
        connect: { id: amcContractId }
      }
    };

    // Create AMC machine
    const machine = await amcMachineRepository.create(createData);
    
    return NextResponse.json(machine, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('Error creating AMC machine:', error);
    return NextResponse.json(
      { error: 'Failed to create AMC machine' },
      { status: 500 }
    );
  }
}
