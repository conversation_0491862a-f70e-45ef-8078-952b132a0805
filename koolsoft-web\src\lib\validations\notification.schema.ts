import { z } from 'zod';

/**
 * Sales Event Types Enum
 */
export const salesEventTypeSchema = z.enum([
  'LEAD_CREATED',
  'LEAD_STATUS_CHANGED',
  'OPPORTUNITY_CREATED',
  'OPPORTUNITY_STATUS_CHANGED',
  'PROSPECT_CREATED',
  'PROSPECT_STATUS_CHANGED',
  'ORDER_CREATED',
  'ORDER_STATUS_CHANGED',
  'CONVERSION_EVENT',
], {
  errorMap: () => ({ message: 'Invalid sales event type' })
});

/**
 * Sales Entity Types Enum
 */
export const salesEntityTypeSchema = z.enum([
  'lead',
  'opportunity',
  'prospect',
  'order',
], {
  errorMap: () => ({ message: 'Invalid sales entity type' })
});

/**
 * Notification Priority Enum
 */
export const notificationPrioritySchema = z.enum([
  'HIGH',
  'NORMAL',
  'LOW',
], {
  errorMap: () => ({ message: 'Invalid notification priority' })
});

/**
 * Notification Status Enum
 */
export const notificationStatusSchema = z.enum([
  'PENDING',
  'PROCESSING',
  'SENT',
  'FAILED',
  'CANCELLED',
], {
  errorMap: () => ({ message: 'Invalid notification status' })
});

/**
 * Create Sales Notification Event Schema
 */
export const createSalesNotificationEventSchema = z.object({
  eventType: salesEventTypeSchema,
  entityType: salesEntityTypeSchema,
  entityId: z.string().uuid({ message: 'Valid entity ID is required' }),
  userId: z.string().uuid().optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  oldStatus: z.string().max(50).optional(),
  newStatus: z.string().max(50).optional(),
  eventData: z.record(z.any()).optional(),
});

/**
 * Update Notification Preferences Schema
 */
export const updateNotificationPreferencesSchema = z.object({
  salesLeadCreated: z.boolean().optional(),
  salesLeadStatusChanged: z.boolean().optional(),
  salesOpportunityCreated: z.boolean().optional(),
  salesOpportunityStatusChanged: z.boolean().optional(),
  salesProspectCreated: z.boolean().optional(),
  salesProspectStatusChanged: z.boolean().optional(),
  salesOrderCreated: z.boolean().optional(),
  salesOrderStatusChanged: z.boolean().optional(),
  salesConversionEvents: z.boolean().optional(),
  dailySalesSummary: z.boolean().optional(),
  weeklySalesReport: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Notification Queue Filter Schema
 */
export const notificationQueueFilterSchema = z.object({
  status: notificationStatusSchema.optional(),
  priority: notificationPrioritySchema.optional(),
  recipientUserId: z.string().uuid().optional(),
  eventType: salesEventTypeSchema.optional(),
  entityType: salesEntityTypeSchema.optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  skip: z.coerce.number().int().min(0).default(0),
  take: z.coerce.number().int().min(1).max(100).default(10),
});

/**
 * Notification Event Filter Schema
 */
export const notificationEventFilterSchema = z.object({
  eventType: salesEventTypeSchema.optional(),
  entityType: salesEntityTypeSchema.optional(),
  entityId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  processed: z.boolean().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  skip: z.coerce.number().int().min(0).default(0),
  take: z.coerce.number().int().min(1).max(100).default(10),
});

/**
 * Send Test Notification Schema
 */
export const sendTestNotificationSchema = z.object({
  templateName: z.string().min(2).max(100),
  recipientEmail: z.string().email({ message: 'Valid email address is required' }),
  templateData: z.record(z.any()).optional(),
  priority: notificationPrioritySchema.default('NORMAL'),
});

/**
 * Bulk Update Notification Preferences Schema
 */
export const bulkUpdateNotificationPreferencesSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1).max(100),
  preferences: updateNotificationPreferencesSchema,
});

/**
 * Notification Statistics Filter Schema
 */
export const notificationStatisticsFilterSchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  groupBy: z.enum(['day', 'week', 'month']).default('day'),
});

/**
 * Process Notifications Schema
 */
export const processNotificationsSchema = z.object({
  limit: z.coerce.number().int().min(1).max(100).default(50),
  priority: notificationPrioritySchema.optional(),
});

/**
 * Retry Failed Notifications Schema
 */
export const retryFailedNotificationsSchema = z.object({
  notificationIds: z.array(z.string().uuid()).min(1).max(50),
  resetAttempts: z.boolean().default(false),
});

/**
 * Cleanup Notifications Schema
 */
export const cleanupNotificationsSchema = z.object({
  olderThanDays: z.coerce.number().int().min(1).max(365).default(30),
  statusesToClean: z.array(notificationStatusSchema).default(['SENT', 'FAILED']),
  dryRun: z.boolean().default(true),
});

/**
 * Export Notifications Schema
 */
export const exportNotificationsSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: notificationQueueFilterSchema.optional(),
  includeEventData: z.boolean().default(false),
  includeTemplateData: z.boolean().default(false),
});

/**
 * Notification Template Variables Schema
 */
export const notificationTemplateVariablesSchema = z.object({
  recipientName: z.string().optional(),
  eventType: z.string().optional(),
  entityType: z.string().optional(),
  entityId: z.string().optional(),
  oldStatus: z.string().optional(),
  newStatus: z.string().optional(),
  customerName: z.string().optional(),
  customerEmail: z.string().optional(),
  executiveName: z.string().optional(),
  userName: z.string().optional(),
  eventData: z.record(z.any()).optional(),
  createdAt: z.date().optional(),
});

/**
 * Notification Preference Defaults Schema
 */
export const notificationPreferenceDefaultsSchema = z.object({
  role: z.enum(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']),
  preferences: updateNotificationPreferencesSchema,
});

// Type exports for TypeScript
export type SalesEventType = z.infer<typeof salesEventTypeSchema>;
export type SalesEntityType = z.infer<typeof salesEntityTypeSchema>;
export type NotificationPriority = z.infer<typeof notificationPrioritySchema>;
export type NotificationStatus = z.infer<typeof notificationStatusSchema>;
export type CreateSalesNotificationEvent = z.infer<typeof createSalesNotificationEventSchema>;
export type UpdateNotificationPreferences = z.infer<typeof updateNotificationPreferencesSchema>;
export type NotificationQueueFilter = z.infer<typeof notificationQueueFilterSchema>;
export type NotificationEventFilter = z.infer<typeof notificationEventFilterSchema>;
export type SendTestNotification = z.infer<typeof sendTestNotificationSchema>;
export type BulkUpdateNotificationPreferences = z.infer<typeof bulkUpdateNotificationPreferencesSchema>;
export type NotificationStatisticsFilter = z.infer<typeof notificationStatisticsFilterSchema>;
export type ProcessNotifications = z.infer<typeof processNotificationsSchema>;
export type RetryFailedNotifications = z.infer<typeof retryFailedNotificationsSchema>;
export type CleanupNotifications = z.infer<typeof cleanupNotificationsSchema>;
export type ExportNotifications = z.infer<typeof exportNotificationsSchema>;
export type NotificationTemplateVariables = z.infer<typeof notificationTemplateVariablesSchema>;
export type NotificationPreferenceDefaults = z.infer<typeof notificationPreferenceDefaultsSchema>;
