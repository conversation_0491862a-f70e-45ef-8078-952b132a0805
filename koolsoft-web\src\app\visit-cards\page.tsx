'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import {
  Home,
  FileText,
  Calendar,
  Download,
  Eye,
  Edit,
  Trash,
  Search,
  Plus,
  RefreshCw,
  ChevronDown,
  User,
  Building
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

/**
 * Visit Cards Page
 *
 * This page displays a list of all visit cards with pagination, sorting, and filtering.
 * Users can view, edit, and delete visit cards from this page.
 */
export default function VisitCardsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for visit cards data
  const [visitCards, setVisitCards] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteVisitCardId, setDeleteVisitCardId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Filter state
  const [filters, setFilters] = useState({
    customerId: searchParams?.get('customerId') || '',
    userId: searchParams?.get('userId') || '',
    status: searchParams?.get('status') || '',
    startDate: searchParams?.get('startDate') || '',
    endDate: searchParams?.get('endDate') || '',
    search: searchParams?.get('search') || ''
  });

  // Fetch visit cards data
  const fetchVisitCards = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const skip = (currentPage - 1) * pageSize;
      const queryParams = new URLSearchParams({
        skip: skip.toString(),
        take: pageSize.toString()
      });

      // Add filters to query parameters
      if (filters.customerId) queryParams.append('customerId', filters.customerId);
      if (filters.userId) queryParams.append('userId', filters.userId);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);
      if (filters.search) queryParams.append('search', filters.search);

      // Fetch data from API
      const response = await fetch(`/api/visit-cards?${queryParams.toString()}`, {
        credentials: 'include' // Include credentials for authentication
      });

      if (!response.ok) {
        throw new Error('Failed to fetch visit cards');
      }

      const data = await response.json();

      // Update state with fetched data
      setVisitCards(data.visitCards || []);
      setTotalItems(data.meta?.total || 0);
      setTotalPages(Math.ceil((data.meta?.total || 0) / pageSize));
    } catch (error: any) {
      console.error('Error fetching visit cards:', error);
      setError(error.message || 'Failed to load visit cards');
      toast({
        title: 'Error',
        description: 'Failed to load visit cards. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on initial load and when filters or pagination changes
  useEffect(() => {
    fetchVisitCards();
  }, [currentPage, pageSize, filters]);

  // Handle filter change
  const handleFilterChange = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Reset to first page when filters change
    setCurrentPage(1);

    // Update URL with filter parameters
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value) queryParams.append(key, value);
    });

    // Update URL without refreshing the page
    router.push(`/visit-cards?${queryParams.toString()}`);
  };

  // Handle filter input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Handle filter select change
  const handleSelectChange = (name: string, value: string) => {
    // Convert "all" value to empty string for API filtering
    const apiValue = value === 'all' ? '' : value;
    setFilters(prev => ({ ...prev, [name]: apiValue }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle delete visit card
  const handleDeleteVisitCard = async () => {
    if (!deleteVisitCardId) return;

    try {
      setIsDeleting(true);

      const response = await fetch(`/api/visit-cards/${deleteVisitCardId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to delete visit card');
      }

      // Refresh the list after deletion
      fetchVisitCards();

      toast({
        title: 'Success',
        description: 'Visit card deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting visit card:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete visit card. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setDeleteVisitCardId(null);
    }
  };

  // Render loading state
  if (isLoading && !visitCards.length) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <div className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Visit Card Management</CardTitle>
              <CardDescription className="text-gray-100">
                View, search, and manage all visit cards
              </CardDescription>
            </div>
            <Button asChild variant="secondary">
              <Link href="/visit-cards/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Visit Card
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 pt-6">
          {/* Filter Form */}
          <form onSubmit={handleFilterChange} className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="flex space-x-2">
                <Input
                  id="search"
                  name="search"
                  placeholder="Search by notes or file name"
                  value={filters.search}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateRange">Date Range</Label>
              <div className="flex space-x-2">
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  placeholder="Start Date"
                  value={filters.startDate}
                  onChange={handleInputChange}
                />
                <Input
                  id="endDate"
                  name="endDate"
                  type="date"
                  placeholder="End Date"
                  value={filters.endDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="md:col-span-3 flex justify-end">
              <Button type="submit" className="w-full md:w-auto">
                <Search className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </form>

          {/* Visit Cards Table */}
          {error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : visitCards.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              No visit cards found. Try adjusting your filters or create a new visit card.
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>File Name</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Upload Date</TableHead>
                    <TableHead>Notes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {visitCards.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{card.filePath.split('/').pop()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-2 text-gray-500" />
                          <Link href={`/customers/${card.customerId}`} className="text-blue-600 hover:underline">
                            {card.customer?.name || 'Unknown Customer'}
                          </Link>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{formatDate(card.uploadDate)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {card.notes || <span className="text-gray-500">No notes</span>}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(card.filePath, '_blank')}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <Link href={`/visit-cards/${card.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600"
                                onClick={() => setDeleteVisitCardId(card.id)}
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action cannot be undone. This will permanently delete the
                                  visit card and remove it from our servers.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setDeleteVisitCardId(null)}>
                                  Cancel
                                </AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={handleDeleteVisitCard}
                                  className="bg-red-600 text-white hover:bg-red-700"
                                  disabled={isDeleting}
                                >
                                  {isDeleting ? 'Deleting...' : 'Delete'}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <PaginationItem key={pageNumber}>
                        <PaginationLink
                          onClick={() => handlePageChange(pageNumber)}
                          isActive={currentPage === pageNumber}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  {totalPages > 5 && (
                    <>
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => handlePageChange(totalPages)}
                          isActive={currentPage === totalPages}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
