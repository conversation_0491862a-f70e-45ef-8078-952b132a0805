import { PrismaClient, Prisma } from '@prisma/client';
import { PrismaRepository } from './prisma.repository';

/**
 * Notification Preference Repository
 *
 * This repository handles database operations for the Notification Preference entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class NotificationPreferenceRepository extends PrismaRepository<
  Prisma.NotificationPreferenceGetPayload<{}>,
  string,
  Prisma.NotificationPreferenceCreateInput,
  Prisma.NotificationPreferenceUpdateInput
> {
  createTransactionRepository(tx: any): NotificationPreferenceRepository {
    return new NotificationPreferenceRepository(tx);
  }
  constructor(prismaClient?: PrismaClient) {
    super('NotificationPreference');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find notification preferences by user ID
   * @param userId User ID
   * @returns Promise resolving to the notification preferences or null if not found
   */
  async findByUserId(userId: string): Promise<Prisma.NotificationPreferenceGetPayload<{}> | null> {
    return this.model.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Create or update notification preferences for a user
   * @param userId User ID
   * @param preferences Notification preferences
   * @returns Promise resolving to the created or updated notification preferences
   */
  async upsertByUserId(
    userId: string,
    preferences: Partial<Omit<Prisma.NotificationPreferenceCreateInput, 'user'>>
  ): Promise<Prisma.NotificationPreferenceGetPayload<{}>> {
    return this.model.upsert({
      where: { userId },
      update: preferences,
      create: {
        userId,
        ...preferences,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  /**
   * Get default notification preferences based on user role
   * @param role User role
   * @returns Default notification preferences
   */
  getDefaultPreferences(role: string): Omit<Prisma.NotificationPreferenceCreateInput, 'userId' | 'user'> {
    const basePreferences = {
      salesLeadCreated: true,
      salesLeadStatusChanged: true,
      salesOpportunityCreated: true,
      salesOpportunityStatusChanged: true,
      salesProspectCreated: true,
      salesProspectStatusChanged: true,
      salesOrderCreated: true,
      salesOrderStatusChanged: true,
      salesConversionEvents: true,
      dailySalesSummary: false,
      weeklySalesReport: false,
      isActive: true,
    };

    // Customize based on role
    switch (role.toUpperCase()) {
      case 'ADMIN':
      case 'MANAGER':
        return {
          ...basePreferences,
          dailySalesSummary: true,
          weeklySalesReport: true,
        };
      case 'EXECUTIVE':
        return {
          ...basePreferences,
          dailySalesSummary: true,
        };
      case 'USER':
      default:
        return basePreferences;
    }
  }

  /**
   * Find users with specific notification preferences enabled
   * @param notificationType Type of notification
   * @param additionalFilters Additional filters
   * @returns Promise resolving to an array of users with the notification enabled
   */
  async findUsersWithNotificationEnabled(
    notificationType: keyof Omit<Prisma.NotificationPreferenceCreateInput, 'userId' | 'isActive' | 'createdAt' | 'updatedAt'>,
    additionalFilters?: {
      roles?: string[];
      isActive?: boolean;
    }
  ): Promise<Array<{ userId: string; email: string; name: string; role: string }>> {
    const where: any = {
      [notificationType]: true,
      isActive: true,
    };

    if (additionalFilters?.isActive !== undefined) {
      where.user = {
        isActive: additionalFilters.isActive,
      };
    }

    if (additionalFilters?.roles && additionalFilters.roles.length > 0) {
      where.user = {
        ...where.user,
        role: {
          in: additionalFilters.roles,
        },
      };
    }

    const preferences = await this.model.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    return preferences.map((pref: any) => ({
      userId: pref.userId,
      email: pref.user.email,
      name: pref.user.name,
      role: pref.user.role,
    }));
  }

  /**
   * Bulk update notification preferences for multiple users
   * @param updates Array of user ID and preference updates
   * @returns Promise resolving to the number of updated records
   */
  async bulkUpdate(
    updates: Array<{
      userId: string;
      preferences: Partial<Omit<Prisma.NotificationPreferenceUpdateInput, 'user'>>;
    }>
  ): Promise<number> {
    let updatedCount = 0;

    for (const update of updates) {
      try {
        await this.model.update({
          where: { userId: update.userId },
          data: update.preferences,
        });
        updatedCount++;
      } catch (error) {
        console.error(`Failed to update preferences for user ${update.userId}:`, error);
      }
    }

    return updatedCount;
  }

  /**
   * Get notification preferences statistics
   * @returns Promise resolving to statistics about notification preferences
   */
  async getStatistics(): Promise<{
    totalUsers: number;
    activePreferences: number;
    preferencesByType: Record<string, number>;
  }> {
    const [totalUsers, activePreferences, allPreferences] = await Promise.all([
      this.model.count(),
      this.model.count({ where: { isActive: true } }),
      this.model.findMany({
        where: { isActive: true },
        select: {
          salesLeadCreated: true,
          salesLeadStatusChanged: true,
          salesOpportunityCreated: true,
          salesOpportunityStatusChanged: true,
          salesProspectCreated: true,
          salesProspectStatusChanged: true,
          salesOrderCreated: true,
          salesOrderStatusChanged: true,
          salesConversionEvents: true,
          dailySalesSummary: true,
          weeklySalesReport: true,
        },
      }),
    ]);

    const preferencesByType: Record<string, number> = {};
    const preferenceKeys = [
      'salesLeadCreated',
      'salesLeadStatusChanged',
      'salesOpportunityCreated',
      'salesOpportunityStatusChanged',
      'salesProspectCreated',
      'salesProspectStatusChanged',
      'salesOrderCreated',
      'salesOrderStatusChanged',
      'salesConversionEvents',
      'dailySalesSummary',
      'weeklySalesReport',
    ];

    preferenceKeys.forEach(key => {
      preferencesByType[key] = allPreferences.filter((pref: any) => pref[key as keyof typeof pref]).length;
    });

    return {
      totalUsers,
      activePreferences,
      preferencesByType,
    };
  }
}

/**
 * Get the notification preference repository instance
 * @returns Notification preference repository instance
 */
export function getNotificationPreferenceRepository(): NotificationPreferenceRepository {
  return new NotificationPreferenceRepository();
}
