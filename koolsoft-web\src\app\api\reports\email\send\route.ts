import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { EmailDistributionService } from '@/lib/services/email-distribution.service';
import { sendReportEmailSchema } from '@/lib/validations/email-distribution.schema';
import { z } from 'zod';

/**
 * POST /api/reports/email/send
 * Send report via email
 */
export const POST = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const validatedData = sendReportEmailSchema.parse(body);

    const emailDistributionService = new EmailDistributionService();

    // Send the report email
    const result = await emailDistributionService.sendReportEmail(validatedData, user.id);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          deliveryId: result.deliveryId,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
        },
        message: `Email sent successfully to ${result.sentCount} recipient(s)${result.failedCount > 0 ? ` (${result.failedCount} failed)` : ''}`,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to send emails',
        details: {
          deliveryId: result.deliveryId,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          errors: result.errors,
        },
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error sending report email:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to send report email'
      },
      { status: 500 }
    );
  }
});
