/**
 * History Repairs API Routes
 * 
 * This file handles API routes for repair history management.
 * It provides endpoints for CRUD operations on repair history records.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { HistoryRepairRepository } from '@/lib/repositories/history-repair.repository';
import { 
  createHistoryRepairSchema, 
  historyFilterSchema 
} from '@/lib/validations/history.schema';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

// Initialize repository
const historyRepairRepository = new HistoryRepairRepository();

/**
 * GET /api/history/repairs
 * Get repair history records with filtering and pagination
 */
async function getRepairHistory(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Validate query parameters
    const queryParams = historyFilterSchema.parse({
      customerId: searchParams.get('customerId'),
      historyCardId: searchParams.get('historyCardId'),
      technicianId: searchParams.get('technicianId'),
      dateFrom: searchParams.get('dateFrom'),
      dateTo: searchParams.get('dateTo'),
      status: searchParams.getAll('status'),
      priority: searchParams.getAll('priority'),
      page: searchParams.get('page'),
      limit: searchParams.get('limit'),
      sortBy: searchParams.get('sortBy'),
      sortOrder: searchParams.get('sortOrder'),
    });

    let repairs;
    let total = 0;

    if (queryParams.customerId) {
      // Get repairs by customer ID
      repairs = await historyRepairRepository.findByCustomerId(
        queryParams.customerId,
        queryParams
      );
      total = await historyRepairRepository.count({
        where: { historyCard: { customerId: queryParams.customerId } }
      });
    } else if ((queryParams as any).historyCardId) {
      // Get repairs by history card ID
      repairs = await historyRepairRepository.findByHistoryCardId(
        (queryParams as any).historyCardId,
        queryParams
      );
      total = await historyRepairRepository.count({
        where: { historyCardId: (queryParams as any).historyCardId }
      });
    } else if (queryParams.technicianId) {
      // Get repairs by technician ID
      repairs = await historyRepairRepository.findByTechnicianId(
        queryParams.technicianId,
        queryParams
      );
      total = await historyRepairRepository.count({
        where: { technicianId: queryParams.technicianId }
      });
    } else {
      // Get all repairs with pagination
      repairs = await historyRepairRepository.findMany({
        include: {
          // technician: {
          //   select: {
          //     id: true,
          //     name: true,
          //     email: true,
          //     role: true,
          //   }
          // }, // TODO: Add technician relationship to schema
          historyCard: {
            include: {
              customer: {
                select: {
                  id: true,
                  name: true,
                  address: true,
                  city: true,
                  state: true,
                  phone: true,
                  email: true,
                }
              }
            }
          }
        },
        orderBy: { repairDate: queryParams.sortOrder || 'desc' },
        skip: (queryParams.page - 1) * queryParams.limit,
        take: queryParams.limit,
      });
      total = await historyRepairRepository.count();
    }

    const totalPages = Math.ceil(total / queryParams.limit);

    return NextResponse.json({
      success: true,
      data: repairs,
      pagination: {
        page: queryParams.page,
        limit: queryParams.limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching repair history:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch repair history',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/history/repairs
 * Create a new repair history record
 */
async function createRepairHistory(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate request body
    const validatedData = createHistoryRepairSchema.parse(body);

    // Create the repair history record
    const repairHistory = await historyRepairRepository.create({
      ...validatedData,
      repairDate: new Date(validatedData.repairDate),
      historyCard: { connect: { id: validatedData.historyCardId } },
      // followUpDate: validatedData.followUpDate ? new Date(validatedData.followUpDate) : undefined,
    });

    return NextResponse.json({
      success: true,
      data: repairHistory,
      message: 'Repair history record created successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating repair history:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create repair history record',
      },
      { status: 500 }
    );
  }
}

// Export handlers with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forHistoryRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], getRepairHistory),
  {
    action: 'list_repair_history',
    getEntityId: () => 'list',
  }
);

export const POST = ActivityLoggerMiddlewareFactory.forHistoryRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE'], createRepairHistory),
  {
    action: 'create_repair_history',
    getEntityId: (req: any) => {
      try {
        // Note: res parameter not available in this context
        // const responseData = res.json();
        return 'unknown';
      } catch {
        return 'unknown';
      }
    },
  }
);
