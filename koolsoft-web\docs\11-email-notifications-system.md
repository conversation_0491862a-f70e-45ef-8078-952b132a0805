# Email Notifications for Sales Events System

## Overview

The Email Notifications for Sales Events system provides automated email notifications for various sales activities in the KoolSoft application. This system ensures that relevant stakeholders are promptly informed about important sales events such as lead creation, status changes, opportunity updates, and order processing.

## Features

### Core Functionality
- **Automated Event Detection**: Automatically detects sales events and triggers notifications
- **User Preference Management**: Allows users to customize their notification preferences
- **Queue-based Processing**: Reliable email delivery with retry mechanisms
- **Template-based Emails**: Professional email templates with variable substitution
- **Role-based Notifications**: Different notification settings based on user roles
- **Delivery Tracking**: Complete audit trail of notification delivery status

### Supported Sales Events
- **Lead Management**: New lead creation and status changes
- **Opportunity Management**: New opportunity creation and status updates
- **Prospect Management**: New prospect creation and status changes
- **Order Management**: New order creation and status updates (high priority)
- **Conversion Events**: Sales conversion notifications
- **Periodic Reports**: Daily sales summaries and weekly reports

## Architecture

### Database Schema

#### NotificationPreference
Stores user-specific email notification preferences:
- `userId`: Reference to the user
- `salesLeadCreated`: Enable/disable lead creation notifications
- `salesLeadStatusChanged`: Enable/disable lead status change notifications
- `salesOpportunityCreated`: Enable/disable opportunity creation notifications
- `salesOpportunityStatusChanged`: Enable/disable opportunity status change notifications
- `salesProspectCreated`: Enable/disable prospect creation notifications
- `salesProspectStatusChanged`: Enable/disable prospect status change notifications
- `salesOrderCreated`: Enable/disable order creation notifications
- `salesOrderStatusChanged`: Enable/disable order status change notifications
- `salesConversionEvents`: Enable/disable conversion event notifications
- `dailySalesSummary`: Enable/disable daily sales summary emails
- `weeklySalesReport`: Enable/disable weekly sales report emails
- `isActive`: Master switch for all notifications

#### SalesNotificationEvent
Tracks sales events that trigger notifications:
- `eventType`: Type of event (LEAD_CREATED, LEAD_STATUS_CHANGED, etc.)
- `entityType`: Type of entity (lead, opportunity, prospect, order)
- `entityId`: ID of the related entity
- `userId`: User who triggered the event
- `customerId`: Related customer
- `executiveId`: Assigned executive
- `oldStatus`: Previous status (for status changes)
- `newStatus`: New status
- `eventData`: Additional event-specific data
- `processed`: Whether the event has been processed

#### SalesNotificationQueue
Manages email delivery queue:
- `eventId`: Reference to the triggering event
- `recipientUserId`: User receiving the notification
- `recipientEmail`: Email address for delivery
- `templateName`: Email template to use
- `templateData`: Data for template variable substitution
- `priority`: Notification priority (HIGH, NORMAL, LOW)
- `status`: Delivery status (PENDING, PROCESSING, SENT, FAILED, CANCELLED)
- `attempts`: Number of delivery attempts
- `maxAttempts`: Maximum retry attempts
- `sentAt`: Timestamp when email was sent
- `failureReason`: Reason for delivery failure

### Services

#### SalesNotificationService
Core service managing the notification workflow:
- **Event Creation**: Creates notification events for sales activities
- **Event Processing**: Processes events and creates queue items
- **Recipient Determination**: Identifies users who should receive notifications
- **Queue Management**: Manages the notification delivery queue
- **Preference Management**: Handles user notification preferences
- **Statistics**: Provides notification system statistics

### API Endpoints

#### Notification Preferences
- `GET /api/notifications/preferences` - Get user's notification preferences
- `PUT /api/notifications/preferences` - Update notification preferences
- `POST /api/notifications/preferences` - Reset preferences to defaults

#### Notification Events
- `GET /api/notifications/events` - Get notification events with filtering
- `POST /api/notifications/events/process` - Process pending events (Admin only)

#### Notification Queue
- `GET /api/notifications/queue` - Get notification queue with filtering
- `POST /api/notifications/queue/process` - Process pending notifications (Admin only)

#### Statistics
- `GET /api/notifications/statistics` - Get notification system statistics (Admin only)

## User Interface

### Notification Preferences Page
Located at `/notifications/preferences`, allows users to:
- Configure which types of notifications to receive
- Enable/disable the master notification switch
- Reset preferences to role-based defaults
- Save custom preference settings

### Notification History Page
Located at `/notifications/history`, provides:
- View of all email notifications sent to the user
- Filtering by status, priority, and event type
- Delivery status and attempt information
- Search functionality

### Admin Notification Dashboard
Located at `/admin/notifications`, offers:
- System-wide notification statistics
- Process pending notifications and events
- Monitor notification success rates
- View queue status and performance metrics

## Email Templates

### Available Templates
- `sales-lead-created`: New sales lead creation notification
- `sales-lead-status-changed`: Sales lead status change notification
- `sales-opportunity-created`: New sales opportunity creation notification
- `sales-opportunity-status-changed`: Sales opportunity status change notification
- `sales-prospect-created`: New sales prospect creation notification
- `sales-prospect-status-changed`: Sales prospect status change notification
- `sales-order-created`: New sales order creation notification (high priority)
- `sales-order-status-changed`: Sales order status change notification

### Template Variables
All templates support the following variables:
- `{{recipientName}}`: Name of the notification recipient
- `{{customerName}}`: Name of the related customer
- `{{executiveName}}`: Name of the assigned executive
- `{{userName}}`: Name of the user who triggered the event
- `{{eventType}}`: Type of the sales event
- `{{oldStatus}}`: Previous status (for status changes)
- `{{newStatus}}`: New status
- `{{eventData.*}}`: Event-specific data (contact person, phone, etc.)

## Integration Points

### Sales API Integration
The notification system is integrated with all sales API endpoints:
- **Sales Leads**: `/api/sales/leads` - Triggers LEAD_CREATED and LEAD_STATUS_CHANGED events
- **Sales Opportunities**: `/api/sales/opportunities` - Triggers OPPORTUNITY_CREATED and OPPORTUNITY_STATUS_CHANGED events
- **Sales Prospects**: `/api/sales/prospects` - Triggers PROSPECT_CREATED and PROSPECT_STATUS_CHANGED events
- **Sales Orders**: `/api/sales/orders` - Triggers ORDER_CREATED and ORDER_STATUS_CHANGED events

### Role-based Notification Rules
- **ADMIN/MANAGER**: Receive all notifications by default
- **EXECUTIVE**: Receive notifications for entities they're assigned to or created
- **USER**: Receive notifications for entities they created

## Configuration

### Environment Variables
The system uses the existing email configuration:
- `EMAIL_HOST`: SMTP server hostname
- `EMAIL_PORT`: SMTP server port
- `EMAIL_SECURE`: Whether to use secure connection
- `EMAIL_USER`: SMTP authentication username
- `EMAIL_PASS`: SMTP authentication password
- `EMAIL_FROM`: Default sender email address

### Default Preferences by Role
- **ADMIN/MANAGER**: All notifications enabled, including daily/weekly reports
- **EXECUTIVE**: All sales notifications enabled, daily summary enabled
- **USER**: Basic sales notifications enabled

## Testing

### Automated Testing
The system includes comprehensive testing via `scripts/test-notifications.ts`:
- Tests notification preference creation and management
- Verifies sales event creation and processing
- Validates notification queue functionality
- Checks email template processing
- Monitors delivery status and statistics

### Manual Testing
1. Login with admin credentials (<EMAIL> / Admin@123)
2. Navigate to `/notifications/preferences` to configure settings
3. Create or update sales entities to trigger notifications
4. Check `/notifications/history` for delivery status
5. Use `/admin/notifications` to monitor system performance

## Maintenance

### Queue Processing
- Notifications are processed asynchronously when events are created
- Manual processing available via admin dashboard
- Failed notifications are retried up to 3 times
- Old notifications are automatically cleaned up after 30 days

### Monitoring
- System provides comprehensive statistics and metrics
- Failed notifications are logged with failure reasons
- Delivery success rates are tracked and displayed
- Performance metrics available in admin dashboard

## Security Considerations

- Role-based access control for all notification endpoints
- User isolation - users can only see their own notifications
- Admin/Manager roles required for system-wide operations
- Email content is sanitized and validated
- Audit trail maintained for all notification activities

## Future Enhancements

- Real-time notifications via WebSocket
- SMS notification support
- Advanced notification scheduling
- Custom notification templates
- Notification analytics and reporting
- Integration with external notification services
