'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Save, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

// Form validation schema
const leadFormSchema = z.object({
  customerId: z.string().uuid('Please select a customer'),
  executiveId: z.string().uuid('Please select an executive'),
  leadDate: z.date({ required_error: 'Lead date is required' }),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  status: z.string().default('NEW'),
  prospectPercentage: z.number().int().min(0).max(100).optional(),
  followUpDate: z.date().optional(),
  nextVisitDate: z.date().optional(),
  remarks: z.string().max(1000).optional(),
});

type LeadFormData = z.infer<typeof leadFormSchema>;

// Status options
const statusOptions = [
  { value: 'NEW', label: 'New' },
  { value: 'CONTACTED', label: 'Contacted' },
  { value: 'QUALIFIED', label: 'Qualified' },
  { value: 'PROPOSAL', label: 'Proposal' },
  { value: 'NEGOTIATION', label: 'Negotiation' },
  { value: 'CLOSED_WON', label: 'Closed Won' },
  { value: 'CLOSED_LOST', label: 'Closed Lost' },
];

interface Customer {
  id: string;
  name: string;
  city?: string;
  phone?: string;
}

interface Executive {
  id: string;
  name: string;
  designation?: string;
}

export default function NewLeadPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingExecutives, setLoadingExecutives] = useState(true);

  const form = useForm({
    resolver: zodResolver(leadFormSchema),
    defaultValues: {
      leadDate: new Date(),
      status: 'NEW',
    },
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch } = form;

  // Load customers
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const response = await fetch('/api/customers?take=1000', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch customers');
        }

        const result = await response.json();
        if (result.success) {
          setCustomers(result.data.customers || []);
        }
      } catch (error) {
        console.error('Error fetching customers:', error);
        toast({
          title: 'Error',
          description: 'Failed to load customers. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoadingCustomers(false);
      }
    };

    fetchCustomers();
  }, [toast]);

  // Load executives (users with EXECUTIVE role or higher)
  useEffect(() => {
    const fetchExecutives = async () => {
      try {
        const response = await fetch('/api/users?roles=ADMIN,MANAGER,EXECUTIVE&take=1000', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch executives');
        }

        const result = await response.json();
        if (result.success) {
          setExecutives(result.data.users || []);
        }
      } catch (error) {
        console.error('Error fetching executives:', error);
        toast({
          title: 'Error',
          description: 'Failed to load executives. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoadingExecutives(false);
      }
    };

    fetchExecutives();
  }, [toast]);

  // Handle form submission
  const onSubmit = async (data: LeadFormData) => {
    try {
      setLoading(true);

      const response = await fetch('/api/sales/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...data,
          leadDate: data.leadDate.toISOString(),
          followUpDate: data.followUpDate?.toISOString(),
          nextVisitDate: data.nextVisitDate?.toISOString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create lead');
      }

      const result = await response.json();
      
      if (result.success) {
        toast({
          title: 'Success',
          description: 'Lead created successfully.',
        });
        router.push('/leads');
      } else {
        throw new Error(result.error || 'Failed to create lead');
      }
    } catch (error) {
      console.error('Error creating lead:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create lead. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="bg-primary text-primary-foreground">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">New Lead</CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Create a new sales lead
              </CardDescription>
            </div>
            <Button asChild variant="secondary">
              <Link href="/leads">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Leads
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="text-black">Lead Information</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customerId" className="text-black">
                  Customer *
                </Label>
                <Select
                  onValueChange={(value) => setValue('customerId', value)}
                  disabled={loadingCustomers}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingCustomers ? "Loading customers..." : "Select customer"} />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        <div className="flex flex-col">
                          <span>{customer.name}</span>
                          {customer.city && (
                            <span className="text-sm text-gray-500">{customer.city}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.customerId && (
                  <p className="text-sm text-destructive">{errors.customerId.message}</p>
                )}
              </div>

              {/* Executive Selection */}
              <div className="space-y-2">
                <Label htmlFor="executiveId" className="text-black">
                  Executive *
                </Label>
                <Select
                  onValueChange={(value) => setValue('executiveId', value)}
                  disabled={loadingExecutives}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingExecutives ? "Loading executives..." : "Select executive"} />
                  </SelectTrigger>
                  <SelectContent>
                    {executives.map((executive) => (
                      <SelectItem key={executive.id} value={executive.id}>
                        <div className="flex flex-col">
                          <span>{executive.name}</span>
                          {executive.designation && (
                            <span className="text-sm text-gray-500">{executive.designation}</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.executiveId && (
                  <p className="text-sm text-destructive">{errors.executiveId.message}</p>
                )}
              </div>

              {/* Lead Date */}
              <div className="space-y-2">
                <Label className="text-black">Lead Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !watch('leadDate') && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {watch('leadDate') ? format(watch('leadDate'), "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={watch('leadDate')}
                      onSelect={(date) => date && setValue('leadDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.leadDate && (
                  <p className="text-sm text-destructive">{errors.leadDate.message}</p>
                )}
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status" className="text-black">Status</Label>
                <Select
                  defaultValue="NEW"
                  onValueChange={(value) => setValue('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive">{errors.status.message}</p>
                )}
              </div>

              {/* Contact Person */}
              <div className="space-y-2">
                <Label htmlFor="contactPerson" className="text-black">Contact Person</Label>
                <Input
                  id="contactPerson"
                  placeholder="Enter contact person name"
                  {...register('contactPerson')}
                />
                {errors.contactPerson && (
                  <p className="text-sm text-destructive">{errors.contactPerson.message}</p>
                )}
              </div>

              {/* Contact Phone */}
              <div className="space-y-2">
                <Label htmlFor="contactPhone" className="text-black">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  placeholder="Enter contact phone number"
                  {...register('contactPhone')}
                />
                {errors.contactPhone && (
                  <p className="text-sm text-destructive">{errors.contactPhone.message}</p>
                )}
              </div>

              {/* Prospect Percentage */}
              <div className="space-y-2">
                <Label htmlFor="prospectPercentage" className="text-black">Prospect Percentage</Label>
                <Input
                  id="prospectPercentage"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="Enter prospect percentage (0-100)"
                  {...register('prospectPercentage', { valueAsNumber: true })}
                />
                {errors.prospectPercentage && (
                  <p className="text-sm text-destructive">{errors.prospectPercentage.message}</p>
                )}
              </div>

              {/* Follow Up Date */}
              <div className="space-y-2">
                <Label className="text-black">Follow Up Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !watch('followUpDate') && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {watch('followUpDate') ? format(watch('followUpDate') as Date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={watch('followUpDate')}
                      onSelect={(date) => setValue('followUpDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.followUpDate && (
                  <p className="text-sm text-destructive">{errors.followUpDate.message}</p>
                )}
              </div>

              {/* Next Visit Date */}
              <div className="space-y-2">
                <Label className="text-black">Next Visit Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !watch('nextVisitDate') && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {watch('nextVisitDate') ? format(watch('nextVisitDate') as Date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={watch('nextVisitDate')}
                      onSelect={(date) => setValue('nextVisitDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.nextVisitDate && (
                  <p className="text-sm text-destructive">{errors.nextVisitDate.message}</p>
                )}
              </div>
            </div>

            {/* Remarks */}
            <div className="space-y-2">
              <Label htmlFor="remarks" className="text-black">Remarks</Label>
              <Textarea
                id="remarks"
                placeholder="Enter any additional remarks or notes"
                rows={4}
                {...register('remarks')}
              />
              {errors.remarks && (
                <p className="text-sm text-destructive">{errors.remarks.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" asChild>
                <Link href="/leads">Cancel</Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Create Lead
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
