import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository } from '@/lib/repositories';
import { updateQuotationStatusSchema } from '@/lib/validations/quotation.schema';
import { z } from 'zod';

/**
 * PATCH /api/quotations/[id]/status
 * Update quotation status
 */
export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateQuotationStatusSchema.parse({ ...body, id });

      const quotationRepository = getQuotationRepository();

      // Check if quotation exists
      const existingQuotation = await quotationRepository.findById(id);
      if (!existingQuotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      // Update quotation status
      const updatedQuotation = await quotationRepository.update(id, {
        status: validatedData.status,
        notes: validatedData.notes || existingQuotation.notes,
      });

      // Fetch the complete updated quotation with relations
      const completeQuotation = await quotationRepository.findWithAllRelations(id);

      return NextResponse.json({
        success: true,
        data: completeQuotation,
        message: `Quotation status updated to ${validatedData.status}`,
      });
    } catch (error) {
      console.error('Error updating quotation status:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid status update data',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update quotation status',
        },
        { status: 500 }
      );
    }
  }
);
