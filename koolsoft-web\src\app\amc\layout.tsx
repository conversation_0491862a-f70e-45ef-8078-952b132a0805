'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { FileText } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * AMC Layout Component
 *
 * This component provides a consistent layout for all AMC-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function AMCLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title and breadcrumbs based on the pathname
  let pageTitle = 'AMC Management';
  let breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/' },
    { label: 'AMC Management', href: '/amc', icon: <FileText className="h-4 w-4" /> }
  ];

  if (pathname && pathname !== '/amc') {
    if (pathname.includes('/components')) {
      pageTitle = 'Component Management';
      breadcrumbs.push({ label: 'Components', current: true });
    } else if (pathname.includes('/machines')) {
      if (pathname.match(/\/machines\/[^\/]+$/)) {
        pageTitle = 'Machine Details';
        breadcrumbs.push(
          { label: 'Machines', href: '/amc/machines' },
          { label: 'Machine Details', current: true }
        );
      } else {
        pageTitle = 'Machine Management';
        breadcrumbs.push({ label: 'Machines', current: true });
      }
    } else if (pathname.includes('/contracts')) {
      if (pathname.includes('/renew')) {
        pageTitle = 'Renew AMC Contract';
        breadcrumbs.push(
          { label: 'Contracts', href: '/amc' },
          { label: 'Renew Contract', current: true }
        );
      } else if (pathname.includes('/payments')) {
        pageTitle = 'Contract Payments';
        breadcrumbs.push(
          { label: 'Contracts', href: '/amc' },
          { label: 'Payments', current: true }
        );
      } else if (pathname.includes('/edit')) {
        pageTitle = 'Edit AMC Contract';
        breadcrumbs.push(
          { label: 'Contracts', href: '/amc' },
          { label: 'Edit Contract', current: true }
        );
      } else if (pathname.match(/\/contracts\/[^\/]+$/)) {
        pageTitle = 'Contract Details';
        breadcrumbs.push(
          { label: 'Contracts', href: '/amc' },
          { label: 'Contract Details', current: true }
        );
      } else {
        pageTitle = 'Contract Management';
        breadcrumbs.push({ label: 'Contracts', current: true });
      }
    } else if (pathname.includes('/edit')) {
      pageTitle = 'Edit AMC Contract';
      breadcrumbs.push({ label: 'Edit Contract', current: true });
    } else if (pathname.includes('/new')) {
      pageTitle = 'New AMC Contract';
      breadcrumbs.push({ label: 'New Contract', current: true });
    } else if (pathname.includes('/expiring')) {
      pageTitle = 'Expiring AMC Contracts';
      breadcrumbs.push({ label: 'Expiring Contracts', current: true });
    } else if (pathname.includes('/payments')) {
      pageTitle = 'Payment Management';
      breadcrumbs.push({ label: 'Payments', current: true });
    } else if (pathname.includes('/service-dates')) {
      pageTitle = 'Service Dates Management';
      breadcrumbs.push({ label: 'Service Dates', current: true });
    } else {
      pageTitle = 'AMC Details';
      breadcrumbs.push({ label: 'Details', current: true });
    }
  }





  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
