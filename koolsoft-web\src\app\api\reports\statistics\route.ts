import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportRepository } from '@/lib/repositories/report.repository';
import { reportStatisticsSchema } from '@/lib/validations/report.schema';
import { buildReportResponse, buildErrorResponse } from '@/lib/utils/report-utils';
import { z } from 'zod';

/**
 * GET /api/reports/statistics
 * Get report statistics and summary data
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function getReportStatistics(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract parameters
    const params = {
      reportType: searchParams.get('reportType') || 'AMC',
      period: searchParams.get('period') || 'MONTH',
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
    };
    
    // Validate parameters
    const validatedParams = reportStatisticsSchema.parse(params);
    
    const reportRepository = new ReportRepository();
    
    // Get statistics for the specified report type
    const statistics = await reportRepository.getReportStatistics(
      validatedParams.reportType,
      validatedParams.period,
      validatedParams.startDate,
      validatedParams.endDate
    );
    
    return NextResponse.json(
      buildReportResponse(statistics, undefined, {
        reportType: validatedParams.reportType,
        period: validatedParams.period,
        dateRange: {
          startDate: validatedParams.startDate?.toISOString(),
          endDate: validatedParams.endDate?.toISOString(),
        },
      })
    );
    
  } catch (error) {
    console.error('Error fetching report statistics:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        buildErrorResponse('Validation error', error.errors),
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      buildErrorResponse('Failed to fetch report statistics'),
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getReportStatistics
);
