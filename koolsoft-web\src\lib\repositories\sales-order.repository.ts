import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Order Repository
 *
 * This repository handles database operations for the Sales Order entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesOrderRepository extends PrismaRepository<
  Prisma.sales_ordersGetPayload<{
    include: {
      customer: true;
      executive: true;
    };
  }>,
  string,
  Prisma.sales_ordersCreateInput,
  Prisma.sales_ordersUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('sales_orders');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): SalesOrderRepository {
    return new SalesOrderRepository(tx);
  }

  /**
   * Find sales orders with filtering, pagination, and sorting
   * @param filters Filter criteria
   * @returns Promise resolving to paginated sales orders with metadata
   */
  async findWithFilters(filters: {
    customerId?: string;
    executiveId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    skip?: number;
    take?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      customerId,
      executiveId,
      status,
      startDate,
      endDate,
      search,
      skip = 0,
      take = 10,
      sortBy = 'orderDate',
      sortOrder = 'desc',
    } = filters;

    // Build where clause
    const where: Prisma.sales_ordersWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.orderDate = {};
      if (startDate) {
        where.orderDate.gte = startDate;
      }
      if (endDate) {
        where.orderDate.lte = endDate;
      }
    }

    if (search) {
      where.OR = [
        {
          customer: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          contactPerson: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          contactPhone: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          remarks: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    let orderBy: Prisma.sales_ordersOrderByWithRelationInput = {};
    if (sortBy === 'customer') {
      orderBy = { customer: { name: sortOrder } };
    } else if (sortBy === 'executive') {
      orderBy = { executive: { name: sortOrder } };
    } else if (sortBy === 'orderDate') {
      orderBy = { orderDate: sortOrder };
    } else if (sortBy === 'status') {
      orderBy = { status: sortOrder };
    } else if (sortBy === 'amount') {
      orderBy = { amount: sortOrder };
    } else if (sortBy === 'deliveryDate') {
      orderBy = { deliveryDate: sortOrder };
    } else if (sortBy === 'actualDeliveryDate') {
      orderBy = { actualDeliveryDate: sortOrder };
    } else {
      orderBy = { orderDate: sortOrder }; // Default fallback
    }

    // Execute queries
    const [orders, total] = await Promise.all([
      this.model.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      this.model.count({ where }),
    ]);

    return {
      data: orders,
      pagination: {
        total,
        skip,
        take,
        pages: Math.ceil(total / take),
      },
    };
  }

  /**
   * Find sales orders by customer ID
   * @param customerId Customer ID
   * @param options Query options
   * @returns Promise resolving to sales orders
   */
  async findByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { customerId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { orderDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales orders by executive ID
   * @param executiveId Executive ID
   * @param options Query options
   * @returns Promise resolving to sales orders
   */
  async findByExecutiveId(
    executiveId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { executiveId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { orderDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales orders by status
   * @param status Order status
   * @param options Query options
   * @returns Promise resolving to sales orders
   */
  async findByStatus(
    status: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { status },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { orderDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Get sales orders statistics
   * @param filters Optional filters
   * @returns Promise resolving to statistics
   */
  async getStatistics(filters?: {
    customerId?: string;
    executiveId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { customerId, executiveId, startDate, endDate } = filters || {};

    // Build where clause
    const where: Prisma.sales_ordersWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (startDate || endDate) {
      where.orderDate = {};
      if (startDate) {
        where.orderDate.gte = startDate;
      }
      if (endDate) {
        where.orderDate.lte = endDate;
      }
    }

    // Get statistics
    const [
      total,
      pendingOrders,
      confirmedOrders,
      completedOrders,
      cancelledOrders,
      totalAmount,
      averageOrderValue,
    ] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, status: 'PENDING' } }),
      this.model.count({ where: { ...where, status: 'CONFIRMED' } }),
      this.model.count({ where: { ...where, status: 'COMPLETED' } }),
      this.model.count({ where: { ...where, status: 'CANCELLED' } }),
      this.model.aggregate({
        where,
        _sum: {
          amount: true,
        },
      }),
      this.model.aggregate({
        where,
        _avg: {
          amount: true,
        },
      }),
    ]);

    return {
      total,
      pendingOrders,
      confirmedOrders,
      completedOrders,
      cancelledOrders,
      totalAmount: totalAmount._sum.amount || 0,
      averageOrderValue: averageOrderValue._avg.amount || 0,
    };
  }

  /**
   * Get overdue orders (delivery date passed but not delivered)
   * @param date Date to check for overdue orders (defaults to today)
   * @returns Promise resolving to overdue orders
   */
  async getOverdueOrders(date?: Date) {
    const checkDate = date || new Date();
    checkDate.setHours(0, 0, 0, 0); // Start of day

    return this.model.findMany({
      where: {
        deliveryDate: {
          lt: checkDate,
        },
        status: {
          notIn: ['DELIVERED', 'COMPLETED', 'CANCELLED'],
        },
        actualDeliveryDate: null,
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
      orderBy: { deliveryDate: 'asc' },
    });
  }

  /**
   * Update order status
   * @param id Order ID
   * @param status New status
   * @param actualDeliveryDate Optional actual delivery date
   * @param remarks Optional remarks
   * @returns Promise resolving to updated order
   */
  async updateStatus(id: string, status: string, actualDeliveryDate?: Date, remarks?: string) {
    const updateData: Prisma.sales_ordersUpdateInput = {
      status,
      updatedAt: new Date(),
    };

    if (actualDeliveryDate) {
      updateData.actualDeliveryDate = actualDeliveryDate;
    }

    if (remarks) {
      updateData.remarks = remarks;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }
}
