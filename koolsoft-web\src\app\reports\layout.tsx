'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { FileText } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Reports Layout Component
 *
 * This component provides a consistent layout for all reports-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function ReportsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Reports';
  if (pathname !== '/reports') {
    if (pathname === '/reports/viewer') {
      pageTitle = 'Report Viewer';
    } else if (pathname?.startsWith('/reports/viewer/')) {
      // Extract report type from URL and format it
      const reportType = pathname?.split('/').pop() || '';
      pageTitle = `${reportType.toUpperCase()} Report`;
    } else {
      // Format the report name from the URL path
      // e.g., /reports/amc-summary -> AMC Summary
      const reportName = pathname?.split('/').pop() || '';
      pageTitle = reportName.split('-').map(
        word => word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Reports', href: '/reports', icon: <FileText className="h-4 w-4" /> }
  ];

  // Add additional breadcrumbs for subpages
  if (pathname !== '/reports') {
    if (pathname === '/reports/viewer') {
      breadcrumbs.push({ label: 'Report Viewer', current: true });
    } else if (pathname?.startsWith('/reports/viewer/')) {
      breadcrumbs.push({ label: 'Report Viewer', href: '/reports/viewer' });
      breadcrumbs.push({ label: pageTitle, current: true });
    } else {
      breadcrumbs.push({ label: pageTitle, current: true });
    }
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
