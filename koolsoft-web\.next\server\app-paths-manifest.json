{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/warranties/expiring/route": "app/api/warranties/expiring/route.js", "/api/warranties/route": "app/api/warranties/route.js", "/api/reports/summary/route": "app/api/reports/summary/route.js", "/api/reports/types/route": "app/api/reports/types/route.js", "/api/users/route": "app/api/users/route.js", "/api/reports/formulas/templates/route": "app/api/reports/formulas/templates/route.js", "/api/reports/formulas/route": "app/api/reports/formulas/route.js", "/api/reports/formulas/statistics/route": "app/api/reports/formulas/statistics/route.js", "/api/reports/schedules/route": "app/api/reports/schedules/route.js", "/reports/page": "app/reports/page.js", "/reports/viewer/[type]/page": "app/reports/viewer/[type]/page.js", "/reports/viewer/page": "app/reports/viewer/page.js", "/warranties/page": "app/warranties/page.js", "/reports/formulas/page": "app/reports/formulas/page.js", "/reports/schedules/page": "app/reports/schedules/page.js", "/reports/crystal/page": "app/reports/crystal/page.js", "/reports/crystal/[reportId]/page": "app/reports/crystal/[reportId]/page.js"}