import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ScheduledReportRepository } from '@/lib/repositories/scheduled-report.repository';
import { ScheduledReportExecutionRepository } from '@/lib/repositories/scheduled-report-execution.repository';

/**
 * GET /api/reports/schedules/statistics
 * Get statistics for scheduled reports and executions
 */
async function getScheduledReportStatistics(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    
    const scheduledReportRepository = new ScheduledReportRepository();
    const executionRepository = new ScheduledReportExecutionRepository();
    
    // Get statistics
    const [reportStats, executionSummary, recentExecutions] = await Promise.all([
      scheduledReportRepository.getStatistics(),
      executionRepository.getExecutionSummary(days),
      executionRepository.getRecentExecutions(10),
    ]);
    
    return NextResponse.json({
      success: true,
      data: {
        reports: reportStats,
        executions: executionSummary,
        recentExecutions,
      },
    });
  } catch (error) {
    console.error('Error fetching scheduled report statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scheduled report statistics' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getScheduledReportStatistics
);
