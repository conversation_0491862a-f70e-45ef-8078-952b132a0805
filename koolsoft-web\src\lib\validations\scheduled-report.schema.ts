import { z } from 'zod';
import { validateCronExpression, getNextExecutionTime, CRON_PRESETS } from '@/lib/utils/cron-parser';

/**
 * Validation schemas for scheduled reports
 */

// Report types that support scheduling
export const SCHEDULABLE_REPORT_TYPES = ['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER'] as const;

// Export formats
export const EXPORT_FORMATS = ['PDF', 'EXCEL', 'CSV'] as const;

// Execution statuses
export const EXECUTION_STATUSES = ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED'] as const;

/**
 * Schema for creating a scheduled report
 */
export const createScheduledReportSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(255, 'Name must be less than 255 characters'),
  
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  
  reportType: z.enum(SCHEDULABLE_REPORT_TYPES, {
    errorMap: () => ({ message: 'Invalid report type' }),
  }),
  
  cronExpression: z.string()
    .refine(validateCronExpression, 'Invalid cron expression format'),
  
  isActive: z.boolean().default(true),
  
  parameters: z.record(z.any()).optional(),
  
  emailRecipients: z.array(z.string().email('Invalid email address'))
    .min(1, 'At least one email recipient is required')
    .max(50, 'Maximum 50 email recipients allowed'),
  
  emailSubject: z.string()
    .max(255, 'Email subject must be less than 255 characters')
    .optional(),
  
  emailBody: z.string()
    .max(5000, 'Email body must be less than 5000 characters')
    .optional(),
  
  exportFormat: z.enum(EXPORT_FORMATS).default('PDF'),
});

/**
 * Schema for updating a scheduled report
 */
export const updateScheduledReportSchema = createScheduledReportSchema.partial();

/**
 * Schema for listing scheduled reports with filters
 */
export const listScheduledReportsSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  reportType: z.enum(SCHEDULABLE_REPORT_TYPES).optional(),
  isActive: z.coerce.boolean().optional(),
  search: z.string().max(255).optional(),
  sortBy: z.enum(['name', 'reportType', 'nextRunAt', 'lastRunAt', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Schema for scheduled report execution
 */
export const scheduledReportExecutionSchema = z.object({
  scheduledReportId: z.string().uuid('Valid scheduled report ID is required'),
  status: z.enum(EXECUTION_STATUSES).default('PENDING'),
  startedAt: z.coerce.date().optional(),
  completedAt: z.coerce.date().optional(),
  errorMessage: z.string().max(2000).optional(),
  reportData: z.record(z.any()).optional(),
  filePath: z.string().max(500).optional(),
  emailsSent: z.number().int().min(0).default(0),
  emailErrors: z.array(z.string()).default([]),
  executionTime: z.number().int().min(0).optional(),
  recordCount: z.number().int().min(0).optional(),
});

/**
 * Schema for listing executions with filters
 */
export const listExecutionsSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  scheduledReportId: z.string().uuid().optional(),
  status: z.enum(EXECUTION_STATUSES).optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  sortBy: z.enum(['startedAt', 'completedAt', 'createdAt', 'executionTime']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Schema for manual execution trigger
 */
export const triggerExecutionSchema = z.object({
  scheduledReportId: z.string().uuid('Valid scheduled report ID is required'),
  sendEmail: z.boolean().default(true),
});

// Export CRON_PRESETS from cron-parser utility
export { CRON_PRESETS };

/**
 * Type definitions
 */
export type CreateScheduledReportData = z.infer<typeof createScheduledReportSchema>;
export type UpdateScheduledReportData = z.infer<typeof updateScheduledReportSchema>;
export type ListScheduledReportsParams = z.infer<typeof listScheduledReportsSchema>;
export type ScheduledReportExecutionData = z.infer<typeof scheduledReportExecutionSchema>;
export type ListExecutionsParams = z.infer<typeof listExecutionsSchema>;
export type TriggerExecutionData = z.infer<typeof triggerExecutionSchema>;
export type SchedulableReportType = typeof SCHEDULABLE_REPORT_TYPES[number];
export type ExportFormat = typeof EXPORT_FORMATS[number];
export type ExecutionStatus = typeof EXECUTION_STATUSES[number];
