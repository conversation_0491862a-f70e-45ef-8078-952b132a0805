import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Visit Card Repository
 *
 * This repository handles database operations for the Visit Card entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class VisitCardRepository extends PrismaRepository<
  Prisma.VisitCardGetPayload<{}>,
  string,
  Prisma.VisitCardCreateInput,
  Prisma.VisitCardUpdateInput
> {
  constructor() {
    super('VisitCard');
  }

  /**
   * Find all visit cards with pagination
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async findAll(skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Find visit cards by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Find visit cards by date range
   * @param startDate Start date
   * @param endDate End date
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async findByDateRange(startDate: Date, endDate: Date, skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      where: {
        // Note: visitDate property needs to be added to VisitCardWhereInput
        // visitDate: {
        //   gte: startDate,
        //   lte: endDate,
        // },
      },
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Find visit cards by user ID
   * @param userId User ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async findByUserId(userId: string, skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      // Note: userId property needs to be added to VisitCardWhereInput
      // where: { userId },
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Find visit cards by status
   * @param status Visit card status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      // Note: status property needs to be added to VisitCardWhereInput
      // where: { status },
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Search visit cards by notes or file path
   * @param search Search term
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of visit cards
   */
  async search(search: string, skip?: number, take?: number): Promise<Prisma.VisitCardGetPayload<{}>[]> {
    return this.prisma.visitCard.findMany({
      where: {
        OR: [
          {
            notes: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            filePath: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            // Note: purpose property needs to be added to VisitCardWhereInput
            // purpose: {
            //   contains: search,
            //   mode: 'insensitive',
            // },
          },
        ],
      },
      skip,
      take,
      orderBy: { uploadDate: 'desc' },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Count visit cards matching search criteria
   * @param search Search term
   * @returns Promise resolving to the count of matching visit cards
   */
  async countSearch(search: string): Promise<number> {
    return this.prisma.visitCard.count({
      where: {
        OR: [
          {
            notes: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            filePath: {
              contains: search,
              mode: 'insensitive',
            },
          },
          {
            // Note: purpose property needs to be added to VisitCardWhereInput
            // purpose: {
            //   contains: search,
            //   mode: 'insensitive',
            // },
          },
        ],
      },
    });
  }

  /**
   * Find visit card with all related data
   * @param id Visit card ID
   * @returns Promise resolving to the visit card with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.prisma.visitCard.findUnique({
      where: { id },
      include: {
        customer: true,
      },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  /**
   * Count visit cards by filter
   * @param filter Filter condition
   * @returns Promise resolving to the count of visit cards
   */
  async count(filter?: any): Promise<number> {
    return this.prisma.visitCard.count({
      where: filter,
    });
  }

  /**
   * Create a new visit card
   * @param data Visit card data
   * @returns Promise resolving to the created visit card
   */
  async create(data: Prisma.VisitCardCreateInput): Promise<Prisma.VisitCardGetPayload<{}>> {
    return this.prisma.visitCard.create({
      data,
    });
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.VisitCardGetPayload<{}>,
    string,
    Prisma.VisitCardCreateInput,
    Prisma.VisitCardUpdateInput
  > {
    const repo = new VisitCardRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
