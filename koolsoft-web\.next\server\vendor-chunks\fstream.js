/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fstream";
exports.ids = ["vendor-chunks/fstream"];
exports.modules = {

/***/ "(rsc)/./node_modules/fstream/fstream.js":
/*!*****************************************!*\
  !*** ./node_modules/fstream/fstream.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Abstract = __webpack_require__(/*! ./lib/abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\");\nexports.Reader = __webpack_require__(/*! ./lib/reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\nexports.Writer = __webpack_require__(/*! ./lib/writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\");\nexports.File = {\n  Reader: __webpack_require__(/*! ./lib/file-reader.js */ \"(rsc)/./node_modules/fstream/lib/file-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/file-writer.js */ \"(rsc)/./node_modules/fstream/lib/file-writer.js\")\n};\nexports.Dir = {\n  Reader: __webpack_require__(/*! ./lib/dir-reader.js */ \"(rsc)/./node_modules/fstream/lib/dir-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/dir-writer.js */ \"(rsc)/./node_modules/fstream/lib/dir-writer.js\")\n};\nexports.Link = {\n  Reader: __webpack_require__(/*! ./lib/link-reader.js */ \"(rsc)/./node_modules/fstream/lib/link-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/link-writer.js */ \"(rsc)/./node_modules/fstream/lib/link-writer.js\")\n};\nexports.Proxy = {\n  Reader: __webpack_require__(/*! ./lib/proxy-reader.js */ \"(rsc)/./node_modules/fstream/lib/proxy-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/proxy-writer.js */ \"(rsc)/./node_modules/fstream/lib/proxy-writer.js\")\n};\nexports.Reader.Dir = exports.DirReader = exports.Dir.Reader;\nexports.Reader.File = exports.FileReader = exports.File.Reader;\nexports.Reader.Link = exports.LinkReader = exports.Link.Reader;\nexports.Reader.Proxy = exports.ProxyReader = exports.Proxy.Reader;\nexports.Writer.Dir = exports.DirWriter = exports.Dir.Writer;\nexports.Writer.File = exports.FileWriter = exports.File.Writer;\nexports.Writer.Link = exports.LinkWriter = exports.Link.Writer;\nexports.Writer.Proxy = exports.ProxyWriter = exports.Proxy.Writer;\nexports.collect = __webpack_require__(/*! ./lib/collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/fstream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/abstract.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/abstract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// the parent class for all fstreams.\n\nmodule.exports = Abstract;\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nfunction Abstract() {\n  Stream.call(this);\n}\ninherits(Abstract, Stream);\nAbstract.prototype.on = function (ev, fn) {\n  if (ev === 'ready' && this.ready) {\n    process.nextTick(fn.bind(this));\n  } else {\n    Stream.prototype.on.call(this, ev, fn);\n  }\n  return this;\n};\nAbstract.prototype.abort = function () {\n  this._aborted = true;\n  this.emit('abort');\n};\nAbstract.prototype.destroy = function () {};\nAbstract.prototype.warn = function (msg, code) {\n  var self = this;\n  var er = decorate(msg, code, self);\n  if (!self.listeners('warn')) {\n    console.error('%s %s\\n' + 'path = %s\\n' + 'syscall = %s\\n' + 'fstream_type = %s\\n' + 'fstream_path = %s\\n' + 'fstream_unc_path = %s\\n' + 'fstream_class = %s\\n' + 'fstream_stack =\\n%s\\n', code || 'UNKNOWN', er.stack, er.path, er.syscall, er.fstream_type, er.fstream_path, er.fstream_unc_path, er.fstream_class, er.fstream_stack.join('\\n'));\n  } else {\n    self.emit('warn', er);\n  }\n};\nAbstract.prototype.info = function (msg, code) {\n  this.emit('info', msg, code);\n};\nAbstract.prototype.error = function (msg, code, th) {\n  var er = decorate(msg, code, this);\n  if (th) throw er;else this.emit('error', er);\n};\nfunction decorate(er, code, self) {\n  if (!(er instanceof Error)) er = new Error(er);\n  er.code = er.code || code;\n  er.path = er.path || self.path;\n  er.fstream_type = er.fstream_type || self.type;\n  er.fstream_path = er.fstream_path || self.path;\n  if (self._path !== self.path) {\n    er.fstream_unc_path = er.fstream_unc_path || self._path;\n  }\n  if (self.linkpath) {\n    er.fstream_linkpath = er.fstream_linkpath || self.linkpath;\n  }\n  er.fstream_class = er.fstream_class || self.constructor.name;\n  er.fstream_stack = er.fstream_stack || new Error().stack.split(/\\n/).slice(3).map(function (s) {\n    return s.replace(/^ {4}at /, '');\n  });\n  return er;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/abstract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/collect.js":
/*!*********************************************!*\
  !*** ./node_modules/fstream/lib/collect.js ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = collect;\nfunction collect(stream) {\n  if (stream._collected) return;\n  if (stream._paused) return stream.on('resume', collect.bind(null, stream));\n  stream._collected = true;\n  stream.pause();\n  stream.on('data', save);\n  stream.on('end', save);\n  var buf = [];\n  function save(b) {\n    if (typeof b === 'string') b = new Buffer(b);\n    if (Buffer.isBuffer(b) && !b.length) return;\n    buf.push(b);\n  }\n  stream.on('entry', saveEntry);\n  var entryBuffer = [];\n  function saveEntry(e) {\n    collect(e);\n    entryBuffer.push(e);\n  }\n  stream.on('proxy', proxyPause);\n  function proxyPause(p) {\n    p.pause();\n  }\n\n  // replace the pipe method with a new version that will\n  // unlock the buffered stuff.  if you just call .pipe()\n  // without a destination, then it'll re-play the events.\n  stream.pipe = function (orig) {\n    return function (dest) {\n      // console.error(' === open the pipes', dest && dest.path)\n\n      // let the entries flow through one at a time.\n      // Once they're all done, then we can resume completely.\n      var e = 0;\n      (function unblockEntry() {\n        var entry = entryBuffer[e++];\n        // console.error(\" ==== unblock entry\", entry && entry.path)\n        if (!entry) return resume();\n        entry.on('end', unblockEntry);\n        if (dest) dest.add(entry);else stream.emit('entry', entry);\n      })();\n      function resume() {\n        stream.removeListener('entry', saveEntry);\n        stream.removeListener('data', save);\n        stream.removeListener('end', save);\n        stream.pipe = orig;\n        if (dest) stream.pipe(dest);\n        buf.forEach(function (b) {\n          if (b) stream.emit('data', b);else stream.emit('end');\n        });\n        stream.resume();\n      }\n      return dest;\n    };\n  }(stream.pipe);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/collect.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/dir-reader.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-reader.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// A thing that emits \"entry\" events with Reader objects\n// Pausing it causes it to stop emitting entry events, and also\n// pauses the current entry if there is one.\n\nmodule.exports = DirReader;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\nvar assert = (__webpack_require__(/*! assert */ \"assert\").ok);\ninherits(DirReader, Reader);\nfunction DirReader(props) {\n  var self = this;\n  if (!(self instanceof DirReader)) {\n    throw new Error('DirReader must be called as constructor.');\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    throw new Error('Non-directory type ' + props.type);\n  }\n  self.entries = null;\n  self._index = -1;\n  self._paused = false;\n  self._length = -1;\n  if (props.sort) {\n    this.sort = props.sort;\n  }\n  Reader.call(this, props);\n}\nDirReader.prototype._getEntries = function () {\n  var self = this;\n\n  // race condition.  might pause() before calling _getEntries,\n  // and then resume, and try to get them a second time.\n  if (self._gotEntries) return;\n  self._gotEntries = true;\n  fs.readdir(self._path, function (er, entries) {\n    if (er) return self.error(er);\n    self.entries = entries;\n    self.emit('entries', entries);\n    if (self._paused) self.once('resume', processEntries);else processEntries();\n    function processEntries() {\n      self._length = self.entries.length;\n      if (typeof self.sort === 'function') {\n        self.entries = self.entries.sort(self.sort.bind(self));\n      }\n      self._read();\n    }\n  });\n};\n\n// start walking the dir, and emit an \"entry\" event for each one.\nDirReader.prototype._read = function () {\n  var self = this;\n  if (!self.entries) return self._getEntries();\n  if (self._paused || self._currentEntry || self._aborted) {\n    // console.error('DR paused=%j, current=%j, aborted=%j', self._paused, !!self._currentEntry, self._aborted)\n    return;\n  }\n  self._index++;\n  if (self._index >= self.entries.length) {\n    if (!self._ended) {\n      self._ended = true;\n      self.emit('end');\n      self.emit('close');\n    }\n    return;\n  }\n\n  // ok, handle this one, then.\n\n  // save creating a proxy, by stat'ing the thing now.\n  var p = path.resolve(self._path, self.entries[self._index]);\n  assert(p !== self._path);\n  assert(self.entries[self._index]);\n\n  // set this to prevent trying to _read() again in the stat time.\n  self._currentEntry = p;\n  fs[self.props.follow ? 'stat' : 'lstat'](p, function (er, stat) {\n    if (er) return self.error(er);\n    var who = self._proxy || self;\n    stat.path = p;\n    stat.basename = path.basename(p);\n    stat.dirname = path.dirname(p);\n    var childProps = self.getChildProps.call(who, stat);\n    childProps.path = p;\n    childProps.basename = path.basename(p);\n    childProps.dirname = path.dirname(p);\n    var entry = Reader(childProps, stat);\n\n    // console.error(\"DR Entry\", p, stat.size)\n\n    self._currentEntry = entry;\n\n    // \"entry\" events are for direct entries in a specific dir.\n    // \"child\" events are for any and all children at all levels.\n    // This nomenclature is not completely final.\n\n    entry.on('pause', function (who) {\n      if (!self._paused && !entry._disowned) {\n        self.pause(who);\n      }\n    });\n    entry.on('resume', function (who) {\n      if (self._paused && !entry._disowned) {\n        self.resume(who);\n      }\n    });\n    entry.on('stat', function (props) {\n      self.emit('_entryStat', entry, props);\n      if (entry._aborted) return;\n      if (entry._paused) {\n        entry.once('resume', function () {\n          self.emit('entryStat', entry, props);\n        });\n      } else self.emit('entryStat', entry, props);\n    });\n    entry.on('ready', function EMITCHILD() {\n      // console.error(\"DR emit child\", entry._path)\n      if (self._paused) {\n        // console.error(\"  DR emit child - try again later\")\n        // pause the child, and emit the \"entry\" event once we drain.\n        // console.error(\"DR pausing child entry\")\n        entry.pause(self);\n        return self.once('resume', EMITCHILD);\n      }\n\n      // skip over sockets.  they can't be piped around properly,\n      // so there's really no sense even acknowledging them.\n      // if someone really wants to see them, they can listen to\n      // the \"socket\" events.\n      if (entry.type === 'Socket') {\n        self.emit('socket', entry);\n      } else {\n        self.emitEntry(entry);\n      }\n    });\n    var ended = false;\n    entry.on('close', onend);\n    entry.on('disown', onend);\n    function onend() {\n      if (ended) return;\n      ended = true;\n      self.emit('childEnd', entry);\n      self.emit('entryEnd', entry);\n      self._currentEntry = null;\n      if (!self._paused) {\n        self._read();\n      }\n    }\n\n    // XXX Remove this.  Works in node as of 0.6.2 or so.\n    // Long filenames should not break stuff.\n    entry.on('error', function (er) {\n      if (entry._swallowErrors) {\n        self.warn(er);\n        entry.emit('end');\n        entry.emit('close');\n      } else {\n        self.emit('error', er);\n      }\n    })\n\n    // proxy up some events.\n    ;\n    ['child', 'childEnd', 'warn'].forEach(function (ev) {\n      entry.on(ev, self.emit.bind(self, ev));\n    });\n  });\n};\nDirReader.prototype.disown = function (entry) {\n  entry.emit('beforeDisown');\n  entry._disowned = true;\n  entry.parent = entry.root = null;\n  if (entry === this._currentEntry) {\n    this._currentEntry = null;\n  }\n  entry.emit('disown');\n};\nDirReader.prototype.getChildProps = function () {\n  return {\n    depth: this.depth + 1,\n    root: this.root || this,\n    parent: this,\n    follow: this.follow,\n    filter: this.filter,\n    sort: this.props.sort,\n    hardlinks: this.props.hardlinks\n  };\n};\nDirReader.prototype.pause = function (who) {\n  var self = this;\n  if (self._paused) return;\n  who = who || self;\n  self._paused = true;\n  if (self._currentEntry && self._currentEntry.pause) {\n    self._currentEntry.pause(who);\n  }\n  self.emit('pause', who);\n};\nDirReader.prototype.resume = function (who) {\n  var self = this;\n  if (!self._paused) return;\n  who = who || self;\n  self._paused = false;\n  // console.error('DR Emit Resume', self._path)\n  self.emit('resume', who);\n  if (self._paused) {\n    // console.error('DR Re-paused', self._path)\n    return;\n  }\n  if (self._currentEntry) {\n    if (self._currentEntry.resume) self._currentEntry.resume(who);\n  } else self._read();\n};\nDirReader.prototype.emitEntry = function (entry) {\n  this.emit('entry', entry);\n  this.emit('child', entry);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/dir-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/dir-writer.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-writer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// It is expected that, when .add() returns false, the consumer\n// of the DirWriter will pause until a \"drain\" event occurs. Note\n// that this is *almost always going to be the case*, unless the\n// thing being written is some sort of unsupported type, and thus\n// skipped over.\n\nmodule.exports = DirWriter;\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(rsc)/./node_modules/mkdirp/index.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar collect = __webpack_require__(/*! ./collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\");\ninherits(DirWriter, Writer);\nfunction DirWriter(props) {\n  var self = this;\n  if (!(self instanceof DirWriter)) {\n    self.error('DirWriter must be called as constructor.', null, true);\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    self.error('Non-directory type ' + props.type + ' ' + JSON.stringify(props), null, true);\n  }\n  Writer.call(this, props);\n}\nDirWriter.prototype._create = function () {\n  var self = this;\n  mkdir(self._path, Writer.dirmode, function (er) {\n    if (er) return self.error(er);\n    // ready to start getting entries!\n    self.ready = true;\n    self.emit('ready');\n    self._process();\n  });\n};\n\n// a DirWriter has an add(entry) method, but its .write() doesn't\n// do anything.  Why a no-op rather than a throw?  Because this\n// leaves open the door for writing directory metadata for\n// gnu/solaris style dumpdirs.\nDirWriter.prototype.write = function () {\n  return true;\n};\nDirWriter.prototype.end = function () {\n  this._ended = true;\n  this._process();\n};\nDirWriter.prototype.add = function (entry) {\n  var self = this;\n\n  // console.error('\\tadd', entry._path, '->', self._path)\n  collect(entry);\n  if (!self.ready || self._currentEntry) {\n    self._buffer.push(entry);\n    return false;\n  }\n\n  // create a new writer, and pipe the incoming entry into it.\n  if (self._ended) {\n    return self.error('add after end');\n  }\n  self._buffer.push(entry);\n  self._process();\n  return this._buffer.length === 0;\n};\nDirWriter.prototype._process = function () {\n  var self = this;\n\n  // console.error('DW Process p=%j', self._processing, self.basename)\n\n  if (self._processing) return;\n  var entry = self._buffer.shift();\n  if (!entry) {\n    // console.error(\"DW Drain\")\n    self.emit('drain');\n    if (self._ended) self._finish();\n    return;\n  }\n  self._processing = true;\n  // console.error(\"DW Entry\", entry._path)\n\n  self.emit('entry', entry);\n\n  // ok, add this entry\n  //\n  // don't allow recursive copying\n  var p = entry;\n  var pp;\n  do {\n    pp = p._path || p.path;\n    if (pp === self.root._path || pp === self._path || pp && pp.indexOf(self._path) === 0) {\n      // console.error('DW Exit (recursive)', entry.basename, self._path)\n      self._processing = false;\n      if (entry._collected) entry.pipe();\n      return self._process();\n    }\n    p = p.parent;\n  } while (p);\n\n  // console.error(\"DW not recursive\")\n\n  // chop off the entry's root dir, replace with ours\n  var props = {\n    parent: self,\n    root: self.root || self,\n    type: entry.type,\n    depth: self.depth + 1\n  };\n  pp = entry._path || entry.path || entry.props.path;\n  if (entry.parent) {\n    pp = pp.substr(entry.parent._path.length + 1);\n  }\n  // get rid of any ../../ shenanigans\n  props.path = path.join(self.path, path.join('/', pp));\n\n  // if i have a filter, the child should inherit it.\n  props.filter = self.filter;\n\n  // all the rest of the stuff, copy over from the source.\n  Object.keys(entry.props).forEach(function (k) {\n    if (!props.hasOwnProperty(k)) {\n      props[k] = entry.props[k];\n    }\n  });\n\n  // not sure at this point what kind of writer this is.\n  var child = self._currentChild = new Writer(props);\n  child.on('ready', function () {\n    // console.error(\"DW Child Ready\", child.type, child._path)\n    // console.error(\"  resuming\", entry._path)\n    entry.pipe(child);\n    entry.resume();\n  });\n\n  // XXX Make this work in node.\n  // Long filenames should not break stuff.\n  child.on('error', function (er) {\n    if (child._swallowErrors) {\n      self.warn(er);\n      child.emit('end');\n      child.emit('close');\n    } else {\n      self.emit('error', er);\n    }\n  });\n\n  // we fire _end internally *after* end, so that we don't move on\n  // until any \"end\" listeners have had their chance to do stuff.\n  child.on('close', onend);\n  var ended = false;\n  function onend() {\n    if (ended) return;\n    ended = true;\n    // console.error(\"* DW Child end\", child.basename)\n    self._currentChild = null;\n    self._processing = false;\n    self._process();\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/dir-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/file-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// Basically just a wrapper around an fs.ReadStream\n\nmodule.exports = FileReader;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\nvar EOF = {\n  EOF: true\n};\nvar CLOSE = {\n  CLOSE: true\n};\ninherits(FileReader, Reader);\nfunction FileReader(props) {\n  // console.error(\"    FR create\", props.path, props.size, new Error().stack)\n  var self = this;\n  if (!(self instanceof FileReader)) {\n    throw new Error('FileReader must be called as constructor.');\n  }\n\n  // should already be established as a File type\n  // XXX Todo: preserve hardlinks by tracking dev+inode+nlink,\n  // with a HardLinkReader class.\n  if (!(props.type === 'Link' && props.Link || props.type === 'File' && props.File)) {\n    throw new Error('Non-file type ' + props.type);\n  }\n  self._buffer = [];\n  self._bytesEmitted = 0;\n  Reader.call(self, props);\n}\nFileReader.prototype._getStream = function () {\n  var self = this;\n  var stream = self._stream = fs.createReadStream(self._path, self.props);\n  if (self.props.blksize) {\n    stream.bufferSize = self.props.blksize;\n  }\n  stream.on('open', self.emit.bind(self, 'open'));\n  stream.on('data', function (c) {\n    // console.error('\\t\\t%d %s', c.length, self.basename)\n    self._bytesEmitted += c.length;\n    // no point saving empty chunks\n    if (!c.length) {\n      return;\n    } else if (self._paused || self._buffer.length) {\n      self._buffer.push(c);\n      self._read();\n    } else self.emit('data', c);\n  });\n  stream.on('end', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering End', self._path)\n      self._buffer.push(EOF);\n      self._read();\n    } else {\n      self.emit('end');\n    }\n    if (self._bytesEmitted !== self.props.size) {\n      self.error(\"Didn't get expected byte count\\n\" + 'expect: ' + self.props.size + '\\n' + 'actual: ' + self._bytesEmitted);\n    }\n  });\n  stream.on('close', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering Close', self._path)\n      self._buffer.push(CLOSE);\n      self._read();\n    } else {\n      // console.error('FR close 1', self._path)\n      self.emit('close');\n    }\n  });\n  stream.on('error', function (e) {\n    self.emit('error', e);\n  });\n  self._read();\n};\nFileReader.prototype._read = function () {\n  var self = this;\n  // console.error('FR _read', self._path)\n  if (self._paused) {\n    // console.error('FR _read paused', self._path)\n    return;\n  }\n  if (!self._stream) {\n    // console.error('FR _getStream calling', self._path)\n    return self._getStream();\n  }\n\n  // clear out the buffer, if there is one.\n  if (self._buffer.length) {\n    // console.error('FR _read has buffer', self._buffer.length, self._path)\n    var buf = self._buffer;\n    for (var i = 0, l = buf.length; i < l; i++) {\n      var c = buf[i];\n      if (c === EOF) {\n        // console.error('FR Read emitting buffered end', self._path)\n        self.emit('end');\n      } else if (c === CLOSE) {\n        // console.error('FR Read emitting buffered close', self._path)\n        self.emit('close');\n      } else {\n        // console.error('FR Read emitting buffered data', self._path)\n        self.emit('data', c);\n      }\n      if (self._paused) {\n        // console.error('FR Read Re-pausing at '+i, self._path)\n        self._buffer = buf.slice(i);\n        return;\n      }\n    }\n    self._buffer.length = 0;\n  }\n  // console.error(\"FR _read done\")\n  // that's about all there is to it.\n};\n\nFileReader.prototype.pause = function (who) {\n  var self = this;\n  // console.error('FR Pause', self._path)\n  if (self._paused) return;\n  who = who || self;\n  self._paused = true;\n  if (self._stream) self._stream.pause();\n  self.emit('pause', who);\n};\nFileReader.prototype.resume = function (who) {\n  var self = this;\n  // console.error('FR Resume', self._path)\n  if (!self._paused) return;\n  who = who || self;\n  self.emit('resume', who);\n  self._paused = false;\n  if (self._stream) self._stream.resume();\n  self._read();\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/file-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/file-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = FileWriter;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar EOF = {};\ninherits(FileWriter, Writer);\nfunction FileWriter(props) {\n  var self = this;\n  if (!(self instanceof FileWriter)) {\n    throw new Error('FileWriter must be called as constructor.');\n  }\n\n  // should already be established as a File type\n  if (props.type !== 'File' || !props.File) {\n    throw new Error('Non-file type ' + props.type);\n  }\n  self._buffer = [];\n  self._bytesWritten = 0;\n  Writer.call(this, props);\n}\nFileWriter.prototype._create = function () {\n  var self = this;\n  if (self._stream) return;\n  var so = {};\n  if (self.props.flags) so.flags = self.props.flags;\n  so.mode = Writer.filemode;\n  if (self._old && self._old.blksize) so.bufferSize = self._old.blksize;\n  self._stream = fs.createWriteStream(self._path, so);\n  self._stream.on('open', function () {\n    // console.error(\"FW open\", self._buffer, self._path)\n    self.ready = true;\n    self._buffer.forEach(function (c) {\n      if (c === EOF) self._stream.end();else self._stream.write(c);\n    });\n    self.emit('ready');\n    // give this a kick just in case it needs it.\n    self.emit('drain');\n  });\n  self._stream.on('error', function (er) {\n    self.emit('error', er);\n  });\n  self._stream.on('drain', function () {\n    self.emit('drain');\n  });\n  self._stream.on('close', function () {\n    // console.error('\\n\\nFW Stream Close', self._path, self.size)\n    self._finish();\n  });\n};\nFileWriter.prototype.write = function (c) {\n  var self = this;\n  self._bytesWritten += c.length;\n  if (!self.ready) {\n    if (!Buffer.isBuffer(c) && typeof c !== 'string') {\n      throw new Error('invalid write data');\n    }\n    self._buffer.push(c);\n    return false;\n  }\n  var ret = self._stream.write(c);\n  // console.error('\\t-- fw wrote, _stream says', ret, self._stream._queue.length)\n\n  // allow 2 buffered writes, because otherwise there's just too\n  // much stop and go bs.\n  if (ret === false && self._stream._queue) {\n    return self._stream._queue.length <= 2;\n  } else {\n    return ret;\n  }\n};\nFileWriter.prototype.end = function (c) {\n  var self = this;\n  if (c) self.write(c);\n  if (!self.ready) {\n    self._buffer.push(EOF);\n    return false;\n  }\n  return self._stream.end();\n};\nFileWriter.prototype._finish = function () {\n  var self = this;\n  if (typeof self.size === 'number' && self._bytesWritten !== self.size) {\n    self.error('Did not get expected byte count.\\n' + 'expect: ' + self.size + '\\n' + 'actual: ' + self._bytesWritten);\n  }\n  Writer.prototype._finish.call(self);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/file-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/get-type.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/get-type.js ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = getType;\nfunction getType(st) {\n  var types = ['Directory', 'File', 'SymbolicLink', 'Link',\n  // special for hardlinks from tarballs\n  'BlockDevice', 'CharacterDevice', 'FIFO', 'Socket'];\n  var type;\n  if (st.type && types.indexOf(st.type) !== -1) {\n    st[st.type] = true;\n    return st.type;\n  }\n  for (var i = 0, l = types.length; i < l; i++) {\n    type = types[i];\n    var is = st[type] || st['is' + type];\n    if (typeof is === 'function') is = is.call(st);\n    if (is) {\n      st[type] = true;\n      st.type = type;\n      return type;\n    }\n  }\n  return null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/get-type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/link-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// Basically just a wrapper around an fs.readlink\n//\n// XXX: Enhance this to support the Link type, by keeping\n// a lookup table of {<dev+inode>:<path>}, so that hardlinks\n// can be preserved in tarballs.\n\nmodule.exports = LinkReader;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\ninherits(LinkReader, Reader);\nfunction LinkReader(props) {\n  var self = this;\n  if (!(self instanceof LinkReader)) {\n    throw new Error('LinkReader must be called as constructor.');\n  }\n  if (!(props.type === 'Link' && props.Link || props.type === 'SymbolicLink' && props.SymbolicLink)) {\n    throw new Error('Non-link type ' + props.type);\n  }\n  Reader.call(self, props);\n}\n\n// When piping a LinkReader into a LinkWriter, we have to\n// already have the linkpath property set, so that has to\n// happen *before* the \"ready\" event, which means we need to\n// override the _stat method.\nLinkReader.prototype._stat = function (currentStat) {\n  var self = this;\n  fs.readlink(self._path, function (er, linkpath) {\n    if (er) return self.error(er);\n    self.linkpath = self.props.linkpath = linkpath;\n    self.emit('linkpath', linkpath);\n    Reader.prototype._stat.call(self, currentStat);\n  });\n};\nLinkReader.prototype._read = function () {\n  var self = this;\n  if (self._paused) return;\n  // basically just a no-op, since we got all the info we need\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end');\n    self.emit('close');\n    self._ended = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvbGluay1yZWFkZXIuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBQSxNQUFNLENBQUNDLE9BQU8sR0FBR0MsVUFBVTtBQUUzQixJQUFJQyxFQUFFLEdBQUdDLG1CQUFPLENBQUMsb0VBQWEsQ0FBQztBQUMvQixJQUFJQyxRQUFRLEdBQUdELG1CQUFPLENBQUMsMkRBQVUsQ0FBQztBQUNsQyxJQUFJRSxNQUFNLEdBQUdGLG1CQUFPLENBQUMsK0RBQWEsQ0FBQztBQUVuQ0MsUUFBUSxDQUFDSCxVQUFVLEVBQUVJLE1BQU0sQ0FBQztBQUU1QixTQUFTSixVQUFVQSxDQUFFSyxLQUFLLEVBQUU7RUFDMUIsSUFBSUMsSUFBSSxHQUFHLElBQUk7RUFDZixJQUFJLEVBQUVBLElBQUksWUFBWU4sVUFBVSxDQUFDLEVBQUU7SUFDakMsTUFBTSxJQUFJTyxLQUFLLENBQUMsMkNBQTJDLENBQUM7RUFDOUQ7RUFFQSxJQUFJLEVBQUdGLEtBQUssQ0FBQ0csSUFBSSxLQUFLLE1BQU0sSUFBSUgsS0FBSyxDQUFDSSxJQUFJLElBQ3ZDSixLQUFLLENBQUNHLElBQUksS0FBSyxjQUFjLElBQUlILEtBQUssQ0FBQ0ssWUFBYSxDQUFDLEVBQUU7SUFDeEQsTUFBTSxJQUFJSCxLQUFLLENBQUMsZ0JBQWdCLEdBQUdGLEtBQUssQ0FBQ0csSUFBSSxDQUFDO0VBQ2hEO0VBRUFKLE1BQU0sQ0FBQ08sSUFBSSxDQUFDTCxJQUFJLEVBQUVELEtBQUssQ0FBQztBQUMxQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBTCxVQUFVLENBQUNZLFNBQVMsQ0FBQ0MsS0FBSyxHQUFHLFVBQVVDLFdBQVcsRUFBRTtFQUNsRCxJQUFJUixJQUFJLEdBQUcsSUFBSTtFQUNmTCxFQUFFLENBQUNjLFFBQVEsQ0FBQ1QsSUFBSSxDQUFDVSxLQUFLLEVBQUUsVUFBVUMsRUFBRSxFQUFFQyxRQUFRLEVBQUU7SUFDOUMsSUFBSUQsRUFBRSxFQUFFLE9BQU9YLElBQUksQ0FBQ2EsS0FBSyxDQUFDRixFQUFFLENBQUM7SUFDN0JYLElBQUksQ0FBQ1ksUUFBUSxHQUFHWixJQUFJLENBQUNELEtBQUssQ0FBQ2EsUUFBUSxHQUFHQSxRQUFRO0lBQzlDWixJQUFJLENBQUNjLElBQUksQ0FBQyxVQUFVLEVBQUVGLFFBQVEsQ0FBQztJQUMvQmQsTUFBTSxDQUFDUSxTQUFTLENBQUNDLEtBQUssQ0FBQ0YsSUFBSSxDQUFDTCxJQUFJLEVBQUVRLFdBQVcsQ0FBQztFQUNoRCxDQUFDLENBQUM7QUFDSixDQUFDO0FBRURkLFVBQVUsQ0FBQ1ksU0FBUyxDQUFDUyxLQUFLLEdBQUcsWUFBWTtFQUN2QyxJQUFJZixJQUFJLEdBQUcsSUFBSTtFQUNmLElBQUlBLElBQUksQ0FBQ2dCLE9BQU8sRUFBRTtFQUNsQjtFQUNBO0VBQ0EsSUFBSSxDQUFDaEIsSUFBSSxDQUFDaUIsTUFBTSxFQUFFO0lBQ2hCakIsSUFBSSxDQUFDYyxJQUFJLENBQUMsS0FBSyxDQUFDO0lBQ2hCZCxJQUFJLENBQUNjLElBQUksQ0FBQyxPQUFPLENBQUM7SUFDbEJkLElBQUksQ0FBQ2lCLE1BQU0sR0FBRyxJQUFJO0VBQ3BCO0FBQ0YsQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGZzdHJlYW1cXGxpYlxcbGluay1yZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQmFzaWNhbGx5IGp1c3QgYSB3cmFwcGVyIGFyb3VuZCBhbiBmcy5yZWFkbGlua1xuLy9cbi8vIFhYWDogRW5oYW5jZSB0aGlzIHRvIHN1cHBvcnQgdGhlIExpbmsgdHlwZSwgYnkga2VlcGluZ1xuLy8gYSBsb29rdXAgdGFibGUgb2YgezxkZXYraW5vZGU+OjxwYXRoPn0sIHNvIHRoYXQgaGFyZGxpbmtzXG4vLyBjYW4gYmUgcHJlc2VydmVkIGluIHRhcmJhbGxzLlxuXG5tb2R1bGUuZXhwb3J0cyA9IExpbmtSZWFkZXJcblxudmFyIGZzID0gcmVxdWlyZSgnZ3JhY2VmdWwtZnMnKVxudmFyIGluaGVyaXRzID0gcmVxdWlyZSgnaW5oZXJpdHMnKVxudmFyIFJlYWRlciA9IHJlcXVpcmUoJy4vcmVhZGVyLmpzJylcblxuaW5oZXJpdHMoTGlua1JlYWRlciwgUmVhZGVyKVxuXG5mdW5jdGlvbiBMaW5rUmVhZGVyIChwcm9wcykge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgaWYgKCEoc2VsZiBpbnN0YW5jZW9mIExpbmtSZWFkZXIpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdMaW5rUmVhZGVyIG11c3QgYmUgY2FsbGVkIGFzIGNvbnN0cnVjdG9yLicpXG4gIH1cblxuICBpZiAoISgocHJvcHMudHlwZSA9PT0gJ0xpbmsnICYmIHByb3BzLkxpbmspIHx8XG4gICAgKHByb3BzLnR5cGUgPT09ICdTeW1ib2xpY0xpbmsnICYmIHByb3BzLlN5bWJvbGljTGluaykpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdOb24tbGluayB0eXBlICcgKyBwcm9wcy50eXBlKVxuICB9XG5cbiAgUmVhZGVyLmNhbGwoc2VsZiwgcHJvcHMpXG59XG5cbi8vIFdoZW4gcGlwaW5nIGEgTGlua1JlYWRlciBpbnRvIGEgTGlua1dyaXRlciwgd2UgaGF2ZSB0b1xuLy8gYWxyZWFkeSBoYXZlIHRoZSBsaW5rcGF0aCBwcm9wZXJ0eSBzZXQsIHNvIHRoYXQgaGFzIHRvXG4vLyBoYXBwZW4gKmJlZm9yZSogdGhlIFwicmVhZHlcIiBldmVudCwgd2hpY2ggbWVhbnMgd2UgbmVlZCB0b1xuLy8gb3ZlcnJpZGUgdGhlIF9zdGF0IG1ldGhvZC5cbkxpbmtSZWFkZXIucHJvdG90eXBlLl9zdGF0ID0gZnVuY3Rpb24gKGN1cnJlbnRTdGF0KSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBmcy5yZWFkbGluayhzZWxmLl9wYXRoLCBmdW5jdGlvbiAoZXIsIGxpbmtwYXRoKSB7XG4gICAgaWYgKGVyKSByZXR1cm4gc2VsZi5lcnJvcihlcilcbiAgICBzZWxmLmxpbmtwYXRoID0gc2VsZi5wcm9wcy5saW5rcGF0aCA9IGxpbmtwYXRoXG4gICAgc2VsZi5lbWl0KCdsaW5rcGF0aCcsIGxpbmtwYXRoKVxuICAgIFJlYWRlci5wcm90b3R5cGUuX3N0YXQuY2FsbChzZWxmLCBjdXJyZW50U3RhdClcbiAgfSlcbn1cblxuTGlua1JlYWRlci5wcm90b3R5cGUuX3JlYWQgPSBmdW5jdGlvbiAoKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoc2VsZi5fcGF1c2VkKSByZXR1cm5cbiAgLy8gYmFzaWNhbGx5IGp1c3QgYSBuby1vcCwgc2luY2Ugd2UgZ290IGFsbCB0aGUgaW5mbyB3ZSBuZWVkXG4gIC8vIGZyb20gdGhlIF9zdGF0IG1ldGhvZFxuICBpZiAoIXNlbGYuX2VuZGVkKSB7XG4gICAgc2VsZi5lbWl0KCdlbmQnKVxuICAgIHNlbGYuZW1pdCgnY2xvc2UnKVxuICAgIHNlbGYuX2VuZGVkID0gdHJ1ZVxuICB9XG59XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsIkxpbmtSZWFkZXIiLCJmcyIsInJlcXVpcmUiLCJpbmhlcml0cyIsIlJlYWRlciIsInByb3BzIiwic2VsZiIsIkVycm9yIiwidHlwZSIsIkxpbmsiLCJTeW1ib2xpY0xpbmsiLCJjYWxsIiwicHJvdG90eXBlIiwiX3N0YXQiLCJjdXJyZW50U3RhdCIsInJlYWRsaW5rIiwiX3BhdGgiLCJlciIsImxpbmtwYXRoIiwiZXJyb3IiLCJlbWl0IiwiX3JlYWQiLCJfcGF1c2VkIiwiX2VuZGVkIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/link-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/link-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = LinkWriter;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar rimraf = __webpack_require__(/*! rimraf */ \"(rsc)/./node_modules/fstream/node_modules/rimraf/rimraf.js\");\ninherits(LinkWriter, Writer);\nfunction LinkWriter(props) {\n  var self = this;\n  if (!(self instanceof LinkWriter)) {\n    throw new Error('LinkWriter must be called as constructor.');\n  }\n\n  // should already be established as a Link type\n  if (!(props.type === 'Link' && props.Link || props.type === 'SymbolicLink' && props.SymbolicLink)) {\n    throw new Error('Non-link type ' + props.type);\n  }\n  if (props.linkpath === '') props.linkpath = '.';\n  if (!props.linkpath) {\n    self.error('Need linkpath property to create ' + props.type);\n  }\n  Writer.call(this, props);\n}\nLinkWriter.prototype._create = function () {\n  // console.error(\" LW _create\")\n  var self = this;\n  var hard = self.type === 'Link' || process.platform === 'win32';\n  var link = hard ? 'link' : 'symlink';\n  var lp = hard ? path.resolve(self.dirname, self.linkpath) : self.linkpath;\n\n  // can only change the link path by clobbering\n  // For hard links, let's just assume that's always the case, since\n  // there's no good way to read them if we don't already know.\n  if (hard) return clobber(self, lp, link);\n  fs.readlink(self._path, function (er, p) {\n    // only skip creation if it's exactly the same link\n    if (p && p === lp) return finish(self);\n    clobber(self, lp, link);\n  });\n};\nfunction clobber(self, lp, link) {\n  rimraf(self._path, function (er) {\n    if (er) return self.error(er);\n    create(self, lp, link);\n  });\n}\nfunction create(self, lp, link) {\n  fs[link](lp, self._path, function (er) {\n    // if this is a hard link, and we're in the process of writing out a\n    // directory, it's very possible that the thing we're linking to\n    // doesn't exist yet (especially if it was intended as a symlink),\n    // so swallow ENOENT errors here and just soldier in.\n    // Additionally, an EPERM or EACCES can happen on win32 if it's trying\n    // to make a link to a directory.  Again, just skip it.\n    // A better solution would be to have fs.symlink be supported on\n    // windows in some nice fashion.\n    if (er) {\n      if ((er.code === 'ENOENT' || er.code === 'EACCES' || er.code === 'EPERM') && process.platform === 'win32') {\n        self.ready = true;\n        self.emit('ready');\n        self.emit('end');\n        self.emit('close');\n        self.end = self._finish = function () {};\n      } else return self.error(er);\n    }\n    finish(self);\n  });\n}\nfunction finish(self) {\n  self.ready = true;\n  self.emit('ready');\n  if (self._ended && !self._finished) self._finish();\n}\nLinkWriter.prototype.end = function () {\n  // console.error(\"LW finish in end\")\n  this._ended = true;\n  if (this.ready) {\n    this._finished = true;\n    this._finish();\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/link-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/proxy-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-reader.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// A reader for when we don't yet know what kind of thing\n// the thing is.\n\nmodule.exports = ProxyReader;\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\ninherits(ProxyReader, Reader);\nfunction ProxyReader(props) {\n  var self = this;\n  if (!(self instanceof ProxyReader)) {\n    throw new Error('ProxyReader must be called as constructor.');\n  }\n  self.props = props;\n  self._buffer = [];\n  self.ready = false;\n  Reader.call(self, props);\n}\nProxyReader.prototype._stat = function () {\n  var self = this;\n  var props = self.props;\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat';\n  fs[stat](props.path, function (er, current) {\n    var type;\n    if (er || !current) {\n      type = 'File';\n    } else {\n      type = getType(current);\n    }\n    props[type] = true;\n    props.type = self.type = type;\n    self._old = current;\n    self._addProxy(Reader(props, current));\n  });\n};\nProxyReader.prototype._addProxy = function (proxy) {\n  var self = this;\n  if (self._proxyTarget) {\n    return self.error('proxy already set');\n  }\n  self._proxyTarget = proxy;\n  proxy._proxy = self;\n  ['error', 'data', 'end', 'close', 'linkpath', 'entry', 'entryEnd', 'child', 'childEnd', 'warn', 'stat'].forEach(function (ev) {\n    // console.error('~~ proxy event', ev, self.path)\n    proxy.on(ev, self.emit.bind(self, ev));\n  });\n  self.emit('proxy', proxy);\n  proxy.on('ready', function () {\n    // console.error(\"~~ proxy is ready!\", self.path)\n    self.ready = true;\n    self.emit('ready');\n  });\n  var calls = self._buffer;\n  self._buffer.length = 0;\n  calls.forEach(function (c) {\n    proxy[c[0]].apply(proxy, c[1]);\n  });\n};\nProxyReader.prototype.pause = function () {\n  return this._proxyTarget ? this._proxyTarget.pause() : false;\n};\nProxyReader.prototype.resume = function () {\n  return this._proxyTarget ? this._proxyTarget.resume() : false;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/proxy-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/proxy-writer.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-writer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// A writer for when we don't know what kind of thing\n// the thing is.  That is, it's not explicitly set,\n// so we're going to make it whatever the thing already\n// is, or \"File\"\n//\n// Until then, collect all events.\n\nmodule.exports = ProxyWriter;\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\");\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar collect = __webpack_require__(/*! ./collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\ninherits(ProxyWriter, Writer);\nfunction ProxyWriter(props) {\n  var self = this;\n  if (!(self instanceof ProxyWriter)) {\n    throw new Error('ProxyWriter must be called as constructor.');\n  }\n  self.props = props;\n  self._needDrain = false;\n  Writer.call(self, props);\n}\nProxyWriter.prototype._stat = function () {\n  var self = this;\n  var props = self.props;\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat';\n  fs[stat](props.path, function (er, current) {\n    var type;\n    if (er || !current) {\n      type = 'File';\n    } else {\n      type = getType(current);\n    }\n    props[type] = true;\n    props.type = self.type = type;\n    self._old = current;\n    self._addProxy(Writer(props, current));\n  });\n};\nProxyWriter.prototype._addProxy = function (proxy) {\n  // console.error(\"~~ set proxy\", this.path)\n  var self = this;\n  if (self._proxy) {\n    return self.error('proxy already set');\n  }\n  self._proxy = proxy;\n  ['ready', 'error', 'close', 'pipe', 'drain', 'warn'].forEach(function (ev) {\n    proxy.on(ev, self.emit.bind(self, ev));\n  });\n  self.emit('proxy', proxy);\n  var calls = self._buffer;\n  calls.forEach(function (c) {\n    // console.error(\"~~ ~~ proxy buffered call\", c[0], c[1])\n    proxy[c[0]].apply(proxy, c[1]);\n  });\n  self._buffer.length = 0;\n  if (self._needsDrain) self.emit('drain');\n};\nProxyWriter.prototype.add = function (entry) {\n  // console.error(\"~~ proxy add\")\n  collect(entry);\n  if (!this._proxy) {\n    this._buffer.push(['add', [entry]]);\n    this._needDrain = true;\n    return false;\n  }\n  return this._proxy.add(entry);\n};\nProxyWriter.prototype.write = function (c) {\n  // console.error('~~ proxy write')\n  if (!this._proxy) {\n    this._buffer.push(['write', [c]]);\n    this._needDrain = true;\n    return false;\n  }\n  return this._proxy.write(c);\n};\nProxyWriter.prototype.end = function (c) {\n  // console.error('~~ proxy end')\n  if (!this._proxy) {\n    this._buffer.push(['end', [c]]);\n    return false;\n  }\n  return this._proxy.end(c);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/proxy-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/reader.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/reader.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = Reader;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\");\nvar hardLinks = Reader.hardLinks = {};\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\");\n\n// Must do this *before* loading the child classes\ninherits(Reader, Abstract);\nvar LinkReader = __webpack_require__(/*! ./link-reader.js */ \"(rsc)/./node_modules/fstream/lib/link-reader.js\");\nfunction Reader(props, currentStat) {\n  var self = this;\n  if (!(self instanceof Reader)) return new Reader(props, currentStat);\n  if (typeof props === 'string') {\n    props = {\n      path: props\n    };\n  }\n\n  // polymorphism.\n  // call fstream.Reader(dir) to get a DirReader object, etc.\n  // Note that, unlike in the Writer case, ProxyReader is going\n  // to be the *normal* state of affairs, since we rarely know\n  // the type of a file prior to reading it.\n\n  var type;\n  var ClassType;\n  if (props.type && typeof props.type === 'function') {\n    type = props.type;\n    ClassType = type;\n  } else {\n    type = getType(props);\n    ClassType = Reader;\n  }\n  if (currentStat && !type) {\n    type = getType(currentStat);\n    props[type] = true;\n    props.type = type;\n  }\n  switch (type) {\n    case 'Directory':\n      ClassType = __webpack_require__(/*! ./dir-reader.js */ \"(rsc)/./node_modules/fstream/lib/dir-reader.js\");\n      break;\n    case 'Link':\n    // XXX hard links are just files.\n    // However, it would be good to keep track of files' dev+inode\n    // and nlink values, and create a HardLinkReader that emits\n    // a linkpath value of the original copy, so that the tar\n    // writer can preserve them.\n    // ClassType = HardLinkReader\n    // break\n\n    case 'File':\n      ClassType = __webpack_require__(/*! ./file-reader.js */ \"(rsc)/./node_modules/fstream/lib/file-reader.js\");\n      break;\n    case 'SymbolicLink':\n      ClassType = LinkReader;\n      break;\n    case 'Socket':\n      ClassType = __webpack_require__(/*! ./socket-reader.js */ \"(rsc)/./node_modules/fstream/lib/socket-reader.js\");\n      break;\n    case null:\n      ClassType = __webpack_require__(/*! ./proxy-reader.js */ \"(rsc)/./node_modules/fstream/lib/proxy-reader.js\");\n      break;\n  }\n  if (!(self instanceof ClassType)) {\n    return new ClassType(props);\n  }\n  Abstract.call(self);\n  if (!props.path) {\n    self.error('Must provide a path', null, true);\n  }\n  self.readable = true;\n  self.writable = false;\n  self.type = type;\n  self.props = props;\n  self.depth = props.depth = props.depth || 0;\n  self.parent = props.parent || null;\n  self.root = props.root || props.parent && props.parent.root || self;\n  self._path = self.path = path.resolve(props.path);\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_');\n    if (self._path.length >= 260) {\n      // how DOES one create files on the moon?\n      // if the path has spaces in it, then UNC will fail.\n      self._swallowErrors = true;\n      // if (self._path.indexOf(\" \") === -1) {\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\');\n      // }\n    }\n  }\n\n  self.basename = props.basename = path.basename(self.path);\n  self.dirname = props.dirname = path.dirname(self.path);\n\n  // these have served their purpose, and are now just noisy clutter\n  props.parent = props.root = null;\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size;\n  self.filter = typeof props.filter === 'function' ? props.filter : null;\n  if (props.sort === 'alpha') props.sort = alphasort;\n\n  // start the ball rolling.\n  // this will stat the thing, and then call self._read()\n  // to start reading whatever it is.\n  // console.error(\"calling stat\", props.path, currentStat)\n  self._stat(currentStat);\n}\nfunction alphasort(a, b) {\n  return a === b ? 0 : a.toLowerCase() > b.toLowerCase() ? 1 : a.toLowerCase() < b.toLowerCase() ? -1 : a > b ? 1 : -1;\n}\nReader.prototype._stat = function (currentStat) {\n  var self = this;\n  var props = self.props;\n  var stat = props.follow ? 'stat' : 'lstat';\n  // console.error(\"Reader._stat\", self._path, currentStat)\n  if (currentStat) process.nextTick(statCb.bind(null, null, currentStat));else fs[stat](self._path, statCb);\n  function statCb(er, props_) {\n    // console.error(\"Reader._stat, statCb\", self._path, props_, props_.nlink)\n    if (er) return self.error(er);\n    Object.keys(props_).forEach(function (k) {\n      props[k] = props_[k];\n    });\n\n    // if it's not the expected size, then abort here.\n    if (undefined !== self.size && props.size !== self.size) {\n      return self.error('incorrect size');\n    }\n    self.size = props.size;\n    var type = getType(props);\n    var handleHardlinks = props.hardlinks !== false;\n\n    // special little thing for handling hardlinks.\n    if (handleHardlinks && type !== 'Directory' && props.nlink && props.nlink > 1) {\n      var k = props.dev + ':' + props.ino;\n      // console.error(\"Reader has nlink\", self._path, k)\n      if (hardLinks[k] === self._path || !hardLinks[k]) {\n        hardLinks[k] = self._path;\n      } else {\n        // switch into hardlink mode.\n        type = self.type = self.props.type = 'Link';\n        self.Link = self.props.Link = true;\n        self.linkpath = self.props.linkpath = hardLinks[k];\n        // console.error(\"Hardlink detected, switching mode\", self._path, self.linkpath)\n        // Setting __proto__ would arguably be the \"correct\"\n        // approach here, but that just seems too wrong.\n        self._stat = self._read = LinkReader.prototype._read;\n      }\n    }\n    if (self.type && self.type !== type) {\n      self.error('Unexpected type: ' + type);\n    }\n\n    // if the filter doesn't pass, then just skip over this one.\n    // still have to emit end so that dir-walking can move on.\n    if (self.filter) {\n      var who = self._proxy || self;\n      // special handling for ProxyReaders\n      if (!self.filter.call(who, who, props)) {\n        if (!self._disowned) {\n          self.abort();\n          self.emit('end');\n          self.emit('close');\n        }\n        return;\n      }\n    }\n\n    // last chance to abort or disown before the flow starts!\n    var events = ['_stat', 'stat', 'ready'];\n    var e = 0;\n    (function go() {\n      if (self._aborted) {\n        self.emit('end');\n        self.emit('close');\n        return;\n      }\n      if (self._paused && self.type !== 'Directory') {\n        self.once('resume', go);\n        return;\n      }\n      var ev = events[e++];\n      if (!ev) {\n        return self._read();\n      }\n      self.emit(ev, props);\n      go();\n    })();\n  }\n};\nReader.prototype.pipe = function (dest) {\n  var self = this;\n  if (typeof dest.add === 'function') {\n    // piping to a multi-compatible, and we've got directory entries.\n    self.on('entry', function (entry) {\n      var ret = dest.add(entry);\n      if (ret === false) {\n        self.pause();\n      }\n    });\n  }\n\n  // console.error(\"R Pipe apply Stream Pipe\")\n  return Stream.prototype.pipe.apply(this, arguments);\n};\nReader.prototype.pause = function (who) {\n  this._paused = true;\n  who = who || this;\n  this.emit('pause', who);\n  if (this._stream) this._stream.pause(who);\n};\nReader.prototype.resume = function (who) {\n  this._paused = false;\n  who = who || this;\n  this.emit('resume', who);\n  if (this._stream) this._stream.resume(who);\n  this._read();\n};\nReader.prototype._read = function () {\n  this.error('Cannot read unknown type: ' + this.type);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/socket-reader.js":
/*!***************************************************!*\
  !*** ./node_modules/fstream/lib/socket-reader.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// Just get the stats, and then don't do anything.\n// You can't really \"read\" from a socket.  You \"connect\" to it.\n// Mostly, this is here so that reading a dir with a socket in it\n// doesn't blow up.\n\nmodule.exports = SocketReader;\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\");\ninherits(SocketReader, Reader);\nfunction SocketReader(props) {\n  var self = this;\n  if (!(self instanceof SocketReader)) {\n    throw new Error('SocketReader must be called as constructor.');\n  }\n  if (!(props.type === 'Socket' && props.Socket)) {\n    throw new Error('Non-socket type ' + props.type);\n  }\n  Reader.call(self, props);\n}\nSocketReader.prototype._read = function () {\n  var self = this;\n  if (self._paused) return;\n  // basically just a no-op, since we got all the info we have\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end');\n    self.emit('close');\n    self._ended = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/socket-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/writer.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/writer.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = Writer;\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar rimraf = __webpack_require__(/*! rimraf */ \"(rsc)/./node_modules/fstream/node_modules/rimraf/rimraf.js\");\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(rsc)/./node_modules/mkdirp/index.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar umask = process.platform === 'win32' ? 0 : process.umask();\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\");\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\");\n\n// Must do this *before* loading the child classes\ninherits(Writer, Abstract);\nWriter.dirmode = parseInt('0777', 8) & ~umask;\nWriter.filemode = parseInt('0666', 8) & ~umask;\nvar DirWriter = __webpack_require__(/*! ./dir-writer.js */ \"(rsc)/./node_modules/fstream/lib/dir-writer.js\");\nvar LinkWriter = __webpack_require__(/*! ./link-writer.js */ \"(rsc)/./node_modules/fstream/lib/link-writer.js\");\nvar FileWriter = __webpack_require__(/*! ./file-writer.js */ \"(rsc)/./node_modules/fstream/lib/file-writer.js\");\nvar ProxyWriter = __webpack_require__(/*! ./proxy-writer.js */ \"(rsc)/./node_modules/fstream/lib/proxy-writer.js\");\n\n// props is the desired state.  current is optionally the current stat,\n// provided here so that subclasses can avoid statting the target\n// more than necessary.\nfunction Writer(props, current) {\n  var self = this;\n  if (typeof props === 'string') {\n    props = {\n      path: props\n    };\n  }\n\n  // polymorphism.\n  // call fstream.Writer(dir) to get a DirWriter object, etc.\n  var type = getType(props);\n  var ClassType = Writer;\n  switch (type) {\n    case 'Directory':\n      ClassType = DirWriter;\n      break;\n    case 'File':\n      ClassType = FileWriter;\n      break;\n    case 'Link':\n    case 'SymbolicLink':\n      ClassType = LinkWriter;\n      break;\n    case null:\n    default:\n      // Don't know yet what type to create, so we wrap in a proxy.\n      ClassType = ProxyWriter;\n      break;\n  }\n  if (!(self instanceof ClassType)) return new ClassType(props);\n\n  // now get down to business.\n\n  Abstract.call(self);\n  if (!props.path) self.error('Must provide a path', null, true);\n\n  // props is what we want to set.\n  // set some convenience properties as well.\n  self.type = props.type;\n  self.props = props;\n  self.depth = props.depth || 0;\n  self.clobber = props.clobber === false ? props.clobber : true;\n  self.parent = props.parent || null;\n  self.root = props.root || props.parent && props.parent.root || self;\n  self._path = self.path = path.resolve(props.path);\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_');\n    if (self._path.length >= 260) {\n      self._swallowErrors = true;\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\');\n    }\n  }\n  self.basename = path.basename(props.path);\n  self.dirname = path.dirname(props.path);\n  self.linkpath = props.linkpath || null;\n  props.parent = props.root = null;\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size;\n  if (typeof props.mode === 'string') {\n    props.mode = parseInt(props.mode, 8);\n  }\n  self.readable = false;\n  self.writable = true;\n\n  // buffer until ready, or while handling another entry\n  self._buffer = [];\n  self.ready = false;\n  self.filter = typeof props.filter === 'function' ? props.filter : null;\n\n  // start the ball rolling.\n  // this checks what's there already, and then calls\n  // self._create() to call the impl-specific creation stuff.\n  self._stat(current);\n}\n\n// Calling this means that it's something we can't create.\n// Just assert that it's already there, otherwise raise a warning.\nWriter.prototype._create = function () {\n  var self = this;\n  fs[self.props.follow ? 'stat' : 'lstat'](self._path, function (er) {\n    if (er) {\n      return self.warn('Cannot create ' + self._path + '\\n' + 'Unsupported type: ' + self.type, 'ENOTSUP');\n    }\n    self._finish();\n  });\n};\nWriter.prototype._stat = function (current) {\n  var self = this;\n  var props = self.props;\n  var stat = props.follow ? 'stat' : 'lstat';\n  var who = self._proxy || self;\n  if (current) statCb(null, current);else fs[stat](self._path, statCb);\n  function statCb(er, current) {\n    if (self.filter && !self.filter.call(who, who, current)) {\n      self._aborted = true;\n      self.emit('end');\n      self.emit('close');\n      return;\n    }\n\n    // if it's not there, great.  We'll just create it.\n    // if it is there, then we'll need to change whatever differs\n    if (er || !current) {\n      return create(self);\n    }\n    self._old = current;\n    var currentType = getType(current);\n\n    // if it's a type change, then we need to clobber or error.\n    // if it's not a type change, then let the impl take care of it.\n    if (currentType !== self.type || self.type === 'File' && current.nlink > 1) {\n      return rimraf(self._path, function (er) {\n        if (er) return self.error(er);\n        self._old = null;\n        create(self);\n      });\n    }\n\n    // otherwise, just handle in the app-specific way\n    // this creates a fs.WriteStream, or mkdir's, or whatever\n    create(self);\n  }\n};\nfunction create(self) {\n  // console.error(\"W create\", self._path, Writer.dirmode)\n\n  // XXX Need to clobber non-dirs that are in the way,\n  // unless { clobber: false } in the props.\n  mkdir(path.dirname(self._path), Writer.dirmode, function (er, made) {\n    // console.error(\"W created\", path.dirname(self._path), er)\n    if (er) return self.error(er);\n\n    // later on, we have to set the mode and owner for these\n    self._madeDir = made;\n    return self._create();\n  });\n}\nfunction endChmod(self, want, current, path, cb) {\n  var wantMode = want.mode;\n  var chmod = want.follow || self.type !== 'SymbolicLink' ? 'chmod' : 'lchmod';\n  if (!fs[chmod]) return cb();\n  if (typeof wantMode !== 'number') return cb();\n  var curMode = current.mode & parseInt('0777', 8);\n  wantMode = wantMode & parseInt('0777', 8);\n  if (wantMode === curMode) return cb();\n  fs[chmod](path, wantMode, cb);\n}\nfunction endChown(self, want, current, path, cb) {\n  // Don't even try it unless root.  Too easy to EPERM.\n  if (process.platform === 'win32') return cb();\n  if (!process.getuid || process.getuid() !== 0) return cb();\n  if (typeof want.uid !== 'number' && typeof want.gid !== 'number') return cb();\n  if (current.uid === want.uid && current.gid === want.gid) return cb();\n  var chown = self.props.follow || self.type !== 'SymbolicLink' ? 'chown' : 'lchown';\n  if (!fs[chown]) return cb();\n  if (typeof want.uid !== 'number') want.uid = current.uid;\n  if (typeof want.gid !== 'number') want.gid = current.gid;\n  fs[chown](path, want.uid, want.gid, cb);\n}\nfunction endUtimes(self, want, current, path, cb) {\n  if (!fs.utimes || process.platform === 'win32') return cb();\n  var utimes = want.follow || self.type !== 'SymbolicLink' ? 'utimes' : 'lutimes';\n  if (utimes === 'lutimes' && !fs[utimes]) {\n    utimes = 'utimes';\n  }\n  if (!fs[utimes]) return cb();\n  var curA = current.atime;\n  var curM = current.mtime;\n  var meA = want.atime;\n  var meM = want.mtime;\n  if (meA === undefined) meA = curA;\n  if (meM === undefined) meM = curM;\n  if (!isDate(meA)) meA = new Date(meA);\n  if (!isDate(meM)) meA = new Date(meM);\n  if (meA.getTime() === curA.getTime() && meM.getTime() === curM.getTime()) return cb();\n  fs[utimes](path, meA, meM, cb);\n}\n\n// XXX This function is beastly.  Break it up!\nWriter.prototype._finish = function () {\n  var self = this;\n  if (self._finishing) return;\n  self._finishing = true;\n\n  // console.error(\" W Finish\", self._path, self.size)\n\n  // set up all the things.\n  // At this point, we're already done writing whatever we've gotta write,\n  // adding files to the dir, etc.\n  var todo = 0;\n  var errState = null;\n  var done = false;\n  if (self._old) {\n    // the times will almost *certainly* have changed.\n    // adds the utimes syscall, but remove another stat.\n    self._old.atime = new Date(0);\n    self._old.mtime = new Date(0);\n    // console.error(\" W Finish Stale Stat\", self._path, self.size)\n    setProps(self._old);\n  } else {\n    var stat = self.props.follow ? 'stat' : 'lstat';\n    // console.error(\" W Finish Stating\", self._path, self.size)\n    fs[stat](self._path, function (er, current) {\n      // console.error(\" W Finish Stated\", self._path, self.size, current)\n      if (er) {\n        // if we're in the process of writing out a\n        // directory, it's very possible that the thing we're linking to\n        // doesn't exist yet (especially if it was intended as a symlink),\n        // so swallow ENOENT errors here and just soldier on.\n        if (er.code === 'ENOENT' && (self.type === 'Link' || self.type === 'SymbolicLink') && process.platform === 'win32') {\n          self.ready = true;\n          self.emit('ready');\n          self.emit('end');\n          self.emit('close');\n          self.end = self._finish = function () {};\n          return;\n        } else return self.error(er);\n      }\n      setProps(self._old = current);\n    });\n  }\n  return;\n  function setProps(current) {\n    todo += 3;\n    endChmod(self, self.props, current, self._path, next('chmod'));\n    endChown(self, self.props, current, self._path, next('chown'));\n    endUtimes(self, self.props, current, self._path, next('utimes'));\n  }\n  function next(what) {\n    return function (er) {\n      // console.error(\"   W Finish\", what, todo)\n      if (errState) return;\n      if (er) {\n        er.fstream_finish_call = what;\n        return self.error(errState = er);\n      }\n      if (--todo > 0) return;\n      if (done) return;\n      done = true;\n\n      // we may still need to set the mode/etc. on some parent dirs\n      // that were created previously.  delay end/close until then.\n      if (!self._madeDir) return end();else endMadeDir(self, self._path, end);\n      function end(er) {\n        if (er) {\n          er.fstream_finish_call = 'setupMadeDir';\n          return self.error(er);\n        }\n        // all the props have been set, so we're completely done.\n        self.emit('end');\n        self.emit('close');\n      }\n    };\n  }\n};\nfunction endMadeDir(self, p, cb) {\n  var made = self._madeDir;\n  // everything *between* made and path.dirname(self._path)\n  // needs to be set up.  Note that this may just be one dir.\n  var d = path.dirname(p);\n  endMadeDir_(self, d, function (er) {\n    if (er) return cb(er);\n    if (d === made) {\n      return cb();\n    }\n    endMadeDir(self, d, cb);\n  });\n}\nfunction endMadeDir_(self, p, cb) {\n  var dirProps = {};\n  Object.keys(self.props).forEach(function (k) {\n    dirProps[k] = self.props[k];\n\n    // only make non-readable dirs if explicitly requested.\n    if (k === 'mode' && self.type !== 'Directory') {\n      dirProps[k] = dirProps[k] | parseInt('0111', 8);\n    }\n  });\n  var todo = 3;\n  var errState = null;\n  fs.stat(p, function (er, current) {\n    if (er) return cb(errState = er);\n    endChmod(self, dirProps, current, p, next);\n    endChown(self, dirProps, current, p, next);\n    endUtimes(self, dirProps, current, p, next);\n  });\n  function next(er) {\n    if (errState) return;\n    if (er) return cb(errState = er);\n    if (--todo === 0) return cb();\n  }\n}\nWriter.prototype.pipe = function () {\n  this.error(\"Can't pipe from writable stream\");\n};\nWriter.prototype.add = function () {\n  this.error(\"Can't add to non-Directory type\");\n};\nWriter.prototype.write = function () {\n  return true;\n};\nfunction objectToString(d) {\n  return Object.prototype.toString.call(d);\n}\nfunction isDate(d) {\n  return typeof d === 'object' && objectToString(d) === '[object Date]';\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/node_modules/rimraf/rimraf.js":
/*!************************************************************!*\
  !*** ./node_modules/fstream/node_modules/rimraf/rimraf.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = rimraf;\nrimraf.sync = rimrafSync;\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar glob = undefined;\ntry {\n  glob = __webpack_require__(/*! glob */ \"(rsc)/./node_modules/glob/glob.js\");\n} catch (_err) {\n  // treat glob as optional.\n}\nvar _0666 = parseInt('666', 8);\nvar defaultGlobOpts = {\n  nosort: true,\n  silent: true\n};\n\n// for EMFILE handling\nvar timeout = 0;\nvar isWindows = process.platform === \"win32\";\nfunction defaults(options) {\n  var methods = ['unlink', 'chmod', 'stat', 'lstat', 'rmdir', 'readdir'];\n  methods.forEach(function (m) {\n    options[m] = options[m] || fs[m];\n    m = m + 'Sync';\n    options[m] = options[m] || fs[m];\n  });\n  options.maxBusyTries = options.maxBusyTries || 3;\n  options.emfileWait = options.emfileWait || 1000;\n  if (options.glob === false) {\n    options.disableGlob = true;\n  }\n  if (options.disableGlob !== true && glob === undefined) {\n    throw Error('glob dependency not found, set `options.disableGlob = true` if intentional');\n  }\n  options.disableGlob = options.disableGlob || false;\n  options.glob = options.glob || defaultGlobOpts;\n}\nfunction rimraf(p, options, cb) {\n  if (typeof options === 'function') {\n    cb = options;\n    options = {};\n  }\n  assert(p, 'rimraf: missing path');\n  assert.equal(typeof p, 'string', 'rimraf: path should be a string');\n  assert.equal(typeof cb, 'function', 'rimraf: callback function required');\n  assert(options, 'rimraf: invalid options argument provided');\n  assert.equal(typeof options, 'object', 'rimraf: options should be object');\n  defaults(options);\n  var busyTries = 0;\n  var errState = null;\n  var n = 0;\n  if (options.disableGlob || !glob.hasMagic(p)) return afterGlob(null, [p]);\n  options.lstat(p, function (er, stat) {\n    if (!er) return afterGlob(null, [p]);\n    glob(p, options.glob, afterGlob);\n  });\n  function next(er) {\n    errState = errState || er;\n    if (--n === 0) cb(errState);\n  }\n  function afterGlob(er, results) {\n    if (er) return cb(er);\n    n = results.length;\n    if (n === 0) return cb();\n    results.forEach(function (p) {\n      rimraf_(p, options, function CB(er) {\n        if (er) {\n          if ((er.code === \"EBUSY\" || er.code === \"ENOTEMPTY\" || er.code === \"EPERM\") && busyTries < options.maxBusyTries) {\n            busyTries++;\n            var time = busyTries * 100;\n            // try again, with the same exact callback as this one.\n            return setTimeout(function () {\n              rimraf_(p, options, CB);\n            }, time);\n          }\n\n          // this one won't happen if graceful-fs is used.\n          if (er.code === \"EMFILE\" && timeout < options.emfileWait) {\n            return setTimeout(function () {\n              rimraf_(p, options, CB);\n            }, timeout++);\n          }\n\n          // already gone\n          if (er.code === \"ENOENT\") er = null;\n        }\n        timeout = 0;\n        next(er);\n      });\n    });\n  }\n}\n\n// Two possible strategies.\n// 1. Assume it's a file.  unlink it, then do the dir stuff on EPERM or EISDIR\n// 2. Assume it's a directory.  readdir, then do the file stuff on ENOTDIR\n//\n// Both result in an extra syscall when you guess wrong.  However, there\n// are likely far more normal files in the world than directories.  This\n// is based on the assumption that a the average number of files per\n// directory is >= 1.\n//\n// If anyone ever complains about this, then I guess the strategy could\n// be made configurable somehow.  But until then, YAGNI.\nfunction rimraf_(p, options, cb) {\n  assert(p);\n  assert(options);\n  assert(typeof cb === 'function');\n\n  // sunos lets the root user unlink directories, which is... weird.\n  // so we have to lstat here and make sure it's not a dir.\n  options.lstat(p, function (er, st) {\n    if (er && er.code === \"ENOENT\") return cb(null);\n\n    // Windows can EPERM on stat.  Life is suffering.\n    if (er && er.code === \"EPERM\" && isWindows) fixWinEPERM(p, options, er, cb);\n    if (st && st.isDirectory()) return rmdir(p, options, er, cb);\n    options.unlink(p, function (er) {\n      if (er) {\n        if (er.code === \"ENOENT\") return cb(null);\n        if (er.code === \"EPERM\") return isWindows ? fixWinEPERM(p, options, er, cb) : rmdir(p, options, er, cb);\n        if (er.code === \"EISDIR\") return rmdir(p, options, er, cb);\n      }\n      return cb(er);\n    });\n  });\n}\nfunction fixWinEPERM(p, options, er, cb) {\n  assert(p);\n  assert(options);\n  assert(typeof cb === 'function');\n  if (er) assert(er instanceof Error);\n  options.chmod(p, _0666, function (er2) {\n    if (er2) cb(er2.code === \"ENOENT\" ? null : er);else options.stat(p, function (er3, stats) {\n      if (er3) cb(er3.code === \"ENOENT\" ? null : er);else if (stats.isDirectory()) rmdir(p, options, er, cb);else options.unlink(p, cb);\n    });\n  });\n}\nfunction fixWinEPERMSync(p, options, er) {\n  assert(p);\n  assert(options);\n  if (er) assert(er instanceof Error);\n  try {\n    options.chmodSync(p, _0666);\n  } catch (er2) {\n    if (er2.code === \"ENOENT\") return;else throw er;\n  }\n  try {\n    var stats = options.statSync(p);\n  } catch (er3) {\n    if (er3.code === \"ENOENT\") return;else throw er;\n  }\n  if (stats.isDirectory()) rmdirSync(p, options, er);else options.unlinkSync(p);\n}\nfunction rmdir(p, options, originalEr, cb) {\n  assert(p);\n  assert(options);\n  if (originalEr) assert(originalEr instanceof Error);\n  assert(typeof cb === 'function');\n\n  // try to rmdir first, and only readdir on ENOTEMPTY or EEXIST (SunOS)\n  // if we guessed wrong, and it's not a directory, then\n  // raise the original error.\n  options.rmdir(p, function (er) {\n    if (er && (er.code === \"ENOTEMPTY\" || er.code === \"EEXIST\" || er.code === \"EPERM\")) rmkids(p, options, cb);else if (er && er.code === \"ENOTDIR\") cb(originalEr);else cb(er);\n  });\n}\nfunction rmkids(p, options, cb) {\n  assert(p);\n  assert(options);\n  assert(typeof cb === 'function');\n  options.readdir(p, function (er, files) {\n    if (er) return cb(er);\n    var n = files.length;\n    if (n === 0) return options.rmdir(p, cb);\n    var errState;\n    files.forEach(function (f) {\n      rimraf(path.join(p, f), options, function (er) {\n        if (errState) return;\n        if (er) return cb(errState = er);\n        if (--n === 0) options.rmdir(p, cb);\n      });\n    });\n  });\n}\n\n// this looks simpler, and is strictly *faster*, but will\n// tie up the JavaScript thread and fail on excessively\n// deep directory trees.\nfunction rimrafSync(p, options) {\n  options = options || {};\n  defaults(options);\n  assert(p, 'rimraf: missing path');\n  assert.equal(typeof p, 'string', 'rimraf: path should be a string');\n  assert(options, 'rimraf: missing options');\n  assert.equal(typeof options, 'object', 'rimraf: options should be object');\n  var results;\n  if (options.disableGlob || !glob.hasMagic(p)) {\n    results = [p];\n  } else {\n    try {\n      options.lstatSync(p);\n      results = [p];\n    } catch (er) {\n      results = glob.sync(p, options.glob);\n    }\n  }\n  if (!results.length) return;\n  for (var i = 0; i < results.length; i++) {\n    var p = results[i];\n    try {\n      var st = options.lstatSync(p);\n    } catch (er) {\n      if (er.code === \"ENOENT\") return;\n\n      // Windows can EPERM on stat.  Life is suffering.\n      if (er.code === \"EPERM\" && isWindows) fixWinEPERMSync(p, options, er);\n    }\n    try {\n      // sunos lets the root user unlink directories, which is... weird.\n      if (st && st.isDirectory()) rmdirSync(p, options, null);else options.unlinkSync(p);\n    } catch (er) {\n      if (er.code === \"ENOENT\") return;\n      if (er.code === \"EPERM\") return isWindows ? fixWinEPERMSync(p, options, er) : rmdirSync(p, options, er);\n      if (er.code !== \"EISDIR\") throw er;\n      rmdirSync(p, options, er);\n    }\n  }\n}\nfunction rmdirSync(p, options, originalEr) {\n  assert(p);\n  assert(options);\n  if (originalEr) assert(originalEr instanceof Error);\n  try {\n    options.rmdirSync(p);\n  } catch (er) {\n    if (er.code === \"ENOENT\") return;\n    if (er.code === \"ENOTDIR\") throw originalEr;\n    if (er.code === \"ENOTEMPTY\" || er.code === \"EEXIST\" || er.code === \"EPERM\") rmkidsSync(p, options);\n  }\n}\nfunction rmkidsSync(p, options) {\n  assert(p);\n  assert(options);\n  options.readdirSync(p).forEach(function (f) {\n    rimrafSync(path.join(p, f), options);\n  });\n\n  // We only end up here once we got ENOTEMPTY at least once, and\n  // at this point, we are guaranteed to have removed all the kids.\n  // So, we know that it won't be ENOENT or ENOTDIR or anything else.\n  // try really hard to delete stuff on windows, because it has a\n  // PROFOUNDLY annoying habit of not closing handles promptly when\n  // files are deleted, resulting in spurious ENOTEMPTY errors.\n  var retries = isWindows ? 100 : 1;\n  var i = 0;\n  do {\n    var threw = true;\n    try {\n      var ret = options.rmdirSync(p, options);\n      threw = false;\n      return ret;\n    } finally {\n      if (++i < retries && threw) continue;\n    }\n  } while (true);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/node_modules/rimraf/rimraf.js\n");

/***/ })

};
;