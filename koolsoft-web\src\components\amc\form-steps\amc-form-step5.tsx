'use client';

import React, { useState, useEffect } from 'react';
import { useAMCForm } from '@/contexts/amc-form-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, Plus, Edit, Trash2, CreditCard, Building2, Crown } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import { useAllDivisions } from '@/lib/hooks/useDivisions';

interface Payment {
  id?: string;
  paymentDate: Date;
  amount: number;
  paymentMode: 'CASH' | 'CHEQUE' | 'BANK_TRANSFER' | 'ONLINE';
  receiptNo?: string;
  particulars?: string;
}

interface Division {
  id?: string;
  divisionId: string;
  name?: string;
  description?: string;
  percentage: number;
  isPrimary: boolean;
}

export function AMCFormStep5() {
  const { state, updateFormData, goToNextStep, goToPreviousStep } = useAMCForm();
  const { divisions: allDivisions, isLoading: divisionsLoading, fetchDivisions } = useAllDivisions();

  // Payment state
  const [payments, setPayments] = useState<Payment[]>(state.formData.payments || []);
  const [isAddingPayment, setIsAddingPayment] = useState(false);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);
  const [paymentForm, setPaymentForm] = useState<Payment>({
    paymentDate: new Date(),
    amount: 0,
    paymentMode: 'CASH',
    receiptNo: '',
    particulars: '',
  });

  // Division state
  const [divisions, setDivisions] = useState<Division[]>(state.formData.divisions || []);
  const [isAddingDivision, setIsAddingDivision] = useState(false);
  const [editingDivision, setEditingDivision] = useState<Division | null>(null);
  const [divisionForm, setDivisionForm] = useState<Division>({
    divisionId: '',
    percentage: 0,
    isPrimary: false,
  });

  // Load divisions data on mount
  useEffect(() => {
    fetchDivisions();
  }, [fetchDivisions]);

  // Enrich divisions with name and description when allDivisions loads
  useEffect(() => {
    if (allDivisions.length > 0 && divisions.length > 0) {
      const enrichedDivisions = divisions.map(div => {
        const divisionData = allDivisions.find(d => d.id === div.divisionId);
        return {
          ...div,
          name: divisionData?.name || 'Unknown Division',
          description: divisionData?.description,
        };
      });
      setDivisions(enrichedDivisions);
    }
  }, [allDivisions]);

  const contractAmount = state.formData.amount || 0;
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const balance = contractAmount - totalPaid;

  // Division calculations
  const totalPercentage = divisions.reduce((sum, division) => sum + division.percentage, 0);
  const remainingPercentage = 100 - totalPercentage;
  const primaryDivision = divisions.find(div => div.isPrimary);

  const handleAddPayment = () => {
    if (paymentForm.amount <= 0) {
      showErrorToast('Please enter a valid amount');
      return;
    }

    const newPayment: Payment = {
      ...paymentForm,
      id: Date.now().toString(), // Temporary ID for form management
    };

    const updatedPayments = [...payments, newPayment];
    setPayments(updatedPayments);
    updateFormData({ payments: updatedPayments });

    // Reset form
    setPaymentForm({
      paymentDate: new Date(),
      amount: 0,
      paymentMode: 'CASH',
      receiptNo: '',
      particulars: '',
    });
    setIsAddingPayment(false);
    showSuccessToast('Payment added successfully');
  };

  const handleEditPayment = (payment: Payment) => {
    setPaymentForm(payment);
    setEditingPayment(payment);
    setIsAddingPayment(true);
  };

  const handleUpdatePayment = () => {
    if (paymentForm.amount <= 0) {
      showErrorToast('Please enter a valid amount');
      return;
    }

    const updatedPayments = payments.map(p => 
      p.id === editingPayment?.id ? { ...paymentForm } : p
    );
    setPayments(updatedPayments);
    updateFormData({ payments: updatedPayments });

    // Reset form
    setPaymentForm({
      paymentDate: new Date(),
      amount: 0,
      paymentMode: 'CASH',
      receiptNo: '',
      particulars: '',
    });
    setEditingPayment(null);
    setIsAddingPayment(false);
    showSuccessToast('Payment updated successfully');
  };

  const handleDeletePayment = (paymentId: string) => {
    const updatedPayments = payments.filter(p => p.id !== paymentId);
    setPayments(updatedPayments);
    updateFormData({ payments: updatedPayments });
    showSuccessToast('Payment removed successfully');
  };

  // Division management functions
  const handleAddDivision = () => {
    if (!divisionForm.divisionId) {
      showErrorToast('Please select a division');
      return;
    }

    if (divisionForm.percentage <= 0 || divisionForm.percentage > 100) {
      showErrorToast('Please enter a valid percentage between 1 and 100');
      return;
    }

    // Check if division already exists
    if (divisions.some(d => d.divisionId === divisionForm.divisionId)) {
      showErrorToast('This division is already assigned');
      return;
    }

    // Get division details
    const divisionData = allDivisions.find(d => d.id === divisionForm.divisionId);

    const newDivision: Division = {
      ...divisionForm,
      id: Date.now().toString(),
      name: divisionData?.name || 'Unknown Division',
      description: divisionData?.description,
    };

    // If this is marked as primary, unmark others
    let updatedDivisions = [...divisions];
    if (newDivision.isPrimary) {
      updatedDivisions = updatedDivisions.map(d => ({ ...d, isPrimary: false }));
    }

    updatedDivisions.push(newDivision);
    setDivisions(updatedDivisions);
    updateFormData({ divisions: updatedDivisions });

    // Reset form
    setDivisionForm({
      divisionId: '',
      percentage: 0,
      isPrimary: false,
    });
    setIsAddingDivision(false);
    showSuccessToast('Division added successfully');
  };

  const handleEditDivision = (division: Division) => {
    setDivisionForm(division);
    setEditingDivision(division);
    setIsAddingDivision(true);
  };

  const handleUpdateDivision = () => {
    if (!divisionForm.divisionId) {
      showErrorToast('Please select a division');
      return;
    }

    if (divisionForm.percentage <= 0 || divisionForm.percentage > 100) {
      showErrorToast('Please enter a valid percentage between 1 and 100');
      return;
    }

    // Check if division already exists (excluding current)
    if (divisions.some(d => d.divisionId === divisionForm.divisionId && d.id !== editingDivision?.id)) {
      showErrorToast('This division is already assigned');
      return;
    }

    // Get division details
    const divisionData = allDivisions.find(d => d.id === divisionForm.divisionId);

    let updatedDivisions = divisions.map(d =>
      d.id === editingDivision?.id
        ? {
            ...divisionForm,
            name: divisionData?.name || 'Unknown Division',
            description: divisionData?.description,
          }
        : d
    );

    // If this is marked as primary, unmark others
    if (divisionForm.isPrimary) {
      updatedDivisions = updatedDivisions.map(d =>
        d.id === editingDivision?.id ? d : { ...d, isPrimary: false }
      );
    }

    setDivisions(updatedDivisions);
    updateFormData({ divisions: updatedDivisions });

    // Reset form
    setDivisionForm({
      divisionId: '',
      percentage: 0,
      isPrimary: false,
    });
    setEditingDivision(null);
    setIsAddingDivision(false);
    showSuccessToast('Division updated successfully');
  };

  const handleDeleteDivision = (divisionId: string) => {
    const updatedDivisions = divisions.filter(d => d.id !== divisionId);
    setDivisions(updatedDivisions);
    updateFormData({ divisions: updatedDivisions });
    showSuccessToast('Division removed successfully');
  };

  const handleSetPrimary = (divisionId: string) => {
    const updatedDivisions = divisions.map(d => ({
      ...d,
      isPrimary: d.id === divisionId,
    }));
    setDivisions(updatedDivisions);
    updateFormData({ divisions: updatedDivisions });
    showSuccessToast('Primary division updated');
  };

  const getPaymentModeColor = (mode: string) => {
    switch (mode) {
      case 'CASH':
        return 'bg-green-100 text-green-800';
      case 'CHEQUE':
        return 'bg-blue-100 text-blue-800';
      case 'BANK_TRANSFER':
        return 'bg-purple-100 text-purple-800';
      case 'ONLINE':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleNext = () => {
    // Validate divisions if any are assigned
    if (divisions.length > 0) {
      if (Math.abs(totalPercentage - 100) > 0.01) {
        showErrorToast('Division percentages must sum to 100%');
        return;
      }
    }

    // Update form data before proceeding
    updateFormData({ payments, divisions });
    goToNextStep();
  };

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="text-white">Payment Summary</CardTitle>
            <p className="text-gray-100 text-sm">
              Track payments for this AMC contract
            </p>
          </div>
          <CreditCard className="h-5 w-5 text-white" />
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-black">Contract Amount</p>
              <p className="text-2xl font-bold text-black">
                ₹{contractAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-sm font-medium text-black">Total Paid</p>
              <p className="text-2xl font-bold text-green-600">
                ₹{totalPaid.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
              </p>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <p className="text-sm font-medium text-black">Balance</p>
              <p className={`text-2xl font-bold ${balance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                ₹{Math.abs(balance).toLocaleString('en-IN', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payments List */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="text-white">Payments</CardTitle>
            <p className="text-gray-100 text-sm">
              {payments.length} payment{payments.length !== 1 ? 's' : ''} added
            </p>
          </div>
          <Dialog open={isAddingPayment} onOpenChange={setIsAddingPayment}>
            <DialogTrigger asChild>
              <Button variant="secondary">
                <Plus className="h-4 w-4 mr-2" />
                Add Payment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="text-black">
                  {editingPayment ? 'Edit Payment' : 'Add Payment'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {/* Payment Date */}
                <div className="space-y-2">
                  <Label className="text-black">Payment Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !paymentForm.paymentDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {paymentForm.paymentDate ? format(paymentForm.paymentDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={paymentForm.paymentDate}
                        onSelect={(date) => setPaymentForm(prev => ({ ...prev, paymentDate: date || new Date() }))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Amount */}
                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-black">Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={paymentForm.amount}
                    onChange={(e) => setPaymentForm(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                  />
                </div>

                {/* Payment Mode */}
                <div className="space-y-2">
                  <Label className="text-black">Payment Mode</Label>
                  <Select
                    value={paymentForm.paymentMode}
                    onValueChange={(value) => setPaymentForm(prev => ({ ...prev, paymentMode: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">Cash</SelectItem>
                      <SelectItem value="CHEQUE">Cheque</SelectItem>
                      <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                      <SelectItem value="ONLINE">Online</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Receipt Number */}
                <div className="space-y-2">
                  <Label htmlFor="receiptNo" className="text-black">Receipt Number</Label>
                  <Input
                    id="receiptNo"
                    value={paymentForm.receiptNo}
                    onChange={(e) => setPaymentForm(prev => ({ ...prev, receiptNo: e.target.value }))}
                    placeholder="Optional"
                  />
                </div>

                {/* Particulars */}
                <div className="space-y-2">
                  <Label htmlFor="particulars" className="text-black">Particulars</Label>
                  <Textarea
                    id="particulars"
                    value={paymentForm.particulars}
                    onChange={(e) => setPaymentForm(prev => ({ ...prev, particulars: e.target.value }))}
                    placeholder="Payment details or notes"
                    rows={3}
                  />
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddingPayment(false);
                      setEditingPayment(null);
                      setPaymentForm({
                        paymentDate: new Date(),
                        amount: 0,
                        paymentMode: 'CASH',
                        receiptNo: '',
                        particulars: '',
                      });
                    }}
                  >
                    Cancel
                  </Button>
                  <Button onClick={editingPayment ? handleUpdatePayment : handleAddPayment}>
                    {editingPayment ? 'Update' : 'Add'} Payment
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent className="p-6">
          {payments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No payments added yet. Click "Add Payment" to get started.
            </div>
          ) : (
            <div className="space-y-3">
              {payments.map((payment) => (
                <div
                  key={payment.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="font-medium text-black">
                        ₹{payment.amount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {format(payment.paymentDate, 'dd MMM yyyy')}
                      </p>
                    </div>
                    <Badge className={getPaymentModeColor(payment.paymentMode)}>
                      {payment.paymentMode}
                    </Badge>
                    {payment.receiptNo && (
                      <span className="text-sm text-black">
                        Receipt: {payment.receiptNo}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditPayment(payment)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeletePayment(payment.id!)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Division Assignment */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="text-white">Division Assignment</CardTitle>
            <p className="text-gray-100 text-sm">
              Assign divisions with percentage allocation (must total 100%)
            </p>
          </div>
          <Building2 className="h-5 w-5 text-white" />
        </CardHeader>
        <CardContent className="p-6">
          {/* Division Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-black">Total Assigned</p>
              <p className={`text-2xl font-bold ${Math.abs(totalPercentage - 100) < 0.01 ? 'text-green-600' : 'text-red-600'}`}>
                {totalPercentage.toFixed(1)}%
              </p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-black">Remaining</p>
              <p className={`text-2xl font-bold ${remainingPercentage >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                {remainingPercentage.toFixed(1)}%
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-sm font-medium text-black">Primary Division</p>
              <p className="text-lg font-bold text-purple-600">
                {primaryDivision?.name || 'None'}
              </p>
            </div>
          </div>

          {/* Add Division Button */}
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-black">
              Assigned Divisions ({divisions.length})
            </h3>
            <Dialog open={isAddingDivision} onOpenChange={setIsAddingDivision}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Division
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-black">
                    {editingDivision ? 'Edit Division' : 'Add Division'}
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {/* Division Selection */}
                  <div className="space-y-2">
                    <Label className="text-black">Division</Label>
                    <Select
                      value={divisionForm.divisionId}
                      onValueChange={(value) => setDivisionForm(prev => ({ ...prev, divisionId: value }))}
                      disabled={divisionsLoading}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a division" />
                      </SelectTrigger>
                      <SelectContent>
                        {allDivisions.map((division) => (
                          <SelectItem key={division.id} value={division.id}>
                            {division.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Percentage */}
                  <div className="space-y-2">
                    <Label htmlFor="percentage" className="text-black">
                      Percentage (Remaining: {remainingPercentage.toFixed(1)}%)
                    </Label>
                    <Input
                      id="percentage"
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={divisionForm.percentage}
                      onChange={(e) => setDivisionForm(prev => ({ ...prev, percentage: parseFloat(e.target.value) || 0 }))}
                      placeholder="0.0"
                    />
                  </div>

                  {/* Primary Division */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isPrimary"
                      checked={divisionForm.isPrimary}
                      onCheckedChange={(checked) => setDivisionForm(prev => ({ ...prev, isPrimary: !!checked }))}
                    />
                    <Label htmlFor="isPrimary" className="text-black">
                      Mark as primary division
                    </Label>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsAddingDivision(false);
                        setEditingDivision(null);
                        setDivisionForm({
                          divisionId: '',
                          percentage: 0,
                          isPrimary: false,
                        });
                      }}
                    >
                      Cancel
                    </Button>
                    <Button onClick={editingDivision ? handleUpdateDivision : handleAddDivision}>
                      {editingDivision ? 'Update' : 'Add'} Division
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Divisions List */}
          {divisions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No divisions assigned yet. Click "Add Division" to get started.
            </div>
          ) : (
            <div className="space-y-3">
              {divisions.map((division) => (
                <div
                  key={division.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-black">{division.name}</p>
                        {division.isPrimary && (
                          <Crown className="h-4 w-4 text-yellow-500" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {division.percentage.toFixed(1)}% allocation
                      </p>
                      {division.description && (
                        <p className="text-xs text-muted-foreground">
                          {division.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {!division.isPrimary && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSetPrimary(division.id!)}
                        title="Set as primary"
                      >
                        <Crown className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditDivision(division)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteDivision(division.id!)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Validation Messages */}
          {divisions.length > 0 && Math.abs(totalPercentage - 100) > 0.01 && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                ⚠️ Division percentages must sum to 100%. Current total: {totalPercentage.toFixed(1)}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={goToPreviousStep}>
          Previous
        </Button>
        <Button onClick={handleNext}>
          Next: Review & Submit
        </Button>
      </div>
    </div>
  );
}
