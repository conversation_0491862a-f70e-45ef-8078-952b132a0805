/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/glob";
exports.ids = ["vendor-chunks/glob"];
exports.modules = {

/***/ "(rsc)/./node_modules/glob/common.js":
/*!*************************************!*\
  !*** ./node_modules/glob/common.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.setopts = setopts;\nexports.ownProp = ownProp;\nexports.makeAbs = makeAbs;\nexports.finish = finish;\nexports.mark = mark;\nexports.isIgnored = isIgnored;\nexports.childrenIgnored = childrenIgnored;\nfunction ownProp(obj, field) {\n  return Object.prototype.hasOwnProperty.call(obj, field);\n}\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar minimatch = __webpack_require__(/*! minimatch */ \"(rsc)/./node_modules/minimatch/minimatch.js\");\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(rsc)/./node_modules/path-is-absolute/index.js\");\nvar Minimatch = minimatch.Minimatch;\nfunction alphasort(a, b) {\n  return a.localeCompare(b, 'en');\n}\nfunction setupIgnores(self, options) {\n  self.ignore = options.ignore || [];\n  if (!Array.isArray(self.ignore)) self.ignore = [self.ignore];\n  if (self.ignore.length) {\n    self.ignore = self.ignore.map(ignoreMap);\n  }\n}\n\n// ignore patterns are always in dot:true mode.\nfunction ignoreMap(pattern) {\n  var gmatcher = null;\n  if (pattern.slice(-3) === '/**') {\n    var gpattern = pattern.replace(/(\\/\\*\\*)+$/, '');\n    gmatcher = new Minimatch(gpattern, {\n      dot: true\n    });\n  }\n  return {\n    matcher: new Minimatch(pattern, {\n      dot: true\n    }),\n    gmatcher: gmatcher\n  };\n}\nfunction setopts(self, pattern, options) {\n  if (!options) options = {};\n\n  // base-matching: just use globstar for that.\n  if (options.matchBase && -1 === pattern.indexOf(\"/\")) {\n    if (options.noglobstar) {\n      throw new Error(\"base matching requires globstar\");\n    }\n    pattern = \"**/\" + pattern;\n  }\n  self.silent = !!options.silent;\n  self.pattern = pattern;\n  self.strict = options.strict !== false;\n  self.realpath = !!options.realpath;\n  self.realpathCache = options.realpathCache || Object.create(null);\n  self.follow = !!options.follow;\n  self.dot = !!options.dot;\n  self.mark = !!options.mark;\n  self.nodir = !!options.nodir;\n  if (self.nodir) self.mark = true;\n  self.sync = !!options.sync;\n  self.nounique = !!options.nounique;\n  self.nonull = !!options.nonull;\n  self.nosort = !!options.nosort;\n  self.nocase = !!options.nocase;\n  self.stat = !!options.stat;\n  self.noprocess = !!options.noprocess;\n  self.absolute = !!options.absolute;\n  self.fs = options.fs || fs;\n  self.maxLength = options.maxLength || Infinity;\n  self.cache = options.cache || Object.create(null);\n  self.statCache = options.statCache || Object.create(null);\n  self.symlinks = options.symlinks || Object.create(null);\n  setupIgnores(self, options);\n  self.changedCwd = false;\n  var cwd = process.cwd();\n  if (!ownProp(options, \"cwd\")) self.cwd = cwd;else {\n    self.cwd = path.resolve(options.cwd);\n    self.changedCwd = self.cwd !== cwd;\n  }\n  self.root = options.root || path.resolve(self.cwd, \"/\");\n  self.root = path.resolve(self.root);\n  if (process.platform === \"win32\") self.root = self.root.replace(/\\\\/g, \"/\");\n\n  // TODO: is an absolute `cwd` supposed to be resolved against `root`?\n  // e.g. { cwd: '/test', root: __dirname } === path.join(__dirname, '/test')\n  self.cwdAbs = isAbsolute(self.cwd) ? self.cwd : makeAbs(self, self.cwd);\n  if (process.platform === \"win32\") self.cwdAbs = self.cwdAbs.replace(/\\\\/g, \"/\");\n  self.nomount = !!options.nomount;\n\n  // disable comments and negation in Minimatch.\n  // Note that they are not supported in Glob itself anyway.\n  options.nonegate = true;\n  options.nocomment = true;\n  // always treat \\ in patterns as escapes, not path separators\n  options.allowWindowsEscape = false;\n  self.minimatch = new Minimatch(pattern, options);\n  self.options = self.minimatch.options;\n}\nfunction finish(self) {\n  var nou = self.nounique;\n  var all = nou ? [] : Object.create(null);\n  for (var i = 0, l = self.matches.length; i < l; i++) {\n    var matches = self.matches[i];\n    if (!matches || Object.keys(matches).length === 0) {\n      if (self.nonull) {\n        // do like the shell, and spit out the literal glob\n        var literal = self.minimatch.globSet[i];\n        if (nou) all.push(literal);else all[literal] = true;\n      }\n    } else {\n      // had matches\n      var m = Object.keys(matches);\n      if (nou) all.push.apply(all, m);else m.forEach(function (m) {\n        all[m] = true;\n      });\n    }\n  }\n  if (!nou) all = Object.keys(all);\n  if (!self.nosort) all = all.sort(alphasort);\n\n  // at *some* point we statted all of these\n  if (self.mark) {\n    for (var i = 0; i < all.length; i++) {\n      all[i] = self._mark(all[i]);\n    }\n    if (self.nodir) {\n      all = all.filter(function (e) {\n        var notDir = !/\\/$/.test(e);\n        var c = self.cache[e] || self.cache[makeAbs(self, e)];\n        if (notDir && c) notDir = c !== 'DIR' && !Array.isArray(c);\n        return notDir;\n      });\n    }\n  }\n  if (self.ignore.length) all = all.filter(function (m) {\n    return !isIgnored(self, m);\n  });\n  self.found = all;\n}\nfunction mark(self, p) {\n  var abs = makeAbs(self, p);\n  var c = self.cache[abs];\n  var m = p;\n  if (c) {\n    var isDir = c === 'DIR' || Array.isArray(c);\n    var slash = p.slice(-1) === '/';\n    if (isDir && !slash) m += '/';else if (!isDir && slash) m = m.slice(0, -1);\n    if (m !== p) {\n      var mabs = makeAbs(self, m);\n      self.statCache[mabs] = self.statCache[abs];\n      self.cache[mabs] = self.cache[abs];\n    }\n  }\n  return m;\n}\n\n// lotta situps...\nfunction makeAbs(self, f) {\n  var abs = f;\n  if (f.charAt(0) === '/') {\n    abs = path.join(self.root, f);\n  } else if (isAbsolute(f) || f === '') {\n    abs = f;\n  } else if (self.changedCwd) {\n    abs = path.resolve(self.cwd, f);\n  } else {\n    abs = path.resolve(f);\n  }\n  if (process.platform === 'win32') abs = abs.replace(/\\\\/g, '/');\n  return abs;\n}\n\n// Return true, if pattern ends with globstar '**', for the accompanying parent directory.\n// Ex:- If node_modules/** is the pattern, add 'node_modules' to ignore list along with it's contents\nfunction isIgnored(self, path) {\n  if (!self.ignore.length) return false;\n  return self.ignore.some(function (item) {\n    return item.matcher.match(path) || !!(item.gmatcher && item.gmatcher.match(path));\n  });\n}\nfunction childrenIgnored(self, path) {\n  if (!self.ignore.length) return false;\n  return self.ignore.some(function (item) {\n    return !!(item.gmatcher && item.gmatcher.match(path));\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2xvYi9jb21tb24uanMiLCJtYXBwaW5ncyI6IkFBQUFBLGVBQWUsR0FBR0MsT0FBTztBQUN6QkQsZUFBZSxHQUFHRSxPQUFPO0FBQ3pCRixlQUFlLEdBQUdHLE9BQU87QUFDekJILGNBQWMsR0FBR0ksTUFBTTtBQUN2QkosWUFBWSxHQUFHSyxJQUFJO0FBQ25CTCxpQkFBaUIsR0FBR00sU0FBUztBQUM3Qk4sdUJBQXVCLEdBQUdPLGVBQWU7QUFFekMsU0FBU0wsT0FBT0EsQ0FBRU0sR0FBRyxFQUFFQyxLQUFLLEVBQUU7RUFDNUIsT0FBT0MsTUFBTSxDQUFDQyxTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDTCxHQUFHLEVBQUVDLEtBQUssQ0FBQztBQUN6RDtBQUVBLElBQUlLLEVBQUUsR0FBR0MsbUJBQU8sQ0FBQyxjQUFJLENBQUM7QUFDdEIsSUFBSUMsSUFBSSxHQUFHRCxtQkFBTyxDQUFDLGtCQUFNLENBQUM7QUFDMUIsSUFBSUUsU0FBUyxHQUFHRixtQkFBTyxDQUFDLDhEQUFXLENBQUM7QUFDcEMsSUFBSUcsVUFBVSxHQUFHSCxtQkFBTyxDQUFDLHdFQUFrQixDQUFDO0FBQzVDLElBQUlJLFNBQVMsR0FBR0YsU0FBUyxDQUFDRSxTQUFTO0FBRW5DLFNBQVNDLFNBQVNBLENBQUVDLENBQUMsRUFBRUMsQ0FBQyxFQUFFO0VBQ3hCLE9BQU9ELENBQUMsQ0FBQ0UsYUFBYSxDQUFDRCxDQUFDLEVBQUUsSUFBSSxDQUFDO0FBQ2pDO0FBRUEsU0FBU0UsWUFBWUEsQ0FBRUMsSUFBSSxFQUFFQyxPQUFPLEVBQUU7RUFDcENELElBQUksQ0FBQ0UsTUFBTSxHQUFHRCxPQUFPLENBQUNDLE1BQU0sSUFBSSxFQUFFO0VBRWxDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPLENBQUNKLElBQUksQ0FBQ0UsTUFBTSxDQUFDLEVBQzdCRixJQUFJLENBQUNFLE1BQU0sR0FBRyxDQUFDRixJQUFJLENBQUNFLE1BQU0sQ0FBQztFQUU3QixJQUFJRixJQUFJLENBQUNFLE1BQU0sQ0FBQ0csTUFBTSxFQUFFO0lBQ3RCTCxJQUFJLENBQUNFLE1BQU0sR0FBR0YsSUFBSSxDQUFDRSxNQUFNLENBQUNJLEdBQUcsQ0FBQ0MsU0FBUyxDQUFDO0VBQzFDO0FBQ0Y7O0FBRUE7QUFDQSxTQUFTQSxTQUFTQSxDQUFFQyxPQUFPLEVBQUU7RUFDM0IsSUFBSUMsUUFBUSxHQUFHLElBQUk7RUFDbkIsSUFBSUQsT0FBTyxDQUFDRSxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxLQUFLLEVBQUU7SUFDL0IsSUFBSUMsUUFBUSxHQUFHSCxPQUFPLENBQUNJLE9BQU8sQ0FBQyxZQUFZLEVBQUUsRUFBRSxDQUFDO0lBQ2hESCxRQUFRLEdBQUcsSUFBSWYsU0FBUyxDQUFDaUIsUUFBUSxFQUFFO01BQUVFLEdBQUcsRUFBRTtJQUFLLENBQUMsQ0FBQztFQUNuRDtFQUVBLE9BQU87SUFDTEMsT0FBTyxFQUFFLElBQUlwQixTQUFTLENBQUNjLE9BQU8sRUFBRTtNQUFFSyxHQUFHLEVBQUU7SUFBSyxDQUFDLENBQUM7SUFDOUNKLFFBQVEsRUFBRUE7RUFDWixDQUFDO0FBQ0g7QUFFQSxTQUFTakMsT0FBT0EsQ0FBRXdCLElBQUksRUFBRVEsT0FBTyxFQUFFUCxPQUFPLEVBQUU7RUFDeEMsSUFBSSxDQUFDQSxPQUFPLEVBQ1ZBLE9BQU8sR0FBRyxDQUFDLENBQUM7O0VBRWQ7RUFDQSxJQUFJQSxPQUFPLENBQUNjLFNBQVMsSUFBSSxDQUFDLENBQUMsS0FBS1AsT0FBTyxDQUFDUSxPQUFPLENBQUMsR0FBRyxDQUFDLEVBQUU7SUFDcEQsSUFBSWYsT0FBTyxDQUFDZ0IsVUFBVSxFQUFFO01BQ3RCLE1BQU0sSUFBSUMsS0FBSyxDQUFDLGlDQUFpQyxDQUFDO0lBQ3BEO0lBQ0FWLE9BQU8sR0FBRyxLQUFLLEdBQUdBLE9BQU87RUFDM0I7RUFFQVIsSUFBSSxDQUFDbUIsTUFBTSxHQUFHLENBQUMsQ0FBQ2xCLE9BQU8sQ0FBQ2tCLE1BQU07RUFDOUJuQixJQUFJLENBQUNRLE9BQU8sR0FBR0EsT0FBTztFQUN0QlIsSUFBSSxDQUFDb0IsTUFBTSxHQUFHbkIsT0FBTyxDQUFDbUIsTUFBTSxLQUFLLEtBQUs7RUFDdENwQixJQUFJLENBQUNxQixRQUFRLEdBQUcsQ0FBQyxDQUFDcEIsT0FBTyxDQUFDb0IsUUFBUTtFQUNsQ3JCLElBQUksQ0FBQ3NCLGFBQWEsR0FBR3JCLE9BQU8sQ0FBQ3FCLGFBQWEsSUFBSXJDLE1BQU0sQ0FBQ3NDLE1BQU0sQ0FBQyxJQUFJLENBQUM7RUFDakV2QixJQUFJLENBQUN3QixNQUFNLEdBQUcsQ0FBQyxDQUFDdkIsT0FBTyxDQUFDdUIsTUFBTTtFQUM5QnhCLElBQUksQ0FBQ2EsR0FBRyxHQUFHLENBQUMsQ0FBQ1osT0FBTyxDQUFDWSxHQUFHO0VBQ3hCYixJQUFJLENBQUNwQixJQUFJLEdBQUcsQ0FBQyxDQUFDcUIsT0FBTyxDQUFDckIsSUFBSTtFQUMxQm9CLElBQUksQ0FBQ3lCLEtBQUssR0FBRyxDQUFDLENBQUN4QixPQUFPLENBQUN3QixLQUFLO0VBQzVCLElBQUl6QixJQUFJLENBQUN5QixLQUFLLEVBQ1p6QixJQUFJLENBQUNwQixJQUFJLEdBQUcsSUFBSTtFQUNsQm9CLElBQUksQ0FBQzBCLElBQUksR0FBRyxDQUFDLENBQUN6QixPQUFPLENBQUN5QixJQUFJO0VBQzFCMUIsSUFBSSxDQUFDMkIsUUFBUSxHQUFHLENBQUMsQ0FBQzFCLE9BQU8sQ0FBQzBCLFFBQVE7RUFDbEMzQixJQUFJLENBQUM0QixNQUFNLEdBQUcsQ0FBQyxDQUFDM0IsT0FBTyxDQUFDMkIsTUFBTTtFQUM5QjVCLElBQUksQ0FBQzZCLE1BQU0sR0FBRyxDQUFDLENBQUM1QixPQUFPLENBQUM0QixNQUFNO0VBQzlCN0IsSUFBSSxDQUFDOEIsTUFBTSxHQUFHLENBQUMsQ0FBQzdCLE9BQU8sQ0FBQzZCLE1BQU07RUFDOUI5QixJQUFJLENBQUMrQixJQUFJLEdBQUcsQ0FBQyxDQUFDOUIsT0FBTyxDQUFDOEIsSUFBSTtFQUMxQi9CLElBQUksQ0FBQ2dDLFNBQVMsR0FBRyxDQUFDLENBQUMvQixPQUFPLENBQUMrQixTQUFTO0VBQ3BDaEMsSUFBSSxDQUFDaUMsUUFBUSxHQUFHLENBQUMsQ0FBQ2hDLE9BQU8sQ0FBQ2dDLFFBQVE7RUFDbENqQyxJQUFJLENBQUNYLEVBQUUsR0FBR1ksT0FBTyxDQUFDWixFQUFFLElBQUlBLEVBQUU7RUFFMUJXLElBQUksQ0FBQ2tDLFNBQVMsR0FBR2pDLE9BQU8sQ0FBQ2lDLFNBQVMsSUFBSUMsUUFBUTtFQUM5Q25DLElBQUksQ0FBQ29DLEtBQUssR0FBR25DLE9BQU8sQ0FBQ21DLEtBQUssSUFBSW5ELE1BQU0sQ0FBQ3NDLE1BQU0sQ0FBQyxJQUFJLENBQUM7RUFDakR2QixJQUFJLENBQUNxQyxTQUFTLEdBQUdwQyxPQUFPLENBQUNvQyxTQUFTLElBQUlwRCxNQUFNLENBQUNzQyxNQUFNLENBQUMsSUFBSSxDQUFDO0VBQ3pEdkIsSUFBSSxDQUFDc0MsUUFBUSxHQUFHckMsT0FBTyxDQUFDcUMsUUFBUSxJQUFJckQsTUFBTSxDQUFDc0MsTUFBTSxDQUFDLElBQUksQ0FBQztFQUV2RHhCLFlBQVksQ0FBQ0MsSUFBSSxFQUFFQyxPQUFPLENBQUM7RUFFM0JELElBQUksQ0FBQ3VDLFVBQVUsR0FBRyxLQUFLO0VBQ3ZCLElBQUlDLEdBQUcsR0FBR0MsT0FBTyxDQUFDRCxHQUFHLENBQUMsQ0FBQztFQUN2QixJQUFJLENBQUMvRCxPQUFPLENBQUN3QixPQUFPLEVBQUUsS0FBSyxDQUFDLEVBQzFCRCxJQUFJLENBQUN3QyxHQUFHLEdBQUdBLEdBQUcsTUFDWDtJQUNIeEMsSUFBSSxDQUFDd0MsR0FBRyxHQUFHakQsSUFBSSxDQUFDbUQsT0FBTyxDQUFDekMsT0FBTyxDQUFDdUMsR0FBRyxDQUFDO0lBQ3BDeEMsSUFBSSxDQUFDdUMsVUFBVSxHQUFHdkMsSUFBSSxDQUFDd0MsR0FBRyxLQUFLQSxHQUFHO0VBQ3BDO0VBRUF4QyxJQUFJLENBQUMyQyxJQUFJLEdBQUcxQyxPQUFPLENBQUMwQyxJQUFJLElBQUlwRCxJQUFJLENBQUNtRCxPQUFPLENBQUMxQyxJQUFJLENBQUN3QyxHQUFHLEVBQUUsR0FBRyxDQUFDO0VBQ3ZEeEMsSUFBSSxDQUFDMkMsSUFBSSxHQUFHcEQsSUFBSSxDQUFDbUQsT0FBTyxDQUFDMUMsSUFBSSxDQUFDMkMsSUFBSSxDQUFDO0VBQ25DLElBQUlGLE9BQU8sQ0FBQ0csUUFBUSxLQUFLLE9BQU8sRUFDOUI1QyxJQUFJLENBQUMyQyxJQUFJLEdBQUczQyxJQUFJLENBQUMyQyxJQUFJLENBQUMvQixPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQzs7RUFFM0M7RUFDQTtFQUNBWixJQUFJLENBQUM2QyxNQUFNLEdBQUdwRCxVQUFVLENBQUNPLElBQUksQ0FBQ3dDLEdBQUcsQ0FBQyxHQUFHeEMsSUFBSSxDQUFDd0MsR0FBRyxHQUFHOUQsT0FBTyxDQUFDc0IsSUFBSSxFQUFFQSxJQUFJLENBQUN3QyxHQUFHLENBQUM7RUFDdkUsSUFBSUMsT0FBTyxDQUFDRyxRQUFRLEtBQUssT0FBTyxFQUM5QjVDLElBQUksQ0FBQzZDLE1BQU0sR0FBRzdDLElBQUksQ0FBQzZDLE1BQU0sQ0FBQ2pDLE9BQU8sQ0FBQyxLQUFLLEVBQUUsR0FBRyxDQUFDO0VBQy9DWixJQUFJLENBQUM4QyxPQUFPLEdBQUcsQ0FBQyxDQUFDN0MsT0FBTyxDQUFDNkMsT0FBTzs7RUFFaEM7RUFDQTtFQUNBN0MsT0FBTyxDQUFDOEMsUUFBUSxHQUFHLElBQUk7RUFDdkI5QyxPQUFPLENBQUMrQyxTQUFTLEdBQUcsSUFBSTtFQUN4QjtFQUNBL0MsT0FBTyxDQUFDZ0Qsa0JBQWtCLEdBQUcsS0FBSztFQUVsQ2pELElBQUksQ0FBQ1IsU0FBUyxHQUFHLElBQUlFLFNBQVMsQ0FBQ2MsT0FBTyxFQUFFUCxPQUFPLENBQUM7RUFDaERELElBQUksQ0FBQ0MsT0FBTyxHQUFHRCxJQUFJLENBQUNSLFNBQVMsQ0FBQ1MsT0FBTztBQUN2QztBQUVBLFNBQVN0QixNQUFNQSxDQUFFcUIsSUFBSSxFQUFFO0VBQ3JCLElBQUlrRCxHQUFHLEdBQUdsRCxJQUFJLENBQUMyQixRQUFRO0VBQ3ZCLElBQUl3QixHQUFHLEdBQUdELEdBQUcsR0FBRyxFQUFFLEdBQUdqRSxNQUFNLENBQUNzQyxNQUFNLENBQUMsSUFBSSxDQUFDO0VBRXhDLEtBQUssSUFBSTZCLENBQUMsR0FBRyxDQUFDLEVBQUVDLENBQUMsR0FBR3JELElBQUksQ0FBQ3NELE9BQU8sQ0FBQ2pELE1BQU0sRUFBRStDLENBQUMsR0FBR0MsQ0FBQyxFQUFFRCxDQUFDLEVBQUcsRUFBRTtJQUNwRCxJQUFJRSxPQUFPLEdBQUd0RCxJQUFJLENBQUNzRCxPQUFPLENBQUNGLENBQUMsQ0FBQztJQUM3QixJQUFJLENBQUNFLE9BQU8sSUFBSXJFLE1BQU0sQ0FBQ3NFLElBQUksQ0FBQ0QsT0FBTyxDQUFDLENBQUNqRCxNQUFNLEtBQUssQ0FBQyxFQUFFO01BQ2pELElBQUlMLElBQUksQ0FBQzRCLE1BQU0sRUFBRTtRQUNmO1FBQ0EsSUFBSTRCLE9BQU8sR0FBR3hELElBQUksQ0FBQ1IsU0FBUyxDQUFDaUUsT0FBTyxDQUFDTCxDQUFDLENBQUM7UUFDdkMsSUFBSUYsR0FBRyxFQUNMQyxHQUFHLENBQUNPLElBQUksQ0FBQ0YsT0FBTyxDQUFDLE1BRWpCTCxHQUFHLENBQUNLLE9BQU8sQ0FBQyxHQUFHLElBQUk7TUFDdkI7SUFDRixDQUFDLE1BQU07TUFDTDtNQUNBLElBQUlHLENBQUMsR0FBRzFFLE1BQU0sQ0FBQ3NFLElBQUksQ0FBQ0QsT0FBTyxDQUFDO01BQzVCLElBQUlKLEdBQUcsRUFDTEMsR0FBRyxDQUFDTyxJQUFJLENBQUNFLEtBQUssQ0FBQ1QsR0FBRyxFQUFFUSxDQUFDLENBQUMsTUFFdEJBLENBQUMsQ0FBQ0UsT0FBTyxDQUFDLFVBQVVGLENBQUMsRUFBRTtRQUNyQlIsR0FBRyxDQUFDUSxDQUFDLENBQUMsR0FBRyxJQUFJO01BQ2YsQ0FBQyxDQUFDO0lBQ047RUFDRjtFQUVBLElBQUksQ0FBQ1QsR0FBRyxFQUNOQyxHQUFHLEdBQUdsRSxNQUFNLENBQUNzRSxJQUFJLENBQUNKLEdBQUcsQ0FBQztFQUV4QixJQUFJLENBQUNuRCxJQUFJLENBQUM2QixNQUFNLEVBQ2RzQixHQUFHLEdBQUdBLEdBQUcsQ0FBQ1csSUFBSSxDQUFDbkUsU0FBUyxDQUFDOztFQUUzQjtFQUNBLElBQUlLLElBQUksQ0FBQ3BCLElBQUksRUFBRTtJQUNiLEtBQUssSUFBSXdFLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsR0FBR0QsR0FBRyxDQUFDOUMsTUFBTSxFQUFFK0MsQ0FBQyxFQUFFLEVBQUU7TUFDbkNELEdBQUcsQ0FBQ0MsQ0FBQyxDQUFDLEdBQUdwRCxJQUFJLENBQUMrRCxLQUFLLENBQUNaLEdBQUcsQ0FBQ0MsQ0FBQyxDQUFDLENBQUM7SUFDN0I7SUFDQSxJQUFJcEQsSUFBSSxDQUFDeUIsS0FBSyxFQUFFO01BQ2QwQixHQUFHLEdBQUdBLEdBQUcsQ0FBQ2EsTUFBTSxDQUFDLFVBQVVDLENBQUMsRUFBRTtRQUM1QixJQUFJQyxNQUFNLEdBQUcsQ0FBRSxLQUFLLENBQUNDLElBQUksQ0FBQ0YsQ0FBQyxDQUFFO1FBQzdCLElBQUlHLENBQUMsR0FBR3BFLElBQUksQ0FBQ29DLEtBQUssQ0FBQzZCLENBQUMsQ0FBQyxJQUFJakUsSUFBSSxDQUFDb0MsS0FBSyxDQUFDMUQsT0FBTyxDQUFDc0IsSUFBSSxFQUFFaUUsQ0FBQyxDQUFDLENBQUM7UUFDckQsSUFBSUMsTUFBTSxJQUFJRSxDQUFDLEVBQ2JGLE1BQU0sR0FBR0UsQ0FBQyxLQUFLLEtBQUssSUFBSSxDQUFDakUsS0FBSyxDQUFDQyxPQUFPLENBQUNnRSxDQUFDLENBQUM7UUFDM0MsT0FBT0YsTUFBTTtNQUNmLENBQUMsQ0FBQztJQUNKO0VBQ0Y7RUFFQSxJQUFJbEUsSUFBSSxDQUFDRSxNQUFNLENBQUNHLE1BQU0sRUFDcEI4QyxHQUFHLEdBQUdBLEdBQUcsQ0FBQ2EsTUFBTSxDQUFDLFVBQVNMLENBQUMsRUFBRTtJQUMzQixPQUFPLENBQUM5RSxTQUFTLENBQUNtQixJQUFJLEVBQUUyRCxDQUFDLENBQUM7RUFDNUIsQ0FBQyxDQUFDO0VBRUozRCxJQUFJLENBQUNxRSxLQUFLLEdBQUdsQixHQUFHO0FBQ2xCO0FBRUEsU0FBU3ZFLElBQUlBLENBQUVvQixJQUFJLEVBQUVzRSxDQUFDLEVBQUU7RUFDdEIsSUFBSUMsR0FBRyxHQUFHN0YsT0FBTyxDQUFDc0IsSUFBSSxFQUFFc0UsQ0FBQyxDQUFDO0VBQzFCLElBQUlGLENBQUMsR0FBR3BFLElBQUksQ0FBQ29DLEtBQUssQ0FBQ21DLEdBQUcsQ0FBQztFQUN2QixJQUFJWixDQUFDLEdBQUdXLENBQUM7RUFDVCxJQUFJRixDQUFDLEVBQUU7SUFDTCxJQUFJSSxLQUFLLEdBQUdKLENBQUMsS0FBSyxLQUFLLElBQUlqRSxLQUFLLENBQUNDLE9BQU8sQ0FBQ2dFLENBQUMsQ0FBQztJQUMzQyxJQUFJSyxLQUFLLEdBQUdILENBQUMsQ0FBQzVELEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLEdBQUc7SUFFL0IsSUFBSThELEtBQUssSUFBSSxDQUFDQyxLQUFLLEVBQ2pCZCxDQUFDLElBQUksR0FBRyxNQUNMLElBQUksQ0FBQ2EsS0FBSyxJQUFJQyxLQUFLLEVBQ3RCZCxDQUFDLEdBQUdBLENBQUMsQ0FBQ2pELEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFFcEIsSUFBSWlELENBQUMsS0FBS1csQ0FBQyxFQUFFO01BQ1gsSUFBSUksSUFBSSxHQUFHaEcsT0FBTyxDQUFDc0IsSUFBSSxFQUFFMkQsQ0FBQyxDQUFDO01BQzNCM0QsSUFBSSxDQUFDcUMsU0FBUyxDQUFDcUMsSUFBSSxDQUFDLEdBQUcxRSxJQUFJLENBQUNxQyxTQUFTLENBQUNrQyxHQUFHLENBQUM7TUFDMUN2RSxJQUFJLENBQUNvQyxLQUFLLENBQUNzQyxJQUFJLENBQUMsR0FBRzFFLElBQUksQ0FBQ29DLEtBQUssQ0FBQ21DLEdBQUcsQ0FBQztJQUNwQztFQUNGO0VBRUEsT0FBT1osQ0FBQztBQUNWOztBQUVBO0FBQ0EsU0FBU2pGLE9BQU9BLENBQUVzQixJQUFJLEVBQUUyRSxDQUFDLEVBQUU7RUFDekIsSUFBSUosR0FBRyxHQUFHSSxDQUFDO0VBQ1gsSUFBSUEsQ0FBQyxDQUFDQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEtBQUssR0FBRyxFQUFFO0lBQ3ZCTCxHQUFHLEdBQUdoRixJQUFJLENBQUNzRixJQUFJLENBQUM3RSxJQUFJLENBQUMyQyxJQUFJLEVBQUVnQyxDQUFDLENBQUM7RUFDL0IsQ0FBQyxNQUFNLElBQUlsRixVQUFVLENBQUNrRixDQUFDLENBQUMsSUFBSUEsQ0FBQyxLQUFLLEVBQUUsRUFBRTtJQUNwQ0osR0FBRyxHQUFHSSxDQUFDO0VBQ1QsQ0FBQyxNQUFNLElBQUkzRSxJQUFJLENBQUN1QyxVQUFVLEVBQUU7SUFDMUJnQyxHQUFHLEdBQUdoRixJQUFJLENBQUNtRCxPQUFPLENBQUMxQyxJQUFJLENBQUN3QyxHQUFHLEVBQUVtQyxDQUFDLENBQUM7RUFDakMsQ0FBQyxNQUFNO0lBQ0xKLEdBQUcsR0FBR2hGLElBQUksQ0FBQ21ELE9BQU8sQ0FBQ2lDLENBQUMsQ0FBQztFQUN2QjtFQUVBLElBQUlsQyxPQUFPLENBQUNHLFFBQVEsS0FBSyxPQUFPLEVBQzlCMkIsR0FBRyxHQUFHQSxHQUFHLENBQUMzRCxPQUFPLENBQUMsS0FBSyxFQUFFLEdBQUcsQ0FBQztFQUUvQixPQUFPMkQsR0FBRztBQUNaOztBQUdBO0FBQ0E7QUFDQSxTQUFTMUYsU0FBU0EsQ0FBRW1CLElBQUksRUFBRVQsSUFBSSxFQUFFO0VBQzlCLElBQUksQ0FBQ1MsSUFBSSxDQUFDRSxNQUFNLENBQUNHLE1BQU0sRUFDckIsT0FBTyxLQUFLO0VBRWQsT0FBT0wsSUFBSSxDQUFDRSxNQUFNLENBQUM0RSxJQUFJLENBQUMsVUFBU0MsSUFBSSxFQUFFO0lBQ3JDLE9BQU9BLElBQUksQ0FBQ2pFLE9BQU8sQ0FBQ2tFLEtBQUssQ0FBQ3pGLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRXdGLElBQUksQ0FBQ3RFLFFBQVEsSUFBSXNFLElBQUksQ0FBQ3RFLFFBQVEsQ0FBQ3VFLEtBQUssQ0FBQ3pGLElBQUksQ0FBQyxDQUFDO0VBQ25GLENBQUMsQ0FBQztBQUNKO0FBRUEsU0FBU1QsZUFBZUEsQ0FBRWtCLElBQUksRUFBRVQsSUFBSSxFQUFFO0VBQ3BDLElBQUksQ0FBQ1MsSUFBSSxDQUFDRSxNQUFNLENBQUNHLE1BQU0sRUFDckIsT0FBTyxLQUFLO0VBRWQsT0FBT0wsSUFBSSxDQUFDRSxNQUFNLENBQUM0RSxJQUFJLENBQUMsVUFBU0MsSUFBSSxFQUFFO0lBQ3JDLE9BQU8sQ0FBQyxFQUFFQSxJQUFJLENBQUN0RSxRQUFRLElBQUlzRSxJQUFJLENBQUN0RSxRQUFRLENBQUN1RSxLQUFLLENBQUN6RixJQUFJLENBQUMsQ0FBQztFQUN2RCxDQUFDLENBQUM7QUFDSiIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGdsb2JcXGNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLnNldG9wdHMgPSBzZXRvcHRzXG5leHBvcnRzLm93blByb3AgPSBvd25Qcm9wXG5leHBvcnRzLm1ha2VBYnMgPSBtYWtlQWJzXG5leHBvcnRzLmZpbmlzaCA9IGZpbmlzaFxuZXhwb3J0cy5tYXJrID0gbWFya1xuZXhwb3J0cy5pc0lnbm9yZWQgPSBpc0lnbm9yZWRcbmV4cG9ydHMuY2hpbGRyZW5JZ25vcmVkID0gY2hpbGRyZW5JZ25vcmVkXG5cbmZ1bmN0aW9uIG93blByb3AgKG9iaiwgZmllbGQpIHtcbiAgcmV0dXJuIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvYmosIGZpZWxkKVxufVxuXG52YXIgZnMgPSByZXF1aXJlKFwiZnNcIilcbnZhciBwYXRoID0gcmVxdWlyZShcInBhdGhcIilcbnZhciBtaW5pbWF0Y2ggPSByZXF1aXJlKFwibWluaW1hdGNoXCIpXG52YXIgaXNBYnNvbHV0ZSA9IHJlcXVpcmUoXCJwYXRoLWlzLWFic29sdXRlXCIpXG52YXIgTWluaW1hdGNoID0gbWluaW1hdGNoLk1pbmltYXRjaFxuXG5mdW5jdGlvbiBhbHBoYXNvcnQgKGEsIGIpIHtcbiAgcmV0dXJuIGEubG9jYWxlQ29tcGFyZShiLCAnZW4nKVxufVxuXG5mdW5jdGlvbiBzZXR1cElnbm9yZXMgKHNlbGYsIG9wdGlvbnMpIHtcbiAgc2VsZi5pZ25vcmUgPSBvcHRpb25zLmlnbm9yZSB8fCBbXVxuXG4gIGlmICghQXJyYXkuaXNBcnJheShzZWxmLmlnbm9yZSkpXG4gICAgc2VsZi5pZ25vcmUgPSBbc2VsZi5pZ25vcmVdXG5cbiAgaWYgKHNlbGYuaWdub3JlLmxlbmd0aCkge1xuICAgIHNlbGYuaWdub3JlID0gc2VsZi5pZ25vcmUubWFwKGlnbm9yZU1hcClcbiAgfVxufVxuXG4vLyBpZ25vcmUgcGF0dGVybnMgYXJlIGFsd2F5cyBpbiBkb3Q6dHJ1ZSBtb2RlLlxuZnVuY3Rpb24gaWdub3JlTWFwIChwYXR0ZXJuKSB7XG4gIHZhciBnbWF0Y2hlciA9IG51bGxcbiAgaWYgKHBhdHRlcm4uc2xpY2UoLTMpID09PSAnLyoqJykge1xuICAgIHZhciBncGF0dGVybiA9IHBhdHRlcm4ucmVwbGFjZSgvKFxcL1xcKlxcKikrJC8sICcnKVxuICAgIGdtYXRjaGVyID0gbmV3IE1pbmltYXRjaChncGF0dGVybiwgeyBkb3Q6IHRydWUgfSlcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgbWF0Y2hlcjogbmV3IE1pbmltYXRjaChwYXR0ZXJuLCB7IGRvdDogdHJ1ZSB9KSxcbiAgICBnbWF0Y2hlcjogZ21hdGNoZXJcbiAgfVxufVxuXG5mdW5jdGlvbiBzZXRvcHRzIChzZWxmLCBwYXR0ZXJuLCBvcHRpb25zKSB7XG4gIGlmICghb3B0aW9ucylcbiAgICBvcHRpb25zID0ge31cblxuICAvLyBiYXNlLW1hdGNoaW5nOiBqdXN0IHVzZSBnbG9ic3RhciBmb3IgdGhhdC5cbiAgaWYgKG9wdGlvbnMubWF0Y2hCYXNlICYmIC0xID09PSBwYXR0ZXJuLmluZGV4T2YoXCIvXCIpKSB7XG4gICAgaWYgKG9wdGlvbnMubm9nbG9ic3Rhcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiYmFzZSBtYXRjaGluZyByZXF1aXJlcyBnbG9ic3RhclwiKVxuICAgIH1cbiAgICBwYXR0ZXJuID0gXCIqKi9cIiArIHBhdHRlcm5cbiAgfVxuXG4gIHNlbGYuc2lsZW50ID0gISFvcHRpb25zLnNpbGVudFxuICBzZWxmLnBhdHRlcm4gPSBwYXR0ZXJuXG4gIHNlbGYuc3RyaWN0ID0gb3B0aW9ucy5zdHJpY3QgIT09IGZhbHNlXG4gIHNlbGYucmVhbHBhdGggPSAhIW9wdGlvbnMucmVhbHBhdGhcbiAgc2VsZi5yZWFscGF0aENhY2hlID0gb3B0aW9ucy5yZWFscGF0aENhY2hlIHx8IE9iamVjdC5jcmVhdGUobnVsbClcbiAgc2VsZi5mb2xsb3cgPSAhIW9wdGlvbnMuZm9sbG93XG4gIHNlbGYuZG90ID0gISFvcHRpb25zLmRvdFxuICBzZWxmLm1hcmsgPSAhIW9wdGlvbnMubWFya1xuICBzZWxmLm5vZGlyID0gISFvcHRpb25zLm5vZGlyXG4gIGlmIChzZWxmLm5vZGlyKVxuICAgIHNlbGYubWFyayA9IHRydWVcbiAgc2VsZi5zeW5jID0gISFvcHRpb25zLnN5bmNcbiAgc2VsZi5ub3VuaXF1ZSA9ICEhb3B0aW9ucy5ub3VuaXF1ZVxuICBzZWxmLm5vbnVsbCA9ICEhb3B0aW9ucy5ub251bGxcbiAgc2VsZi5ub3NvcnQgPSAhIW9wdGlvbnMubm9zb3J0XG4gIHNlbGYubm9jYXNlID0gISFvcHRpb25zLm5vY2FzZVxuICBzZWxmLnN0YXQgPSAhIW9wdGlvbnMuc3RhdFxuICBzZWxmLm5vcHJvY2VzcyA9ICEhb3B0aW9ucy5ub3Byb2Nlc3NcbiAgc2VsZi5hYnNvbHV0ZSA9ICEhb3B0aW9ucy5hYnNvbHV0ZVxuICBzZWxmLmZzID0gb3B0aW9ucy5mcyB8fCBmc1xuXG4gIHNlbGYubWF4TGVuZ3RoID0gb3B0aW9ucy5tYXhMZW5ndGggfHwgSW5maW5pdHlcbiAgc2VsZi5jYWNoZSA9IG9wdGlvbnMuY2FjaGUgfHwgT2JqZWN0LmNyZWF0ZShudWxsKVxuICBzZWxmLnN0YXRDYWNoZSA9IG9wdGlvbnMuc3RhdENhY2hlIHx8IE9iamVjdC5jcmVhdGUobnVsbClcbiAgc2VsZi5zeW1saW5rcyA9IG9wdGlvbnMuc3ltbGlua3MgfHwgT2JqZWN0LmNyZWF0ZShudWxsKVxuXG4gIHNldHVwSWdub3JlcyhzZWxmLCBvcHRpb25zKVxuXG4gIHNlbGYuY2hhbmdlZEN3ZCA9IGZhbHNlXG4gIHZhciBjd2QgPSBwcm9jZXNzLmN3ZCgpXG4gIGlmICghb3duUHJvcChvcHRpb25zLCBcImN3ZFwiKSlcbiAgICBzZWxmLmN3ZCA9IGN3ZFxuICBlbHNlIHtcbiAgICBzZWxmLmN3ZCA9IHBhdGgucmVzb2x2ZShvcHRpb25zLmN3ZClcbiAgICBzZWxmLmNoYW5nZWRDd2QgPSBzZWxmLmN3ZCAhPT0gY3dkXG4gIH1cblxuICBzZWxmLnJvb3QgPSBvcHRpb25zLnJvb3QgfHwgcGF0aC5yZXNvbHZlKHNlbGYuY3dkLCBcIi9cIilcbiAgc2VsZi5yb290ID0gcGF0aC5yZXNvbHZlKHNlbGYucm9vdClcbiAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09IFwid2luMzJcIilcbiAgICBzZWxmLnJvb3QgPSBzZWxmLnJvb3QucmVwbGFjZSgvXFxcXC9nLCBcIi9cIilcblxuICAvLyBUT0RPOiBpcyBhbiBhYnNvbHV0ZSBgY3dkYCBzdXBwb3NlZCB0byBiZSByZXNvbHZlZCBhZ2FpbnN0IGByb290YD9cbiAgLy8gZS5nLiB7IGN3ZDogJy90ZXN0Jywgcm9vdDogX19kaXJuYW1lIH0gPT09IHBhdGguam9pbihfX2Rpcm5hbWUsICcvdGVzdCcpXG4gIHNlbGYuY3dkQWJzID0gaXNBYnNvbHV0ZShzZWxmLmN3ZCkgPyBzZWxmLmN3ZCA6IG1ha2VBYnMoc2VsZiwgc2VsZi5jd2QpXG4gIGlmIChwcm9jZXNzLnBsYXRmb3JtID09PSBcIndpbjMyXCIpXG4gICAgc2VsZi5jd2RBYnMgPSBzZWxmLmN3ZEFicy5yZXBsYWNlKC9cXFxcL2csIFwiL1wiKVxuICBzZWxmLm5vbW91bnQgPSAhIW9wdGlvbnMubm9tb3VudFxuXG4gIC8vIGRpc2FibGUgY29tbWVudHMgYW5kIG5lZ2F0aW9uIGluIE1pbmltYXRjaC5cbiAgLy8gTm90ZSB0aGF0IHRoZXkgYXJlIG5vdCBzdXBwb3J0ZWQgaW4gR2xvYiBpdHNlbGYgYW55d2F5LlxuICBvcHRpb25zLm5vbmVnYXRlID0gdHJ1ZVxuICBvcHRpb25zLm5vY29tbWVudCA9IHRydWVcbiAgLy8gYWx3YXlzIHRyZWF0IFxcIGluIHBhdHRlcm5zIGFzIGVzY2FwZXMsIG5vdCBwYXRoIHNlcGFyYXRvcnNcbiAgb3B0aW9ucy5hbGxvd1dpbmRvd3NFc2NhcGUgPSBmYWxzZVxuXG4gIHNlbGYubWluaW1hdGNoID0gbmV3IE1pbmltYXRjaChwYXR0ZXJuLCBvcHRpb25zKVxuICBzZWxmLm9wdGlvbnMgPSBzZWxmLm1pbmltYXRjaC5vcHRpb25zXG59XG5cbmZ1bmN0aW9uIGZpbmlzaCAoc2VsZikge1xuICB2YXIgbm91ID0gc2VsZi5ub3VuaXF1ZVxuICB2YXIgYWxsID0gbm91ID8gW10gOiBPYmplY3QuY3JlYXRlKG51bGwpXG5cbiAgZm9yICh2YXIgaSA9IDAsIGwgPSBzZWxmLm1hdGNoZXMubGVuZ3RoOyBpIDwgbDsgaSArKykge1xuICAgIHZhciBtYXRjaGVzID0gc2VsZi5tYXRjaGVzW2ldXG4gICAgaWYgKCFtYXRjaGVzIHx8IE9iamVjdC5rZXlzKG1hdGNoZXMpLmxlbmd0aCA9PT0gMCkge1xuICAgICAgaWYgKHNlbGYubm9udWxsKSB7XG4gICAgICAgIC8vIGRvIGxpa2UgdGhlIHNoZWxsLCBhbmQgc3BpdCBvdXQgdGhlIGxpdGVyYWwgZ2xvYlxuICAgICAgICB2YXIgbGl0ZXJhbCA9IHNlbGYubWluaW1hdGNoLmdsb2JTZXRbaV1cbiAgICAgICAgaWYgKG5vdSlcbiAgICAgICAgICBhbGwucHVzaChsaXRlcmFsKVxuICAgICAgICBlbHNlXG4gICAgICAgICAgYWxsW2xpdGVyYWxdID0gdHJ1ZVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBoYWQgbWF0Y2hlc1xuICAgICAgdmFyIG0gPSBPYmplY3Qua2V5cyhtYXRjaGVzKVxuICAgICAgaWYgKG5vdSlcbiAgICAgICAgYWxsLnB1c2guYXBwbHkoYWxsLCBtKVxuICAgICAgZWxzZVxuICAgICAgICBtLmZvckVhY2goZnVuY3Rpb24gKG0pIHtcbiAgICAgICAgICBhbGxbbV0gPSB0cnVlXG4gICAgICAgIH0pXG4gICAgfVxuICB9XG5cbiAgaWYgKCFub3UpXG4gICAgYWxsID0gT2JqZWN0LmtleXMoYWxsKVxuXG4gIGlmICghc2VsZi5ub3NvcnQpXG4gICAgYWxsID0gYWxsLnNvcnQoYWxwaGFzb3J0KVxuXG4gIC8vIGF0ICpzb21lKiBwb2ludCB3ZSBzdGF0dGVkIGFsbCBvZiB0aGVzZVxuICBpZiAoc2VsZi5tYXJrKSB7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhbGwubGVuZ3RoOyBpKyspIHtcbiAgICAgIGFsbFtpXSA9IHNlbGYuX21hcmsoYWxsW2ldKVxuICAgIH1cbiAgICBpZiAoc2VsZi5ub2Rpcikge1xuICAgICAgYWxsID0gYWxsLmZpbHRlcihmdW5jdGlvbiAoZSkge1xuICAgICAgICB2YXIgbm90RGlyID0gISgvXFwvJC8udGVzdChlKSlcbiAgICAgICAgdmFyIGMgPSBzZWxmLmNhY2hlW2VdIHx8IHNlbGYuY2FjaGVbbWFrZUFicyhzZWxmLCBlKV1cbiAgICAgICAgaWYgKG5vdERpciAmJiBjKVxuICAgICAgICAgIG5vdERpciA9IGMgIT09ICdESVInICYmICFBcnJheS5pc0FycmF5KGMpXG4gICAgICAgIHJldHVybiBub3REaXJcbiAgICAgIH0pXG4gICAgfVxuICB9XG5cbiAgaWYgKHNlbGYuaWdub3JlLmxlbmd0aClcbiAgICBhbGwgPSBhbGwuZmlsdGVyKGZ1bmN0aW9uKG0pIHtcbiAgICAgIHJldHVybiAhaXNJZ25vcmVkKHNlbGYsIG0pXG4gICAgfSlcblxuICBzZWxmLmZvdW5kID0gYWxsXG59XG5cbmZ1bmN0aW9uIG1hcmsgKHNlbGYsIHApIHtcbiAgdmFyIGFicyA9IG1ha2VBYnMoc2VsZiwgcClcbiAgdmFyIGMgPSBzZWxmLmNhY2hlW2Fic11cbiAgdmFyIG0gPSBwXG4gIGlmIChjKSB7XG4gICAgdmFyIGlzRGlyID0gYyA9PT0gJ0RJUicgfHwgQXJyYXkuaXNBcnJheShjKVxuICAgIHZhciBzbGFzaCA9IHAuc2xpY2UoLTEpID09PSAnLydcblxuICAgIGlmIChpc0RpciAmJiAhc2xhc2gpXG4gICAgICBtICs9ICcvJ1xuICAgIGVsc2UgaWYgKCFpc0RpciAmJiBzbGFzaClcbiAgICAgIG0gPSBtLnNsaWNlKDAsIC0xKVxuXG4gICAgaWYgKG0gIT09IHApIHtcbiAgICAgIHZhciBtYWJzID0gbWFrZUFicyhzZWxmLCBtKVxuICAgICAgc2VsZi5zdGF0Q2FjaGVbbWFic10gPSBzZWxmLnN0YXRDYWNoZVthYnNdXG4gICAgICBzZWxmLmNhY2hlW21hYnNdID0gc2VsZi5jYWNoZVthYnNdXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG1cbn1cblxuLy8gbG90dGEgc2l0dXBzLi4uXG5mdW5jdGlvbiBtYWtlQWJzIChzZWxmLCBmKSB7XG4gIHZhciBhYnMgPSBmXG4gIGlmIChmLmNoYXJBdCgwKSA9PT0gJy8nKSB7XG4gICAgYWJzID0gcGF0aC5qb2luKHNlbGYucm9vdCwgZilcbiAgfSBlbHNlIGlmIChpc0Fic29sdXRlKGYpIHx8IGYgPT09ICcnKSB7XG4gICAgYWJzID0gZlxuICB9IGVsc2UgaWYgKHNlbGYuY2hhbmdlZEN3ZCkge1xuICAgIGFicyA9IHBhdGgucmVzb2x2ZShzZWxmLmN3ZCwgZilcbiAgfSBlbHNlIHtcbiAgICBhYnMgPSBwYXRoLnJlc29sdmUoZilcbiAgfVxuXG4gIGlmIChwcm9jZXNzLnBsYXRmb3JtID09PSAnd2luMzInKVxuICAgIGFicyA9IGFicy5yZXBsYWNlKC9cXFxcL2csICcvJylcblxuICByZXR1cm4gYWJzXG59XG5cblxuLy8gUmV0dXJuIHRydWUsIGlmIHBhdHRlcm4gZW5kcyB3aXRoIGdsb2JzdGFyICcqKicsIGZvciB0aGUgYWNjb21wYW55aW5nIHBhcmVudCBkaXJlY3RvcnkuXG4vLyBFeDotIElmIG5vZGVfbW9kdWxlcy8qKiBpcyB0aGUgcGF0dGVybiwgYWRkICdub2RlX21vZHVsZXMnIHRvIGlnbm9yZSBsaXN0IGFsb25nIHdpdGggaXQncyBjb250ZW50c1xuZnVuY3Rpb24gaXNJZ25vcmVkIChzZWxmLCBwYXRoKSB7XG4gIGlmICghc2VsZi5pZ25vcmUubGVuZ3RoKVxuICAgIHJldHVybiBmYWxzZVxuXG4gIHJldHVybiBzZWxmLmlnbm9yZS5zb21lKGZ1bmN0aW9uKGl0ZW0pIHtcbiAgICByZXR1cm4gaXRlbS5tYXRjaGVyLm1hdGNoKHBhdGgpIHx8ICEhKGl0ZW0uZ21hdGNoZXIgJiYgaXRlbS5nbWF0Y2hlci5tYXRjaChwYXRoKSlcbiAgfSlcbn1cblxuZnVuY3Rpb24gY2hpbGRyZW5JZ25vcmVkIChzZWxmLCBwYXRoKSB7XG4gIGlmICghc2VsZi5pZ25vcmUubGVuZ3RoKVxuICAgIHJldHVybiBmYWxzZVxuXG4gIHJldHVybiBzZWxmLmlnbm9yZS5zb21lKGZ1bmN0aW9uKGl0ZW0pIHtcbiAgICByZXR1cm4gISEoaXRlbS5nbWF0Y2hlciAmJiBpdGVtLmdtYXRjaGVyLm1hdGNoKHBhdGgpKVxuICB9KVxufVxuIl0sIm5hbWVzIjpbImV4cG9ydHMiLCJzZXRvcHRzIiwib3duUHJvcCIsIm1ha2VBYnMiLCJmaW5pc2giLCJtYXJrIiwiaXNJZ25vcmVkIiwiY2hpbGRyZW5JZ25vcmVkIiwib2JqIiwiZmllbGQiLCJPYmplY3QiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJmcyIsInJlcXVpcmUiLCJwYXRoIiwibWluaW1hdGNoIiwiaXNBYnNvbHV0ZSIsIk1pbmltYXRjaCIsImFscGhhc29ydCIsImEiLCJiIiwibG9jYWxlQ29tcGFyZSIsInNldHVwSWdub3JlcyIsInNlbGYiLCJvcHRpb25zIiwiaWdub3JlIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwibWFwIiwiaWdub3JlTWFwIiwicGF0dGVybiIsImdtYXRjaGVyIiwic2xpY2UiLCJncGF0dGVybiIsInJlcGxhY2UiLCJkb3QiLCJtYXRjaGVyIiwibWF0Y2hCYXNlIiwiaW5kZXhPZiIsIm5vZ2xvYnN0YXIiLCJFcnJvciIsInNpbGVudCIsInN0cmljdCIsInJlYWxwYXRoIiwicmVhbHBhdGhDYWNoZSIsImNyZWF0ZSIsImZvbGxvdyIsIm5vZGlyIiwic3luYyIsIm5vdW5pcXVlIiwibm9udWxsIiwibm9zb3J0Iiwibm9jYXNlIiwic3RhdCIsIm5vcHJvY2VzcyIsImFic29sdXRlIiwibWF4TGVuZ3RoIiwiSW5maW5pdHkiLCJjYWNoZSIsInN0YXRDYWNoZSIsInN5bWxpbmtzIiwiY2hhbmdlZEN3ZCIsImN3ZCIsInByb2Nlc3MiLCJyZXNvbHZlIiwicm9vdCIsInBsYXRmb3JtIiwiY3dkQWJzIiwibm9tb3VudCIsIm5vbmVnYXRlIiwibm9jb21tZW50IiwiYWxsb3dXaW5kb3dzRXNjYXBlIiwibm91IiwiYWxsIiwiaSIsImwiLCJtYXRjaGVzIiwia2V5cyIsImxpdGVyYWwiLCJnbG9iU2V0IiwicHVzaCIsIm0iLCJhcHBseSIsImZvckVhY2giLCJzb3J0IiwiX21hcmsiLCJmaWx0ZXIiLCJlIiwibm90RGlyIiwidGVzdCIsImMiLCJmb3VuZCIsInAiLCJhYnMiLCJpc0RpciIsInNsYXNoIiwibWFicyIsImYiLCJjaGFyQXQiLCJqb2luIiwic29tZSIsIml0ZW0iLCJtYXRjaCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/glob/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/glob/glob.js":
/*!***********************************!*\
  !*** ./node_modules/glob/glob.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// Approach:\n//\n// 1. Get the minimatch set\n// 2. For each pattern in the set, PROCESS(pattern, false)\n// 3. Store matches per-set, then uniq them\n//\n// PROCESS(pattern, inGlobStar)\n// Get the first [n] items from pattern that are all strings\n// Join these together.  This is PREFIX.\n//   If there is no more remaining, then stat(PREFIX) and\n//   add to matches if it succeeds.  END.\n//\n// If inGlobStar and PREFIX is symlink and points to dir\n//   set ENTRIES = []\n// else readdir(PREFIX) as ENTRIES\n//   If fail, END\n//\n// with ENTRIES\n//   If pattern[n] is GLOBSTAR\n//     // handle the case where the globstar match is empty\n//     // by pruning it out, and testing the resulting pattern\n//     PROCESS(pattern[0..n] + pattern[n+1 .. $], false)\n//     // handle other cases.\n//     for ENTRY in ENTRIES (not dotfiles)\n//       // attach globstar + tail onto the entry\n//       // Mark that this entry is a globstar match\n//       PROCESS(pattern[0..n] + ENTRY + pattern[n .. $], true)\n//\n//   else // not globstar\n//     for ENTRY in ENTRIES (not dotfiles, unless pattern[n] is dot)\n//       Test ENTRY against pattern[n]\n//       If fails, continue\n//       If passes, PROCESS(pattern[0..n] + item + pattern[n+1 .. $])\n//\n// Caveat:\n//   Cache all stats and readdirs results to minimize syscall.  Since all\n//   we ever care about is existence and directory-ness, we can just keep\n//   `true` for files, and [children,...] for directories, or `false` for\n//   things that don't exist.\n\nmodule.exports = glob;\nvar rp = __webpack_require__(/*! fs.realpath */ \"(rsc)/./node_modules/fs.realpath/index.js\");\nvar minimatch = __webpack_require__(/*! minimatch */ \"(rsc)/./node_modules/minimatch/minimatch.js\");\nvar Minimatch = minimatch.Minimatch;\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\");\nvar EE = (__webpack_require__(/*! events */ \"events\").EventEmitter);\nvar path = __webpack_require__(/*! path */ \"path\");\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(rsc)/./node_modules/path-is-absolute/index.js\");\nvar globSync = __webpack_require__(/*! ./sync.js */ \"(rsc)/./node_modules/glob/sync.js\");\nvar common = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/glob/common.js\");\nvar setopts = common.setopts;\nvar ownProp = common.ownProp;\nvar inflight = __webpack_require__(/*! inflight */ \"(rsc)/./node_modules/inflight/inflight.js\");\nvar util = __webpack_require__(/*! util */ \"util\");\nvar childrenIgnored = common.childrenIgnored;\nvar isIgnored = common.isIgnored;\nvar once = __webpack_require__(/*! once */ \"(rsc)/./node_modules/once/once.js\");\nfunction glob(pattern, options, cb) {\n  if (typeof options === 'function') cb = options, options = {};\n  if (!options) options = {};\n  if (options.sync) {\n    if (cb) throw new TypeError('callback provided to sync glob');\n    return globSync(pattern, options);\n  }\n  return new Glob(pattern, options, cb);\n}\nglob.sync = globSync;\nvar GlobSync = glob.GlobSync = globSync.GlobSync;\n\n// old api surface\nglob.glob = glob;\nfunction extend(origin, add) {\n  if (add === null || typeof add !== 'object') {\n    return origin;\n  }\n  var keys = Object.keys(add);\n  var i = keys.length;\n  while (i--) {\n    origin[keys[i]] = add[keys[i]];\n  }\n  return origin;\n}\nglob.hasMagic = function (pattern, options_) {\n  var options = extend({}, options_);\n  options.noprocess = true;\n  var g = new Glob(pattern, options);\n  var set = g.minimatch.set;\n  if (!pattern) return false;\n  if (set.length > 1) return true;\n  for (var j = 0; j < set[0].length; j++) {\n    if (typeof set[0][j] !== 'string') return true;\n  }\n  return false;\n};\nglob.Glob = Glob;\ninherits(Glob, EE);\nfunction Glob(pattern, options, cb) {\n  if (typeof options === 'function') {\n    cb = options;\n    options = null;\n  }\n  if (options && options.sync) {\n    if (cb) throw new TypeError('callback provided to sync glob');\n    return new GlobSync(pattern, options);\n  }\n  if (!(this instanceof Glob)) return new Glob(pattern, options, cb);\n  setopts(this, pattern, options);\n  this._didRealPath = false;\n\n  // process each pattern in the minimatch set\n  var n = this.minimatch.set.length;\n\n  // The matches are stored as {<filename>: true,...} so that\n  // duplicates are automagically pruned.\n  // Later, we do an Object.keys() on these.\n  // Keep them as a list so we can fill in when nonull is set.\n  this.matches = new Array(n);\n  if (typeof cb === 'function') {\n    cb = once(cb);\n    this.on('error', cb);\n    this.on('end', function (matches) {\n      cb(null, matches);\n    });\n  }\n  var self = this;\n  this._processing = 0;\n  this._emitQueue = [];\n  this._processQueue = [];\n  this.paused = false;\n  if (this.noprocess) return this;\n  if (n === 0) return done();\n  var sync = true;\n  for (var i = 0; i < n; i++) {\n    this._process(this.minimatch.set[i], i, false, done);\n  }\n  sync = false;\n  function done() {\n    --self._processing;\n    if (self._processing <= 0) {\n      if (sync) {\n        process.nextTick(function () {\n          self._finish();\n        });\n      } else {\n        self._finish();\n      }\n    }\n  }\n}\nGlob.prototype._finish = function () {\n  assert(this instanceof Glob);\n  if (this.aborted) return;\n  if (this.realpath && !this._didRealpath) return this._realpath();\n  common.finish(this);\n  this.emit('end', this.found);\n};\nGlob.prototype._realpath = function () {\n  if (this._didRealpath) return;\n  this._didRealpath = true;\n  var n = this.matches.length;\n  if (n === 0) return this._finish();\n  var self = this;\n  for (var i = 0; i < this.matches.length; i++) this._realpathSet(i, next);\n  function next() {\n    if (--n === 0) self._finish();\n  }\n};\nGlob.prototype._realpathSet = function (index, cb) {\n  var matchset = this.matches[index];\n  if (!matchset) return cb();\n  var found = Object.keys(matchset);\n  var self = this;\n  var n = found.length;\n  if (n === 0) return cb();\n  var set = this.matches[index] = Object.create(null);\n  found.forEach(function (p, i) {\n    // If there's a problem with the stat, then it means that\n    // one or more of the links in the realpath couldn't be\n    // resolved.  just return the abs value in that case.\n    p = self._makeAbs(p);\n    rp.realpath(p, self.realpathCache, function (er, real) {\n      if (!er) set[real] = true;else if (er.syscall === 'stat') set[p] = true;else self.emit('error', er); // srsly wtf right here\n\n      if (--n === 0) {\n        self.matches[index] = set;\n        cb();\n      }\n    });\n  });\n};\nGlob.prototype._mark = function (p) {\n  return common.mark(this, p);\n};\nGlob.prototype._makeAbs = function (f) {\n  return common.makeAbs(this, f);\n};\nGlob.prototype.abort = function () {\n  this.aborted = true;\n  this.emit('abort');\n};\nGlob.prototype.pause = function () {\n  if (!this.paused) {\n    this.paused = true;\n    this.emit('pause');\n  }\n};\nGlob.prototype.resume = function () {\n  if (this.paused) {\n    this.emit('resume');\n    this.paused = false;\n    if (this._emitQueue.length) {\n      var eq = this._emitQueue.slice(0);\n      this._emitQueue.length = 0;\n      for (var i = 0; i < eq.length; i++) {\n        var e = eq[i];\n        this._emitMatch(e[0], e[1]);\n      }\n    }\n    if (this._processQueue.length) {\n      var pq = this._processQueue.slice(0);\n      this._processQueue.length = 0;\n      for (var i = 0; i < pq.length; i++) {\n        var p = pq[i];\n        this._processing--;\n        this._process(p[0], p[1], p[2], p[3]);\n      }\n    }\n  }\n};\nGlob.prototype._process = function (pattern, index, inGlobStar, cb) {\n  assert(this instanceof Glob);\n  assert(typeof cb === 'function');\n  if (this.aborted) return;\n  this._processing++;\n  if (this.paused) {\n    this._processQueue.push([pattern, index, inGlobStar, cb]);\n    return;\n  }\n\n  //console.error('PROCESS %d', this._processing, pattern)\n\n  // Get the first [n] parts of pattern that are all strings.\n  var n = 0;\n  while (typeof pattern[n] === 'string') {\n    n++;\n  }\n  // now n is the index of the first one that is *not* a string.\n\n  // see if there's anything else\n  var prefix;\n  switch (n) {\n    // if not, then this is rather simple\n    case pattern.length:\n      this._processSimple(pattern.join('/'), index, cb);\n      return;\n    case 0:\n      // pattern *starts* with some non-trivial item.\n      // going to readdir(cwd), but not include the prefix in matches.\n      prefix = null;\n      break;\n    default:\n      // pattern has some string bits in the front.\n      // whatever it starts with, whether that's 'absolute' like /foo/bar,\n      // or 'relative' like '../baz'\n      prefix = pattern.slice(0, n).join('/');\n      break;\n  }\n  var remain = pattern.slice(n);\n\n  // get the list of entries.\n  var read;\n  if (prefix === null) read = '.';else if (isAbsolute(prefix) || isAbsolute(pattern.map(function (p) {\n    return typeof p === 'string' ? p : '[*]';\n  }).join('/'))) {\n    if (!prefix || !isAbsolute(prefix)) prefix = '/' + prefix;\n    read = prefix;\n  } else read = prefix;\n  var abs = this._makeAbs(read);\n\n  //if ignored, skip _processing\n  if (childrenIgnored(this, read)) return cb();\n  var isGlobStar = remain[0] === minimatch.GLOBSTAR;\n  if (isGlobStar) this._processGlobStar(prefix, read, abs, remain, index, inGlobStar, cb);else this._processReaddir(prefix, read, abs, remain, index, inGlobStar, cb);\n};\nGlob.prototype._processReaddir = function (prefix, read, abs, remain, index, inGlobStar, cb) {\n  var self = this;\n  this._readdir(abs, inGlobStar, function (er, entries) {\n    return self._processReaddir2(prefix, read, abs, remain, index, inGlobStar, entries, cb);\n  });\n};\nGlob.prototype._processReaddir2 = function (prefix, read, abs, remain, index, inGlobStar, entries, cb) {\n  // if the abs isn't a dir, then nothing can match!\n  if (!entries) return cb();\n\n  // It will only match dot entries if it starts with a dot, or if\n  // dot is set.  Stuff like @(.foo|.bar) isn't allowed.\n  var pn = remain[0];\n  var negate = !!this.minimatch.negate;\n  var rawGlob = pn._glob;\n  var dotOk = this.dot || rawGlob.charAt(0) === '.';\n  var matchedEntries = [];\n  for (var i = 0; i < entries.length; i++) {\n    var e = entries[i];\n    if (e.charAt(0) !== '.' || dotOk) {\n      var m;\n      if (negate && !prefix) {\n        m = !e.match(pn);\n      } else {\n        m = e.match(pn);\n      }\n      if (m) matchedEntries.push(e);\n    }\n  }\n\n  //console.error('prd2', prefix, entries, remain[0]._glob, matchedEntries)\n\n  var len = matchedEntries.length;\n  // If there are no matched entries, then nothing matches.\n  if (len === 0) return cb();\n\n  // if this is the last remaining pattern bit, then no need for\n  // an additional stat *unless* the user has specified mark or\n  // stat explicitly.  We know they exist, since readdir returned\n  // them.\n\n  if (remain.length === 1 && !this.mark && !this.stat) {\n    if (!this.matches[index]) this.matches[index] = Object.create(null);\n    for (var i = 0; i < len; i++) {\n      var e = matchedEntries[i];\n      if (prefix) {\n        if (prefix !== '/') e = prefix + '/' + e;else e = prefix + e;\n      }\n      if (e.charAt(0) === '/' && !this.nomount) {\n        e = path.join(this.root, e);\n      }\n      this._emitMatch(index, e);\n    }\n    // This was the last one, and no stats were needed\n    return cb();\n  }\n\n  // now test all matched entries as stand-ins for that part\n  // of the pattern.\n  remain.shift();\n  for (var i = 0; i < len; i++) {\n    var e = matchedEntries[i];\n    var newPattern;\n    if (prefix) {\n      if (prefix !== '/') e = prefix + '/' + e;else e = prefix + e;\n    }\n    this._process([e].concat(remain), index, inGlobStar, cb);\n  }\n  cb();\n};\nGlob.prototype._emitMatch = function (index, e) {\n  if (this.aborted) return;\n  if (isIgnored(this, e)) return;\n  if (this.paused) {\n    this._emitQueue.push([index, e]);\n    return;\n  }\n  var abs = isAbsolute(e) ? e : this._makeAbs(e);\n  if (this.mark) e = this._mark(e);\n  if (this.absolute) e = abs;\n  if (this.matches[index][e]) return;\n  if (this.nodir) {\n    var c = this.cache[abs];\n    if (c === 'DIR' || Array.isArray(c)) return;\n  }\n  this.matches[index][e] = true;\n  var st = this.statCache[abs];\n  if (st) this.emit('stat', e, st);\n  this.emit('match', e);\n};\nGlob.prototype._readdirInGlobStar = function (abs, cb) {\n  if (this.aborted) return;\n\n  // follow all symlinked directories forever\n  // just proceed as if this is a non-globstar situation\n  if (this.follow) return this._readdir(abs, false, cb);\n  var lstatkey = 'lstat\\0' + abs;\n  var self = this;\n  var lstatcb = inflight(lstatkey, lstatcb_);\n  if (lstatcb) self.fs.lstat(abs, lstatcb);\n  function lstatcb_(er, lstat) {\n    if (er && er.code === 'ENOENT') return cb();\n    var isSym = lstat && lstat.isSymbolicLink();\n    self.symlinks[abs] = isSym;\n\n    // If it's not a symlink or a dir, then it's definitely a regular file.\n    // don't bother doing a readdir in that case.\n    if (!isSym && lstat && !lstat.isDirectory()) {\n      self.cache[abs] = 'FILE';\n      cb();\n    } else self._readdir(abs, false, cb);\n  }\n};\nGlob.prototype._readdir = function (abs, inGlobStar, cb) {\n  if (this.aborted) return;\n  cb = inflight('readdir\\0' + abs + '\\0' + inGlobStar, cb);\n  if (!cb) return;\n\n  //console.error('RD %j %j', +inGlobStar, abs)\n  if (inGlobStar && !ownProp(this.symlinks, abs)) return this._readdirInGlobStar(abs, cb);\n  if (ownProp(this.cache, abs)) {\n    var c = this.cache[abs];\n    if (!c || c === 'FILE') return cb();\n    if (Array.isArray(c)) return cb(null, c);\n  }\n  var self = this;\n  self.fs.readdir(abs, readdirCb(this, abs, cb));\n};\nfunction readdirCb(self, abs, cb) {\n  return function (er, entries) {\n    if (er) self._readdirError(abs, er, cb);else self._readdirEntries(abs, entries, cb);\n  };\n}\nGlob.prototype._readdirEntries = function (abs, entries, cb) {\n  if (this.aborted) return;\n\n  // if we haven't asked to stat everything, then just\n  // assume that everything in there exists, so we can avoid\n  // having to stat it a second time.\n  if (!this.mark && !this.stat) {\n    for (var i = 0; i < entries.length; i++) {\n      var e = entries[i];\n      if (abs === '/') e = abs + e;else e = abs + '/' + e;\n      this.cache[e] = true;\n    }\n  }\n  this.cache[abs] = entries;\n  return cb(null, entries);\n};\nGlob.prototype._readdirError = function (f, er, cb) {\n  if (this.aborted) return;\n\n  // handle errors, and cache the information\n  switch (er.code) {\n    case 'ENOTSUP': // https://github.com/isaacs/node-glob/issues/205\n    case 'ENOTDIR':\n      // totally normal. means it *does* exist.\n      var abs = this._makeAbs(f);\n      this.cache[abs] = 'FILE';\n      if (abs === this.cwdAbs) {\n        var error = new Error(er.code + ' invalid cwd ' + this.cwd);\n        error.path = this.cwd;\n        error.code = er.code;\n        this.emit('error', error);\n        this.abort();\n      }\n      break;\n    case 'ENOENT': // not terribly unusual\n    case 'ELOOP':\n    case 'ENAMETOOLONG':\n    case 'UNKNOWN':\n      this.cache[this._makeAbs(f)] = false;\n      break;\n    default:\n      // some unusual error.  Treat as failure.\n      this.cache[this._makeAbs(f)] = false;\n      if (this.strict) {\n        this.emit('error', er);\n        // If the error is handled, then we abort\n        // if not, we threw out of here\n        this.abort();\n      }\n      if (!this.silent) console.error('glob error', er);\n      break;\n  }\n  return cb();\n};\nGlob.prototype._processGlobStar = function (prefix, read, abs, remain, index, inGlobStar, cb) {\n  var self = this;\n  this._readdir(abs, inGlobStar, function (er, entries) {\n    self._processGlobStar2(prefix, read, abs, remain, index, inGlobStar, entries, cb);\n  });\n};\nGlob.prototype._processGlobStar2 = function (prefix, read, abs, remain, index, inGlobStar, entries, cb) {\n  //console.error('pgs2', prefix, remain[0], entries)\n\n  // no entries means not a dir, so it can never have matches\n  // foo.txt/** doesn't match foo.txt\n  if (!entries) return cb();\n\n  // test without the globstar, and with every child both below\n  // and replacing the globstar.\n  var remainWithoutGlobStar = remain.slice(1);\n  var gspref = prefix ? [prefix] : [];\n  var noGlobStar = gspref.concat(remainWithoutGlobStar);\n\n  // the noGlobStar pattern exits the inGlobStar state\n  this._process(noGlobStar, index, false, cb);\n  var isSym = this.symlinks[abs];\n  var len = entries.length;\n\n  // If it's a symlink, and we're in a globstar, then stop\n  if (isSym && inGlobStar) return cb();\n  for (var i = 0; i < len; i++) {\n    var e = entries[i];\n    if (e.charAt(0) === '.' && !this.dot) continue;\n\n    // these two cases enter the inGlobStar state\n    var instead = gspref.concat(entries[i], remainWithoutGlobStar);\n    this._process(instead, index, true, cb);\n    var below = gspref.concat(entries[i], remain);\n    this._process(below, index, true, cb);\n  }\n  cb();\n};\nGlob.prototype._processSimple = function (prefix, index, cb) {\n  // XXX review this.  Shouldn't it be doing the mounting etc\n  // before doing stat?  kinda weird?\n  var self = this;\n  this._stat(prefix, function (er, exists) {\n    self._processSimple2(prefix, index, er, exists, cb);\n  });\n};\nGlob.prototype._processSimple2 = function (prefix, index, er, exists, cb) {\n  //console.error('ps2', prefix, exists)\n\n  if (!this.matches[index]) this.matches[index] = Object.create(null);\n\n  // If it doesn't exist, then just mark the lack of results\n  if (!exists) return cb();\n  if (prefix && isAbsolute(prefix) && !this.nomount) {\n    var trail = /[\\/\\\\]$/.test(prefix);\n    if (prefix.charAt(0) === '/') {\n      prefix = path.join(this.root, prefix);\n    } else {\n      prefix = path.resolve(this.root, prefix);\n      if (trail) prefix += '/';\n    }\n  }\n  if (process.platform === 'win32') prefix = prefix.replace(/\\\\/g, '/');\n\n  // Mark this as a match\n  this._emitMatch(index, prefix);\n  cb();\n};\n\n// Returns either 'DIR', 'FILE', or false\nGlob.prototype._stat = function (f, cb) {\n  var abs = this._makeAbs(f);\n  var needDir = f.slice(-1) === '/';\n  if (f.length > this.maxLength) return cb();\n  if (!this.stat && ownProp(this.cache, abs)) {\n    var c = this.cache[abs];\n    if (Array.isArray(c)) c = 'DIR';\n\n    // It exists, but maybe not how we need it\n    if (!needDir || c === 'DIR') return cb(null, c);\n    if (needDir && c === 'FILE') return cb();\n\n    // otherwise we have to stat, because maybe c=true\n    // if we know it exists, but not what it is.\n  }\n\n  var exists;\n  var stat = this.statCache[abs];\n  if (stat !== undefined) {\n    if (stat === false) return cb(null, stat);else {\n      var type = stat.isDirectory() ? 'DIR' : 'FILE';\n      if (needDir && type === 'FILE') return cb();else return cb(null, type, stat);\n    }\n  }\n  var self = this;\n  var statcb = inflight('stat\\0' + abs, lstatcb_);\n  if (statcb) self.fs.lstat(abs, statcb);\n  function lstatcb_(er, lstat) {\n    if (lstat && lstat.isSymbolicLink()) {\n      // If it's a symlink, then treat it as the target, unless\n      // the target does not exist, then treat it as a file.\n      return self.fs.stat(abs, function (er, stat) {\n        if (er) self._stat2(f, abs, null, lstat, cb);else self._stat2(f, abs, er, stat, cb);\n      });\n    } else {\n      self._stat2(f, abs, er, lstat, cb);\n    }\n  }\n};\nGlob.prototype._stat2 = function (f, abs, er, stat, cb) {\n  if (er && (er.code === 'ENOENT' || er.code === 'ENOTDIR')) {\n    this.statCache[abs] = false;\n    return cb();\n  }\n  var needDir = f.slice(-1) === '/';\n  this.statCache[abs] = stat;\n  if (abs.slice(-1) === '/' && stat && !stat.isDirectory()) return cb(null, false, stat);\n  var c = true;\n  if (stat) c = stat.isDirectory() ? 'DIR' : 'FILE';\n  this.cache[abs] = this.cache[abs] || c;\n  if (needDir && c === 'FILE') return cb();\n  return cb(null, c, stat);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/glob/glob.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/glob/sync.js":
/*!***********************************!*\
  !*** ./node_modules/glob/sync.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = globSync;\nglobSync.GlobSync = GlobSync;\nvar rp = __webpack_require__(/*! fs.realpath */ \"(rsc)/./node_modules/fs.realpath/index.js\");\nvar minimatch = __webpack_require__(/*! minimatch */ \"(rsc)/./node_modules/minimatch/minimatch.js\");\nvar Minimatch = minimatch.Minimatch;\nvar Glob = (__webpack_require__(/*! ./glob.js */ \"(rsc)/./node_modules/glob/glob.js\").Glob);\nvar util = __webpack_require__(/*! util */ \"util\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar isAbsolute = __webpack_require__(/*! path-is-absolute */ \"(rsc)/./node_modules/path-is-absolute/index.js\");\nvar common = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/glob/common.js\");\nvar setopts = common.setopts;\nvar ownProp = common.ownProp;\nvar childrenIgnored = common.childrenIgnored;\nvar isIgnored = common.isIgnored;\nfunction globSync(pattern, options) {\n  if (typeof options === 'function' || arguments.length === 3) throw new TypeError('callback provided to sync glob\\n' + 'See: https://github.com/isaacs/node-glob/issues/167');\n  return new GlobSync(pattern, options).found;\n}\nfunction GlobSync(pattern, options) {\n  if (!pattern) throw new Error('must provide pattern');\n  if (typeof options === 'function' || arguments.length === 3) throw new TypeError('callback provided to sync glob\\n' + 'See: https://github.com/isaacs/node-glob/issues/167');\n  if (!(this instanceof GlobSync)) return new GlobSync(pattern, options);\n  setopts(this, pattern, options);\n  if (this.noprocess) return this;\n  var n = this.minimatch.set.length;\n  this.matches = new Array(n);\n  for (var i = 0; i < n; i++) {\n    this._process(this.minimatch.set[i], i, false);\n  }\n  this._finish();\n}\nGlobSync.prototype._finish = function () {\n  assert.ok(this instanceof GlobSync);\n  if (this.realpath) {\n    var self = this;\n    this.matches.forEach(function (matchset, index) {\n      var set = self.matches[index] = Object.create(null);\n      for (var p in matchset) {\n        try {\n          p = self._makeAbs(p);\n          var real = rp.realpathSync(p, self.realpathCache);\n          set[real] = true;\n        } catch (er) {\n          if (er.syscall === 'stat') set[self._makeAbs(p)] = true;else throw er;\n        }\n      }\n    });\n  }\n  common.finish(this);\n};\nGlobSync.prototype._process = function (pattern, index, inGlobStar) {\n  assert.ok(this instanceof GlobSync);\n\n  // Get the first [n] parts of pattern that are all strings.\n  var n = 0;\n  while (typeof pattern[n] === 'string') {\n    n++;\n  }\n  // now n is the index of the first one that is *not* a string.\n\n  // See if there's anything else\n  var prefix;\n  switch (n) {\n    // if not, then this is rather simple\n    case pattern.length:\n      this._processSimple(pattern.join('/'), index);\n      return;\n    case 0:\n      // pattern *starts* with some non-trivial item.\n      // going to readdir(cwd), but not include the prefix in matches.\n      prefix = null;\n      break;\n    default:\n      // pattern has some string bits in the front.\n      // whatever it starts with, whether that's 'absolute' like /foo/bar,\n      // or 'relative' like '../baz'\n      prefix = pattern.slice(0, n).join('/');\n      break;\n  }\n  var remain = pattern.slice(n);\n\n  // get the list of entries.\n  var read;\n  if (prefix === null) read = '.';else if (isAbsolute(prefix) || isAbsolute(pattern.map(function (p) {\n    return typeof p === 'string' ? p : '[*]';\n  }).join('/'))) {\n    if (!prefix || !isAbsolute(prefix)) prefix = '/' + prefix;\n    read = prefix;\n  } else read = prefix;\n  var abs = this._makeAbs(read);\n\n  //if ignored, skip processing\n  if (childrenIgnored(this, read)) return;\n  var isGlobStar = remain[0] === minimatch.GLOBSTAR;\n  if (isGlobStar) this._processGlobStar(prefix, read, abs, remain, index, inGlobStar);else this._processReaddir(prefix, read, abs, remain, index, inGlobStar);\n};\nGlobSync.prototype._processReaddir = function (prefix, read, abs, remain, index, inGlobStar) {\n  var entries = this._readdir(abs, inGlobStar);\n\n  // if the abs isn't a dir, then nothing can match!\n  if (!entries) return;\n\n  // It will only match dot entries if it starts with a dot, or if\n  // dot is set.  Stuff like @(.foo|.bar) isn't allowed.\n  var pn = remain[0];\n  var negate = !!this.minimatch.negate;\n  var rawGlob = pn._glob;\n  var dotOk = this.dot || rawGlob.charAt(0) === '.';\n  var matchedEntries = [];\n  for (var i = 0; i < entries.length; i++) {\n    var e = entries[i];\n    if (e.charAt(0) !== '.' || dotOk) {\n      var m;\n      if (negate && !prefix) {\n        m = !e.match(pn);\n      } else {\n        m = e.match(pn);\n      }\n      if (m) matchedEntries.push(e);\n    }\n  }\n  var len = matchedEntries.length;\n  // If there are no matched entries, then nothing matches.\n  if (len === 0) return;\n\n  // if this is the last remaining pattern bit, then no need for\n  // an additional stat *unless* the user has specified mark or\n  // stat explicitly.  We know they exist, since readdir returned\n  // them.\n\n  if (remain.length === 1 && !this.mark && !this.stat) {\n    if (!this.matches[index]) this.matches[index] = Object.create(null);\n    for (var i = 0; i < len; i++) {\n      var e = matchedEntries[i];\n      if (prefix) {\n        if (prefix.slice(-1) !== '/') e = prefix + '/' + e;else e = prefix + e;\n      }\n      if (e.charAt(0) === '/' && !this.nomount) {\n        e = path.join(this.root, e);\n      }\n      this._emitMatch(index, e);\n    }\n    // This was the last one, and no stats were needed\n    return;\n  }\n\n  // now test all matched entries as stand-ins for that part\n  // of the pattern.\n  remain.shift();\n  for (var i = 0; i < len; i++) {\n    var e = matchedEntries[i];\n    var newPattern;\n    if (prefix) newPattern = [prefix, e];else newPattern = [e];\n    this._process(newPattern.concat(remain), index, inGlobStar);\n  }\n};\nGlobSync.prototype._emitMatch = function (index, e) {\n  if (isIgnored(this, e)) return;\n  var abs = this._makeAbs(e);\n  if (this.mark) e = this._mark(e);\n  if (this.absolute) {\n    e = abs;\n  }\n  if (this.matches[index][e]) return;\n  if (this.nodir) {\n    var c = this.cache[abs];\n    if (c === 'DIR' || Array.isArray(c)) return;\n  }\n  this.matches[index][e] = true;\n  if (this.stat) this._stat(e);\n};\nGlobSync.prototype._readdirInGlobStar = function (abs) {\n  // follow all symlinked directories forever\n  // just proceed as if this is a non-globstar situation\n  if (this.follow) return this._readdir(abs, false);\n  var entries;\n  var lstat;\n  var stat;\n  try {\n    lstat = this.fs.lstatSync(abs);\n  } catch (er) {\n    if (er.code === 'ENOENT') {\n      // lstat failed, doesn't exist\n      return null;\n    }\n  }\n  var isSym = lstat && lstat.isSymbolicLink();\n  this.symlinks[abs] = isSym;\n\n  // If it's not a symlink or a dir, then it's definitely a regular file.\n  // don't bother doing a readdir in that case.\n  if (!isSym && lstat && !lstat.isDirectory()) this.cache[abs] = 'FILE';else entries = this._readdir(abs, false);\n  return entries;\n};\nGlobSync.prototype._readdir = function (abs, inGlobStar) {\n  var entries;\n  if (inGlobStar && !ownProp(this.symlinks, abs)) return this._readdirInGlobStar(abs);\n  if (ownProp(this.cache, abs)) {\n    var c = this.cache[abs];\n    if (!c || c === 'FILE') return null;\n    if (Array.isArray(c)) return c;\n  }\n  try {\n    return this._readdirEntries(abs, this.fs.readdirSync(abs));\n  } catch (er) {\n    this._readdirError(abs, er);\n    return null;\n  }\n};\nGlobSync.prototype._readdirEntries = function (abs, entries) {\n  // if we haven't asked to stat everything, then just\n  // assume that everything in there exists, so we can avoid\n  // having to stat it a second time.\n  if (!this.mark && !this.stat) {\n    for (var i = 0; i < entries.length; i++) {\n      var e = entries[i];\n      if (abs === '/') e = abs + e;else e = abs + '/' + e;\n      this.cache[e] = true;\n    }\n  }\n  this.cache[abs] = entries;\n\n  // mark and cache dir-ness\n  return entries;\n};\nGlobSync.prototype._readdirError = function (f, er) {\n  // handle errors, and cache the information\n  switch (er.code) {\n    case 'ENOTSUP': // https://github.com/isaacs/node-glob/issues/205\n    case 'ENOTDIR':\n      // totally normal. means it *does* exist.\n      var abs = this._makeAbs(f);\n      this.cache[abs] = 'FILE';\n      if (abs === this.cwdAbs) {\n        var error = new Error(er.code + ' invalid cwd ' + this.cwd);\n        error.path = this.cwd;\n        error.code = er.code;\n        throw error;\n      }\n      break;\n    case 'ENOENT': // not terribly unusual\n    case 'ELOOP':\n    case 'ENAMETOOLONG':\n    case 'UNKNOWN':\n      this.cache[this._makeAbs(f)] = false;\n      break;\n    default:\n      // some unusual error.  Treat as failure.\n      this.cache[this._makeAbs(f)] = false;\n      if (this.strict) throw er;\n      if (!this.silent) console.error('glob error', er);\n      break;\n  }\n};\nGlobSync.prototype._processGlobStar = function (prefix, read, abs, remain, index, inGlobStar) {\n  var entries = this._readdir(abs, inGlobStar);\n\n  // no entries means not a dir, so it can never have matches\n  // foo.txt/** doesn't match foo.txt\n  if (!entries) return;\n\n  // test without the globstar, and with every child both below\n  // and replacing the globstar.\n  var remainWithoutGlobStar = remain.slice(1);\n  var gspref = prefix ? [prefix] : [];\n  var noGlobStar = gspref.concat(remainWithoutGlobStar);\n\n  // the noGlobStar pattern exits the inGlobStar state\n  this._process(noGlobStar, index, false);\n  var len = entries.length;\n  var isSym = this.symlinks[abs];\n\n  // If it's a symlink, and we're in a globstar, then stop\n  if (isSym && inGlobStar) return;\n  for (var i = 0; i < len; i++) {\n    var e = entries[i];\n    if (e.charAt(0) === '.' && !this.dot) continue;\n\n    // these two cases enter the inGlobStar state\n    var instead = gspref.concat(entries[i], remainWithoutGlobStar);\n    this._process(instead, index, true);\n    var below = gspref.concat(entries[i], remain);\n    this._process(below, index, true);\n  }\n};\nGlobSync.prototype._processSimple = function (prefix, index) {\n  // XXX review this.  Shouldn't it be doing the mounting etc\n  // before doing stat?  kinda weird?\n  var exists = this._stat(prefix);\n  if (!this.matches[index]) this.matches[index] = Object.create(null);\n\n  // If it doesn't exist, then just mark the lack of results\n  if (!exists) return;\n  if (prefix && isAbsolute(prefix) && !this.nomount) {\n    var trail = /[\\/\\\\]$/.test(prefix);\n    if (prefix.charAt(0) === '/') {\n      prefix = path.join(this.root, prefix);\n    } else {\n      prefix = path.resolve(this.root, prefix);\n      if (trail) prefix += '/';\n    }\n  }\n  if (process.platform === 'win32') prefix = prefix.replace(/\\\\/g, '/');\n\n  // Mark this as a match\n  this._emitMatch(index, prefix);\n};\n\n// Returns either 'DIR', 'FILE', or false\nGlobSync.prototype._stat = function (f) {\n  var abs = this._makeAbs(f);\n  var needDir = f.slice(-1) === '/';\n  if (f.length > this.maxLength) return false;\n  if (!this.stat && ownProp(this.cache, abs)) {\n    var c = this.cache[abs];\n    if (Array.isArray(c)) c = 'DIR';\n\n    // It exists, but maybe not how we need it\n    if (!needDir || c === 'DIR') return c;\n    if (needDir && c === 'FILE') return false;\n\n    // otherwise we have to stat, because maybe c=true\n    // if we know it exists, but not what it is.\n  }\n\n  var exists;\n  var stat = this.statCache[abs];\n  if (!stat) {\n    var lstat;\n    try {\n      lstat = this.fs.lstatSync(abs);\n    } catch (er) {\n      if (er && (er.code === 'ENOENT' || er.code === 'ENOTDIR')) {\n        this.statCache[abs] = false;\n        return false;\n      }\n    }\n    if (lstat && lstat.isSymbolicLink()) {\n      try {\n        stat = this.fs.statSync(abs);\n      } catch (er) {\n        stat = lstat;\n      }\n    } else {\n      stat = lstat;\n    }\n  }\n  this.statCache[abs] = stat;\n  var c = true;\n  if (stat) c = stat.isDirectory() ? 'DIR' : 'FILE';\n  this.cache[abs] = this.cache[abs] || c;\n  if (needDir && c === 'FILE') return false;\n  return c;\n};\nGlobSync.prototype._mark = function (p) {\n  return common.mark(this, p);\n};\nGlobSync.prototype._makeAbs = function (f) {\n  return common.makeAbs(this, f);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/glob/sync.js\n");

/***/ })

};
;