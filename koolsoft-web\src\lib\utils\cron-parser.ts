/**
 * Cron expression parser and next execution calculator
 */

export interface CronComponents {
  minute: string;
  hour: string;
  dayOfMonth: string;
  month: string;
  dayOfWeek: string;
}

/**
 * Parse a cron expression into its components
 */
export function parseCronExpression(cronExpression: string): CronComponents {
  const parts = cronExpression.trim().split(/\s+/);
  
  if (parts.length !== 5) {
    throw new Error('Invalid cron expression: must have 5 parts (minute hour day month day-of-week)');
  }

  return {
    minute: parts[0],
    hour: parts[1],
    dayOfMonth: parts[2],
    month: parts[3],
    dayOfWeek: parts[4],
  };
}

/**
 * Calculate the next execution time based on a cron expression
 */
export function getNextExecutionTime(cronExpression: string, fromDate?: Date): Date {
  const now = fromDate || new Date();
  const components = parseCronExpression(cronExpression);
  
  // Start from the next minute
  const nextExecution = new Date(now);
  nextExecution.setSeconds(0);
  nextExecution.setMilliseconds(0);
  nextExecution.setMinutes(nextExecution.getMinutes() + 1);
  
  // Handle common patterns
  if (cronExpression === '0 9 * * *') {
    // Daily at 9 AM
    const next = new Date(nextExecution);
    next.setHours(9, 0, 0, 0);
    
    // If 9 AM today has passed, schedule for tomorrow
    if (next <= now) {
      next.setDate(next.getDate() + 1);
    }
    return next;
  }
  
  if (cronExpression === '0 9 * * 1') {
    // Weekly on Monday at 9 AM
    const next = new Date(nextExecution);
    next.setHours(9, 0, 0, 0);
    
    // Calculate days until next Monday
    const currentDay = next.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const daysUntilMonday = currentDay === 0 ? 1 : (8 - currentDay) % 7;
    
    // If it's Monday and 9 AM hasn't passed yet, use today
    if (currentDay === 1 && next > now) {
      return next;
    }
    
    // Otherwise, schedule for next Monday
    next.setDate(next.getDate() + (daysUntilMonday || 7));
    return next;
  }
  
  if (cronExpression === '0 9 1 * *') {
    // Monthly on 1st at 9 AM
    const next = new Date(nextExecution);
    next.setDate(1);
    next.setHours(9, 0, 0, 0);
    
    // If 1st of this month at 9 AM has passed, go to next month
    if (next <= now) {
      next.setMonth(next.getMonth() + 1);
    }
    return next;
  }
  
  if (cronExpression === '0 9 1 1,4,7,10 *') {
    // Quarterly on 1st at 9 AM (Jan, Apr, Jul, Oct)
    const next = new Date(nextExecution);
    next.setDate(1);
    next.setHours(9, 0, 0, 0);
    
    const quarterlyMonths = [0, 3, 6, 9]; // Jan, Apr, Jul, Oct (0-indexed)
    const currentMonth = next.getMonth();
    
    // Find next quarterly month
    let nextQuarterMonth = quarterlyMonths.find(month => month > currentMonth);
    
    if (!nextQuarterMonth) {
      // No more quarters this year, go to January next year
      next.setFullYear(next.getFullYear() + 1);
      next.setMonth(0);
    } else {
      next.setMonth(nextQuarterMonth);
    }
    
    return next;
  }
  
  if (cronExpression === '0 9 1 1 *') {
    // Yearly on Jan 1st at 9 AM
    const next = new Date(nextExecution);
    next.setMonth(0); // January
    next.setDate(1);
    next.setHours(9, 0, 0, 0);
    
    // If Jan 1st of this year at 9 AM has passed, go to next year
    if (next <= now) {
      next.setFullYear(next.getFullYear() + 1);
    }
    return next;
  }
  
  // For custom cron expressions, use a simplified approach
  // This is a basic implementation - in production you might want to use a proper cron library
  return calculateNextExecutionSimple(components, nextExecution);
}

/**
 * Simple calculation for custom cron expressions
 */
function calculateNextExecutionSimple(components: CronComponents, fromDate: Date): Date {
  const next = new Date(fromDate);
  
  // Handle minute
  if (components.minute !== '*') {
    const minute = parseInt(components.minute);
    if (!isNaN(minute) && minute >= 0 && minute <= 59) {
      next.setMinutes(minute);
    }
  }
  
  // Handle hour
  if (components.hour !== '*') {
    const hour = parseInt(components.hour);
    if (!isNaN(hour) && hour >= 0 && hour <= 23) {
      next.setHours(hour);
    }
  }
  
  // If the calculated time is in the past, add a day
  if (next <= fromDate) {
    next.setDate(next.getDate() + 1);
  }
  
  return next;
}

/**
 * Validate a cron expression
 */
export function validateCronExpression(cronExpression: string): boolean {
  try {
    const components = parseCronExpression(cronExpression);
    
    // Basic validation
    const minuteValid = validateCronField(components.minute, 0, 59);
    const hourValid = validateCronField(components.hour, 0, 23);
    const dayOfMonthValid = validateCronField(components.dayOfMonth, 1, 31);
    const monthValid = validateCronField(components.month, 1, 12);
    const dayOfWeekValid = validateCronField(components.dayOfWeek, 0, 6);
    
    return minuteValid && hourValid && dayOfMonthValid && monthValid && dayOfWeekValid;
  } catch {
    return false;
  }
}

/**
 * Validate a single cron field
 */
function validateCronField(field: string, min: number, max: number): boolean {
  if (field === '*') return true;
  
  // Handle ranges (e.g., "1-5")
  if (field.includes('-')) {
    const [start, end] = field.split('-').map(Number);
    return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
  }
  
  // Handle lists (e.g., "1,3,5")
  if (field.includes(',')) {
    const values = field.split(',').map(Number);
    return values.every(val => !isNaN(val) && val >= min && val <= max);
  }
  
  // Handle step values (e.g., "*/5")
  if (field.includes('/')) {
    const [base, step] = field.split('/');
    const stepNum = Number(step);
    return !isNaN(stepNum) && stepNum > 0 && (base === '*' || validateCronField(base, min, max));
  }
  
  // Handle single number
  const num = Number(field);
  return !isNaN(num) && num >= min && num <= max;
}

/**
 * Convert cron expression to human-readable description
 */
export function describeCronExpression(cronExpression: string): string {
  const descriptions: Record<string, string> = {
    '0 9 * * *': 'Daily at 9:00 AM',
    '0 9 * * 1': 'Weekly on Monday at 9:00 AM',
    '0 9 * * 2': 'Weekly on Tuesday at 9:00 AM',
    '0 9 * * 3': 'Weekly on Wednesday at 9:00 AM',
    '0 9 * * 4': 'Weekly on Thursday at 9:00 AM',
    '0 9 * * 5': 'Weekly on Friday at 9:00 AM',
    '0 9 1 * *': 'Monthly on the 1st at 9:00 AM',
    '0 9 1 1,4,7,10 *': 'Quarterly (Jan, Apr, Jul, Oct) on the 1st at 9:00 AM',
    '0 9 1 1 *': 'Yearly on January 1st at 9:00 AM',
    '0 0 * * *': 'Daily at midnight',
    '0 12 * * *': 'Daily at noon',
    '0 18 * * *': 'Daily at 6:00 PM',
    '0 9 * * 0': 'Weekly on Sunday at 9:00 AM',
    '0 9 * * 6': 'Weekly on Saturday at 9:00 AM',
  };
  
  if (descriptions[cronExpression]) {
    return descriptions[cronExpression];
  }
  
  // Try to parse and describe custom expressions
  try {
    const components = parseCronExpression(cronExpression);
    let description = '';
    
    // Frequency
    if (components.dayOfWeek !== '*') {
      description = 'Weekly';
    } else if (components.dayOfMonth !== '*') {
      description = 'Monthly';
    } else if (components.month !== '*') {
      description = 'Yearly';
    } else {
      description = 'Daily';
    }
    
    // Time
    if (components.hour !== '*' && components.minute !== '*') {
      const hour = parseInt(components.hour);
      const minute = parseInt(components.minute);
      if (!isNaN(hour) && !isNaN(minute)) {
        const time = new Date();
        time.setHours(hour, minute);
        description += ` at ${time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
    }
    
    return description;
  } catch {
    return cronExpression;
  }
}

/**
 * Get common cron presets
 */
export const CRON_PRESETS = {
  DAILY: '0 9 * * *',
  WEEKLY: '0 9 * * 1',
  MONTHLY: '0 9 1 * *',
  QUARTERLY: '0 9 1 1,4,7,10 *',
  YEARLY: '0 9 1 1 *',
} as const;
