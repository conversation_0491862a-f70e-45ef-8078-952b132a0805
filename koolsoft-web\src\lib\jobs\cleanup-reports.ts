import { ScheduledReportExecutionRepository } from '@/lib/repositories/scheduled-report-execution.repository';
import { cleanupOldReports } from '@/lib/utils/report-generator';

/**
 * Cleanup old report files and execution records
 * This job should be run periodically to maintain system performance
 */
export async function cleanupOldReportsJob() {
  console.log('Starting cleanup of old reports and execution records...');
  
  try {
    const executionRepository = new ScheduledReportExecutionRepository();
    
    // Clean up old execution records (keep 90 days)
    const deletedExecutions = await executionRepository.cleanupOldExecutions(90);
    console.log(`Cleaned up ${deletedExecutions} old execution records`);
    
    // Clean up old report files (keep 7 days)
    const deletedFiles = await cleanupOldReports(7);
    console.log(`Cleaned up ${deletedFiles} old report files`);
    
    return {
      success: true,
      deletedExecutions,
      deletedFiles,
      message: `Cleanup completed: ${deletedExecutions} execution records and ${deletedFiles} files removed`,
    };
  } catch (error) {
    console.error('Error during cleanup job:', error);
    throw error;
  }
}
