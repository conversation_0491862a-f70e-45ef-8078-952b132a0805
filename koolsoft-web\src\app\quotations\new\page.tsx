'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createQuotationSchema, type CreateQuotation } from '@/lib/validations/quotation.schema';
import {
  Plus,
  Trash2,
  Save,
  FileText,
  Calculator,
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  city?: string;
}

interface Executive {
  id: string;
  name: string;
  email?: string;
  designation?: string;
}

interface Product {
  id: string;
  name: string;
  description?: string;
}

interface Model {
  id: string;
  name: string;
  description?: string;
  specs?: string;
  tonnage?: number;
  bslMRP?: number;
  bslMCP?: number;
  taplMRP?: number;
  taplMCP?: number;
}

/**
 * New Quotation Page
 *
 * This page provides a form for creating new quotations with line items.
 * It follows the established KoolSoft UI patterns and form validation standards.
 */
export default function NewQuotationPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(createQuotationSchema),
    defaultValues: {
      quotationDate: new Date(),
      status: 'DRAFT',
      items: [
        {
          description: '',
          quantity: 1,
          unitPrice: 0,
          totalPrice: 0,
          taxRate: 18,
          taxAmount: 0,
          discount: 0,
          discountType: 'PERCENTAGE',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  const watchedItems = watch('items');

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        const [customersRes, executivesRes, productsRes, modelsRes] = await Promise.all([
          fetch('/api/customers?limit=100', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&limit=100', { credentials: 'include' }),
          fetch('/api/products?limit=100', { credentials: 'include' }),
          fetch('/api/models?limit=100', { credentials: 'include' }),
        ]);

        const [customersData, executivesData, productsData, modelsData] = await Promise.all([
          customersRes.json(),
          executivesRes.json(),
          productsRes.json(),
          modelsRes.json(),
        ]);

        // Handle customers data
        if (customersData.success && Array.isArray(customersData.data)) {
          setCustomers(customersData.data);
        } else {
          console.warn('Invalid customers data:', customersData);
          setCustomers([]);
        }

        // Handle executives data
        if (executivesData.success && Array.isArray(executivesData.data)) {
          setExecutives(executivesData.data);
        } else {
          console.warn('Invalid executives data:', executivesData);
          setExecutives([]);
        }

        // Handle products data
        if (productsData.success && Array.isArray(productsData.data)) {
          setProducts(productsData.data);
        } else {
          console.warn('Invalid products data:', productsData);
          setProducts([]);
        }

        // Handle models data
        if (modelsData.success && Array.isArray(modelsData.data)) {
          setModels(modelsData.data);
        } else {
          console.warn('Invalid models data:', modelsData);
          setModels([]);
        }
      } catch (error) {
        console.error('Error fetching reference data:', error);
        toast({
          title: 'Warning',
          description: 'Some reference data could not be loaded.',
          variant: 'destructive',
        });
      }
    };

    fetchReferenceData();
  }, [toast]);

  // Calculate item totals
  const calculateItemTotal = (index: number) => {
    const item = watchedItems[index];
    if (!item) return;

    const quantity = item.quantity || 0;
    const unitPrice = item.unitPrice || 0;
    const taxRate = item.taxRate || 0;
    const discount = item.discount || 0;
    const discountType = item.discountType || 'PERCENTAGE';

    let totalPrice = quantity * unitPrice;
    
    // Apply discount
    if (discount > 0) {
      if (discountType === 'PERCENTAGE') {
        totalPrice = totalPrice * (1 - discount / 100);
      } else {
        totalPrice = totalPrice - discount;
      }
    }

    const taxAmount = (totalPrice * taxRate) / 100;

    setValue(`items.${index}.totalPrice`, totalPrice);
    setValue(`items.${index}.taxAmount`, taxAmount);
  };

  // Add new item
  const addItem = () => {
    append({
      description: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
      taxRate: 18,
      taxAmount: 0,
      discount: 0,
      discountType: 'PERCENTAGE',
    });
  };

  // Remove item
  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  // Handle form submission
  const onSubmit = async (data: CreateQuotation) => {
    try {
      setLoading(true);

      const response = await fetch('/api/quotations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create quotation');
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: 'Quotation created successfully.',
        });
        router.push(`/quotations/${result.data.id}`);
      } else {
        throw new Error(result.error || 'Failed to create quotation');
      }
    } catch (error) {
      console.error('Error creating quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to create quotation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = watchedItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
    const totalTax = watchedItems.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
    const total = subtotal + totalTax;

    return { subtotal, totalTax, total };
  };

  const { subtotal, totalTax, total } = calculateTotals();

  return (
    <DashboardLayout title="New Quotation" requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Create New Quotation</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Generate a new quotation for customer products and services
            </CardDescription>
          </CardHeader>
        </Card>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="customerId">Customer *</Label>
                  <Select onValueChange={(value) => setValue('customerId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(customers) && customers.length > 0 ? (
                        customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.name} {customer.city && `- ${customer.city}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No customers available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.customerId && (
                    <p className="text-sm text-red-600">{errors.customerId.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="executiveId">Executive *</Label>
                  <Select onValueChange={(value) => setValue('executiveId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select executive" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(executives) && executives.length > 0 ? (
                        executives.map((executive) => (
                          <SelectItem key={executive.id} value={executive.id}>
                            {executive.name} {executive.designation && `- ${executive.designation}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No executives available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.executiveId && (
                    <p className="text-sm text-red-600">{errors.executiveId.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="quotationDate">Quotation Date *</Label>
                  <Input
                    type="date"
                    {...register('quotationDate', { valueAsDate: true })}
                  />
                  {errors.quotationDate && (
                    <p className="text-sm text-red-600">{errors.quotationDate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="validUntil">Valid Until</Label>
                  <Input
                    type="date"
                    {...register('validUntil', { valueAsDate: true })}
                  />
                  {errors.validUntil && (
                    <p className="text-sm text-red-600">{errors.validUntil.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactPerson">Contact Person</Label>
                  <Input {...register('contactPerson')} />
                  {errors.contactPerson && (
                    <p className="text-sm text-red-600">{errors.contactPerson.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input {...register('contactPhone')} />
                  {errors.contactPhone && (
                    <p className="text-sm text-red-600">{errors.contactPhone.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="subject">Subject</Label>
                <Input {...register('subject')} placeholder="Quotation subject" />
                {errors.subject && (
                  <p className="text-sm text-red-600">{errors.subject.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea {...register('notes')} rows={3} />
                {errors.notes && (
                  <p className="text-sm text-red-600">{errors.notes.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Line Items</CardTitle>
                <Button type="button" onClick={addItem} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeItem(index)}
                          variant="ghost"
                          size="sm"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="md:col-span-2">
                        <Label>Description *</Label>
                        <Input
                          {...register(`items.${index}.description`)}
                          placeholder="Item description"
                        />
                        {errors.items?.[index]?.description && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.description?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Quantity *</Label>
                        <Input
                          type="number"
                          min="1"
                          {...register(`items.${index}.quantity`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                        {errors.items?.[index]?.quantity && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.quantity?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Unit Price *</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...register(`items.${index}.unitPrice`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                        {errors.items?.[index]?.unitPrice && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.unitPrice?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Tax Rate (%)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          {...register(`items.${index}.taxRate`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                      </div>

                      <div>
                        <Label>Discount</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...register(`items.${index}.discount`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                      </div>

                      <div>
                        <Label>Total Price</Label>
                        <Input
                          type="number"
                          readOnly
                          value={watchedItems[index]?.totalPrice?.toFixed(2) || '0.00'}
                          className="bg-gray-50"
                        />
                      </div>

                      <div>
                        <Label>Tax Amount</Label>
                        <Input
                          type="number"
                          readOnly
                          value={watchedItems[index]?.taxAmount?.toFixed(2) || '0.00'}
                          className="bg-gray-50"
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Specifications</Label>
                      <Textarea
                        {...register(`items.${index}.specifications`)}
                        rows={2}
                        placeholder="Technical specifications or additional details"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Totals */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Totals</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-w-md ml-auto">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Tax:</span>
                  <span>₹{totalTax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>₹{total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Creating...' : 'Create Quotation'}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
