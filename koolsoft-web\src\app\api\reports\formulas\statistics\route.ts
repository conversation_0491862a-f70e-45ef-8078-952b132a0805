/**
 * Formula Statistics API Route
 * 
 * Provides statistical information about formulas including
 * usage counts, categories, and performance metrics.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { FormulaRepository } from '@/lib/repositories/formula.repository';

const formulaRepository = new FormulaRepository();

/**
 * GET /api/reports/formulas/statistics
 * Get formula statistics and metrics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const statistics = await formulaRepository.getFormulaStatistics();

    return NextResponse.json({
      success: true,
      data: statistics,
    });

  } catch (error) {
    console.error('Error fetching formula statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch formula statistics' },
      { status: 500 }
    );
  }
}
