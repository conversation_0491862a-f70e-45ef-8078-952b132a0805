import * as fs from 'fs';
import * as path from 'path';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as ExcelJS from 'exceljs';
import { ExportFormat } from '@/lib/validations/scheduled-report.schema';

/**
 * Utility for generating report files in various formats
 */

// Ensure reports directory exists
const REPORTS_DIR = path.join(process.cwd(), 'public', 'reports');
if (!fs.existsSync(REPORTS_DIR)) {
  fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * Generate a report file in the specified format
 */
export async function generateReportFile(
  reportData: any,
  format: ExportFormat,
  reportName: string
): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = `${reportName}_${timestamp}.${format.toLowerCase()}`;
  const filePath = path.join(REPORTS_DIR, fileName);

  switch (format) {
    case 'PDF':
      await generatePDFReport(reportData, filePath, reportName);
      break;
    case 'EXCEL':
      await generateExcelReport(reportData, filePath, reportName);
      break;
    case 'CSV':
      await generateCSVReport(reportData, filePath);
      break;
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }

  return filePath;
}

/**
 * Generate PDF report
 */
async function generatePDFReport(reportData: any, filePath: string, reportName: string) {
  const doc = new jsPDF();
  
  // Add header
  doc.setFontSize(20);
  doc.text('KoolSoft', 20, 20);
  
  doc.setFontSize(16);
  doc.text(reportName, 20, 35);
  
  doc.setFontSize(12);
  doc.text(`Generated: ${new Date().toLocaleString()}`, 20, 45);
  doc.text(`Total Records: ${reportData.data?.length || 0}`, 20, 55);

  // Add table if data exists
  if (reportData.data && reportData.data.length > 0) {
    const tableData = formatDataForTable(reportData.data);
    
    autoTable(doc, {
      head: [tableData.headers],
      body: tableData.rows,
      startY: 70,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [15, 82, 186], // KoolSoft blue
        textColor: 255,
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
    });
  } else {
    doc.text('No data available for the selected criteria.', 20, 70);
  }

  // Save the PDF to file
  const pdfBuffer = doc.output('arraybuffer');
  fs.writeFileSync(filePath, Buffer.from(pdfBuffer));
}

/**
 * Generate Excel report
 */
async function generateExcelReport(reportData: any, filePath: string, reportName: string) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(reportName);

  // Add header information
  worksheet.addRow(['KoolSoft - ' + reportName]);
  worksheet.addRow(['Generated:', new Date().toLocaleString()]);
  worksheet.addRow(['Total Records:', reportData.data?.length || 0]);
  worksheet.addRow([]); // Empty row

  // Style the header
  worksheet.getRow(1).font = { bold: true, size: 16 };
  worksheet.getRow(2).font = { bold: true };
  worksheet.getRow(3).font = { bold: true };

  if (reportData.data && reportData.data.length > 0) {
    const tableData = formatDataForTable(reportData.data);
    
    // Add headers
    const headerRow = worksheet.addRow(tableData.headers);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0F52BA' }, // KoolSoft blue
    };
    headerRow.font = { color: { argb: 'FFFFFFFF' }, bold: true };

    // Add data rows
    tableData.rows.forEach(row => {
      worksheet.addRow(row);
    });

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      if (column.header) {
        column.width = Math.max(column.header.length + 2, 15);
      }
    });
  } else {
    worksheet.addRow(['No data available for the selected criteria.']);
  }

  // Save the workbook
  await workbook.xlsx.writeFile(filePath);
}

/**
 * Generate CSV report
 */
async function generateCSVReport(reportData: any, filePath: string) {
  let csvContent = '';

  if (reportData.data && reportData.data.length > 0) {
    const tableData = formatDataForTable(reportData.data);
    
    // Add headers
    csvContent += tableData.headers.map(header => `"${header}"`).join(',') + '\n';
    
    // Add data rows
    tableData.rows.forEach(row => {
      csvContent += row.map(cell => `"${cell || ''}"`).join(',') + '\n';
    });
  } else {
    csvContent = 'No data available for the selected criteria.\n';
  }

  // Write to file
  fs.writeFileSync(filePath, csvContent, 'utf8');
}

/**
 * Format data for table display
 */
function formatDataForTable(data: any[]): { headers: string[], rows: any[][] } {
  if (!data || data.length === 0) {
    return { headers: [], rows: [] };
  }

  const firstItem = data[0];
  const headers: string[] = [];
  const rows: any[][] = [];

  // Determine headers based on the first item
  if (firstItem.customer) {
    // AMC/Warranty/Service reports with customer relation
    headers.push('Customer', 'Executive', 'Date', 'Status', 'Amount');
    
    data.forEach(item => {
      rows.push([
        item.customer?.name || '',
        item.users?.name || item.executive?.name || '',
        formatDate(item.startDate || item.installDate || item.reportDate || item.orderDate),
        item.status || '',
        formatCurrency(item.amount) || '',
      ]);
    });
  } else if (firstItem.name && firstItem.email) {
    // Customer reports
    headers.push('Name', 'Email', 'Phone', 'City', 'State', 'Active');
    
    data.forEach(item => {
      rows.push([
        item.name || '',
        item.email || '',
        item.phone || '',
        item.city || '',
        item.state || '',
        item.isActive ? 'Yes' : 'No',
      ]);
    });
  } else {
    // Generic format - use all available fields
    const allKeys = new Set<string>();
    data.forEach(item => {
      Object.keys(item).forEach(key => {
        if (typeof item[key] !== 'object' || item[key] === null) {
          allKeys.add(key);
        }
      });
    });
    
    headers.push(...Array.from(allKeys));
    
    data.forEach(item => {
      const row: any[] = [];
      headers.forEach(header => {
        let value = item[header];
        if (value instanceof Date) {
          value = formatDate(value);
        } else if (typeof value === 'boolean') {
          value = value ? 'Yes' : 'No';
        } else if (value === null || value === undefined) {
          value = '';
        }
        row.push(value);
      });
      rows.push(row);
    });
  }

  return { headers, rows };
}

/**
 * Format date for display
 */
function formatDate(date: any): string {
  if (!date) return '';
  
  try {
    const d = new Date(date);
    return d.toLocaleDateString();
  } catch {
    return String(date);
  }
}

/**
 * Format currency for display
 */
function formatCurrency(amount: any): string {
  if (!amount && amount !== 0) return '';
  
  try {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(Number(amount));
  } catch {
    return String(amount);
  }
}

/**
 * Clean up old report files
 */
export async function cleanupOldReports(daysToKeep: number = 7) {
  const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
  
  try {
    const files = fs.readdirSync(REPORTS_DIR);
    let deletedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(REPORTS_DIR, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    }
    
    console.log(`Cleaned up ${deletedCount} old report files`);
    return deletedCount;
  } catch (error) {
    console.error('Error cleaning up old reports:', error);
    return 0;
  }
}
