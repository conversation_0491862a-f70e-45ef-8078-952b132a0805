/**
 * Formula Validator for KoolSoft Report Formula Engine
 * 
 * This module provides validation for formula ASTs to catch potential
 * issues and provide warnings for best practices.
 */

import { FormulaAST } from './parser';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class FormulaValidator {
  /**
   * Validate a formula AST
   */
  validate(ast: FormulaAST): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    this.validateNode(ast, result);
    result.isValid = result.errors.length === 0;

    return result;
  }

  /**
   * Validate a single AST node
   */
  private validateNode(node: FormulaAST, result: ValidationResult): void {
    switch (node.type) {
      case 'BinaryExpression':
        this.validateBinaryExpression(node, result);
        break;

      case 'UnaryExpression':
        this.validateUnaryExpression(node, result);
        break;

      case 'FunctionCall':
        this.validateFunctionCall(node, result);
        break;

      case 'Identifier':
        this.validateIdentifier(node, result);
        break;

      case 'Literal':
        this.validateLiteral(node, result);
        break;
    }
  }

  /**
   * Validate binary expression
   */
  private validateBinaryExpression(node: FormulaAST, result: ValidationResult): void {
    if (!node.left || !node.right || !node.operator) {
      result.errors.push('Binary expression missing operands or operator');
      return;
    }

    // Validate operands
    this.validateNode(node.left, result);
    this.validateNode(node.right, result);

    // Check for division by zero potential
    if (node.operator === '/' && node.right.type === 'Literal' && node.right.value === 0) {
      result.errors.push('Division by zero detected');
    }

    // Check for modulo by zero potential
    if (node.operator === '%' && node.right.type === 'Literal' && node.right.value === 0) {
      result.errors.push('Modulo by zero detected');
    }

    // Warn about potential precision issues with floating point
    if ((node.operator === '/' || node.operator === '%') && 
        this.isFloatingPoint(node.left) || this.isFloatingPoint(node.right)) {
      result.warnings.push('Floating point division may result in precision issues');
    }

    // Warn about very large exponents
    if (node.operator === '^' && node.right.type === 'Literal' && 
        typeof node.right.value === 'number' && node.right.value > 100) {
      result.warnings.push('Very large exponents may cause performance issues or overflow');
    }
  }

  /**
   * Validate unary expression
   */
  private validateUnaryExpression(node: FormulaAST, result: ValidationResult): void {
    if (!node.argument || !node.operator) {
      result.errors.push('Unary expression missing argument or operator');
      return;
    }

    this.validateNode(node.argument, result);

    // Check for valid unary operators
    if (node.operator !== '+' && node.operator !== '-') {
      result.errors.push(`Invalid unary operator: ${node.operator}`);
    }
  }

  /**
   * Validate function call
   */
  private validateFunctionCall(node: FormulaAST, result: ValidationResult): void {
    if (!node.name) {
      result.errors.push('Function call missing name');
      return;
    }

    if (!node.arguments) {
      result.errors.push('Function call missing arguments array');
      return;
    }

    // Validate all arguments
    node.arguments.forEach(arg => this.validateNode(arg, result));

    // Function-specific validation
    this.validateSpecificFunction(node, result);
  }

  /**
   * Validate specific function requirements
   */
  private validateSpecificFunction(node: FormulaAST, result: ValidationResult): void {
    const funcName = node.name!.toUpperCase();
    const argCount = node.arguments!.length;

    // Define function argument requirements
    const functionRequirements: Record<string, { min: number; max?: number; types?: string[] }> = {
      'ABS': { min: 1, max: 1 },
      'ROUND': { min: 1, max: 2 },
      'CEIL': { min: 1, max: 1 },
      'FLOOR': { min: 1, max: 1 },
      'SQRT': { min: 1, max: 1 },
      'POW': { min: 2, max: 2 },
      'MAX': { min: 1 },
      'MIN': { min: 1 },
      'SUM': { min: 1 },
      'AVERAGE': { min: 1 },
      'COUNT': { min: 1 },
      'MEDIAN': { min: 1 },
      'PERCENTAGE': { min: 2, max: 2 },
      'GROWTH': { min: 2, max: 2 },
      'DISCOUNT': { min: 2, max: 2 },
      'TAX': { min: 2, max: 2 },
      'MARGIN': { min: 2, max: 2 },
      'DAYS': { min: 2, max: 2 },
      'MONTHS': { min: 2, max: 2 },
      'YEARS': { min: 2, max: 2 },
      'IF': { min: 3, max: 3 },
      'CONCAT': { min: 1 },
      'UPPER': { min: 1, max: 1 },
      'LOWER': { min: 1, max: 1 },
      'LEN': { min: 1, max: 1 },
    };

    const requirements = functionRequirements[funcName];
    if (requirements) {
      // Check minimum arguments
      if (argCount < requirements.min) {
        result.errors.push(`Function ${funcName} requires at least ${requirements.min} argument(s), got ${argCount}`);
      }

      // Check maximum arguments
      if (requirements.max && argCount > requirements.max) {
        result.errors.push(`Function ${funcName} accepts at most ${requirements.max} argument(s), got ${argCount}`);
      }
    }

    // Specific function warnings
    switch (funcName) {
      case 'SQRT':
        // Warn if argument could be negative
        const arg = node.arguments![0];
        if (arg.type === 'Literal' && typeof arg.value === 'number' && arg.value < 0) {
          result.errors.push('SQRT function cannot accept negative numbers');
        }
        break;

      case 'PERCENTAGE':
      case 'GROWTH':
      case 'MARGIN':
        // Warn about potential division by zero
        const denominator = node.arguments![1];
        if (denominator.type === 'Literal' && denominator.value === 0) {
          result.warnings.push(`${funcName} function may result in division by zero`);
        }
        break;

      case 'IF':
        // Warn about complex conditions
        const condition = node.arguments![0];
        if (this.getNodeDepth(condition) > 3) {
          result.warnings.push('Complex IF conditions may be hard to understand and maintain');
        }
        break;
    }
  }

  /**
   * Validate identifier
   */
  private validateIdentifier(node: FormulaAST, result: ValidationResult): void {
    if (!node.name) {
      result.errors.push('Identifier missing name');
      return;
    }

    // Check for valid identifier format
    if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(node.name)) {
      result.errors.push(`Invalid identifier format: ${node.name}`);
    }

    // Warn about very long identifiers
    if (node.name.length > 50) {
      result.warnings.push(`Very long identifier: ${node.name}`);
    }

    // Warn about potential reserved words (basic check)
    const reservedWords = ['true', 'false', 'null', 'undefined', 'function', 'return', 'if', 'else'];
    if (reservedWords.includes(node.name.toLowerCase())) {
      result.warnings.push(`Identifier '${node.name}' may conflict with reserved words`);
    }
  }

  /**
   * Validate literal
   */
  private validateLiteral(node: FormulaAST, result: ValidationResult): void {
    if (node.value === undefined) {
      result.errors.push('Literal missing value');
      return;
    }

    // Check for very large numbers
    if (typeof node.value === 'number') {
      if (Math.abs(node.value) > Number.MAX_SAFE_INTEGER) {
        result.warnings.push('Very large numbers may lose precision');
      }

      // Check for NaN or Infinity
      if (!isFinite(node.value)) {
        result.errors.push('Invalid numeric literal: NaN or Infinity');
      }
    }

    // Check for very long strings
    if (typeof node.value === 'string' && node.value.length > 1000) {
      result.warnings.push('Very long string literals may impact performance');
    }
  }

  /**
   * Check if node represents a floating point number
   */
  private isFloatingPoint(node: FormulaAST): boolean {
    return node.type === 'Literal' && 
           typeof node.value === 'number' && 
           !Number.isInteger(node.value);
  }

  /**
   * Get the depth of an AST node
   */
  private getNodeDepth(node: FormulaAST): number {
    switch (node.type) {
      case 'BinaryExpression':
        const leftDepth = node.left ? this.getNodeDepth(node.left) : 0;
        const rightDepth = node.right ? this.getNodeDepth(node.right) : 0;
        return 1 + Math.max(leftDepth, rightDepth);

      case 'UnaryExpression':
        return 1 + (node.argument ? this.getNodeDepth(node.argument) : 0);

      case 'FunctionCall':
        if (!node.arguments || node.arguments.length === 0) return 1;
        const maxArgDepth = Math.max(...node.arguments.map(arg => this.getNodeDepth(arg)));
        return 1 + maxArgDepth;

      default:
        return 1;
    }
  }

  /**
   * Validate formula complexity
   */
  validateComplexity(ast: FormulaAST, maxDepth: number = 10, maxNodes: number = 100): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    const depth = this.getNodeDepth(ast);
    const nodeCount = this.countNodes(ast);

    if (depth > maxDepth) {
      result.errors.push(`Formula too complex: depth ${depth} exceeds maximum ${maxDepth}`);
    }

    if (nodeCount > maxNodes) {
      result.errors.push(`Formula too complex: ${nodeCount} nodes exceeds maximum ${maxNodes}`);
    }

    // Warnings for approaching limits
    if (depth > maxDepth * 0.8) {
      result.warnings.push(`Formula approaching complexity limit: depth ${depth}`);
    }

    if (nodeCount > maxNodes * 0.8) {
      result.warnings.push(`Formula approaching complexity limit: ${nodeCount} nodes`);
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * Count total nodes in AST
   */
  private countNodes(node: FormulaAST): number {
    let count = 1;

    switch (node.type) {
      case 'BinaryExpression':
        if (node.left) count += this.countNodes(node.left);
        if (node.right) count += this.countNodes(node.right);
        break;

      case 'UnaryExpression':
        if (node.argument) count += this.countNodes(node.argument);
        break;

      case 'FunctionCall':
        if (node.arguments) {
          node.arguments.forEach(arg => {
            count += this.countNodes(arg);
          });
        }
        break;
    }

    return count;
  }
}
