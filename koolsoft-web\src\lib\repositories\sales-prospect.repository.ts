import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Prospect Repository
 *
 * This repository handles database operations for the Sales Prospect entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesProspectRepository extends PrismaRepository<
  Prisma.sales_prospectsGetPayload<{
    include: {
      customer: true;
      executive: true;
    };
  }>,
  string,
  Prisma.sales_prospectsCreateInput,
  Prisma.sales_prospectsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('sales_prospects');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): SalesProspectRepository {
    return new SalesProspectRepository(tx);
  }

  /**
   * Find sales prospects with filtering, pagination, and sorting
   * @param filters Filter criteria
   * @returns Promise resolving to paginated sales prospects with metadata
   */
  async findWithFilters(filters: {
    customerId?: string;
    executiveId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    skip?: number;
    take?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      customerId,
      executiveId,
      status,
      startDate,
      endDate,
      search,
      skip = 0,
      take = 10,
      sortBy = 'prospectDate',
      sortOrder = 'desc',
    } = filters;

    // Build where clause
    const where: Prisma.sales_prospectsWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.prospectDate = {};
      if (startDate) {
        where.prospectDate.gte = startDate;
      }
      if (endDate) {
        where.prospectDate.lte = endDate;
      }
    }

    if (search) {
      where.OR = [
        {
          customer: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          contactPerson: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          contactPhone: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          remarks: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          lostReason: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    let orderBy: Prisma.sales_prospectsOrderByWithRelationInput = {};
    if (sortBy === 'customer') {
      orderBy = { customer: { name: sortOrder } };
    } else if (sortBy === 'executive') {
      orderBy = { executive: { name: sortOrder } };
    } else if (sortBy === 'prospectDate') {
      orderBy = { prospectDate: sortOrder };
    } else if (sortBy === 'status') {
      orderBy = { status: sortOrder };
    } else if (sortBy === 'prospectPercentage') {
      orderBy = { prospectPercentage: sortOrder };
    } else if (sortBy === 'followUpDate') {
      orderBy = { followUpDate: sortOrder };
    } else if (sortBy === 'nextVisitDate') {
      orderBy = { nextVisitDate: sortOrder };
    } else {
      orderBy = { prospectDate: sortOrder }; // Default fallback
    }

    // Execute queries
    const [prospects, total] = await Promise.all([
      this.model.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      this.model.count({ where }),
    ]);

    return {
      data: prospects,
      pagination: {
        total,
        skip,
        take,
        pages: Math.ceil(total / take),
      },
    };
  }

  /**
   * Find sales prospects by customer ID
   * @param customerId Customer ID
   * @param options Query options
   * @returns Promise resolving to sales prospects
   */
  async findByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { customerId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { prospectDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales prospects by executive ID
   * @param executiveId Executive ID
   * @param options Query options
   * @returns Promise resolving to sales prospects
   */
  async findByExecutiveId(
    executiveId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { executiveId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { prospectDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales prospects by status
   * @param status Prospect status
   * @param options Query options
   * @returns Promise resolving to sales prospects
   */
  async findByStatus(
    status: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { status },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { prospectDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Get sales prospects statistics
   * @param filters Optional filters
   * @returns Promise resolving to statistics
   */
  async getStatistics(filters?: {
    customerId?: string;
    executiveId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { customerId, executiveId, startDate, endDate } = filters || {};

    // Build where clause
    const where: Prisma.sales_prospectsWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (startDate || endDate) {
      where.prospectDate = {};
      if (startDate) {
        where.prospectDate.gte = startDate;
      }
      if (endDate) {
        where.prospectDate.lte = endDate;
      }
    }

    // Get statistics
    const [
      total,
      activeProspects,
      qualifiedProspects,
      convertedProspects,
      lostProspects,
      averageProspectPercentage,
    ] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, status: 'ACTIVE' } }),
      this.model.count({ where: { ...where, status: 'QUALIFIED' } }),
      this.model.count({ where: { ...where, status: 'CONVERTED' } }),
      this.model.count({ where: { ...where, status: 'LOST' } }),
      this.model.aggregate({
        where: {
          ...where,
          prospectPercentage: { not: null },
        },
        _avg: {
          prospectPercentage: true,
        },
      }),
    ]);

    return {
      total,
      activeProspects,
      qualifiedProspects,
      convertedProspects,
      lostProspects,
      averageProspectPercentage: averageProspectPercentage._avg.prospectPercentage || 0,
      conversionRate: total > 0 ? (convertedProspects / total) * 100 : 0,
    };
  }

  /**
   * Get prospects requiring follow-up
   * @param date Date to check for follow-ups (defaults to today)
   * @returns Promise resolving to prospects requiring follow-up
   */
  async getProspectsRequiringFollowUp(date?: Date) {
    const checkDate = date || new Date();
    checkDate.setHours(23, 59, 59, 999); // End of day

    return this.model.findMany({
      where: {
        followUpDate: {
          lte: checkDate,
        },
        status: {
          notIn: ['CONVERTED', 'LOST', 'INACTIVE'],
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
      orderBy: { followUpDate: 'asc' },
    });
  }

  /**
   * Update prospect status
   * @param id Prospect ID
   * @param status New status
   * @param lostReason Optional lost reason (required when status is LOST)
   * @param remarks Optional remarks
   * @returns Promise resolving to updated prospect
   */
  async updateStatus(id: string, status: string, lostReason?: string, remarks?: string) {
    const updateData: Prisma.sales_prospectsUpdateInput = {
      status,
      updatedAt: new Date(),
    };

    if (lostReason) {
      updateData.lostReason = lostReason;
    }

    if (remarks) {
      updateData.remarks = remarks;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }
}
