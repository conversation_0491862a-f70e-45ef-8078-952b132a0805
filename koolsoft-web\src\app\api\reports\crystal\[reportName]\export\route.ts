import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { CrystalReportRepository } from '@/lib/repositories/crystal-report.repository';
import { generateCrystalReportPDF, generateCrystalReportExcel } from '@/lib/utils/crystal-report-export';
import { z } from 'zod';

/**
 * Crystal Report Export Parameters Schema
 */
const crystalReportExportSchema = z.object({
  format: z.enum(['PDF', 'EXCEL']),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  customerId: z.string().optional(),
  executiveId: z.string().optional(),
  amcStatus: z.string().optional(),
  warrantyStatus: z.string().optional(),
  serviceStatus: z.string().optional(),
  contractType: z.string().optional(),
  groupBy: z.enum(['customer', 'executive', 'status', 'none']).optional(),
  includeCharts: z.string().transform(val => val === 'true').optional(),
  includeSummary: z.string().transform(val => val === 'true').optional(),
});

/**
 * GET /api/reports/crystal/[reportName]/export
 * Export Crystal Report in PDF or Excel format
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function exportCrystalReport(
  request: NextRequest,
  { params }: { params: { reportName: string } }
) {
  try {
    const { reportName } = params;
    const { searchParams } = new URL(request.url);
    
    // Extract and validate parameters
    const rawParams: any = {};
    searchParams.forEach((value, key) => {
      if (value) {
        rawParams[key] = value;
      }
    });
    
    const validatedParams = crystalReportExportSchema.parse(rawParams);
    const { format, ...reportParams } = validatedParams;
    
    // Initialize repository and get report data
    const crystalReportRepository = new CrystalReportRepository();
    
    let reportData;
    switch (reportName.toLowerCase()) {
      case 'amc-summary':
        reportData = await crystalReportRepository.getAMCSummaryReport(reportParams);
        break;
        
      case 'amc-detail':
        reportData = await crystalReportRepository.getAMCDetailReport(reportParams);
        break;
        
      case 'warranty-summary':
        reportData = await crystalReportRepository.getWarrantySummaryReport(reportParams);
        break;
        
      case 'warranty-detail':
        reportData = await crystalReportRepository.getWarrantyDetailReport(reportParams);
        break;
        
      case 'service-summary':
        reportData = await crystalReportRepository.getServiceSummaryReport(reportParams);
        break;
        
      case 'service-detail':
        reportData = await crystalReportRepository.getServiceDetailReport(reportParams);
        break;
        
      case 'sales-summary':
        reportData = await crystalReportRepository.getSalesSummaryReport(reportParams);
        break;
        
      case 'customer-summary':
        reportData = await crystalReportRepository.getCustomerSummaryReport(reportParams);
        break;
        
      default:
        return NextResponse.json(
          { error: `Unsupported Crystal Report: ${reportName}` },
          { status: 400 }
        );
    }
    
    // Generate export file
    let buffer: Buffer;
    let mimeType: string;
    let fileExtension: string;
    
    const exportParams = {
      reportName,
      reportTitle: getCrystalReportTitle(reportName),
      data: reportData.data,
      summary: reportData.summary,
      parameters: reportParams,
      includeCharts: validatedParams.includeCharts || false,
      includeSummary: validatedParams.includeSummary !== false,
    };
    
    if (format === 'PDF') {
      buffer = await generateCrystalReportPDF(exportParams);
      mimeType = 'application/pdf';
      fileExtension = 'pdf';
    } else {
      buffer = await generateCrystalReportExcel(exportParams);
      mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileExtension = 'xlsx';
    }
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const filename = `${reportName}_${timestamp}.${fileExtension}`;
    
    // Return file response
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': buffer.length.toString(),
      },
    });
    
  } catch (error) {
    console.error('Crystal Report Export API Error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid export parameters', 
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to export Crystal Report' },
      { status: 500 }
    );
  }
}

/**
 * Get Crystal Report title for display
 */
function getCrystalReportTitle(reportName: string): string {
  const titles: Record<string, string> = {
    'amc-summary': 'AMC Summary Report',
    'amc-detail': 'AMC Detail Report',
    'amc-expiry': 'AMC Expiry Report',
    'warranty-summary': 'Warranty Summary Report',
    'warranty-detail': 'Warranty Detail Report',
    'warranty-expiry': 'Warranty Expiry Report',
    'service-summary': 'Service Summary Report',
    'service-detail': 'Service Detail Report',
    'sales-summary': 'Sales Summary Report',
    'customer-summary': 'Customer Summary Report',
  };
  
  return titles[reportName.toLowerCase()] || 'Crystal Report';
}

/**
 * Crystal Report Export Configuration
 */
export const CRYSTAL_EXPORT_CONFIG = {
  // PDF Configuration
  pdf: {
    format: 'A4' as const,
    orientation: 'portrait' as const,
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
    fonts: {
      header: { size: 16, weight: 'bold' },
      subheader: { size: 12, weight: 'bold' },
      body: { size: 10, weight: 'normal' },
      footer: { size: 8, weight: 'normal' },
    },
    colors: {
      primary: '#0F52BA',
      secondary: '#6B7280',
      success: '#10B981',
      warning: '#F59E0B',
      danger: '#EF4444',
    },
  },
  
  // Excel Configuration
  excel: {
    sheetNames: {
      data: 'Report Data',
      summary: 'Summary',
      charts: 'Charts',
    },
    styles: {
      header: {
        font: { bold: true, size: 14, color: { argb: 'FFFFFFFF' } },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0F52BA' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
      },
      subheader: {
        font: { bold: true, size: 12 },
        fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3F4F6' } },
        alignment: { horizontal: 'center', vertical: 'middle' },
      },
      data: {
        font: { size: 10 },
        alignment: { horizontal: 'left', vertical: 'middle' },
      },
      number: {
        font: { size: 10 },
        alignment: { horizontal: 'right', vertical: 'middle' },
        numFmt: '#,##0',
      },
      currency: {
        font: { size: 10 },
        alignment: { horizontal: 'right', vertical: 'middle' },
        numFmt: '₹#,##0.00',
      },
      date: {
        font: { size: 10 },
        alignment: { horizontal: 'center', vertical: 'middle' },
        numFmt: 'dd-mmm-yyyy',
      },
    },
  },
};

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  exportCrystalReport
);
