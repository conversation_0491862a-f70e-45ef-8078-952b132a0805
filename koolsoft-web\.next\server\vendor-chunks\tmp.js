"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tmp";
exports.ids = ["vendor-chunks/tmp"];
exports.modules = {

/***/ "(rsc)/./node_modules/tmp/lib/tmp.js":
/*!*************************************!*\
  !*** ./node_modules/tmp/lib/tmp.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/*!\n * Tmp\n *\n * Copyright (c) 2011-2017 KARASZI Istvan <<EMAIL>>\n *\n * MIT Licensed\n */\n\n/*\n * Module dependencies.\n */\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst os = __webpack_require__(/*! os */ \"os\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst _c = {\n  fs: fs.constants,\n  os: os.constants\n};\n\n/*\n * The working inner variables.\n */\nconst\n  // the random characters to choose from\n  RANDOM_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n  TEMPLATE_PATTERN = /XXXXXX/,\n  DEFAULT_TRIES = 3,\n  CREATE_FLAGS = (_c.O_CREAT || _c.fs.O_CREAT) | (_c.O_EXCL || _c.fs.O_EXCL) | (_c.O_RDWR || _c.fs.O_RDWR),\n  // constants are off on the windows platform and will not match the actual errno codes\n  IS_WIN32 = os.platform() === 'win32',\n  EBADF = _c.EBADF || _c.os.errno.EBADF,\n  ENOENT = _c.ENOENT || _c.os.errno.ENOENT,\n  DIR_MODE = 0o700 /* 448 */,\n  FILE_MODE = 0o600 /* 384 */,\n  EXIT = 'exit',\n  // this will hold the objects need to be removed on exit\n  _removeObjects = [],\n  // API change in fs.rmdirSync leads to error when passing in a second parameter, e.g. the callback\n  FN_RMDIR_SYNC = fs.rmdirSync.bind(fs);\nlet _gracefulCleanup = false;\n\n/**\n * Recursively remove a directory and its contents.\n *\n * @param {string} dirPath path of directory to remove\n * @param {Function} callback\n * @private\n */\nfunction rimraf(dirPath, callback) {\n  return fs.rm(dirPath, {\n    recursive: true\n  }, callback);\n}\n\n/**\n * Recursively remove a directory and its contents, synchronously.\n *\n * @param {string} dirPath path of directory to remove\n * @private\n */\nfunction FN_RIMRAF_SYNC(dirPath) {\n  return fs.rmSync(dirPath, {\n    recursive: true\n  });\n}\n\n/**\n * Gets a temporary file name.\n *\n * @param {(Options|tmpNameCallback)} options options or callback\n * @param {?tmpNameCallback} callback the callback function\n */\nfunction tmpName(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n  try {\n    _assertAndSanitizeOptions(opts);\n  } catch (err) {\n    return cb(err);\n  }\n  let tries = opts.tries;\n  (function _getUniqueName() {\n    try {\n      const name = _generateTmpName(opts);\n\n      // check whether the path exists then retry if needed\n      fs.stat(name, function (err) {\n        /* istanbul ignore else */\n        if (!err) {\n          /* istanbul ignore else */\n          if (tries-- > 0) return _getUniqueName();\n          return cb(new Error('Could not get a unique tmp filename, max tries reached ' + name));\n        }\n        cb(null, name);\n      });\n    } catch (err) {\n      cb(err);\n    }\n  })();\n}\n\n/**\n * Synchronous version of tmpName.\n *\n * @param {Object} options\n * @returns {string} the generated random name\n * @throws {Error} if the options are invalid or could not generate a filename\n */\nfunction tmpNameSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n  _assertAndSanitizeOptions(opts);\n  let tries = opts.tries;\n  do {\n    const name = _generateTmpName(opts);\n    try {\n      fs.statSync(name);\n    } catch (e) {\n      return name;\n    }\n  } while (tries-- > 0);\n  throw new Error('Could not get a unique tmp filename, max tries reached');\n}\n\n/**\n * Creates and opens a temporary file.\n *\n * @param {(Options|null|undefined|fileCallback)} options the config options or the callback function or null or undefined\n * @param {?fileCallback} callback\n */\nfunction file(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create and open the file\n    fs.open(name, CREATE_FLAGS, opts.mode || FILE_MODE, function _fileCreated(err, fd) {\n      /* istanbu ignore else */\n      if (err) return cb(err);\n      if (opts.discardDescriptor) {\n        return fs.close(fd, function _discardCallback(possibleErr) {\n          // the chance of getting an error on close here is rather low and might occur in the most edgiest cases only\n          return cb(possibleErr, name, undefined, _prepareTmpFileRemoveCallback(name, -1, opts, false));\n        });\n      } else {\n        // detachDescriptor passes the descriptor whereas discardDescriptor closes it, either way, we no longer care\n        // about the descriptor\n        const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n        cb(null, name, fd, _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, false));\n      }\n    });\n  });\n}\n\n/**\n * Synchronous version of file.\n *\n * @param {Options} options\n * @returns {FileSyncObject} object consists of name, fd and removeCallback\n * @throws {Error} if cannot create a file\n */\nfunction fileSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n  const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n  const name = tmpNameSync(opts);\n  var fd = fs.openSync(name, CREATE_FLAGS, opts.mode || FILE_MODE);\n  /* istanbul ignore else */\n  if (opts.discardDescriptor) {\n    fs.closeSync(fd);\n    fd = undefined;\n  }\n  return {\n    name: name,\n    fd: fd,\n    removeCallback: _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, true)\n  };\n}\n\n/**\n * Creates a temporary directory.\n *\n * @param {(Options|dirCallback)} options the options or the callback function\n * @param {?dirCallback} callback\n */\nfunction dir(options, callback) {\n  const args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create the directory\n    fs.mkdir(name, opts.mode || DIR_MODE, function _dirCreated(err) {\n      /* istanbul ignore else */\n      if (err) return cb(err);\n      cb(null, name, _prepareTmpDirRemoveCallback(name, opts, false));\n    });\n  });\n}\n\n/**\n * Synchronous version of dir.\n *\n * @param {Options} options\n * @returns {DirSyncObject} object consists of name and removeCallback\n * @throws {Error} if it cannot create a directory\n */\nfunction dirSync(options) {\n  const args = _parseArguments(options),\n    opts = args[0];\n  const name = tmpNameSync(opts);\n  fs.mkdirSync(name, opts.mode || DIR_MODE);\n  return {\n    name: name,\n    removeCallback: _prepareTmpDirRemoveCallback(name, opts, true)\n  };\n}\n\n/**\n * Removes files asynchronously.\n *\n * @param {Object} fdPath\n * @param {Function} next\n * @private\n */\nfunction _removeFileAsync(fdPath, next) {\n  const _handler = function (err) {\n    if (err && !_isENOENT(err)) {\n      // reraise any unanticipated error\n      return next(err);\n    }\n    next();\n  };\n  if (0 <= fdPath[0]) fs.close(fdPath[0], function () {\n    fs.unlink(fdPath[1], _handler);\n  });else fs.unlink(fdPath[1], _handler);\n}\n\n/**\n * Removes files synchronously.\n *\n * @param {Object} fdPath\n * @private\n */\nfunction _removeFileSync(fdPath) {\n  let rethrownException = null;\n  try {\n    if (0 <= fdPath[0]) fs.closeSync(fdPath[0]);\n  } catch (e) {\n    // reraise any unanticipated error\n    if (!_isEBADF(e) && !_isENOENT(e)) throw e;\n  } finally {\n    try {\n      fs.unlinkSync(fdPath[1]);\n    } catch (e) {\n      // reraise any unanticipated error\n      if (!_isENOENT(e)) rethrownException = e;\n    }\n  }\n  if (rethrownException !== null) {\n    throw rethrownException;\n  }\n}\n\n/**\n * Prepares the callback for removal of the temporary file.\n *\n * Returns either a sync callback or a async callback depending on whether\n * fileSync or file was called, which is expressed by the sync parameter.\n *\n * @param {string} name the path of the file\n * @param {number} fd file descriptor\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {fileCallback | fileCallbackSync}\n * @private\n */\nfunction _prepareTmpFileRemoveCallback(name, fd, opts, sync) {\n  const removeCallbackSync = _prepareRemoveCallback(_removeFileSync, [fd, name], sync);\n  const removeCallback = _prepareRemoveCallback(_removeFileAsync, [fd, name], sync, removeCallbackSync);\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Prepares the callback for removal of the temporary directory.\n *\n * Returns either a sync callback or a async callback depending on whether\n * tmpFileSync or tmpFile was called, which is expressed by the sync parameter.\n *\n * @param {string} name\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {Function} the callback\n * @private\n */\nfunction _prepareTmpDirRemoveCallback(name, opts, sync) {\n  const removeFunction = opts.unsafeCleanup ? rimraf : fs.rmdir.bind(fs);\n  const removeFunctionSync = opts.unsafeCleanup ? FN_RIMRAF_SYNC : FN_RMDIR_SYNC;\n  const removeCallbackSync = _prepareRemoveCallback(removeFunctionSync, name, sync);\n  const removeCallback = _prepareRemoveCallback(removeFunction, name, sync, removeCallbackSync);\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Creates a guarded function wrapping the removeFunction call.\n *\n * The cleanup callback is save to be called multiple times.\n * Subsequent invocations will be ignored.\n *\n * @param {Function} removeFunction\n * @param {string} fileOrDirName\n * @param {boolean} sync\n * @param {cleanupCallbackSync?} cleanupCallbackSync\n * @returns {cleanupCallback | cleanupCallbackSync}\n * @private\n */\nfunction _prepareRemoveCallback(removeFunction, fileOrDirName, sync, cleanupCallbackSync) {\n  let called = false;\n\n  // if sync is true, the next parameter will be ignored\n  return function _cleanupCallback(next) {\n    /* istanbul ignore else */\n    if (!called) {\n      // remove cleanupCallback from cache\n      const toRemove = cleanupCallbackSync || _cleanupCallback;\n      const index = _removeObjects.indexOf(toRemove);\n      /* istanbul ignore else */\n      if (index >= 0) _removeObjects.splice(index, 1);\n      called = true;\n      if (sync || removeFunction === FN_RMDIR_SYNC || removeFunction === FN_RIMRAF_SYNC) {\n        return removeFunction(fileOrDirName);\n      } else {\n        return removeFunction(fileOrDirName, next || function () {});\n      }\n    }\n  };\n}\n\n/**\n * The garbage collector.\n *\n * @private\n */\nfunction _garbageCollector() {\n  /* istanbul ignore else */\n  if (!_gracefulCleanup) return;\n\n  // the function being called removes itself from _removeObjects,\n  // loop until _removeObjects is empty\n  while (_removeObjects.length) {\n    try {\n      _removeObjects[0]();\n    } catch (e) {\n      // already removed?\n    }\n  }\n}\n\n/**\n * Random name generator based on crypto.\n * Adapted from http://blog.tompawlak.org/how-to-generate-random-values-nodejs-javascript\n *\n * @param {number} howMany\n * @returns {string} the generated random name\n * @private\n */\nfunction _randomChars(howMany) {\n  let value = [],\n    rnd = null;\n\n  // make sure that we do not fail because we ran out of entropy\n  try {\n    rnd = crypto.randomBytes(howMany);\n  } catch (e) {\n    rnd = crypto.pseudoRandomBytes(howMany);\n  }\n  for (var i = 0; i < howMany; i++) {\n    value.push(RANDOM_CHARS[rnd[i] % RANDOM_CHARS.length]);\n  }\n  return value.join('');\n}\n\n/**\n * Helper which determines whether a string s is blank, that is undefined, or empty or null.\n *\n * @private\n * @param {string} s\n * @returns {Boolean} true whether the string s is blank, false otherwise\n */\nfunction _isBlank(s) {\n  return s === null || _isUndefined(s) || !s.trim();\n}\n\n/**\n * Checks whether the `obj` parameter is defined or not.\n *\n * @param {Object} obj\n * @returns {boolean} true if the object is undefined\n * @private\n */\nfunction _isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\n\n/**\n * Parses the function arguments.\n *\n * This function helps to have optional arguments.\n *\n * @param {(Options|null|undefined|Function)} options\n * @param {?Function} callback\n * @returns {Array} parsed arguments\n * @private\n */\nfunction _parseArguments(options, callback) {\n  /* istanbul ignore else */\n  if (typeof options === 'function') {\n    return [{}, options];\n  }\n\n  /* istanbul ignore else */\n  if (_isUndefined(options)) {\n    return [{}, callback];\n  }\n\n  // copy options so we do not leak the changes we make internally\n  const actualOptions = {};\n  for (const key of Object.getOwnPropertyNames(options)) {\n    actualOptions[key] = options[key];\n  }\n  return [actualOptions, callback];\n}\n\n/**\n * Generates a new temporary name.\n *\n * @param {Object} opts\n * @returns {string} the new random name according to opts\n * @private\n */\nfunction _generateTmpName(opts) {\n  const tmpDir = opts.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.name)) return path.join(tmpDir, opts.dir, opts.name);\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.template)) return path.join(tmpDir, opts.dir, opts.template).replace(TEMPLATE_PATTERN, _randomChars(6));\n\n  // prefix and postfix\n  const name = [opts.prefix ? opts.prefix : 'tmp', '-', process.pid, '-', _randomChars(12), opts.postfix ? '-' + opts.postfix : ''].join('');\n  return path.join(tmpDir, opts.dir, name);\n}\n\n/**\n * Asserts whether the specified options are valid, also sanitizes options and provides sane defaults for missing\n * options.\n *\n * @param {Options} options\n * @private\n */\nfunction _assertAndSanitizeOptions(options) {\n  options.tmpdir = _getTmpDir(options);\n  const tmpDir = options.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(options.name)) _assertIsRelative(options.name, 'name', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.dir)) _assertIsRelative(options.dir, 'dir', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.template)) {\n    _assertIsRelative(options.template, 'template', tmpDir);\n    if (!options.template.match(TEMPLATE_PATTERN)) throw new Error(`Invalid template, found \"${options.template}\".`);\n  }\n  /* istanbul ignore else */\n  if (!_isUndefined(options.tries) && isNaN(options.tries) || options.tries < 0) throw new Error(`Invalid tries, found \"${options.tries}\".`);\n\n  // if a name was specified we will try once\n  options.tries = _isUndefined(options.name) ? options.tries || DEFAULT_TRIES : 1;\n  options.keep = !!options.keep;\n  options.detachDescriptor = !!options.detachDescriptor;\n  options.discardDescriptor = !!options.discardDescriptor;\n  options.unsafeCleanup = !!options.unsafeCleanup;\n\n  // sanitize dir, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.dir = _isUndefined(options.dir) ? '' : path.relative(tmpDir, _resolvePath(options.dir, tmpDir));\n  options.template = _isUndefined(options.template) ? undefined : path.relative(tmpDir, _resolvePath(options.template, tmpDir));\n  // sanitize further if template is relative to options.dir\n  options.template = _isBlank(options.template) ? undefined : path.relative(options.dir, options.template);\n\n  // for completeness' sake only, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.name = _isUndefined(options.name) ? undefined : options.name;\n  options.prefix = _isUndefined(options.prefix) ? '' : options.prefix;\n  options.postfix = _isUndefined(options.postfix) ? '' : options.postfix;\n}\n\n/**\n * Resolve the specified path name in respect to tmpDir.\n *\n * The specified name might include relative path components, e.g. ../\n * so we need to resolve in order to be sure that is is located inside tmpDir\n *\n * @param name\n * @param tmpDir\n * @returns {string}\n * @private\n */\nfunction _resolvePath(name, tmpDir) {\n  if (name.startsWith(tmpDir)) {\n    return path.resolve(name);\n  } else {\n    return path.resolve(path.join(tmpDir, name));\n  }\n}\n\n/**\n * Asserts whether specified name is relative to the specified tmpDir.\n *\n * @param {string} name\n * @param {string} option\n * @param {string} tmpDir\n * @throws {Error}\n * @private\n */\nfunction _assertIsRelative(name, option, tmpDir) {\n  if (option === 'name') {\n    // assert that name is not absolute and does not contain a path\n    if (path.isAbsolute(name)) throw new Error(`${option} option must not contain an absolute path, found \"${name}\".`);\n    // must not fail on valid .<name> or ..<name> or similar such constructs\n    let basename = path.basename(name);\n    if (basename === '..' || basename === '.' || basename !== name) throw new Error(`${option} option must not contain a path, found \"${name}\".`);\n  } else {\n    // if (option === 'dir' || option === 'template') {\n    // assert that dir or template are relative to tmpDir\n    if (path.isAbsolute(name) && !name.startsWith(tmpDir)) {\n      throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${name}\".`);\n    }\n    let resolvedPath = _resolvePath(name, tmpDir);\n    if (!resolvedPath.startsWith(tmpDir)) throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${resolvedPath}\".`);\n  }\n}\n\n/**\n * Helper for testing against EBADF to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isEBADF(error) {\n  return _isExpectedError(error, -EBADF, 'EBADF');\n}\n\n/**\n * Helper for testing against ENOENT to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isENOENT(error) {\n  return _isExpectedError(error, -ENOENT, 'ENOENT');\n}\n\n/**\n * Helper to determine whether the expected error code matches the actual code and errno,\n * which will differ between the supported node versions.\n *\n * - Node >= 7.0:\n *   error.code {string}\n *   error.errno {number} any numerical value will be negated\n *\n * CAVEAT\n *\n * On windows, the errno for EBADF is -4083 but os.constants.errno.EBADF is different and we must assume that ENOENT\n * is no different here.\n *\n * @param {SystemError} error\n * @param {number} errno\n * @param {string} code\n * @private\n */\nfunction _isExpectedError(error, errno, code) {\n  return IS_WIN32 ? error.code === code : error.code === code && error.errno === errno;\n}\n\n/**\n * Sets the graceful cleanup.\n *\n * If graceful cleanup is set, tmp will remove all controlled temporary objects on process exit, otherwise the\n * temporary objects will remain in place, waiting to be cleaned up on system restart or otherwise scheduled temporary\n * object removals.\n */\nfunction setGracefulCleanup() {\n  _gracefulCleanup = true;\n}\n\n/**\n * Returns the currently configured tmp dir from os.tmpdir().\n *\n * @private\n * @param {?Options} options\n * @returns {string} the currently configured tmp dir\n */\nfunction _getTmpDir(options) {\n  return path.resolve(options && options.tmpdir || os.tmpdir());\n}\n\n// Install process exit listener\nprocess.addListener(EXIT, _garbageCollector);\n\n/**\n * Configuration options.\n *\n * @typedef {Object} Options\n * @property {?boolean} keep the temporary object (file or dir) will not be garbage collected\n * @property {?number} tries the number of tries before give up the name generation\n * @property (?int) mode the access mode, defaults are 0o700 for directories and 0o600 for files\n * @property {?string} template the \"mkstemp\" like filename template\n * @property {?string} name fixed name relative to tmpdir or the specified dir option\n * @property {?string} dir tmp directory relative to the root tmp directory in use\n * @property {?string} prefix prefix for the generated name\n * @property {?string} postfix postfix for the generated name\n * @property {?string} tmpdir the root tmp directory which overrides the os tmpdir\n * @property {?boolean} unsafeCleanup recursively removes the created temporary directory, even when it's not empty\n * @property {?boolean} detachDescriptor detaches the file descriptor, caller is responsible for closing the file, tmp will no longer try closing the file during garbage collection\n * @property {?boolean} discardDescriptor discards the file descriptor (closes file, fd is -1), tmp will no longer try closing the file during garbage collection\n */\n\n/**\n * @typedef {Object} FileSyncObject\n * @property {string} name the name of the file\n * @property {string} fd the file descriptor or -1 if the fd has been discarded\n * @property {fileCallback} removeCallback the callback function to remove the file\n */\n\n/**\n * @typedef {Object} DirSyncObject\n * @property {string} name the name of the directory\n * @property {fileCallback} removeCallback the callback function to remove the directory\n */\n\n/**\n * @callback tmpNameCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n */\n\n/**\n * @callback fileCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback fileCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallback\n * @param {simpleCallback} [next] function to call whenever the tmp object needs to be removed\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallbackSync\n */\n\n/**\n * Callback function for function composition.\n * @see {@link https://github.com/raszi/node-tmp/issues/57|raszi/node-tmp#57}\n *\n * @callback simpleCallback\n */\n\n// exporting all the needed methods\n\n// evaluate _getTmpDir() lazily, mainly for simplifying testing but it also will\n// allow users to reconfigure the temporary directory\nObject.defineProperty(module.exports, \"tmpdir\", ({\n  enumerable: true,\n  configurable: false,\n  get: function () {\n    return _getTmpDir();\n  }\n}));\nmodule.exports.dir = dir;\nmodule.exports.dirSync = dirSync;\nmodule.exports.file = file;\nmodule.exports.fileSync = fileSync;\nmodule.exports.tmpName = tmpName;\nmodule.exports.tmpNameSync = tmpNameSync;\nmodule.exports.setGracefulCleanup = setGracefulCleanup;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tmp/lib/tmp.js\n");

/***/ })

};
;