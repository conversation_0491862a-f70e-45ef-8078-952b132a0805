import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getServiceScheduleRepository } from '@/lib/repositories';
import { serviceSchedulingSchema } from '@/lib/validations/service.schema';

/**
 * DELETE /api/service/schedules/[id]
 * Delete a specific service schedule
 */
/**
 * PATCH /api/service/schedules/[id]
 * Update a service schedule
 */
export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = serviceSchedulingSchema.parse(body);

      const serviceScheduleRepository = getServiceScheduleRepository();

      // Check if schedule exists
      const existingSchedule = await serviceScheduleRepository.findById(id);
      if (!existingSchedule) {
        return NextResponse.json(
          { error: 'Service schedule not found' },
          { status: 404 }
        );
      }

      // Update the schedule
      const updatedSchedule = await serviceScheduleRepository.update(id, {
        serviceReport: { connect: { id: validatedData.serviceReportId } },
        scheduledDate: validatedData.scheduledDate,
        technician: { connect: { id: validatedData.technicianId } },
        estimatedDuration: validatedData.estimatedDuration,
        priority: validatedData.priority,
        notes: validatedData.notes,
      });

      // Get the updated schedule with relations
      const scheduleWithRelations = await serviceScheduleRepository.findWithRelations(id);

      return NextResponse.json({
        message: 'Service schedule updated successfully',
        schedule: scheduleWithRelations,
      });
    } catch (error) {
      console.error('Error updating service schedule:', error);

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('validation')) {
          return NextResponse.json(
            { error: 'Validation failed', details: error.message },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Failed to update service schedule' },
        { status: 500 }
      );
    }
  }
);

export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const serviceScheduleRepository = getServiceScheduleRepository();

      // Check if schedule exists
      const existingSchedule = await serviceScheduleRepository.findById(id);
      if (!existingSchedule) {
        return NextResponse.json(
          { error: 'Service schedule not found' },
          { status: 404 }
        );
      }

      // Delete the schedule
      await serviceScheduleRepository.delete(id);

      return NextResponse.json({
        message: 'Service schedule deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service schedule:', error);
      return NextResponse.json(
        { error: 'Failed to delete service schedule' },
        { status: 500 }
      );
    }
  }
);
