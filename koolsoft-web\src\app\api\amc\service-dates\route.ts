import { NextRequest, NextResponse } from 'next/server';
import { getAMCServiceDateRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { amcServiceDateSchema } from '@/lib/validations/amc-contract.schema';

/**
 * Service date query schema
 */
const serviceDateQuerySchema = z.object({
  contractId: z.string().uuid().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  status: z.enum(['SCHEDULED', 'COMPLETED', 'MISSED']).optional(),
  skip: z.coerce.number().int().nonnegative().default(0),
  take: z.coerce.number().int().positive().max(100).default(50),
  orderBy: z.enum(['serviceDate', 'completedDate', 'createdAt']).default('serviceDate'),
  orderDirection: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * Service date creation schema
 */
const createServiceDateSchema = amcServiceDateSchema.extend({
  amcContractId: z.string().uuid({ message: 'Valid AMC contract ID is required' }),
});

/**
 * GET /api/amc/service-dates
 * Get service dates with optional filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = serviceDateQuerySchema.parse(queryParams);

      const amcServiceDateRepository = getAMCServiceDateRepository();

      let serviceDates;
      let total;

      if (validatedQuery.contractId) {
        // Get service dates for specific contract
        serviceDates = await amcServiceDateRepository.findByContractId(
          validatedQuery.contractId,
          {
            skip: validatedQuery.skip,
            take: validatedQuery.take,
            orderBy: {
              [validatedQuery.orderBy]: validatedQuery.orderDirection,
            },
          }
        );
        total = await amcServiceDateRepository.count({
          amcContractId: validatedQuery.contractId,
        });
      } else if (validatedQuery.startDate && validatedQuery.endDate) {
        // Get service dates by date range
        serviceDates = await amcServiceDateRepository.findByDateRange(
          validatedQuery.startDate,
          validatedQuery.endDate,
          {
            skip: validatedQuery.skip,
            take: validatedQuery.take,
            orderBy: {
              [validatedQuery.orderBy]: validatedQuery.orderDirection,
            },
          }
        );
        total = await amcServiceDateRepository.count({
          serviceDate: {
            gte: validatedQuery.startDate,
            lte: validatedQuery.endDate,
          },
        });
      } else {
        // Get all service dates
        serviceDates = await amcServiceDateRepository.findAll(
          validatedQuery.skip,
          validatedQuery.take
        );
        total = await amcServiceDateRepository.count();
      }

      return NextResponse.json({
        serviceDates,
        meta: {
          total,
          skip: validatedQuery.skip,
          take: validatedQuery.take,
          orderBy: validatedQuery.orderBy,
          orderDirection: validatedQuery.orderDirection,
        },
      });
    } catch (error) {
      console.error('Error fetching service dates:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch service dates' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/amc/service-dates
 * Create a new service date
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createServiceDateSchema.parse(body);

      const amcServiceDateRepository = getAMCServiceDateRepository();

      // Create the service date
      const serviceDate = await amcServiceDateRepository.create({
        amcContract: {
          connect: { id: validatedData.amcContractId }
        },
        serviceDate: validatedData.scheduledDate,
        serviceFlag: 'S', // S for Scheduled
        originalAmcId: validatedData.originalId,
      });

      return NextResponse.json({
        message: 'Service date created successfully',
        serviceDate,
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating service date:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to create service date' },
        { status: 500 }
      );
    }
  }
);
