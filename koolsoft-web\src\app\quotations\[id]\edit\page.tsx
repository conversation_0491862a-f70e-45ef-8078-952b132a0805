'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { updateQuotationSchema, type UpdateQuotation } from '@/lib/validations/quotation.schema';
import {
  Plus,
  Trash2,
  Save,
  <PERSON>Tex<PERSON>,
  <PERSON>culator,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  city?: string;
}

interface Executive {
  id: string;
  name: string;
  email?: string;
  designation?: string;
}

interface QuotationData {
  id: string;
  quotationNumber: string;
  customerId: string;
  executiveId: string;
  quotationDate: string;
  validUntil?: string;
  status: string;
  subject?: string;
  notes?: string;
  termsConditions?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  discount?: number;
  discountType?: string;
  items: Array<{
    id?: string;
    productId?: string;
    modelId?: string;
    brandId?: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
    taxAmount?: number;
    discount?: number;
    discountType?: string;
    specifications?: string;
    notes?: string;
    sortOrder?: number;
  }>;
}

/**
 * Edit Quotation Page
 *
 * This page provides a form for editing existing quotations.
 * It follows the established KoolSoft UI patterns and form validation standards.
 */
export default function EditQuotationPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const quotationId = params?.id as string;

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<UpdateQuotation>({
    resolver: zodResolver(updateQuotationSchema),
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  const watchedItems = watch('items') || [];

  // Fetch quotation data and reference data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const [quotationRes, customersRes, executivesRes] = await Promise.all([
          fetch(`/api/quotations/${quotationId}`, { credentials: 'include' }),
          fetch('/api/customers?limit=100', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&limit=100', { credentials: 'include' }),
        ]);

        const [quotationData, customersData, executivesData] = await Promise.all([
          quotationRes.json(),
          customersRes.json(),
          executivesRes.json(),
        ]);

        if (quotationData.success) {
          const quotation = quotationData.data;
          
          // Reset form with quotation data
          reset({
            id: quotation.id,
            customerId: quotation.customerId,
            executiveId: quotation.executiveId,
            quotationDate: new Date(quotation.quotationDate),
            validUntil: quotation.validUntil ? new Date(quotation.validUntil) : undefined,
            status: quotation.status,
            subject: quotation.subject || '',
            notes: quotation.notes || '',
            termsConditions: quotation.termsConditions || '',
            contactPerson: quotation.contactPerson || '',
            contactPhone: quotation.contactPhone || '',
            contactEmail: quotation.contactEmail || '',
            discount: quotation.discount || 0,
            discountType: quotation.discountType || 'PERCENTAGE',
            items: quotation.items.map((item: any) => ({
              id: item.id,
              productId: item.productId || '',
              modelId: item.modelId || '',
              brandId: item.brandId || '',
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              taxRate: item.taxRate || 18,
              taxAmount: item.taxAmount || 0,
              discount: item.discount || 0,
              discountType: item.discountType || 'PERCENTAGE',
              specifications: item.specifications || '',
              notes: item.notes || '',
              sortOrder: item.sortOrder || 0,
            })),
          });
        } else {
          throw new Error(quotationData.error || 'Failed to fetch quotation');
        }

        // Handle customers data
        if (customersData.success && Array.isArray(customersData.data)) {
          setCustomers(customersData.data);
        } else {
          console.warn('Invalid customers data:', customersData);
          setCustomers([]);
        }

        // Handle executives data
        if (executivesData.success && Array.isArray(executivesData.data)) {
          setExecutives(executivesData.data);
        } else {
          console.warn('Invalid executives data:', executivesData);
          setExecutives([]);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load quotation data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    if (quotationId) {
      fetchData();
    }
  }, [quotationId, reset, toast]);

  // Calculate item totals
  const calculateItemTotal = (index: number) => {
    const item = watchedItems[index];
    if (!item) return;

    const quantity = item.quantity || 0;
    const unitPrice = item.unitPrice || 0;
    const taxRate = item.taxRate || 0;
    const discount = item.discount || 0;
    const discountType = item.discountType || 'PERCENTAGE';

    let totalPrice = quantity * unitPrice;
    
    // Apply discount
    if (discount > 0) {
      if (discountType === 'PERCENTAGE') {
        totalPrice = totalPrice * (1 - discount / 100);
      } else {
        totalPrice = totalPrice - discount;
      }
    }

    const taxAmount = (totalPrice * taxRate) / 100;

    setValue(`items.${index}.totalPrice`, totalPrice);
    setValue(`items.${index}.taxAmount`, taxAmount);
  };

  // Add new item
  const addItem = () => {
    append({
      description: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
      taxRate: 18,
      taxAmount: 0,
      discount: 0,
      discountType: 'PERCENTAGE',
    });
  };

  // Remove item
  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  // Handle form submission
  const onSubmit = async (data: UpdateQuotation) => {
    try {
      setSaving(true);

      const response = await fetch(`/api/quotations/${quotationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update quotation');
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: 'Quotation updated successfully.',
        });
        router.push(`/quotations/${quotationId}`);
      } else {
        throw new Error(result.error || 'Failed to update quotation');
      }
    } catch (error) {
      console.error('Error updating quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to update quotation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = watchedItems.reduce((sum, item) => sum + (item?.totalPrice || 0), 0);
    const totalTax = watchedItems.reduce((sum, item) => sum + (item?.taxAmount || 0), 0);
    const total = subtotal + totalTax;

    return { subtotal, totalTax, total };
  };

  const { subtotal, totalTax, total } = calculateTotals();

  if (loading) {
    return (
      <DashboardLayout title="Loading..." requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading quotation data...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Edit Quotation" requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Edit Quotation</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Update quotation details and line items
            </CardDescription>
          </CardHeader>
        </Card>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="customerId">Customer *</Label>
                  <Select onValueChange={(value) => setValue('customerId', value)} defaultValue={watch('customerId')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(customers) && customers.length > 0 ? (
                        customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.name} {customer.city && `- ${customer.city}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>
                          No customers available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.customerId && (
                    <p className="text-sm text-red-600">{errors.customerId.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="executiveId">Executive *</Label>
                  <Select onValueChange={(value) => setValue('executiveId', value)} defaultValue={watch('executiveId')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select executive" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(executives) && executives.length > 0 ? (
                        executives.map((executive) => (
                          <SelectItem key={executive.id} value={executive.id}>
                            {executive.name} {executive.designation && `- ${executive.designation}`}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No executives available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.executiveId && (
                    <p className="text-sm text-red-600">{errors.executiveId.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="quotationDate">Quotation Date *</Label>
                  <Input
                    type="date"
                    {...register('quotationDate', { valueAsDate: true })}
                  />
                  {errors.quotationDate && (
                    <p className="text-sm text-red-600">{errors.quotationDate.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="validUntil">Valid Until</Label>
                  <Input
                    type="date"
                    {...register('validUntil', { valueAsDate: true })}
                  />
                  {errors.validUntil && (
                    <p className="text-sm text-red-600">{errors.validUntil.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select onValueChange={(value) => setValue('status', value as any)} defaultValue={watch('status')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DRAFT">Draft</SelectItem>
                      <SelectItem value="SENT">Sent</SelectItem>
                      <SelectItem value="ACCEPTED">Accepted</SelectItem>
                      <SelectItem value="REJECTED">Rejected</SelectItem>
                      <SelectItem value="EXPIRED">Expired</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.status && (
                    <p className="text-sm text-red-600">{errors.status.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactPerson">Contact Person</Label>
                  <Input {...register('contactPerson')} />
                  {errors.contactPerson && (
                    <p className="text-sm text-red-600">{errors.contactPerson.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input {...register('contactPhone')} />
                  {errors.contactPhone && (
                    <p className="text-sm text-red-600">{errors.contactPhone.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input type="email" {...register('contactEmail')} />
                  {errors.contactEmail && (
                    <p className="text-sm text-red-600">{errors.contactEmail.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="subject">Subject</Label>
                <Input {...register('subject')} placeholder="Quotation subject" />
                {errors.subject && (
                  <p className="text-sm text-red-600">{errors.subject.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea {...register('notes')} rows={3} />
                {errors.notes && (
                  <p className="text-sm text-red-600">{errors.notes.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="termsConditions">Terms & Conditions</Label>
                <Textarea {...register('termsConditions')} rows={4} />
                {errors.termsConditions && (
                  <p className="text-sm text-red-600">{errors.termsConditions.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Line Items</CardTitle>
                <Button type="button" onClick={addItem} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeItem(index)}
                          variant="ghost"
                          size="sm"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="md:col-span-2">
                        <Label>Description *</Label>
                        <Input
                          {...register(`items.${index}.description`)}
                          placeholder="Item description"
                        />
                        {errors.items?.[index]?.description && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.description?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Quantity *</Label>
                        <Input
                          type="number"
                          min="1"
                          {...register(`items.${index}.quantity`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                        {errors.items?.[index]?.quantity && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.quantity?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Unit Price *</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...register(`items.${index}.unitPrice`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                        {errors.items?.[index]?.unitPrice && (
                          <p className="text-sm text-red-600">
                            {errors.items[index]?.unitPrice?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label>Tax Rate (%)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          {...register(`items.${index}.taxRate`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                      </div>

                      <div>
                        <Label>Discount</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          {...register(`items.${index}.discount`, {
                            valueAsNumber: true,
                            onChange: () => calculateItemTotal(index),
                          })}
                        />
                      </div>

                      <div>
                        <Label>Total Price</Label>
                        <Input
                          type="number"
                          readOnly
                          value={watchedItems[index]?.totalPrice?.toFixed(2) || '0.00'}
                          className="bg-gray-50"
                        />
                      </div>

                      <div>
                        <Label>Tax Amount</Label>
                        <Input
                          type="number"
                          readOnly
                          value={watchedItems[index]?.taxAmount?.toFixed(2) || '0.00'}
                          className="bg-gray-50"
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Specifications</Label>
                      <Textarea
                        {...register(`items.${index}.specifications`)}
                        rows={2}
                        placeholder="Technical specifications or additional details"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Totals */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Totals</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-w-md ml-auto">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Tax:</span>
                  <span>₹{totalTax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>₹{total.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Updating...' : 'Update Quotation'}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
