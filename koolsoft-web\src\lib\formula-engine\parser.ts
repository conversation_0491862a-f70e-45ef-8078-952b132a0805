/**
 * Formula Parser for KoolSoft Report Formula Engine
 * 
 * This module provides safe parsing and evaluation of mathematical expressions
 * with support for business functions and data context integration.
 */

export interface FormulaToken {
  type: 'NUMBER' | 'OPERATOR' | 'FUNCTION' | 'IDENTIFIER' | 'PARENTHESIS' | 'COMMA' | 'STRING';
  value: string;
  position: number;
}

export interface FormulaAST {
  type: 'BinaryExpression' | 'UnaryExpression' | 'FunctionCall' | 'Identifier' | 'Literal';
  operator?: string;
  left?: FormulaAST;
  right?: FormulaAST;
  argument?: FormulaAST;
  name?: string;
  arguments?: FormulaAST[];
  value?: any;
  raw?: string;
}

export class FormulaParseError extends Error {
  constructor(message: string, public position: number) {
    super(message);
    this.name = 'FormulaParseError';
  }
}

export class FormulaParser {
  private tokens: FormulaToken[] = [];
  private current = 0;

  /**
   * Parse a formula string into an Abstract Syntax Tree
   */
  parse(formula: string): FormulaAST {
    this.tokens = this.tokenize(formula);
    this.current = 0;
    
    if (this.tokens.length === 0) {
      throw new FormulaParseError('Empty formula', 0);
    }

    const ast = this.parseExpression();
    
    if (this.current < this.tokens.length) {
      throw new FormulaParseError(
        `Unexpected token: ${this.tokens[this.current].value}`,
        this.tokens[this.current].position
      );
    }

    return ast;
  }

  /**
   * Tokenize formula string
   */
  private tokenize(formula: string): FormulaToken[] {
    const tokens: FormulaToken[] = [];
    let position = 0;

    while (position < formula.length) {
      const char = formula[position];

      // Skip whitespace
      if (/\s/.test(char)) {
        position++;
        continue;
      }

      // Numbers (including decimals)
      if (/\d/.test(char)) {
        let value = '';
        while (position < formula.length && /[\d.]/.test(formula[position])) {
          value += formula[position];
          position++;
        }
        tokens.push({ type: 'NUMBER', value, position: position - value.length });
        continue;
      }

      // Operators
      if (/[+\-*/^%]/.test(char)) {
        tokens.push({ type: 'OPERATOR', value: char, position });
        position++;
        continue;
      }

      // Parentheses
      if (/[()]/.test(char)) {
        tokens.push({ type: 'PARENTHESIS', value: char, position });
        position++;
        continue;
      }

      // Comma
      if (char === ',') {
        tokens.push({ type: 'COMMA', value: char, position });
        position++;
        continue;
      }

      // String literals
      if (char === '"' || char === "'") {
        const quote = char;
        let value = '';
        position++; // Skip opening quote
        
        while (position < formula.length && formula[position] !== quote) {
          if (formula[position] === '\\' && position + 1 < formula.length) {
            position++; // Skip escape character
            value += formula[position];
          } else {
            value += formula[position];
          }
          position++;
        }
        
        if (position >= formula.length) {
          throw new FormulaParseError('Unterminated string literal', position);
        }
        
        position++; // Skip closing quote
        tokens.push({ type: 'STRING', value, position: position - value.length - 2 });
        continue;
      }

      // Identifiers and functions
      if (/[a-zA-Z_]/.test(char)) {
        let value = '';
        while (position < formula.length && /[a-zA-Z0-9_.]/.test(formula[position])) {
          value += formula[position];
          position++;
        }
        
        // Check if it's followed by parenthesis (function call)
        const nextNonWhitespace = this.findNextNonWhitespace(formula, position);
        const isFunction = nextNonWhitespace < formula.length && formula[nextNonWhitespace] === '(';
        
        tokens.push({ 
          type: isFunction ? 'FUNCTION' : 'IDENTIFIER', 
          value, 
          position: position - value.length 
        });
        continue;
      }

      throw new FormulaParseError(`Unexpected character: ${char}`, position);
    }

    return tokens;
  }

  /**
   * Find next non-whitespace character position
   */
  private findNextNonWhitespace(formula: string, start: number): number {
    let pos = start;
    while (pos < formula.length && /\s/.test(formula[pos])) {
      pos++;
    }
    return pos;
  }

  /**
   * Parse expression with operator precedence
   */
  private parseExpression(): FormulaAST {
    return this.parseAdditive();
  }

  /**
   * Parse additive expressions (+ -)
   */
  private parseAdditive(): FormulaAST {
    let left = this.parseMultiplicative();

    while (this.current < this.tokens.length) {
      const token = this.tokens[this.current];
      if (token.type === 'OPERATOR' && (token.value === '+' || token.value === '-')) {
        this.current++;
        const right = this.parseMultiplicative();
        left = {
          type: 'BinaryExpression',
          operator: token.value,
          left,
          right,
        };
      } else {
        break;
      }
    }

    return left;
  }

  /**
   * Parse multiplicative expressions (* / %)
   */
  private parseMultiplicative(): FormulaAST {
    let left = this.parseExponential();

    while (this.current < this.tokens.length) {
      const token = this.tokens[this.current];
      if (token.type === 'OPERATOR' && (token.value === '*' || token.value === '/' || token.value === '%')) {
        this.current++;
        const right = this.parseExponential();
        left = {
          type: 'BinaryExpression',
          operator: token.value,
          left,
          right,
        };
      } else {
        break;
      }
    }

    return left;
  }

  /**
   * Parse exponential expressions (^)
   */
  private parseExponential(): FormulaAST {
    let left = this.parseUnary();

    while (this.current < this.tokens.length) {
      const token = this.tokens[this.current];
      if (token.type === 'OPERATOR' && token.value === '^') {
        this.current++;
        const right = this.parseUnary();
        left = {
          type: 'BinaryExpression',
          operator: token.value,
          left,
          right,
        };
      } else {
        break;
      }
    }

    return left;
  }

  /**
   * Parse unary expressions (- +)
   */
  private parseUnary(): FormulaAST {
    const token = this.tokens[this.current];
    
    if (token && token.type === 'OPERATOR' && (token.value === '-' || token.value === '+')) {
      this.current++;
      const argument = this.parseUnary();
      return {
        type: 'UnaryExpression',
        operator: token.value,
        argument,
      };
    }

    return this.parsePrimary();
  }

  /**
   * Parse primary expressions (numbers, identifiers, function calls, parentheses)
   */
  private parsePrimary(): FormulaAST {
    const token = this.tokens[this.current];

    if (!token) {
      throw new FormulaParseError('Unexpected end of formula', this.current);
    }

    // Numbers
    if (token.type === 'NUMBER') {
      this.current++;
      return {
        type: 'Literal',
        value: parseFloat(token.value),
        raw: token.value,
      };
    }

    // Strings
    if (token.type === 'STRING') {
      this.current++;
      return {
        type: 'Literal',
        value: token.value,
        raw: `"${token.value}"`,
      };
    }

    // Function calls
    if (token.type === 'FUNCTION') {
      return this.parseFunctionCall();
    }

    // Identifiers
    if (token.type === 'IDENTIFIER') {
      this.current++;
      return {
        type: 'Identifier',
        name: token.value,
      };
    }

    // Parentheses
    if (token.type === 'PARENTHESIS' && token.value === '(') {
      this.current++;
      const expression = this.parseExpression();
      
      const closingToken = this.tokens[this.current];
      if (!closingToken || closingToken.type !== 'PARENTHESIS' || closingToken.value !== ')') {
        throw new FormulaParseError('Expected closing parenthesis', this.current);
      }
      
      this.current++;
      return expression;
    }

    throw new FormulaParseError(`Unexpected token: ${token.value}`, token.position);
  }

  /**
   * Parse function call
   */
  private parseFunctionCall(): FormulaAST {
    const nameToken = this.tokens[this.current];
    this.current++; // Skip function name

    const openParen = this.tokens[this.current];
    if (!openParen || openParen.type !== 'PARENTHESIS' || openParen.value !== '(') {
      throw new FormulaParseError('Expected opening parenthesis after function name', this.current);
    }
    this.current++; // Skip opening parenthesis

    const args: FormulaAST[] = [];

    // Handle empty function calls
    if (this.current < this.tokens.length && 
        this.tokens[this.current].type === 'PARENTHESIS' && 
        this.tokens[this.current].value === ')') {
      this.current++;
      return {
        type: 'FunctionCall',
        name: nameToken.value,
        arguments: args,
      };
    }

    // Parse arguments
    while (this.current < this.tokens.length) {
      args.push(this.parseExpression());

      const nextToken = this.tokens[this.current];
      if (!nextToken) {
        throw new FormulaParseError('Expected closing parenthesis', this.current);
      }

      if (nextToken.type === 'PARENTHESIS' && nextToken.value === ')') {
        this.current++;
        break;
      }

      if (nextToken.type === 'COMMA') {
        this.current++;
        continue;
      }

      throw new FormulaParseError(`Expected comma or closing parenthesis, got: ${nextToken.value}`, nextToken.position);
    }

    return {
      type: 'FunctionCall',
      name: nameToken.value,
      arguments: args,
    };
  }
}
