import { NextRequest, NextResponse } from 'next/server';
import { getAMCContractRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Service date generation schema
 */
const generateServiceDatesSchema = z.object({
  numberOfServices: z.number().int().positive({ message: 'Number of services must be a positive integer' }),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  replaceExisting: z.boolean().default(false),
});

/**
 * POST /api/amc/contracts/[id]/generate-service-dates
 * Generate service dates for an AMC contract
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = generateServiceDatesSchema.parse(body);

        const amcContractRepository = getAMCContractRepository();

        // Get the contract
        const contract = await amcContractRepository.findById(id);

        if (!contract) {
          return NextResponse.json(
            { error: 'AMC contract not found' },
            { status: 404 }
          );
        }

        // Use provided dates or contract dates
        const startDate = validatedData.startDate || contract.startDate;
        const endDate = validatedData.endDate || contract.endDate;

        // Calculate interval between service dates
        const contractDuration = endDate.getTime() - startDate.getTime();
        const numberOfServices = validatedData.numberOfServices;
        const interval = Math.floor(contractDuration / (numberOfServices - 1));

        // Generate service dates
        const serviceDates = [];
        for (let i = 0; i < numberOfServices; i++) {
          const date = new Date(startDate.getTime() + i * interval);
          serviceDates.push({
            amcContractId: id,
            serviceDate: date,
            status: 'SCHEDULED',
          });
        }

        // Delete existing service dates if requested
        if (validatedData.replaceExisting) {
          await prisma.amc_service_dates.deleteMany({
            where: { amcContractId: id },
          });
        }

        // Create service dates
        await prisma.amc_service_dates.createMany({
          data: serviceDates,
        });

        // Update contract with number of services
        await amcContractRepository.update(id, {
          numberOfServices: validatedData.numberOfServices,
        });

        // Get updated contract with service dates
        const updatedContract = await amcContractRepository.findWithRelations(id);

        return NextResponse.json({
          message: 'Service dates generated successfully',
          serviceDates: updatedContract.serviceDates,
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error generating service dates:', error);
      return NextResponse.json(
        { error: 'Failed to generate service dates' },
        { status: 500 }
      );
    }
  }
);
