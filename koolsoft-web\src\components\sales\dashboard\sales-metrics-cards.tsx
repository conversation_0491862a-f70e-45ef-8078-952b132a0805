'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  Target, 
  TrendingUp, 
  DollarSign, 
  BarChart3, 
  Percent,
  ShoppingCart,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SalesMetricsCardsProps {
  data: {
    totalLeads: number;
    totalOpportunities: number;
    totalProspects: number;
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    leadToOpportunityRate: number;
    opportunityToProspectRate: number;
    prospectToOrderRate: number;
    overallConversionRate: number;
  };
  isLoading?: boolean;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  isLoading?: boolean;
  subtitle?: string;
}

function MetricCard({ title, value, icon, color, isLoading, subtitle }: MetricCardProps) {
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
              {subtitle && <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />}
            </div>
            <div className={cn("h-12 w-12 rounded-full bg-gray-200 animate-pulse")} />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold text-black">{value}</p>
            {subtitle && (
              <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
          <div className={cn("h-12 w-12 rounded-full flex items-center justify-center", color)}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SalesMetricsCards({ data, isLoading = false, className }: SalesMetricsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (rate: number) => {
    return `${rate.toFixed(1)}%`;
  };

  const metrics = [
    {
      title: 'Total Leads',
      value: data.totalLeads.toLocaleString(),
      icon: <Users className="h-6 w-6 text-white" />,
      color: 'bg-blue-500',
      subtitle: 'Active sales leads',
    },
    {
      title: 'Opportunities',
      value: data.totalOpportunities.toLocaleString(),
      icon: <Target className="h-6 w-6 text-white" />,
      color: 'bg-green-500',
      subtitle: 'Qualified opportunities',
    },
    {
      title: 'Prospects',
      value: data.totalProspects.toLocaleString(),
      icon: <Eye className="h-6 w-6 text-white" />,
      color: 'bg-purple-500',
      subtitle: 'Active prospects',
    },
    {
      title: 'Orders',
      value: data.totalOrders.toLocaleString(),
      icon: <ShoppingCart className="h-6 w-6 text-white" />,
      color: 'bg-orange-500',
      subtitle: 'Confirmed orders',
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(data.totalRevenue),
      icon: <DollarSign className="h-6 w-6 text-white" />,
      color: 'bg-emerald-500',
      subtitle: 'Total sales value',
    },
    {
      title: 'Avg Order Value',
      value: formatCurrency(data.averageOrderValue),
      icon: <BarChart3 className="h-6 w-6 text-white" />,
      color: 'bg-indigo-500',
      subtitle: 'Average per order',
    },
    {
      title: 'Lead → Opportunity',
      value: formatPercentage(data.leadToOpportunityRate),
      icon: <TrendingUp className="h-6 w-6 text-white" />,
      color: 'bg-cyan-500',
      subtitle: 'Conversion rate',
    },
    {
      title: 'Overall Conversion',
      value: formatPercentage(data.overallConversionRate),
      icon: <Percent className="h-6 w-6 text-white" />,
      color: 'bg-pink-500',
      subtitle: 'Lead to order rate',
    },
  ];

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          color={metric.color}
          isLoading={isLoading}
          subtitle={metric.subtitle}
        />
      ))}
    </div>
  );
}
