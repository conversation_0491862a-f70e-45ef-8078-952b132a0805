import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Quotation Item Repository
 *
 * This repository handles database operations for the Quotation Item entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class QuotationItemRepository extends PrismaRepository<
  Prisma.quotation_itemsGetPayload<{
    include: {
      quotation: true;
      product: true;
      model: true;
      brand: true;
    };
  }>,
  string,
  Prisma.quotation_itemsCreateInput,
  Prisma.quotation_itemsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('quotation_items');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<any, string, any, any> {
    const repo = new QuotationItemRepository(tx);
    return repo;
  }

  /**
   * Find quotation items with all related data
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of quotation items with related data
   */
  async findWithRelations(
    filter: Prisma.quotation_itemsWhereInput = {},
    skip?: number,
    take?: number,
    orderBy?: Prisma.quotation_itemsOrderByWithRelationInput
  ) {
    try {
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { sortOrder: 'asc' },
        include: {
          quotation: {
            select: {
              id: true,
              quotationNumber: true,
              quotationDate: true,
              status: true,
            },
          },
          product: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          model: {
            select: {
              id: true,
              name: true,
              description: true,
              specs: true,
              tonnage: true,
              bslMRP: true,
              bslMCP: true,
              bslCP: true,
              taplMRP: true,
              taplMCP: true,
              taplCP: true,
              installCharge: true,
              numberOfComponents: true,
            },
          },
          brand: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('QuotationItemRepository.findWithRelations: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Find quotation items by quotation ID
   * @param quotationId Quotation ID
   * @param options Query options
   * @returns Promise resolving to quotation items
   */
  async findByQuotationId(
    quotationId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 100, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { quotationId },
      include: includeRelations
        ? {
            product: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            model: {
              select: {
                id: true,
                name: true,
                description: true,
                specs: true,
                tonnage: true,
                bslMRP: true,
                bslMCP: true,
                bslCP: true,
                taplMRP: true,
                taplMCP: true,
                taplCP: true,
                installCharge: true,
                numberOfComponents: true,
              },
            },
            brand: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          }
        : undefined,
      orderBy: { sortOrder: 'asc' },
      skip,
      take,
    });
  }

  /**
   * Create multiple quotation items (overrides base method)
   * @param data Array of quotation item data
   * @returns Promise resolving to created quotation items
   */
  async createMany(data: Prisma.quotation_itemsCreateInput[]): Promise<any[]> {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const createdItems = [];

        for (let i = 0; i < data.length; i++) {
          const item = data[i];
          const createdItem = await tx.quotation_items.create({
            data: item,
            include: {
              quotation: {
                select: {
                  id: true,
                  quotationNumber: true,
                  quotationDate: true,
                  status: true,
                },
              },
              product: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  specs: true,
                  tonnage: true,
                  bslMRP: true,
                  bslMCP: true,
                  bslCP: true,
                  taplMRP: true,
                  taplMCP: true,
                  taplCP: true,
                  installCharge: true,
                  numberOfComponents: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          });
          createdItems.push(createdItem);
        }

        return createdItems;
      });
    } catch (error) {
      console.error('QuotationItemRepository.createMany: Error executing transaction:', error);
      throw error;
    }
  }

  /**
   * Update multiple quotation items (overrides base method)
   * @param filter Filter condition
   * @param data Update data
   * @returns Promise resolving to the number of updated entities
   */
  async updateMany(filter: any, data: Prisma.quotation_itemsUpdateInput): Promise<number> {
    try {
      const result = await this.model.updateMany({
        where: filter,
        data,
      });

      return result.count;
    } catch (error) {
      console.error('QuotationItemRepository.updateMany: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Create multiple quotation items for a specific quotation
   * @param quotationId Quotation ID
   * @param items Array of quotation item data
   * @returns Promise resolving to created quotation items
   */
  async createManyForQuotation(
    quotationId: string,
    items: Array<Omit<Prisma.quotation_itemsCreateInput, 'quotation'> & {
      productId?: string;
      modelId?: string;
      brandId?: string;
    }>
  ) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const createdItems = [];

        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          const createdItem = await tx.quotation_items.create({
            data: {
              quotationId,
              productId: item.productId,
              modelId: item.modelId,
              brandId: item.brandId,
              description: item.description,
              quantity: item.quantity || 1,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              taxRate: item.taxRate || 0,
              taxAmount: item.taxAmount || 0,
              discount: item.discount || 0,
              discountType: item.discountType || 'PERCENTAGE',
              specifications: item.specifications,
              notes: item.notes,
              sortOrder: item.sortOrder || i,
            },
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  specs: true,
                  tonnage: true,
                  bslMRP: true,
                  bslMCP: true,
                  bslCP: true,
                  taplMRP: true,
                  taplMCP: true,
                  taplCP: true,
                  installCharge: true,
                  numberOfComponents: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          });
          createdItems.push(createdItem);
        }

        return createdItems;
      });
    } catch (error) {
      console.error('QuotationItemRepository.createManyForQuotation: Error executing transaction:', error);
      throw error;
    }
  }

  /**
   * Update multiple quotation items for a specific quotation
   * @param quotationId Quotation ID
   * @param items Array of quotation item updates
   * @returns Promise resolving to updated quotation items
   */
  async updateManyForQuotation(
    quotationId: string,
    items: Array<{
      id?: string;
      productId?: string;
      modelId?: string;
      brandId?: string;
      description: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      taxRate?: number;
      taxAmount?: number;
      discount?: number;
      discountType?: string;
      specifications?: string;
      notes?: string;
      sortOrder?: number;
    }>
  ) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        // First, delete all existing items for this quotation
        await tx.quotation_items.deleteMany({
          where: { quotationId },
        });

        // Then create new items
        const createdItems = [];

        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          const createdItem = await tx.quotation_items.create({
            data: {
              quotationId,
              productId: item.productId,
              modelId: item.modelId,
              brandId: item.brandId,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              taxRate: item.taxRate || 0,
              taxAmount: item.taxAmount || 0,
              discount: item.discount || 0,
              discountType: item.discountType || 'PERCENTAGE',
              specifications: item.specifications,
              notes: item.notes,
              sortOrder: item.sortOrder || i,
            },
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  specs: true,
                  tonnage: true,
                  bslMRP: true,
                  bslMCP: true,
                  bslCP: true,
                  taplMRP: true,
                  taplMCP: true,
                  taplCP: true,
                  installCharge: true,
                  numberOfComponents: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          });
          createdItems.push(createdItem);
        }

        return createdItems;
      });
    } catch (error) {
      console.error('QuotationItemRepository.updateManyForQuotation: Error executing transaction:', error);
      throw error;
    }
  }

  /**
   * Delete all items for a quotation
   * @param quotationId Quotation ID
   * @returns Promise resolving to deletion result
   */
  async deleteByQuotationId(quotationId: string) {
    try {
      return await this.model.deleteMany({
        where: { quotationId },
      });
    } catch (error) {
      console.error('QuotationItemRepository.deleteByQuotationId: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Calculate totals for quotation items
   * @param quotationId Quotation ID
   * @returns Promise resolving to calculated totals
   */
  async calculateTotals(quotationId: string) {
    try {
      const result = await this.model.aggregate({
        where: { quotationId },
        _sum: {
          totalPrice: true,
          taxAmount: true,
        },
        _count: {
          id: true,
        },
      });

      const subtotal = result._sum.totalPrice || 0;
      const taxAmount = result._sum.taxAmount || 0;
      const totalAmount = subtotal + taxAmount;
      const itemCount = result._count.id || 0;

      return {
        subtotal,
        taxAmount,
        totalAmount,
        itemCount,
      };
    } catch (error) {
      console.error('QuotationItemRepository.calculateTotals: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Reorder quotation items
   * @param quotationId Quotation ID
   * @param itemOrders Array of item IDs with their new sort orders
   * @returns Promise resolving to update result
   */
  async reorderItems(
    quotationId: string,
    itemOrders: Array<{ id: string; sortOrder: number }>
  ) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        const updatePromises = itemOrders.map(({ id, sortOrder }) =>
          tx.quotation_items.update({
            where: { id },
            data: { sortOrder },
          })
        );

        return await Promise.all(updatePromises);
      });
    } catch (error) {
      console.error('QuotationItemRepository.reorderItems: Error executing transaction:', error);
      throw error;
    }
  }
}
