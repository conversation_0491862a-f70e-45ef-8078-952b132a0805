'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Filter, Calendar as CalendarIcon, X, Download } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { SalesDashboardFilters as FilterType } from '@/lib/validations/sales.schema';

interface SalesDashboardFiltersProps {
  filters: FilterType;
  onFiltersChange: (filters: FilterType) => void;
  onExport?: (format: 'CSV' | 'EXCEL') => void;
  isLoading?: boolean;
  className?: string;
}

interface Customer {
  id: string;
  name: string;
}

interface Executive {
  id: string;
  name: string;
}

export function SalesDashboardFilters({
  filters,
  onFiltersChange,
  onExport,
  isLoading = false,
  className
}: SalesDashboardFiltersProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [executives, setExecutives] = useState<Executive[]>([]);
  const [loadingData, setLoadingData] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.startDate ? new Date(filters.startDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.endDate ? new Date(filters.endDate) : undefined
  );

  // Fetch customers and executives for dropdowns
  useEffect(() => {
    const fetchFilterData = async () => {
      setLoadingData(true);
      try {
        const [customersResponse, executivesResponse] = await Promise.all([
          fetch('/api/customers?take=100', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&take=100', { credentials: 'include' })
        ]);

        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData.data?.customers || []);
        }

        if (executivesResponse.ok) {
          const executivesData = await executivesResponse.json();
          setExecutives(executivesData.data?.users || []);
        }
      } catch (error) {
        console.error('Error fetching filter data:', error);
      } finally {
        setLoadingData(false);
      }
    };

    fetchFilterData();
  }, []);

  const handleFilterChange = (key: keyof FilterType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleDateChange = (type: 'start' | 'end', date: Date | undefined) => {
    if (type === 'start') {
      setStartDate(date);
      handleFilterChange('startDate', date ? format(date, 'yyyy-MM-dd') : undefined);
    } else {
      setEndDate(date);
      handleFilterChange('endDate', date ? format(date, 'yyyy-MM-dd') : undefined);
    }
  };

  const clearFilters = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    onFiltersChange({
      period: '30d',
    });
  };

  const hasActiveFilters = filters.customerId || filters.executiveId || filters.startDate || filters.endDate;

  return (
    <Card className={className}>
      <CardHeader className="bg-primary text-white">
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Dashboard Filters
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {/* Period Filter */}
          <div className="space-y-2">
            <Label htmlFor="period">Time Period</Label>
            <Select
              value={filters.period}
              onValueChange={(value) => handleFilterChange('period', value)}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="6m">Last 6 months</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer Filter */}
          <div className="space-y-2">
            <Label htmlFor="customer">Customer</Label>
            <Select
              value={filters.customerId || 'all'}
              onValueChange={(value) => handleFilterChange('customerId', value === 'all' ? undefined : value)}
              disabled={isLoading || loadingData}
            >
              <SelectTrigger>
                <SelectValue placeholder="All customers" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All customers</SelectItem>
                {customers.map((customer) => (
                  <SelectItem key={customer.id} value={customer.id}>
                    {customer.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Executive Filter */}
          <div className="space-y-2">
            <Label htmlFor="executive">Executive</Label>
            <Select
              value={filters.executiveId || 'all'}
              onValueChange={(value) => handleFilterChange('executiveId', value === 'all' ? undefined : value)}
              disabled={isLoading || loadingData}
            >
              <SelectTrigger>
                <SelectValue placeholder="All executives" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All executives</SelectItem>
                {executives.map((executive) => (
                  <SelectItem key={executive.id} value={executive.id}>
                    {executive.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !startDate && "text-muted-foreground"
                  )}
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => handleDateChange('start', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !endDate && "text-muted-foreground"
                  )}
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => handleDateChange('end', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Label>&nbsp;</Label>
            <div className="flex space-x-2">
              {hasActiveFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              )}
              {onExport && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onExport('CSV')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
