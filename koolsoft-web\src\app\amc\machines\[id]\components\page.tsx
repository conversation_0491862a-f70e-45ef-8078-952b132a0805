'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { ComponentList } from '@/components/components/component-list';
import { ComponentForm } from '@/components/components/component-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, AlertCircle, Wrench, Calendar, MapPin } from 'lucide-react';
import { Component } from '@/lib/hooks/useComponents';

interface Machine {
  id: string;
  serialNumber?: string;
  location?: string;
  amcContract?: {
    id: string;
    contractNumber?: string;
    customer?: {
      id: string;
      name: string;
    };
  };
  product?: {
    id: string;
    name: string;
  };
  model?: {
    id: string;
    name: string;
  };
  brand?: {
    id: string;
    name: string;
  };
}

export default function MachineComponentsPage() {
  const params = useParams();
  const machineId = params?.id as string;
  
  const [machine, setMachine] = useState<Machine | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<Component | null>(null);

  useEffect(() => {
    const fetchMachine = async () => {
      try {
        const response = await fetch(`/api/amc/machines/${machineId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch machine details');
        }

        const data = await response.json();
        setMachine(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load machine');
      } finally {
        setLoading(false);
      }
    };

    if (machineId) {
      fetchMachine();
    }
  }, [machineId]);

  // Listen for the add component event from the layout
  useEffect(() => {
    const handleAddComponent = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('addComponent', handleAddComponent);
    return () => window.removeEventListener('addComponent', handleAddComponent);
  }, []);

  const handleAddComponent = () => {
    setShowAddDialog(true);
  };

  const handleEditComponent = (component: Component) => {
    setSelectedComponent(component);
    setShowEditDialog(true);
  };

  const handleFormSuccess = (component: Component) => {
    setShowAddDialog(false);
    setShowEditDialog(false);
    setSelectedComponent(null);
    // The list will refresh automatically
  };

  const handleFormCancel = () => {
    setShowAddDialog(false);
    setShowEditDialog(false);
    setSelectedComponent(null);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !machine) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error || 'Machine not found'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Machine Information */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <CardTitle className="text-white">Machine Information</CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <Wrench className="h-4 w-4 mr-2" />
                Serial Number
              </div>
              <div className="font-medium text-black">
                {machine.serialNumber || 'N/A'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                Location
              </div>
              <div className="font-medium text-black">
                {machine.location || 'N/A'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                Customer
              </div>
              <div className="font-medium text-black">
                {machine.amcContract?.customer?.name || 'N/A'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-gray-600">Brand</div>
              <div className="font-medium text-black">
                {machine.brand?.name || 'N/A'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-gray-600">Product</div>
              <div className="font-medium text-black">
                {machine.product?.name || 'N/A'}
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-gray-600">Model</div>
              <div className="font-medium text-black">
                {machine.model?.name || 'N/A'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Components List */}
      <ComponentList
        machineId={machineId}
        onAddComponent={handleAddComponent}
        onEditComponent={handleEditComponent}
      />

      {/* Add Component Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-black">Add Component to Machine</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <ComponentForm
              machineId={machineId}
              onSuccess={handleFormSuccess}
              onCancel={handleFormCancel}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Component Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-black">Edit Component</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {selectedComponent && (
              <ComponentForm
                machineId={machineId}
                component={selectedComponent}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
