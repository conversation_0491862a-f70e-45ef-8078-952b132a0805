'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft, 
  Save, 
  X,
  AlertTriangle,
  Settings,
  Calendar
} from 'lucide-react';
import { toast } from 'sonner';

interface ComponentFormData {
  serialNumber: string;
  componentNo: string;
  warrantyDate: string;
  section: string;
}

interface ComponentType {
  id: string;
  name: string;
  category: string;
}

export default function EditComponentPage() {
  const params = useParams();
  const router = useRouter();
  const componentId = params?.id as string;

  const [formData, setFormData] = useState<ComponentFormData>({
    serialNumber: '',
    componentNo: '',
    warrantyDate: '',
    section: ''
  });

  const [componentTypes, setComponentTypes] = useState<ComponentType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadComponent();
    loadComponentTypes();
  }, [componentId]);

  const loadComponent = async () => {
    try {
      const response = await fetch(`/api/warranties/components/${componentId}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to load component');
      }

      const component = await response.json();
      setFormData({
        serialNumber: component.serialNumber || '',
        componentNo: component.componentNo?.toString() || '',
        warrantyDate: component.warrantyDate ? component.warrantyDate.split('T')[0] : '',
        section: component.section || ''
      });
    } catch (error: any) {
      console.error('Error loading component:', error);
      toast.error('Failed to load component data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadComponentTypes = async () => {
    try {
      const response = await fetch('/api/component-types', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setComponentTypes(data);
      }
    } catch (error) {
      console.error('Error loading component types:', error);
    }
  };

  const handleInputChange = (field: keyof ComponentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.serialNumber.trim()) {
      newErrors.serialNumber = 'Serial number is required';
    }

    if (formData.componentNo && isNaN(parseInt(formData.componentNo))) {
      newErrors.componentNo = 'Component number must be a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const componentData = {
        serialNumber: formData.serialNumber.trim() || null,
        componentNo: formData.componentNo ? parseInt(formData.componentNo) : null,
        warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,
        section: formData.section.trim() || null
      };

      const response = await fetch(`/api/warranties/components/${componentId}`, {
        method: 'PATCH',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(componentData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to update component');
      }

      toast.success('Component updated successfully');
      router.push(`/warranties/components/${componentId}`);
    } catch (error: any) {
      console.error('Error updating component:', error);
      setErrors({ submit: error.message || 'Failed to update component. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Edit Component</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Update component information and warranty details
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" asChild>
              <Link href={`/warranties/components/${componentId}`}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="serialNumber" className="text-black">Serial Number *</Label>
                <Input
                  id="serialNumber"
                  value={formData.serialNumber}
                  onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                  placeholder="Enter serial number"
                />
                {errors.serialNumber && (
                  <p className="text-sm text-red-600">{errors.serialNumber}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="componentNo" className="text-black">Component Number</Label>
                <Input
                  id="componentNo"
                  type="number"
                  value={formData.componentNo}
                  onChange={(e) => handleInputChange('componentNo', e.target.value)}
                  placeholder="Enter component number"
                />
                {errors.componentNo && (
                  <p className="text-sm text-red-600">{errors.componentNo}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="section" className="text-black">Section</Label>
                <Input
                  id="section"
                  value={formData.section}
                  onChange={(e) => handleInputChange('section', e.target.value)}
                  placeholder="Enter section"
                  maxLength={1}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="warrantyDate" className="text-black">Warranty Date</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="warrantyDate"
                    type="date"
                    value={formData.warrantyDate}
                    onChange={(e) => handleInputChange('warrantyDate', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Error Display */}
            {errors.submit && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-black">{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild>
                <Link href={`/warranties/components/${componentId}`}>
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Component
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
