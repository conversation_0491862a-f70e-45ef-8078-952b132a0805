'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Loader2, Bell, Settings, RotateCcw } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';

interface NotificationPreferences {
  id: string;
  userId: string;
  salesLeadCreated: boolean;
  salesLeadStatusChanged: boolean;
  salesOpportunityCreated: boolean;
  salesOpportunityStatusChanged: boolean;
  salesProspectCreated: boolean;
  salesProspectStatusChanged: boolean;
  salesOrderCreated: boolean;
  salesOrderStatusChanged: boolean;
  salesConversionEvents: boolean;
  dailySalesSummary: boolean;
  weeklySalesReport: boolean;
  isActive: boolean;
}

const preferenceCategories = [
  {
    title: 'Sales Leads',
    description: 'Notifications for sales lead activities',
    preferences: [
      { key: 'salesLeadCreated', label: 'New Lead Created', description: 'When a new sales lead is created' },
      { key: 'salesLeadStatusChanged', label: 'Lead Status Changed', description: 'When a lead status is updated' },
    ],
  },
  {
    title: 'Sales Opportunities',
    description: 'Notifications for sales opportunity activities',
    preferences: [
      { key: 'salesOpportunityCreated', label: 'New Opportunity Created', description: 'When a new sales opportunity is created' },
      { key: 'salesOpportunityStatusChanged', label: 'Opportunity Status Changed', description: 'When an opportunity status is updated' },
    ],
  },
  {
    title: 'Sales Prospects',
    description: 'Notifications for sales prospect activities',
    preferences: [
      { key: 'salesProspectCreated', label: 'New Prospect Created', description: 'When a new sales prospect is created' },
      { key: 'salesProspectStatusChanged', label: 'Prospect Status Changed', description: 'When a prospect status is updated' },
    ],
  },
  {
    title: 'Sales Orders',
    description: 'Notifications for sales order activities',
    preferences: [
      { key: 'salesOrderCreated', label: 'New Order Created', description: 'When a new sales order is created' },
      { key: 'salesOrderStatusChanged', label: 'Order Status Changed', description: 'When an order status is updated' },
    ],
  },
  {
    title: 'Conversions & Reports',
    description: 'Notifications for conversions and periodic reports',
    preferences: [
      { key: 'salesConversionEvents', label: 'Conversion Events', description: 'When sales conversions occur' },
      { key: 'dailySalesSummary', label: 'Daily Sales Summary', description: 'Daily summary of sales activities' },
      { key: 'weeklySalesReport', label: 'Weekly Sales Report', description: 'Weekly sales performance report' },
    ],
  },
];

export default function NotificationPreferencesPage() {
  const { data: session } = useSession();
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [resetting, setResetting] = useState(false);

  useEffect(() => {
    fetchPreferences();
  }, []);

  const fetchPreferences = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/preferences', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch notification preferences');
      }

      const data = await response.json();
      setPreferences(data.data);
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      toast.error('Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (key: string, value: boolean) => {
    if (!preferences) return;
    
    setPreferences({
      ...preferences,
      [key]: value,
    });
  };

  const savePreferences = async () => {
    if (!preferences) return;

    try {
      setSaving(true);
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          salesLeadCreated: preferences.salesLeadCreated,
          salesLeadStatusChanged: preferences.salesLeadStatusChanged,
          salesOpportunityCreated: preferences.salesOpportunityCreated,
          salesOpportunityStatusChanged: preferences.salesOpportunityStatusChanged,
          salesProspectCreated: preferences.salesProspectCreated,
          salesProspectStatusChanged: preferences.salesProspectStatusChanged,
          salesOrderCreated: preferences.salesOrderCreated,
          salesOrderStatusChanged: preferences.salesOrderStatusChanged,
          salesConversionEvents: preferences.salesConversionEvents,
          dailySalesSummary: preferences.dailySalesSummary,
          weeklySalesReport: preferences.weeklySalesReport,
          isActive: preferences.isActive,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save notification preferences');
      }

      const data = await response.json();
      setPreferences(data.data);
      toast.success('Notification preferences saved successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      toast.error('Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = async () => {
    try {
      setResetting(true);
      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to reset notification preferences');
      }

      const data = await response.json();
      setPreferences(data.data);
      toast.success('Notification preferences reset to defaults');
    } catch (error) {
      console.error('Error resetting notification preferences:', error);
      toast.error('Failed to reset notification preferences');
    } finally {
      setResetting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Notification Preferences"
        breadcrumbs={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Notifications', href: '/notifications' },
          { label: 'Preferences' },
        ]}
        requireAuth
        allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      >
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Notification Preferences"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Notifications', href: '/notifications' },
        { label: 'Preferences' },
      ]}
      requireAuth
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
    >
      <div className="space-y-6">
        <Card>
          <CardHeader className="bg-primary text-primary-foreground">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <div>
                <CardTitle>Email Notification Preferences</CardTitle>
                <CardDescription className="text-primary-foreground/80">
                  Configure which email notifications you want to receive for sales events
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {preferences && (
              <div className="space-y-6">
                {/* Master Toggle */}
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <Label htmlFor="master-toggle" className="text-base font-medium">
                      Enable Email Notifications
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Master switch to enable or disable all email notifications
                    </p>
                  </div>
                  <Switch
                    id="master-toggle"
                    checked={preferences.isActive}
                    onCheckedChange={(checked) => updatePreference('isActive', checked)}
                  />
                </div>

                <Separator />

                {/* Preference Categories */}
                <div className="space-y-6">
                  {preferenceCategories.map((category) => (
                    <div key={category.title} className="space-y-4">
                      <div>
                        <h3 className="text-lg font-medium">{category.title}</h3>
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </div>
                      <div className="space-y-3">
                        {category.preferences.map((pref) => (
                          <div key={pref.key} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <Label htmlFor={pref.key} className="font-medium">
                                {pref.label}
                              </Label>
                              <p className="text-sm text-muted-foreground">{pref.description}</p>
                            </div>
                            <Switch
                              id={pref.key}
                              checked={preferences[pref.key as keyof NotificationPreferences] as boolean}
                              onCheckedChange={(checked) => updatePreference(pref.key, checked)}
                              disabled={!preferences.isActive}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Action Buttons */}
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={resetToDefaults}
                    disabled={saving || resetting}
                  >
                    {resetting ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <RotateCcw className="h-4 w-4 mr-2" />
                    )}
                    Reset to Defaults
                  </Button>
                  <Button
                    onClick={savePreferences}
                    disabled={saving || resetting}
                  >
                    {saving ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Settings className="h-4 w-4 mr-2" />
                    )}
                    Save Preferences
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
