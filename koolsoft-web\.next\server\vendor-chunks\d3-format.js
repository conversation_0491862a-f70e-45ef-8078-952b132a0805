"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-format";
exports.ids = ["vendor-chunks/d3-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-format/src/defaultLocale.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/defaultLocale.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   format: () => (/* binding */ format),\n/* harmony export */   formatPrefix: () => (/* binding */ formatPrefix)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/./node_modules/d3-format/src/locale.js\");\n\nvar locale;\nvar format;\nvar formatPrefix;\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\nfunction defaultLocale(definition) {\n  locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9kZWZhdWx0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBdUM7QUFFdkMsSUFBSUMsTUFBTTtBQUNILElBQUlDLE1BQU07QUFDVixJQUFJQyxZQUFZO0FBRXZCQyxhQUFhLENBQUM7RUFDWkMsU0FBUyxFQUFFLEdBQUc7RUFDZEMsUUFBUSxFQUFFLENBQUMsQ0FBQyxDQUFDO0VBQ2JDLFFBQVEsRUFBRSxDQUFDLEdBQUcsRUFBRSxFQUFFO0FBQ3BCLENBQUMsQ0FBQztBQUVhLFNBQVNILGFBQWFBLENBQUNJLFVBQVUsRUFBRTtFQUNoRFAsTUFBTSxHQUFHRCxzREFBWSxDQUFDUSxVQUFVLENBQUM7RUFDakNOLE1BQU0sR0FBR0QsTUFBTSxDQUFDQyxNQUFNO0VBQ3RCQyxZQUFZLEdBQUdGLE1BQU0sQ0FBQ0UsWUFBWTtFQUNsQyxPQUFPRixNQUFNO0FBQ2YiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1mb3JtYXRcXHNyY1xcZGVmYXVsdExvY2FsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZm9ybWF0TG9jYWxlIGZyb20gXCIuL2xvY2FsZS5qc1wiO1xuXG52YXIgbG9jYWxlO1xuZXhwb3J0IHZhciBmb3JtYXQ7XG5leHBvcnQgdmFyIGZvcm1hdFByZWZpeDtcblxuZGVmYXVsdExvY2FsZSh7XG4gIHRob3VzYW5kczogXCIsXCIsXG4gIGdyb3VwaW5nOiBbM10sXG4gIGN1cnJlbmN5OiBbXCIkXCIsIFwiXCJdXG59KTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGVmYXVsdExvY2FsZShkZWZpbml0aW9uKSB7XG4gIGxvY2FsZSA9IGZvcm1hdExvY2FsZShkZWZpbml0aW9uKTtcbiAgZm9ybWF0ID0gbG9jYWxlLmZvcm1hdDtcbiAgZm9ybWF0UHJlZml4ID0gbG9jYWxlLmZvcm1hdFByZWZpeDtcbiAgcmV0dXJuIGxvY2FsZTtcbn1cbiJdLCJuYW1lcyI6WyJmb3JtYXRMb2NhbGUiLCJsb2NhbGUiLCJmb3JtYXQiLCJmb3JtYXRQcmVmaXgiLCJkZWZhdWx0TG9jYWxlIiwidGhvdXNhbmRzIiwiZ3JvdXBpbmciLCJjdXJyZW5jeSIsImRlZmluaXRpb24iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/exponent.js":
/*!************************************************!*\
  !*** ./node_modules/d3-format/src/exponent.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n  return x = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9leHBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUV0RCw2QkFBZSxvQ0FBU0MsQ0FBQyxFQUFFO0VBQ3pCLE9BQU9BLENBQUMsR0FBR0QscUVBQWtCLENBQUNFLElBQUksQ0FBQ0MsR0FBRyxDQUFDRixDQUFDLENBQUMsQ0FBQyxFQUFFQSxDQUFDLEdBQUdBLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBR0csR0FBRztBQUM1RCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWZvcm1hdFxcc3JjXFxleHBvbmVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4KSB7XG4gIHJldHVybiB4ID0gZm9ybWF0RGVjaW1hbFBhcnRzKE1hdGguYWJzKHgpKSwgeCA/IHhbMV0gOiBOYU47XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0RGVjaW1hbFBhcnRzIiwieCIsIk1hdGgiLCJhYnMiLCJOYU4iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/exponent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatDecimal.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/formatDecimal.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatDecimalParts: () => (/* binding */ formatDecimalParts)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nfunction formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i,\n    coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +x.slice(i + 1)];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatDecimal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatGroup.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-format/src/formatGroup.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(grouping, thousands) {\n  return function (value, width) {\n    var i = value.length,\n      t = [],\n      j = 0,\n      g = grouping[0],\n      length = 0;\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n    return t.reverse().join(thousands);\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatGroup.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatNumerals.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/formatNumerals.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(numerals) {\n  return function (value) {\n    return value.replace(/[0-9]/g, function (i) {\n      return numerals[+i];\n    });\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXROdW1lcmFscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLFFBQVEsRUFBRTtFQUNoQyxPQUFPLFVBQVNDLEtBQUssRUFBRTtJQUNyQixPQUFPQSxLQUFLLENBQUNDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsVUFBU0MsQ0FBQyxFQUFFO01BQ3pDLE9BQU9ILFFBQVEsQ0FBQyxDQUFDRyxDQUFDLENBQUM7SUFDckIsQ0FBQyxDQUFDO0VBQ0osQ0FBQztBQUNIIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcZDMtZm9ybWF0XFxzcmNcXGZvcm1hdE51bWVyYWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKG51bWVyYWxzKSB7XG4gIHJldHVybiBmdW5jdGlvbih2YWx1ZSkge1xuICAgIHJldHVybiB2YWx1ZS5yZXBsYWNlKC9bMC05XS9nLCBmdW5jdGlvbihpKSB7XG4gICAgICByZXR1cm4gbnVtZXJhbHNbK2ldO1xuICAgIH0pO1xuICB9O1xufVxuIl0sIm5hbWVzIjpbIm51bWVyYWxzIiwidmFsdWUiLCJyZXBsYWNlIiwiaSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatNumerals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-format/src/formatPrefixAuto.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prefixExponent: () => (/* binding */ prefixExponent)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\nvar prefixExponent;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n  var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1],\n    i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n    n = coefficient.length;\n  return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatRounded.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-format/src/formatRounded.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n  var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRSb3VuZGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEO0FBRXRELDZCQUFlLG9DQUFTQyxDQUFDLEVBQUVDLENBQUMsRUFBRTtFQUM1QixJQUFJQyxDQUFDLEdBQUdILHFFQUFrQixDQUFDQyxDQUFDLEVBQUVDLENBQUMsQ0FBQztFQUNoQyxJQUFJLENBQUNDLENBQUMsRUFBRSxPQUFPRixDQUFDLEdBQUcsRUFBRTtFQUNyQixJQUFJRyxXQUFXLEdBQUdELENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDbEJFLFFBQVEsR0FBR0YsQ0FBQyxDQUFDLENBQUMsQ0FBQztFQUNuQixPQUFPRSxRQUFRLEdBQUcsQ0FBQyxHQUFHLElBQUksR0FBRyxJQUFJQyxLQUFLLENBQUMsQ0FBQ0QsUUFBUSxDQUFDLENBQUNFLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBR0gsV0FBVyxHQUNuRUEsV0FBVyxDQUFDSSxNQUFNLEdBQUdILFFBQVEsR0FBRyxDQUFDLEdBQUdELFdBQVcsQ0FBQ0ssS0FBSyxDQUFDLENBQUMsRUFBRUosUUFBUSxHQUFHLENBQUMsQ0FBQyxHQUFHLEdBQUcsR0FBR0QsV0FBVyxDQUFDSyxLQUFLLENBQUNKLFFBQVEsR0FBRyxDQUFDLENBQUMsR0FDOUdELFdBQVcsR0FBRyxJQUFJRSxLQUFLLENBQUNELFFBQVEsR0FBR0QsV0FBVyxDQUFDSSxNQUFNLEdBQUcsQ0FBQyxDQUFDLENBQUNELElBQUksQ0FBQyxHQUFHLENBQUM7QUFDNUUiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1mb3JtYXRcXHNyY1xcZm9ybWF0Um91bmRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2Zvcm1hdERlY2ltYWxQYXJ0c30gZnJvbSBcIi4vZm9ybWF0RGVjaW1hbC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4LCBwKSB7XG4gIHZhciBkID0gZm9ybWF0RGVjaW1hbFBhcnRzKHgsIHApO1xuICBpZiAoIWQpIHJldHVybiB4ICsgXCJcIjtcbiAgdmFyIGNvZWZmaWNpZW50ID0gZFswXSxcbiAgICAgIGV4cG9uZW50ID0gZFsxXTtcbiAgcmV0dXJuIGV4cG9uZW50IDwgMCA/IFwiMC5cIiArIG5ldyBBcnJheSgtZXhwb25lbnQpLmpvaW4oXCIwXCIpICsgY29lZmZpY2llbnRcbiAgICAgIDogY29lZmZpY2llbnQubGVuZ3RoID4gZXhwb25lbnQgKyAxID8gY29lZmZpY2llbnQuc2xpY2UoMCwgZXhwb25lbnQgKyAxKSArIFwiLlwiICsgY29lZmZpY2llbnQuc2xpY2UoZXhwb25lbnQgKyAxKVxuICAgICAgOiBjb2VmZmljaWVudCArIG5ldyBBcnJheShleHBvbmVudCAtIGNvZWZmaWNpZW50Lmxlbmd0aCArIDIpLmpvaW4oXCIwXCIpO1xufVxuIl0sIm5hbWVzIjpbImZvcm1hdERlY2ltYWxQYXJ0cyIsIngiLCJwIiwiZCIsImNvZWZmaWNpZW50IiwiZXhwb25lbnQiLCJBcnJheSIsImpvaW4iLCJsZW5ndGgiLCJzbGljZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatRounded.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatSpecifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/d3-format/src/formatSpecifier.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormatSpecifier: () => (/* binding */ FormatSpecifier),\n/* harmony export */   \"default\": () => (/* binding */ formatSpecifier)\n/* harmony export */ });\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nfunction formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nfunction FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function () {\n  return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatSpecifier.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatTrim.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-format/src/formatTrim.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\":\n        i0 = i1 = i;\n        break;\n      case \"0\":\n        if (i0 === 0) i0 = i;\n        i1 = i;\n        break;\n      default:\n        if (!+s[i]) break out;\n        if (i0 > 0) i0 = 0;\n        break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRUcmltLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLDZCQUFlLG9DQUFTQSxDQUFDLEVBQUU7RUFDekJDLEdBQUcsRUFBRSxLQUFLLElBQUlDLENBQUMsR0FBR0YsQ0FBQyxDQUFDRyxNQUFNLEVBQUVDLENBQUMsR0FBRyxDQUFDLEVBQUVDLEVBQUUsR0FBRyxDQUFDLENBQUMsRUFBRUMsRUFBRSxFQUFFRixDQUFDLEdBQUdGLENBQUMsRUFBRSxFQUFFRSxDQUFDLEVBQUU7SUFDMUQsUUFBUUosQ0FBQyxDQUFDSSxDQUFDLENBQUM7TUFDVixLQUFLLEdBQUc7UUFBRUMsRUFBRSxHQUFHQyxFQUFFLEdBQUdGLENBQUM7UUFBRTtNQUN2QixLQUFLLEdBQUc7UUFBRSxJQUFJQyxFQUFFLEtBQUssQ0FBQyxFQUFFQSxFQUFFLEdBQUdELENBQUM7UUFBRUUsRUFBRSxHQUFHRixDQUFDO1FBQUU7TUFDeEM7UUFBUyxJQUFJLENBQUMsQ0FBQ0osQ0FBQyxDQUFDSSxDQUFDLENBQUMsRUFBRSxNQUFNSCxHQUFHO1FBQUUsSUFBSUksRUFBRSxHQUFHLENBQUMsRUFBRUEsRUFBRSxHQUFHLENBQUM7UUFBRTtJQUN0RDtFQUNGO0VBQ0EsT0FBT0EsRUFBRSxHQUFHLENBQUMsR0FBR0wsQ0FBQyxDQUFDTyxLQUFLLENBQUMsQ0FBQyxFQUFFRixFQUFFLENBQUMsR0FBR0wsQ0FBQyxDQUFDTyxLQUFLLENBQUNELEVBQUUsR0FBRyxDQUFDLENBQUMsR0FBR04sQ0FBQztBQUN0RCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWZvcm1hdFxcc3JjXFxmb3JtYXRUcmltLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRyaW1zIGluc2lnbmlmaWNhbnQgemVyb3MsIGUuZy4sIHJlcGxhY2VzIDEuMjAwMGsgd2l0aCAxLjJrLlxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24ocykge1xuICBvdXQ6IGZvciAodmFyIG4gPSBzLmxlbmd0aCwgaSA9IDEsIGkwID0gLTEsIGkxOyBpIDwgbjsgKytpKSB7XG4gICAgc3dpdGNoIChzW2ldKSB7XG4gICAgICBjYXNlIFwiLlwiOiBpMCA9IGkxID0gaTsgYnJlYWs7XG4gICAgICBjYXNlIFwiMFwiOiBpZiAoaTAgPT09IDApIGkwID0gaTsgaTEgPSBpOyBicmVhaztcbiAgICAgIGRlZmF1bHQ6IGlmICghK3NbaV0pIGJyZWFrIG91dDsgaWYgKGkwID4gMCkgaTAgPSAwOyBicmVhaztcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGkwID4gMCA/IHMuc2xpY2UoMCwgaTApICsgcy5zbGljZShpMSArIDEpIDogcztcbn1cbiJdLCJuYW1lcyI6WyJzIiwib3V0IiwibiIsImxlbmd0aCIsImkiLCJpMCIsImkxIiwic2xpY2UiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatTrim.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/formatTypes.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-format/src/formatTypes.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/./node_modules/d3-format/src/formatDecimal.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatRounded.js */ \"(ssr)/./node_modules/d3-format/src/formatRounded.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": x => Math.round(x).toString(2),\n  \"c\": x => x + \"\",\n  \"d\": _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": x => Math.round(x).toString(8),\n  \"p\": (x, p) => (0,_formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(x * 100, p),\n  \"r\": _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  \"s\": _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  \"X\": x => Math.round(x).toString(16).toUpperCase(),\n  \"x\": x => Math.round(x).toString(16)\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/formatTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/identity.js":
/*!************************************************!*\
  !*** ./node_modules/d3-format/src/identity.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n  return x;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9pZGVudGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUMsRUFBRTtFQUN6QixPQUFPQSxDQUFDO0FBQ1YiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1mb3JtYXRcXHNyY1xcaWRlbnRpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oeCkge1xuICByZXR1cm4geDtcbn1cbiJdLCJuYW1lcyI6WyJ4Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/locale.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-format/src/locale.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n/* harmony import */ var _formatGroup_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatGroup.js */ \"(ssr)/./node_modules/d3-format/src/formatGroup.js\");\n/* harmony import */ var _formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatNumerals.js */ \"(ssr)/./node_modules/d3-format/src/formatNumerals.js\");\n/* harmony import */ var _formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatSpecifier.js */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var _formatTrim_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./formatTrim.js */ \"(ssr)/./node_modules/d3-format/src/formatTrim.js\");\n/* harmony import */ var _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./formatTypes.js */ \"(ssr)/./node_modules/d3-format/src/formatTypes.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/./node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-format/src/identity.js\");\n\n\n\n\n\n\n\n\nvar map = Array.prototype.map,\n  prefixes = [\"y\", \"z\", \"a\", \"f\", \"p\", \"n\", \"µ\", \"m\", \"\", \"k\", \"M\", \"G\", \"T\", \"P\", \"E\", \"Z\", \"Y\"];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatGroup_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(map.call(locale.grouping, Number), locale.thousands + \"\"),\n    currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n    currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n    decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n    numerals = locale.numerals === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(map.call(locale.numerals, String)),\n    percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n    minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n    nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n  function newFormat(specifier) {\n    specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier);\n    var fill = specifier.fill,\n      align = specifier.align,\n      sign = specifier.sign,\n      symbol = specifier.symbol,\n      zero = specifier.zero,\n      width = specifier.width,\n      comma = specifier.comma,\n      precision = specifier.precision,\n      trim = specifier.trim,\n      type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!_formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || fill === \"0\" && align === \"=\") zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n      suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type],\n      maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));\n    function format(value) {\n      var valuePrefix = prefix,\n        valueSuffix = suffix,\n        i,\n        n,\n        c;\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = (0,_formatTrim_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? sign === \"(\" ? sign : minus : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__.prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n        padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\":\n          value = valuePrefix + value + valueSuffix + padding;\n          break;\n        case \"=\":\n          value = valuePrefix + padding + value + valueSuffix;\n          break;\n        case \"^\":\n          value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);\n          break;\n        default:\n          value = padding + valuePrefix + value + valueSuffix;\n          break;\n      }\n      return numerals(value);\n    }\n    format.toString = function () {\n      return specifier + \"\";\n    };\n    return format;\n  }\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier), specifier.type = \"f\", specifier)),\n      e = Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value) / 3))) * 3,\n      k = Math.pow(10, -e),\n      prefix = prefixes[8 + e / 3];\n    return function (value) {\n      return f(k * value) + prefix;\n    };\n  }\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/locale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionFixed.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionFixed.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step) {\n  return Math.max(0, -(0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25GaXhlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUVyQyw2QkFBZSxvQ0FBU0MsSUFBSSxFQUFFO0VBQzVCLE9BQU9DLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDSCx3REFBUSxDQUFDRSxJQUFJLENBQUNFLEdBQUcsQ0FBQ0gsSUFBSSxDQUFDLENBQUMsQ0FBQztBQUMvQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWZvcm1hdFxcc3JjXFxwcmVjaXNpb25GaXhlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXhwb25lbnQgZnJvbSBcIi4vZXhwb25lbnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oc3RlcCkge1xuICByZXR1cm4gTWF0aC5tYXgoMCwgLWV4cG9uZW50KE1hdGguYWJzKHN0ZXApKSk7XG59XG4iXSwibmFtZXMiOlsiZXhwb25lbnQiLCJzdGVwIiwiTWF0aCIsIm1heCIsImFicyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionFixed.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionPrefix.js":
/*!*******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionPrefix.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value) / 3))) * 3 - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25QcmVmaXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUksRUFBRUMsS0FBSyxFQUFFO0VBQ25DLE9BQU9DLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsRUFBRUQsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUVELElBQUksQ0FBQ0UsR0FBRyxDQUFDLENBQUMsRUFBRUYsSUFBSSxDQUFDRyxLQUFLLENBQUNOLHdEQUFRLENBQUNFLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUdGLHdEQUFRLENBQUNHLElBQUksQ0FBQ0ksR0FBRyxDQUFDTixJQUFJLENBQUMsQ0FBQyxDQUFDO0FBQy9HIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcZDMtZm9ybWF0XFxzcmNcXHByZWNpc2lvblByZWZpeC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZXhwb25lbnQgZnJvbSBcIi4vZXhwb25lbnQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oc3RlcCwgdmFsdWUpIHtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIE1hdGgubWF4KC04LCBNYXRoLm1pbig4LCBNYXRoLmZsb29yKGV4cG9uZW50KHZhbHVlKSAvIDMpKSkgKiAzIC0gZXhwb25lbnQoTWF0aC5hYnMoc3RlcCkpKTtcbn1cbiJdLCJuYW1lcyI6WyJleHBvbmVudCIsInN0ZXAiLCJ2YWx1ZSIsIk1hdGgiLCJtYXgiLCJtaW4iLCJmbG9vciIsImFicyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionPrefix.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-format/src/precisionRound.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-format/src/precisionRound.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/./node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(max) - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(step)) + 1;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9wcmVjaXNpb25Sb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQztBQUVyQyw2QkFBZSxvQ0FBU0MsSUFBSSxFQUFFQyxHQUFHLEVBQUU7RUFDakNELElBQUksR0FBR0UsSUFBSSxDQUFDQyxHQUFHLENBQUNILElBQUksQ0FBQyxFQUFFQyxHQUFHLEdBQUdDLElBQUksQ0FBQ0MsR0FBRyxDQUFDRixHQUFHLENBQUMsR0FBR0QsSUFBSTtFQUNqRCxPQUFPRSxJQUFJLENBQUNELEdBQUcsQ0FBQyxDQUFDLEVBQUVGLHdEQUFRLENBQUNFLEdBQUcsQ0FBQyxHQUFHRix3REFBUSxDQUFDQyxJQUFJLENBQUMsQ0FBQyxHQUFHLENBQUM7QUFDeEQiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1mb3JtYXRcXHNyY1xccHJlY2lzaW9uUm91bmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGV4cG9uZW50IGZyb20gXCIuL2V4cG9uZW50LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHN0ZXAsIG1heCkge1xuICBzdGVwID0gTWF0aC5hYnMoc3RlcCksIG1heCA9IE1hdGguYWJzKG1heCkgLSBzdGVwO1xuICByZXR1cm4gTWF0aC5tYXgoMCwgZXhwb25lbnQobWF4KSAtIGV4cG9uZW50KHN0ZXApKSArIDE7XG59XG4iXSwibmFtZXMiOlsiZXhwb25lbnQiLCJzdGVwIiwibWF4IiwiTWF0aCIsImFicyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-format/src/precisionRound.js\n");

/***/ })

};
;