'use client';

/**
 * Formula Management Page
 * 
 * Main page for managing report formulas with listing, creation,
 * editing, and template management capabilities.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Calculator, 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Co<PERSON>,
  Play,
  BookOpen,
  TrendingUp,
  Filter
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';

interface Formula {
  id: string;
  name: string;
  description?: string;
  formula: string;
  category: string;
  returnType: string;
  complexity: number;
  usageCount: number;
  isTemplate: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
    email: string;
  };
  _count: {
    reportFields: number;
    testCases: number;
  };
}

interface FormulaFilters {
  search: string;
  category: string;
  isTemplate: boolean | null;
  page: number;
  limit: number;
}

export default function FormulasPage() {
  const router = useRouter();
  const [formulas, setFormulas] = useState<Formula[]>([]);
  const [templates, setTemplates] = useState<Record<string, Formula[]>>({});
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FormulaFilters>({
    search: '',
    category: '',
    isTemplate: null,
    page: 1,
    limit: 50,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
  });
  const [statistics, setStatistics] = useState({
    totalFormulas: 0,
    templateFormulas: 0,
    activeFormulas: 0,
    categoryCounts: {} as Record<string, number>,
    mostUsedFormulas: [] as Formula[],
  });

  useEffect(() => {
    loadFormulas();
    loadTemplates();
    loadStatistics();
  }, [filters]);

  const loadFormulas = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (filters.search) params.append('search', filters.search);
      if (filters.category) params.append('category', filters.category);
      if (filters.isTemplate !== null) params.append('isTemplate', filters.isTemplate.toString());
      params.append('page', filters.page.toString());
      params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/reports/formulas?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setFormulas(data.data);
        setPagination(data.pagination);
      } else {
        toast.error('Failed to load formulas');
      }
    } catch (error) {
      console.error('Error loading formulas:', error);
      toast.error('Failed to load formulas');
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/reports/formulas/templates', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setTemplates(data.data.templates);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/reports/formulas/statistics', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setStatistics(data.data);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters(prev => ({ ...prev, category, page: 1 }));
  };

  const handleTemplateFilter = (isTemplate: boolean | null) => {
    setFilters(prev => ({ ...prev, isTemplate, page: 1 }));
  };

  const handleEdit = (formula: Formula) => {
    router.push(`/reports/formulas/${formula.id}/edit`);
  };

  const handleDelete = async (formula: Formula) => {
    if (!confirm(`Are you sure you want to delete "${formula.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/reports/formulas/${formula.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        toast.success('Formula deleted successfully');
        loadFormulas();
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to delete formula');
      }
    } catch (error) {
      console.error('Error deleting formula:', error);
      toast.error('Failed to delete formula');
    }
  };

  const handleDuplicate = (formula: Formula) => {
    router.push(`/reports/formulas/new?duplicate=${formula.id}`);
  };

  const handleTest = (formula: Formula) => {
    router.push(`/reports/formulas/${formula.id}/test`);
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      MATHEMATICAL: 'bg-blue-100 text-blue-800',
      STATISTICAL: 'bg-green-100 text-green-800',
      BUSINESS: 'bg-purple-100 text-purple-800',
      CUSTOM: 'bg-gray-100 text-gray-800',
    };
    return colors[category as keyof typeof colors] || colors.CUSTOM;
  };

  const getComplexityColor = (complexity: number) => {
    if (complexity <= 30) return 'bg-green-100 text-green-800';
    if (complexity <= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getComplexityLabel = (complexity: number) => {
    if (complexity <= 30) return 'Simple';
    if (complexity <= 60) return 'Moderate';
    return 'Complex';
  };

  return (
    <DashboardLayout
      title="Formula Engine"
      breadcrumbs={[
        { label: 'Reports', href: '/reports' },
        { label: 'Formulas', href: '/reports/formulas' },
      ]}
      actions={
        <Button onClick={() => router.push('/reports/formulas/new')}>
          <Plus className="h-4 w-4 mr-2" />
          New Formula
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Formulas</p>
                  <p className="text-2xl font-bold">{statistics.totalFormulas}</p>
                </div>
                <Calculator className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Templates</p>
                  <p className="text-2xl font-bold">{statistics.templateFormulas}</p>
                </div>
                <BookOpen className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active</p>
                  <p className="text-2xl font-bold">{statistics.activeFormulas}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Categories</p>
                  <p className="text-2xl font-bold">{Object.keys(statistics.categoryCounts).length}</p>
                </div>
                <Filter className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="formulas" className="w-full">
          <TabsList>
            <TabsTrigger value="formulas">All Formulas</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="formulas" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search formulas..."
                        value={filters.search}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <select
                      value={filters.category}
                      onChange={(e) => handleCategoryFilter(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">All Categories</option>
                      <option value="MATHEMATICAL">Mathematical</option>
                      <option value="STATISTICAL">Statistical</option>
                      <option value="BUSINESS">Business</option>
                      <option value="CUSTOM">Custom</option>
                    </select>

                    <select
                      value={filters.isTemplate === null ? '' : filters.isTemplate.toString()}
                      onChange={(e) => handleTemplateFilter(
                        e.target.value === '' ? null : e.target.value === 'true'
                      )}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">All Types</option>
                      <option value="true">Templates</option>
                      <option value="false">Custom</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Formulas Table */}
            <Card>
              <CardHeader>
                <CardTitle>Formulas</CardTitle>
                <CardDescription>
                  Manage your report formulas and calculation expressions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">Loading formulas...</div>
                ) : formulas.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No formulas found. Create your first formula to get started.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Complexity</TableHead>
                        <TableHead>Usage</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {formulas.map((formula) => (
                        <TableRow key={formula.id}>
                          <TableCell>
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{formula.name}</span>
                                {formula.isTemplate && (
                                  <Badge variant="secondary" className="text-xs">
                                    Template
                                  </Badge>
                                )}
                              </div>
                              {formula.description && (
                                <p className="text-sm text-gray-500 mt-1">
                                  {formula.description}
                                </p>
                              )}
                              <code className="text-xs bg-gray-100 px-1 py-0.5 rounded mt-1 block">
                                {formula.formula.length > 50 
                                  ? `${formula.formula.substring(0, 50)}...`
                                  : formula.formula
                                }
                              </code>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getCategoryColor(formula.category)}>
                              {formula.category}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getComplexityColor(formula.complexity)}>
                              {getComplexityLabel(formula.complexity)} ({formula.complexity})
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{formula.usageCount} uses</div>
                              <div className="text-gray-500">
                                {formula._count.reportFields} fields
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{formatDate(formula.createdAt)}</div>
                              <div className="text-gray-500">
                                by {formula.creator.name}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleTest(formula)}>
                                  <Play className="h-4 w-4 mr-2" />
                                  Test
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEdit(formula)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDuplicate(formula)}>
                                  <Copy className="h-4 w-4 mr-2" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleDelete(formula)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            {Object.entries(templates).map(([category, categoryTemplates]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Badge className={getCategoryColor(category)}>
                      {category}
                    </Badge>
                    <span>Templates</span>
                  </CardTitle>
                  <CardDescription>
                    Pre-built formulas for {category.toLowerCase()} calculations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {categoryTemplates.map((template) => (
                      <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">{template.name}</h4>
                              <Badge variant="outline" className="text-xs">
                                {template.usageCount} uses
                              </Badge>
                            </div>
                            
                            {template.description && (
                              <p className="text-sm text-gray-600">
                                {template.description}
                              </p>
                            )}
                            
                            <code className="text-xs bg-gray-100 p-2 rounded block">
                              {template.formula}
                            </code>
                            
                            <div className="flex justify-between items-center pt-2">
                              <Badge className={getComplexityColor(template.complexity)}>
                                {getComplexityLabel(template.complexity)}
                              </Badge>
                              
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDuplicate(template)}
                              >
                                <Copy className="h-3 w-3 mr-1" />
                                Use
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
