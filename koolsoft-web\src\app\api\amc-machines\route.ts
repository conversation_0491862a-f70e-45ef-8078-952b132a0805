import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCMachineRepository } from '@/lib/repositories';

// AMC Machine creation schema
const createAMCMachineSchema = z.object({
  serialNumber: z.string().min(1, 'Serial number is required'),
  location: z.string().min(1, 'Location is required'),
  section: z.string().optional(),
  productId: z.string().uuid('Valid product ID is required'),
  modelId: z.string().uuid('Valid model ID is required'),
  brandId: z.string().uuid('Valid brand ID is required'),
  amcContractId: z.string().uuid('Valid AMC contract ID is required'),
  installationDate: z.coerce.date().optional(),
  warrantyEndDate: z.coerce.date().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).default('ACTIVE'),
  notes: z.string().optional(),
});

/**
 * Get all AMC machines
 * Requires authentication
 */
async function getAMCMachines(request: NextRequest) {
  try {
    // Get the repository instance
    const machineRepository = getAMCMachineRepository();

    // Get all AMC machines with related data
    const amcMachines = await machineRepository.findAll();

    return NextResponse.json({
      data: amcMachines,
      meta: {
        total: amcMachines.length,
      },
    });
  } catch (error) {
    console.error('Error fetching AMC machines:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AMC machines' },
      { status: 500 }
    );
  }
}

/**
 * Create a new AMC machine
 * Requires authentication and appropriate role
 */
async function createAMCMachine(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = createAMCMachineSchema.parse(body);

    // Get the repository instance
    const machineRepository = getAMCMachineRepository();

    // Transform data for Prisma - connect the AMC contract
    const { amcContractId, ...machineData } = validatedData;
    const createData = {
      ...machineData,
      amcContract: {
        connect: { id: amcContractId }
      }
    };

    // Create the new AMC machine
    const machine = await machineRepository.create(createData);

    return NextResponse.json(machine, { status: 201 });
  } catch (error) {
    console.error('Error creating AMC machine:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create AMC machine' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  getAMCMachines
);

export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  createAMCMachine
);
