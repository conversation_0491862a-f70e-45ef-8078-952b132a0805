import { NextResponse } from 'next/server';

/**
 * Report Utility Functions
 * Common utilities for report generation and formatting
 */

/**
 * Generate CSV content from data array
 */
export function generateCSV(data: any[], headers?: string[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  // Use provided headers or extract from first object
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create CSV header row
  const headerRow = csvHeaders.map(header => `"${header}"`).join(',');
  
  // Create CSV data rows
  const dataRows = data.map(row => {
    return csvHeaders.map(header => {
      const value = row[header];
      if (value === null || value === undefined) {
        return '""';
      }
      // Handle nested objects
      if (typeof value === 'object') {
        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      }
      // Escape quotes and wrap in quotes
      return `"${String(value).replace(/"/g, '""')}"`;
    }).join(',');
  });
  
  return [headerRow, ...dataRows].join('\n');
}

/**
 * Generate CSV response with proper headers
 */
export function generateCSVResponse(data: any[], filename: string, headers?: string[]): NextResponse {
  const csvContent = generateCSV(data, headers);
  
  return new NextResponse(csvContent, {
    status: 200,
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}.csv"`,
      'Cache-Control': 'no-cache',
    },
  });
}

/**
 * Format date for reports
 */
export function formatReportDate(date: Date | string | null): string {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(d.getTime())) return '';
  
  return d.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
}

/**
 * Format currency for reports
 */
export function formatReportCurrency(amount: number | null): string {
  if (amount === null || amount === undefined) return '₹0.00';
  
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format number for reports
 */
export function formatReportNumber(value: number | null): string {
  if (value === null || value === undefined) return '0';
  
  return new Intl.NumberFormat('en-IN').format(value);
}

/**
 * Sanitize filename for download
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9\-_\.]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_+|_+$/g, '');
}

/**
 * Generate report filename with timestamp
 */
export function generateReportFilename(reportType: string, format: string = 'csv'): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  const sanitizedType = sanitizeFilename(reportType.toLowerCase());
  return `${sanitizedType}_report_${timestamp}.${format.toLowerCase()}`;
}

/**
 * Flatten nested objects for CSV export
 */
export function flattenObject(obj: any, prefix: string = ''): any {
  const flattened: any = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (value === null || value === undefined) {
        flattened[newKey] = '';
      } else if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
        // Recursively flatten nested objects
        Object.assign(flattened, flattenObject(value, newKey));
      } else if (Array.isArray(value)) {
        // Convert arrays to comma-separated strings
        flattened[newKey] = value.join(', ');
      } else {
        flattened[newKey] = value;
      }
    }
  }
  
  return flattened;
}

/**
 * Prepare data for CSV export by flattening nested objects
 */
export function prepareDataForCSV(data: any[]): any[] {
  return data.map(item => flattenObject(item));
}

/**
 * Calculate percentage
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((value / total) * 100 * 100) / 100; // Round to 2 decimal places
}

/**
 * Calculate growth percentage
 */
export function calculateGrowthPercentage(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100 * 100) / 100;
}

/**
 * Group data by date period
 */
export function groupByDatePeriod(data: any[], dateField: string, period: 'day' | 'week' | 'month' | 'quarter' | 'year'): any[] {
  const grouped = new Map();
  
  data.forEach(item => {
    const date = new Date(item[dateField]);
    if (isNaN(date.getTime())) return;
    
    let key: string;
    switch (period) {
      case 'day':
        key = date.toISOString().slice(0, 10);
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().slice(0, 10);
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'quarter':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        key = `${date.getFullYear()}-Q${quarter}`;
        break;
      case 'year':
        key = String(date.getFullYear());
        break;
      default:
        key = date.toISOString().slice(0, 10);
    }
    
    if (!grouped.has(key)) {
      grouped.set(key, []);
    }
    grouped.get(key).push(item);
  });
  
  return Array.from(grouped.entries()).map(([period, items]) => ({
    period,
    count: items.length,
    items,
  })).sort((a, b) => a.period.localeCompare(b.period));
}

/**
 * Validate report filters
 */
export function validateReportFilters(filters: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate date range
  if (filters.startDate && filters.endDate) {
    const start = new Date(filters.startDate);
    const end = new Date(filters.endDate);
    
    if (isNaN(start.getTime())) {
      errors.push('Invalid start date format');
    }
    
    if (isNaN(end.getTime())) {
      errors.push('Invalid end date format');
    }
    
    if (start.getTime() > end.getTime()) {
      errors.push('Start date must be before or equal to end date');
    }
  }
  
  // Validate pagination
  if (filters.page && filters.page < 1) {
    errors.push('Page number must be greater than 0');
  }
  
  if (filters.limit && (filters.limit < 1 || filters.limit > 1000)) {
    errors.push('Limit must be between 1 and 1000');
  }
  
  // Validate amount range
  if (filters.amountMin && filters.amountMax) {
    if (filters.amountMin < 0) {
      errors.push('Minimum amount must be non-negative');
    }
    
    if (filters.amountMax < 0) {
      errors.push('Maximum amount must be non-negative');
    }
    
    if (filters.amountMin > filters.amountMax) {
      errors.push('Minimum amount must be less than or equal to maximum amount');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Build standard API response
 */
export function buildReportResponse(data: any, pagination?: any, metadata?: any) {
  return {
    success: true,
    data,
    pagination,
    metadata: {
      generatedAt: new Date().toISOString(),
      ...metadata,
    },
  };
}

/**
 * Build error response
 */
export function buildErrorResponse(message: string, details?: any) {
  return {
    success: false,
    error: message,
    details,
    timestamp: new Date().toISOString(),
  };
}
