import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { notificationStatisticsFilterSchema } from '@/lib/validations/notification.schema';
import { z } from 'zod';

/**
 * GET /api/notifications/statistics
 * Get notification statistics (Admin/Manager only)
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = notificationStatisticsFilterSchema.parse(queryParams);

      const salesNotificationService = getSalesNotificationService();
      const statistics = await salesNotificationService.getNotificationStatistics(
        validatedParams.startDate,
        validatedParams.endDate
      );

      return NextResponse.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      console.error('Error fetching notification statistics:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch notification statistics',
        },
        { status: 500 }
      );
    }
  }
);
