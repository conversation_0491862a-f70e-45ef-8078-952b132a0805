/**
 * Repository Compatibility Layer
 *
 * This file provides a compatibility layer between the legacy JavaScript repositories
 * and the modern TypeScript repositories. It allows for a gradual migration from the
 * legacy repositories to the modern repositories without breaking existing code.
 */

import { getRepositoryFactory } from '../repository.factory';
import { getCustomerRepository } from '../index';
import { getAMCContractRepository } from '../index';
import { getAMCMachineRepository } from '../index';

// Type definitions for compatibility layer options
interface CustomerQueryOptions {
  skip?: number;
  take?: number;
  orderBy?: any;
  where?: any;
  includeContacts?: boolean;
  includeVisitCards?: boolean;
  includeAMCContracts?: boolean;
  includeWarranties?: boolean;
}

interface AMCContractQueryOptions {
  skip?: number;
  take?: number;
  orderBy?: any;
  where?: any;
}

interface MachineQueryOptions {
  skip?: number;
  take?: number;
  orderBy?: any;
  where?: any;
}

/**
 * Legacy-compatible Customer Repository
 *
 * This object provides the same API as the legacy CustomerRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyCustomerRepository = {
  /**
   * Get all customers from the modern table
   * @param options - Query options
   * @returns List of customers
   */
  async getAllCustomers(options: CustomerQueryOptions = {}) {
    const repository = getCustomerRepository();
    const {
      skip = 0,
      take = 50,
      orderBy = { name: 'asc' },
      where = {},
      includeContacts = false,
      includeVisitCards = false
    } = options;

    // Use the modern repository's findAll method with pagination
    return repository.findAll(take, skip);
  },

  /**
   * Get a customer by ID from the modern table
   * @param id - Customer ID
   * @param options - Query options
   * @returns Customer data
   */
  async getCustomerById(id: string, options: CustomerQueryOptions = {}) {
    const repository = getCustomerRepository();
    const {
      includeContacts = false,
      includeVisitCards = false,
      includeAMCContracts = false,
      includeWarranties = false
    } = options;

    // Use the modern repository's findById method (single parameter)
    return repository.findById(id);
  },

  /**
   * Get a customer by original ID from the modern table
   * @param originalId - Original customer ID from legacy table
   * @param options - Query options
   * @returns Customer data
   */
  async getCustomerByOriginalId(originalId: number, options: CustomerQueryOptions = {}) {
    const repository = getCustomerRepository();
    const {
      includeContacts = false,
      includeVisitCards = false,
      includeAMCContracts = false,
      includeWarranties = false
    } = options;

    // Use findAll with filter to find by originalId
    const customers = await repository.findAll(1, 0);
    // Filter by originalId manually since we don't have findMany
    const filtered = customers.filter((c: any) => c.originalId === originalId);
    return filtered.length > 0 ? filtered[0] : null;
  },

  /**
   * Get a customer from the legacy table
   * @param id - Legacy customer ID
   * @returns Legacy customer data
   */
  async getLegacyCustomer(id: number) {
    const repository = getCustomerRepository();
    // Use findAll with filter to simulate legacy lookup
    const customers = await repository.findAll(1, 0);
    // Filter by originalId manually since we don't have findMany
    const filtered = customers.filter((c: any) => c.originalId === id);
    return filtered.length > 0 ? filtered[0] : null;
  },

  /**
   * Create a new customer in both modern and legacy tables
   * @param customerData - Customer data
   * @returns Created customer
   */
  async createCustomer(customerData: any) {
    const repository = getCustomerRepository();
    return repository.create(customerData);
  },

  /**
   * Update a customer in both modern and legacy tables
   * @param id - Modern customer ID
   * @param customerData - Updated customer data
   * @returns Updated customer
   */
  async updateCustomer(id: string, customerData: any) {
    const repository = getCustomerRepository();
    return repository.update(id, customerData);
  }
};

/**
 * Legacy-compatible AMC Contract Repository
 *
 * This object provides the same API as the legacy AMCContractRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyAMCContractRepository = {
  /**
   * Get all AMC contracts
   * @param options - Query options
   * @returns List of AMC contracts
   */
  async getAllAMCContracts(options: AMCContractQueryOptions = {}) {
    const repository = getAMCContractRepository();
    const { take = 50, skip = 0 } = options;
    return repository.findAll(take, skip);
  },

  /**
   * Get an AMC contract by ID
   * @param id - AMC contract ID
   * @param options - Query options
   * @returns AMC contract data
   */
  async getAMCContractById(id: string, options: AMCContractQueryOptions = {}) {
    const repository = getAMCContractRepository();
    return repository.findById(id);
  },

  /**
   * Create a new AMC contract
   * @param contractData - AMC contract data
   * @returns Created AMC contract
   */
  async createAMCContract(contractData: any) {
    const repository = getAMCContractRepository();
    return repository.create(contractData);
  },

  /**
   * Update an AMC contract
   * @param id - AMC contract ID
   * @param contractData - Updated AMC contract data
   * @returns Updated AMC contract
   */
  async updateAMCContract(id: string, contractData: any) {
    const repository = getAMCContractRepository();
    return repository.update(id, contractData);
  }
};

/**
 * Legacy-compatible Machine Repository
 *
 * This object provides the same API as the legacy MachineRepository,
 * but uses the modern repository implementation internally.
 */
export const LegacyMachineRepository = {
  /**
   * Get all AMC machines
   * @param options - Query options
   * @returns List of AMC machines
   */
  async getAllAMCMachines(options: MachineQueryOptions = {}) {
    const repository = getAMCMachineRepository();
    const { take = 50, skip = 0 } = options;
    return repository.findAll(take, skip);
  },

  /**
   * Create a new AMC machine
   * @param machineData - AMC machine data
   * @returns Created AMC machine
   */
  async createAMCMachine(machineData: any) {
    const repository = getAMCMachineRepository();
    return repository.create(machineData);
  }
};

// Export other compatibility repositories as needed
export const LegacyComponentRepository = {
  // Implement compatibility methods
};

export const LegacyWarrantyRepository = {
  // Implement compatibility methods
};
