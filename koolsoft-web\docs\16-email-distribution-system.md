# Email Report Distribution System

## Overview

The Email Report Distribution System provides comprehensive automated email distribution functionality for KoolSoft reports. Users can configure email distributions, manage recipient lists, and automatically send generated reports via email with professional formatting and delivery tracking.

## Features

### Core Functionality
- **Email Distribution Lists**: Create and manage reusable lists of email recipients
- **Report Email Configurations**: Configure email settings for different report types
- **Automated Email Sending**: Send reports via email with attachments in PDF, Excel, or CSV format
- **Delivery Tracking**: Monitor email delivery status with detailed logging and retry mechanisms
- **Template Management**: Use customizable email templates with placeholder replacement

### Integration
- **Seamless Report Integration**: Works with existing AMC, Warranty, Service, Sales, and Customer reports
- **Role-Based Access Control**: Admin/Manager can configure, all roles can use
- **Professional Branding**: Emails include KoolSoft branding and professional formatting

## Database Schema

### email_distribution_lists
```sql
CREATE TABLE email_distribution_lists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
  description TEXT,
  emails TEXT[] NOT NULL, -- Array of email addresses
  is_active BOOLEAN DEFAULT true,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### report_email_configs
```sql
CREATE TABLE report_email_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_type VARCHAR(50) NOT NULL, -- AMC, WARRANTY, SERVICE, SALES, CUSTOMER
  name VARCHAR(255) NOT NULL,
  description TEXT,
  email_subject VARCHAR(255) NOT NULL,
  email_body TEXT NOT NULL,
  email_template_id UUID REFERENCES email_templates(id),
  distribution_list_id UUID REFERENCES email_distribution_lists(id),
  individual_recipients TEXT[] DEFAULT '{}',
  include_attachment BOOLEAN DEFAULT true,
  attachment_format VARCHAR(20) DEFAULT 'PDF', -- PDF, EXCEL, CSV
  is_active BOOLEAN DEFAULT true,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### report_email_deliveries
```sql
CREATE TABLE report_email_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_id UUID REFERENCES report_email_configs(id) ON DELETE CASCADE,
  report_type VARCHAR(50) NOT NULL,
  report_parameters JSONB NOT NULL,
  recipients TEXT[] NOT NULL,
  email_subject VARCHAR(255) NOT NULL,
  attachment_path TEXT,
  attachment_format VARCHAR(20),
  status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, SENT, FAILED, PARTIAL
  sent_count INTEGER DEFAULT 0,
  failed_count INTEGER DEFAULT 0,
  error_messages TEXT[] DEFAULT '{}',
  sent_at TIMESTAMP,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Distribution Lists
- `GET /api/reports/email/distribution-lists` - List distribution lists
- `POST /api/reports/email/distribution-lists` - Create distribution list
- `GET /api/reports/email/distribution-lists/[id]` - Get distribution list
- `PUT /api/reports/email/distribution-lists/[id]` - Update distribution list
- `DELETE /api/reports/email/distribution-lists/[id]` - Delete distribution list

### Email Configurations
- `GET /api/reports/email/configs` - List email configurations
- `POST /api/reports/email/configs` - Create email configuration
- `GET /api/reports/email/configs/[id]` - Get email configuration
- `PUT /api/reports/email/configs/[id]` - Update email configuration
- `DELETE /api/reports/email/configs/[id]` - Delete email configuration

### Email Sending
- `POST /api/reports/email/send` - Send report via email
- `POST /api/reports/email/deliveries/retry` - Retry failed delivery

### Delivery Tracking
- `GET /api/reports/email/deliveries` - List email deliveries
- `GET /api/reports/email/deliveries/stats` - Get delivery statistics

## Usage Examples

### Creating a Distribution List
```typescript
const distributionList = await fetch('/api/reports/email/distribution-lists', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({
    name: 'Management Team',
    description: 'Senior management distribution list',
    emails: ['<EMAIL>', '<EMAIL>']
  })
});
```

### Sending a Report via Email
```typescript
const emailResult = await fetch('/api/reports/email/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({
    reportType: 'AMC',
    reportParameters: { startDate: '2024-01-01', endDate: '2024-12-31' },
    recipients: ['<EMAIL>'],
    emailSubject: 'Monthly AMC Report',
    emailBody: 'Please find attached the monthly AMC report.',
    includeAttachment: true,
    attachmentFormat: 'PDF'
  })
});
```

## Frontend Components

### EmailDistributionDialog
React component for email distribution with features:
- Recipient management with add/remove functionality
- Email configuration selection
- Subject and body customization
- Attachment format selection
- Real-time validation

## Security Considerations

- **Role-Based Access**: Admin/Manager roles required for configuration management
- **Input Validation**: All email addresses validated using Zod schemas
- **Rate Limiting**: Email sending protected against abuse
- **Secure Templates**: Email templates sanitized to prevent XSS
- **Audit Logging**: All email activities logged for compliance

## Performance Optimization

- **Batch Processing**: Multiple recipients handled efficiently
- **Async Operations**: Email sending doesn't block API responses
- **File Cleanup**: Temporary attachment files automatically cleaned up
- **Database Indexing**: Optimized queries with proper indexes

## Monitoring and Troubleshooting

### Delivery Status Monitoring
- Track email delivery success/failure rates
- Monitor bounce rates and delivery errors
- Retry failed deliveries with exponential backoff

### Common Issues
1. **SMTP Configuration**: Ensure email service credentials are correct
2. **Attachment Size**: Large reports may exceed email size limits
3. **Recipient Validation**: Invalid email addresses will cause delivery failures
4. **Template Variables**: Ensure all placeholders are properly replaced

## Integration with Existing Systems

The Email Distribution System seamlessly integrates with:
- **Reporting System**: All existing report types supported
- **User Management**: Uses existing authentication and authorization
- **Email Service**: Leverages existing SMTP configuration
- **Activity Logging**: All actions logged in activity_logs table

## Future Enhancements

Potential improvements identified for future releases:
- Advanced email analytics dashboard
- Rich text email template editor
- Bulk email operations with progress tracking
- Email scheduling calendar interface
- Integration with external email services (SendGrid, Mailgun)
- Mobile push notifications for email delivery status
