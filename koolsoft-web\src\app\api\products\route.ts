import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * Query parameters schema for products
 */
const productsQuerySchema = z.object({
  search: z.string().optional(),
  brandId: z.string().uuid().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET /api/products
 * Get products with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = productsQuerySchema.parse(queryParams);

      const {
        search,
        brandId,
        page,
        limit,
        sortBy,
        sortOrder,
      } = validatedParams;

      const skip = (page - 1) * limit;

      // Build filter criteria
      const whereClause: any = {
        AND: [
          // Search filter
          ...(search
            ? [
                {
                  OR: [
                    {
                      name: {
                        contains: search,
                        mode: 'insensitive',
                      },
                    },
                    {
                      description: {
                        contains: search,
                        mode: 'insensitive',
                      },
                    },
                  ],
                },
              ]
            : []),
          // Brand filter
          ...(brandId ? [{ brandId }] : []),
        ],
      };

      // Get products with relations
      const products = await prisma.products.findMany({
        where: whereClause,
        include: {
          brand: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          models: {
            select: {
              id: true,
              name: true,
              description: true,
              specs: true,
              tonnage: true,
            },
            orderBy: {
              name: 'asc',
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      });

      // Get total count for pagination
      const totalCount = await prisma.products.count({
        where: whereClause,
      });

      const totalPages = Math.ceil(totalCount / limit);

      return NextResponse.json({
        success: true,
        data: products,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      console.error('Error fetching products:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch products',
        },
        { status: 500 }
      );
    }
  }
);
