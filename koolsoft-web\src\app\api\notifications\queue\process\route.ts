import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { processNotificationsSchema } from '@/lib/validations/notification.schema';
import { z } from 'zod';

/**
 * POST /api/notifications/queue/process
 * Process pending notifications in the queue (Admin/Manager only)
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const validatedData = processNotificationsSchema.parse(body);

      const salesNotificationService = getSalesNotificationService();
      const processedCount = await salesNotificationService.processPendingNotifications(validatedData.limit);

      return NextResponse.json({
        success: true,
        message: `Processed ${processedCount} notifications`,
        processedCount,
      });
    } catch (error) {
      console.error('Error processing notification queue:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to process notification queue',
        },
        { status: 500 }
      );
    }
  }
);
