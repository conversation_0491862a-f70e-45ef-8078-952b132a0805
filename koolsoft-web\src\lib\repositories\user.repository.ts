import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * User Repository
 *
 * This repository handles database operations for the User entity.
 */
export class UserRepository extends PrismaRepository<
  Prisma.usersGetPayload<{}>,
  string,
  Prisma.usersCreateInput,
  Prisma.usersUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('users');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find a user by email
   * @param email User email
   * @returns Promise resolving to the user or null if not found
   */
  async findByEmail(email: string): Promise<Prisma.usersGetPayload<{}> | null> {
    return this.model.findUnique({
      where: { email },
    });
  }

  /**
   * Find users by role
   * @param role User role
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of users
   */
  async findByRole(role: string, skip?: number, take?: number): Promise<Prisma.usersGetPayload<{}>[]> {
    return this.model.findMany({
      where: { role },
      skip,
      take,
    });
  }

  /**
   * Find active users
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active users
   */
  async findActive(skip?: number, take?: number): Promise<Prisma.usersGetPayload<{}>[]> {
    return this.model.findMany({
      where: { isActive: true },
      skip,
      take,
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<Prisma.usersGetPayload<{}>, string, Prisma.usersCreateInput, Prisma.usersUpdateInput> {
    return new UserRepository(tx);
  }
}
