'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { ArrowRightLeft } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Conversion Reports Layout Component
 *
 * This component provides a consistent layout for all conversion reports pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function ConversionReportsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Conversion Reports';
  if (pathname !== '/conversions/reports') {
    // Format the report name from the URL path
    // e.g., /conversions/reports/statistics -> Statistics
    const reportName = pathname?.split('/').pop() || '';
    pageTitle = reportName.charAt(0).toUpperCase() + reportName.slice(1);
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Conversion Reports', href: '/conversions/reports', icon: <ArrowRightLeft className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/conversions/reports') {
    breadcrumbs.push({ label: pageTitle, current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
