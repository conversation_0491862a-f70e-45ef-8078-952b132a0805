import { NextRequest, NextResponse } from 'next/server';
import { withManagerProtection } from '@/lib/auth/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * Report parameters schema
 */
const reportParamsSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  type: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES']).optional(),
});

/**
 * GET /api/reports/summary
 * Get summary report data
 * Manager-only endpoint (accessible by managers and admins)
 */
export const GET = withManagerProtection(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate parameters
    const params = {
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
      type: searchParams.get('type') || undefined,
    };
    
    const validatedParams = reportParamsSchema.parse(params);
    
    // Parse dates
    const startDate = validatedParams.startDate ? new Date(validatedParams.startDate) : new Date(new Date().getFullYear(), 0, 1);
    const endDate = validatedParams.endDate ? new Date(validatedParams.endDate) : new Date();
    
    // Prepare filter based on report type
    const filter: any = {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    };
    
    // Get summary data based on report type
    let summary: any = {};
    
    if (!validatedParams.type || validatedParams.type === 'AMC') {
      // Get AMC summary
      const amcContracts = await prisma.amc_contracts.count({
        where: filter,
      });
      
      const activeAmcContracts = await prisma.amc_contracts.count({
        where: {
          ...filter,
          status: 'ACTIVE',
        },
      });
      
      const expiringAmcContracts = await prisma.amc_contracts.count({
        where: {
          ...filter,
          status: 'ACTIVE',
          endDate: {
            lte: new Date(new Date().setDate(new Date().getDate() + 30)),
          },
        },
      });
      
      summary.amc = {
        total: amcContracts,
        active: activeAmcContracts,
        expiring: expiringAmcContracts,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'WARRANTY') {
      // Get warranty summary
      const warranties = await prisma.warranties.count({
        where: filter,
      });
      
      const activeWarranties = await prisma.warranties.count({
        where: {
          ...filter,
          status: 'ACTIVE',
        },
      });
      
      const expiringWarranties = await prisma.warranties.count({
        where: {
          ...filter,
          status: 'ACTIVE',
          endDate: {
            lte: new Date(new Date().setDate(new Date().getDate() + 30)),
          },
        },
      });
      
      summary.warranty = {
        total: warranties,
        active: activeWarranties,
        expiring: expiringWarranties,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'SERVICE') {
      // Get service summary
      const serviceReports = await prisma.service_reports.count({
        where: filter,
      });
      
      const pendingServiceReports = await prisma.service_reports.count({
        where: {
          ...filter,
          status: 'PENDING',
        },
      });
      
      const completedServiceReports = await prisma.service_reports.count({
        where: {
          ...filter,
          status: 'COMPLETED',
        },
      });
      
      summary.service = {
        total: serviceReports,
        pending: pendingServiceReports,
        completed: completedServiceReports,
      };
    }
    
    if (!validatedParams.type || validatedParams.type === 'SALES') {
      // Get sales summary
      const salesLeads = await prisma.sales_leads.count({
        where: filter,
      });
      
      const salesOpportunities = await prisma.sales_opportunities.count({
        where: filter,
      });
      
      const salesOrders = await prisma.sales_orders.count({
        where: filter,
      });
      
      summary.sales = {
        leads: salesLeads,
        opportunities: salesOpportunities,
        orders: salesOrders,
      };
    }
    
    return NextResponse.json({
      summary,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    console.error('Error generating report summary:', error);
    return NextResponse.json(
      { error: 'Failed to generate report summary' },
      { status: 500 }
    );
  }
});
