'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { FileText } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Visit Cards Layout Component
 *
 * This component provides a consistent layout for all visit card-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function VisitCardsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Visit Cards';
  if (pathname !== '/visit-cards') {
    if (pathname?.includes('/edit')) {
      pageTitle = 'Edit Visit Card';
    } else if (pathname?.includes('/new')) {
      pageTitle = 'New Visit Card';
    } else {
      pageTitle = 'Visit Card Details';
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Visit Cards', href: '/visit-cards', icon: <FileText className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/visit-cards') {
    breadcrumbs.push({ label: pageTitle, current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
