'use client';

import { useContext } from 'react';
import { BaseCrystalReport, CrystalReportContext } from '../base-crystal-report';
import { CrystalReportTable, CrystalReportSummary, CrystalTableColumn } from '../crystal-report-table';

export interface AMCSummaryReportProps {
  parameters: {
    startDate?: string;
    endDate?: string;
    customerId?: string;
    executiveId?: string;
    amcStatus?: string;
    contractType?: string;
    groupBy?: 'customer' | 'executive' | 'status' | 'none';
  };
  onParametersChange?: (parameters: any) => void;
}

/**
 * AMC Summary Report Component
 * 
 * Replicates the Crystal Reports AMC Summary functionality with:
 * - Date range filtering
 * - Customer and executive filtering
 * - Status and contract type filtering
 * - Grouping options
 * - Summary calculations
 */
export function AMCSummaryReport({ parameters, onParametersChange }: AMCSummaryReportProps) {
  const { reportData } = useContext(CrystalReportContext);

  // Define table columns matching Crystal Reports layout
  const columns: CrystalTableColumn[] = [
    {
      key: 'contractNo',
      label: 'Contract No.',
      type: 'text',
      width: '120px',
      align: 'left',
    },
    {
      key: 'customerName',
      label: 'Customer Name',
      type: 'text',
      width: '200px',
      align: 'left',
      groupable: true,
    },
    {
      key: 'executiveName',
      label: 'Executive',
      type: 'text',
      width: '150px',
      align: 'left',
      groupable: true,
    },
    {
      key: 'startDate',
      label: 'Start Date',
      type: 'date',
      width: '120px',
      align: 'center',
    },
    {
      key: 'endDate',
      label: 'End Date',
      type: 'date',
      width: '120px',
      align: 'center',
    },
    {
      key: 'amount',
      label: 'Amount',
      type: 'currency',
      width: '120px',
      align: 'right',
      summable: true,
    },
    {
      key: 'status',
      label: 'Status',
      type: 'status',
      width: '100px',
      align: 'center',
      groupable: true,
    },
    {
      key: 'contractType',
      label: 'Type',
      type: 'text',
      width: '100px',
      align: 'center',
    },
    {
      key: 'serviceType',
      label: 'Service Type',
      type: 'text',
      width: '120px',
      align: 'left',
    },
  ];

  // Transform data for display
  const transformedData = reportData?.data?.map((item: any) => ({
    contractNo: item.contractNo || `AMC-${item.id?.slice(-6)}`,
    customerName: item.customer?.name || 'Unknown Customer',
    executiveName: item.users?.name || 'Unassigned',
    startDate: item.startDate,
    endDate: item.endDate,
    amount: item.amount || 0,
    status: item.status || 'ACTIVE',
    contractType: item.contractType || 'STANDARD',
    serviceType: item.serviceType || 'MAINTENANCE',
  })) || [];

  // Calculate summary data
  const summaryData = reportData?.summary || {
    totalContracts: transformedData.length,
    totalAmount: transformedData.reduce((sum, item) => sum + (item.amount || 0), 0),
    activeContracts: transformedData.filter(item => item.status === 'ACTIVE').length,
    expiredContracts: transformedData.filter(item => item.status === 'EXPIRED').length,
  };

  return (
    <BaseCrystalReport
      reportName="amc-summary"
      reportTitle="AMC Summary Report"
      reportDescription="Comprehensive summary of Annual Maintenance Contracts with filtering and grouping options"
      parameters={parameters}
      onParametersChange={onParametersChange}
      allowExport={true}
      allowPrint={true}
    >
      <div className="p-6 space-y-6">
        {/* Report Summary */}
        <CrystalReportSummary
          title="AMC Summary Statistics"
          data={summaryData}
        />

        {/* Parameters Display */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h3 className="font-semibold text-blue-900 mb-2">Report Parameters</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {parameters.startDate && (
              <div>
                <span className="font-medium text-blue-800">Start Date:</span>
                <div className="text-blue-700">{parameters.startDate}</div>
              </div>
            )}
            {parameters.endDate && (
              <div>
                <span className="font-medium text-blue-800">End Date:</span>
                <div className="text-blue-700">{parameters.endDate}</div>
              </div>
            )}
            {parameters.amcStatus && (
              <div>
                <span className="font-medium text-blue-800">Status:</span>
                <div className="text-blue-700">{parameters.amcStatus}</div>
              </div>
            )}
            {parameters.contractType && (
              <div>
                <span className="font-medium text-blue-800">Contract Type:</span>
                <div className="text-blue-700">{parameters.contractType}</div>
              </div>
            )}
          </div>
        </div>

        {/* Main Data Table */}
        <CrystalReportTable
          data={transformedData}
          columns={columns}
          groupBy={parameters.groupBy !== 'none' ? parameters.groupBy : undefined}
          showGroupSummary={parameters.groupBy !== 'none'}
          showGrandTotal={true}
          maxHeight="500px"
        />

        {/* Additional Statistics */}
        {transformedData.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2">Active Contracts</h4>
              <div className="text-2xl font-bold text-green-700">
                {summaryData.activeContracts}
              </div>
              <div className="text-sm text-green-600">
                {((summaryData.activeContracts / summaryData.totalContracts) * 100).toFixed(1)}% of total
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <h4 className="font-semibold text-red-900 mb-2">Expired Contracts</h4>
              <div className="text-2xl font-bold text-red-700">
                {summaryData.expiredContracts}
              </div>
              <div className="text-sm text-red-600">
                {((summaryData.expiredContracts / summaryData.totalContracts) * 100).toFixed(1)}% of total
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">Average Contract Value</h4>
              <div className="text-2xl font-bold text-blue-700">
                ₹{(summaryData.totalAmount / summaryData.totalContracts).toLocaleString('en-IN', { maximumFractionDigits: 0 })}
              </div>
              <div className="text-sm text-blue-600">
                Per contract
              </div>
            </div>
          </div>
        )}

        {/* Report Footer */}
        <div className="border-t pt-4 text-center text-sm text-gray-500">
          <p>
            This report contains {transformedData.length} AMC contracts
            {parameters.startDate && parameters.endDate && 
              ` for the period from ${parameters.startDate} to ${parameters.endDate}`
            }
          </p>
          <p className="mt-1">
            Generated on {new Date().toLocaleDateString('en-IN', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
      </div>
    </BaseCrystalReport>
  );
}

/**
 * AMC Summary Report with Context Provider
 */
export function AMCSummaryReportWithContext(props: AMCSummaryReportProps) {
  return (
    <CrystalReportContext.Provider value={{
      reportData: null,
      parameters: props.parameters,
      isLoading: false,
      error: null,
    }}>
      <AMCSummaryReport {...props} />
    </CrystalReportContext.Provider>
  );
}
