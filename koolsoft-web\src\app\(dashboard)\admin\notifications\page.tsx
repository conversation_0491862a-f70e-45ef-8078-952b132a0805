'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Loader2, Bell, Play, BarChart3, Settings, AlertTriangle } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';

interface NotificationStatistics {
  events: {
    totalEvents: number;
    processedEvents: number;
    unprocessedEvents: number;
    eventsByType: Record<string, number>;
    eventsByEntityType: Record<string, number>;
  };
  queue: {
    totalNotifications: number;
    pendingNotifications: number;
    sentNotifications: number;
    failedNotifications: number;
    notificationsByStatus: Record<string, number>;
    notificationsByPriority: Record<string, number>;
    averageAttempts: number;
  };
  preferences: {
    totalUsers: number;
    activePreferences: number;
    preferencesByType: Record<string, number>;
  };
}

export default function AdminNotificationsPage() {
  const [statistics, setStatistics] = useState<NotificationStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications/statistics', {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Admin/Manager role required.');
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to fetch notification statistics (${response.status})`);
        }
      }

      const data = await response.json();
      setStatistics(data.data);
    } catch (error) {
      console.error('Error fetching notification statistics:', error);
      toast.error('Failed to load notification statistics');
    } finally {
      setLoading(false);
    }
  };

  const processNotifications = async () => {
    try {
      setProcessing(true);
      const response = await fetch('/api/notifications/queue/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ limit: 50 }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Admin/Manager role required.');
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to process notifications (${response.status})`);
        }
      }

      const data = await response.json();
      toast.success(data.message);
      
      // Refresh statistics
      await fetchStatistics();
    } catch (error) {
      console.error('Error processing notifications:', error);
      toast.error('Failed to process notifications');
    } finally {
      setProcessing(false);
    }
  };

  const processEvents = async () => {
    try {
      setProcessing(true);
      const response = await fetch('/api/notifications/events/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ limit: 50 }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Admin/Manager role required.');
        } else {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to process events (${response.status})`);
        }
      }

      const data = await response.json();
      toast.success(data.message);
      
      // Refresh statistics
      await fetchStatistics();
    } catch (error) {
      console.error('Error processing events:', error);
      toast.error('Failed to process events');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout
        title="Notification Management"
        breadcrumbs={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Admin', href: '/admin' },
          { label: 'Notifications' },
        ]}
        requireAuth
        allowedRoles={['ADMIN', 'MANAGER']}
      >
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Notification Management"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Admin', href: '/admin' },
        { label: 'Notifications' },
      ]}
      requireAuth
      allowedRoles={['ADMIN', 'MANAGER']}
    >
      <div className="space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Events</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics?.events.totalEvents || 0}</div>
              <p className="text-xs text-muted-foreground">
                {statistics?.events.unprocessedEvents || 0} unprocessed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Queue Status</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics?.queue.totalNotifications || 0}</div>
              <p className="text-xs text-muted-foreground">
                {statistics?.queue.pendingNotifications || 0} pending
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistics?.queue.totalNotifications 
                  ? Math.round((statistics.queue.sentNotifications / statistics.queue.totalNotifications) * 100)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                {statistics?.queue.sentNotifications || 0} sent successfully
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics?.preferences.activePreferences || 0}</div>
              <p className="text-xs text-muted-foreground">
                of {statistics?.preferences.totalUsers || 0} total users
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="bg-primary text-primary-foreground">
              <CardTitle className="flex items-center space-x-2">
                <Play className="h-5 w-5" />
                <span>Process Notifications</span>
              </CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Process pending notifications in the queue
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Pending Notifications:</span>
                  <Badge variant="secondary">{statistics?.queue.pendingNotifications || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Failed Notifications:</span>
                  <Badge variant="destructive">{statistics?.queue.failedNotifications || 0}</Badge>
                </div>
                <Button
                  onClick={processNotifications}
                  disabled={processing || (statistics?.queue.pendingNotifications || 0) === 0}
                  className="w-full"
                >
                  {processing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Process Queue
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="bg-primary text-primary-foreground">
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5" />
                <span>Process Events</span>
              </CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Process unprocessed notification events
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Unprocessed Events:</span>
                  <Badge variant="secondary">{statistics?.events.unprocessedEvents || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Total Events:</span>
                  <Badge variant="outline">{statistics?.events.totalEvents || 0}</Badge>
                </div>
                <Button
                  onClick={processEvents}
                  disabled={processing || (statistics?.events.unprocessedEvents || 0) === 0}
                  className="w-full"
                >
                  {processing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 mr-2" />
                  )}
                  Process Events
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Statistics Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Events by Type</CardTitle>
              <CardDescription>Breakdown of notification events by type</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {statistics?.events.eventsByType && Object.entries(statistics.events.eventsByType).map(([type, count]) => (
                  <div key={type} className="flex justify-between items-center">
                    <span className="text-sm">{type.replace(/_/g, ' ')}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Queue Status Breakdown</CardTitle>
              <CardDescription>Current status of notifications in queue</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {statistics?.queue.notificationsByStatus && Object.entries(statistics.queue.notificationsByStatus).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center">
                    <span className="text-sm">{status}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
