'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { 
  XCircle, 
  Search, 
  Filter, 
  FileDown, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  AlertTriangle,
  Clock,
  Calendar,
  Wrench,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

/**
 * Out-of-Warranty Management Page
 * 
 * This page displays and manages products that are no longer under warranty coverage.
 * It includes service request management, payment tracking, and conversion workflows.
 */
export default function OutWarrantyPage() {
  const [outWarranties, setOutWarranties] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [customerFilter, setCustomerFilter] = useState('all');

  // Load out-of-warranty data from API
  useEffect(() => {
    const loadOutWarranties = async () => {
      try {
        setIsLoading(true);

        const response = await fetch('/api/out-warranties', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch out-of-warranty data');
        }

        const data = await response.json();
        setOutWarranties(data.outWarranties || []);
        setError(null);
      } catch (err) {
        console.error('Error loading out-warranties:', err);
        setError('Failed to load out-of-warranty data');
        setOutWarranties([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadOutWarranties();
  }, []);

  // Load customers for filter dropdown
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        setIsLoadingCustomers(true);

        const response = await fetch('/api/customers?take=100', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch customers');
        }

        const data = await response.json();
        setCustomers(data.success ? data.data : data.customers || []);
      } catch (err) {
        console.error('Error loading customers:', err);
        setCustomers([]);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    loadCustomers();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          <Clock className="h-3 w-3 mr-1" />
          Active
        </Badge>;
      case 'COMPLETED':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">
          <Clock className="h-3 w-3 mr-1" />
          Completed
        </Badge>;
      case 'PENDING':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Pending
        </Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const filteredOutWarranties = outWarranties.filter(item => {
    const matchesSearch = searchTerm === '' ||
      item.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.machines.some((machine: any) =>
        machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        machine.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
        machine.product.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesCustomer = customerFilter === 'all' || item.customerId === customerFilter;

    return matchesSearch && matchesStatus && matchesCustomer;
  });

  const handleDelete = async (warrantyId: string, customerName: string) => {
    if (!confirm(`Are you sure you want to delete out-warranty record for ${customerName}? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeletingId(warrantyId);

      const response = await fetch(`/api/out-warranties/${warrantyId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to delete out-warranty');
      }

      // Remove from local state
      setOutWarranties(prev => prev.filter(w => w.id !== warrantyId));
      setError(null);

      // Show success toast
      toast.success('Out-warranty record deleted successfully');
    } catch (error: any) {
      console.error('Error deleting out-warranty:', error);
      setError(error.message || 'Failed to delete out-warranty');
      toast.error(error.message || 'Failed to delete out-warranty');
    } finally {
      setDeletingId(null);
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/out-warranties/export?format=CSV', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export out-of-warranty data');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `out-of-warranty-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Out-warranty data exported successfully');
    } catch (error) {
      console.error('Error exporting out-of-warranty data:', error);
      setError('Failed to export out-of-warranty data');
      toast.error('Failed to export out-of-warranty data');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <XCircle className="h-5 w-5" />
              <span>Out-of-Warranty Management</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Manage products no longer under warranty coverage and track service requests
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/warranties/new?type=out-warranty">
                <Plus className="h-4 w-4 mr-2" />
                New Out-Warranty
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="search" className="text-black">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by customer, model, or serial number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-black">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer" className="text-black">Customer</Label>
              <Select value={customerFilter} onValueChange={setCustomerFilter}>
                <SelectTrigger id="customer">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Customers</SelectItem>
                  {isLoadingCustomers ? (
                    <SelectItem value="loading" disabled>Loading customers...</SelectItem>
                  ) : Array.isArray(customers) && customers.length > 0 ? (
                    customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name} - {customer.city || 'Unknown City'}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-customers" disabled>No customers available</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-black">{error}</AlertDescription>
            </Alert>
          )}

          {/* Out-Warranties Table */}
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black">Customer</TableHead>
                  <TableHead className="text-black">Machine</TableHead>
                  <TableHead className="text-black">Service Requests</TableHead>
                  <TableHead className="text-black">Amount</TableHead>
                  <TableHead className="text-black">Balance</TableHead>
                  <TableHead className="text-black">Last Service</TableHead>
                  <TableHead className="text-black">Status</TableHead>
                  <TableHead className="text-right text-black">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredOutWarranties.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <XCircle className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No out-of-warranty products found</p>
                        <Button asChild>
                          <Link href="/warranties/new?type=out-warranty">
                            <Plus className="h-4 w-4 mr-2" />
                            Add First Out-Warranty Product
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOutWarranties.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{item.customer.name}</div>
                          <div className="text-sm text-gray-500">{item.customer.city}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{item.machines[0]?.model}</div>
                          <div className="text-sm text-gray-500">SN: {item.machines[0]?.serialNumber}</div>
                          <div className="text-sm text-gray-500">{item.machines[0]?.product}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div className="flex items-center space-x-1">
                          <Wrench className="h-4 w-4 text-blue-600" />
                          <span>{item.serviceRequests}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{formatCurrency(item.amount)}</div>
                          <div className="text-sm text-gray-500">Paid: {formatCurrency(item.totalPayments)}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">
                        {item.balance > 0 ? (
                          <div className="flex items-center space-x-1 text-red-600">
                            <DollarSign className="h-4 w-4" />
                            <span>{formatCurrency(item.balance)}</span>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-1 text-green-600">
                            <DollarSign className="h-4 w-4" />
                            <span>Paid</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-black">
                        {item.lastServiceDate ? formatDate(item.lastServiceDate) : 'No service yet'}
                      </TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/out-warranty/${item.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/out-warranty/${item.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(item.id, item.customer.name)}
                            disabled={deletingId === item.id}
                          >
                            {deletingId === item.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {!isLoading && filteredOutWarranties.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-gray-600">
                Showing {filteredOutWarranties.length} of {outWarranties.length} out-of-warranty products
                {customerFilter !== 'all' && ' (filtered by customer)'}
                {statusFilter !== 'all' && ' (filtered by status)'}
                {searchTerm && ' (search results)'}
              </p>
              <div className="text-sm text-gray-500">
                Use filters above to narrow down results
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
