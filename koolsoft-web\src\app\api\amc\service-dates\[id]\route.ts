import { NextRequest, NextResponse } from 'next/server';
import { getAMCServiceDateRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { amcServiceDateSchema } from '@/lib/validations/amc-contract.schema';

/**
 * Service date update schema
 */
const updateServiceDateSchema = amcServiceDateSchema.partial().extend({
  amcContractId: z.string().uuid().optional(),
});

/**
 * GET /api/amc/service-dates/[id]
 * Get a specific service date
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcServiceDateRepository = getAMCServiceDateRepository();

      const serviceDate = await amcServiceDateRepository.findById(id);

      if (!serviceDate) {
        return NextResponse.json(
          { error: 'Service date not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ serviceDate });
    } catch (error) {
      console.error('Error fetching service date:', error);
      return NextResponse.json(
        { error: 'Failed to fetch service date' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/amc/service-dates/[id]
 * Update a service date
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateServiceDateSchema.parse(body);

      const amcServiceDateRepository = getAMCServiceDateRepository();

      // Check if service date exists
      const existingServiceDate = await amcServiceDateRepository.findById(id);
      if (!existingServiceDate) {
        return NextResponse.json(
          { error: 'Service date not found' },
          { status: 404 }
        );
      }

      // Prepare update data
      const updateData: any = {};

      if (validatedData.scheduledDate) {
        updateData.serviceDate = validatedData.scheduledDate;
      }

      if (validatedData.completedDate) {
        updateData.completedDate = validatedData.completedDate;
      }

      if (validatedData.status) {
        // Map status to service flag
        switch (validatedData.status) {
          case 'SCHEDULED':
            updateData.serviceFlag = 'S';
            break;
          case 'COMPLETED':
            updateData.serviceFlag = 'C';
            break;
          case 'MISSED':
            updateData.serviceFlag = 'M';
            break;
        }
      }

      if (validatedData.technicianId) {
        updateData.serviceId = validatedData.technicianId;
      }

      if (validatedData.remarks) {
        updateData.serviceFlag = updateData.serviceFlag || existingServiceDate.serviceFlag;
      }

      // serviceNumber field is not part of the validation schema

      // Update the service date
      const updatedServiceDate = await amcServiceDateRepository.update(id, updateData);

      return NextResponse.json({
        message: 'Service date updated successfully',
        serviceDate: updatedServiceDate,
      });
    } catch (error) {
      console.error('Error updating service date:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to update service date' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/amc/service-dates/[id]
 * Delete a service date
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const amcServiceDateRepository = getAMCServiceDateRepository();

      // Check if service date exists
      const existingServiceDate = await amcServiceDateRepository.findById(id);
      if (!existingServiceDate) {
        return NextResponse.json(
          { error: 'Service date not found' },
          { status: 404 }
        );
      }

      // Delete the service date
      await amcServiceDateRepository.delete(id);

      return NextResponse.json({
        message: 'Service date deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting service date:', error);
      return NextResponse.json(
        { error: 'Failed to delete service date' },
        { status: 500 }
      );
    }
  }
);
