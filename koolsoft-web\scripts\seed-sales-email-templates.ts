import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const salesEmailTemplates = [
  {
    name: 'sales-lead-created',
    subject: 'New Sales Lead Created - {{customerName}}',
    bodyHtml: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #0F52BA; color: white; padding: 20px; text-align: center;">
          <h1>New Sales Lead Created</h1>
        </div>
        <div style="padding: 20px; background-color: #f9f9f9;">
          <p>Hello {{recipientName}},</p>
          <p>A new sales lead has been created in the KoolSoft system.</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>Lead Details:</h3>
            <ul>
              <li><strong>Customer:</strong> {{customerName}}</li>
              <li><strong>Executive:</strong> {{executiveName}}</li>
              <li><strong>Status:</strong> {{newStatus}}</li>
              <li><strong>Contact Person:</strong> {{eventData.contactPerson}}</li>
              <li><strong>Contact Phone:</strong> {{eventData.contactPhone}}</li>
              <li><strong>Prospect Percentage:</strong> {{eventData.prospectPercentage}}%</li>
            </ul>
          </div>
          
          <p>Please review this lead and take appropriate action.</p>
          <p>Best regards,<br>KoolSoft Team</p>
        </div>
      </div>
    `,
    bodyText: `
      New Sales Lead Created
      
      Hello {{recipientName}},
      
      A new sales lead has been created in the KoolSoft system.
      
      Lead Details:
      - Customer: {{customerName}}
      - Executive: {{executiveName}}
      - Status: {{newStatus}}
      - Contact Person: {{eventData.contactPerson}}
      - Contact Phone: {{eventData.contactPhone}}
      - Prospect Percentage: {{eventData.prospectPercentage}}%
      
      Please review this lead and take appropriate action.
      
      Best regards,
      KoolSoft Team
    `,
    category: 'sales',
    variables: ['recipientName', 'customerName', 'executiveName', 'newStatus', 'eventData'],
    description: 'Email template for new sales lead creation notifications',
  },
  {
    name: 'sales-lead-status-changed',
    subject: 'Sales Lead Status Updated - {{customerName}}',
    bodyHtml: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #0F52BA; color: white; padding: 20px; text-align: center;">
          <h1>Sales Lead Status Updated</h1>
        </div>
        <div style="padding: 20px; background-color: #f9f9f9;">
          <p>Hello {{recipientName}},</p>
          <p>A sales lead status has been updated in the KoolSoft system.</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>Lead Details:</h3>
            <ul>
              <li><strong>Customer:</strong> {{customerName}}</li>
              <li><strong>Executive:</strong> {{executiveName}}</li>
              <li><strong>Previous Status:</strong> <span style="color: #666;">{{oldStatus}}</span></li>
              <li><strong>New Status:</strong> <span style="color: #0F52BA; font-weight: bold;">{{newStatus}}</span></li>
              <li><strong>Contact Person:</strong> {{eventData.contactPerson}}</li>
              <li><strong>Prospect Percentage:</strong> {{eventData.prospectPercentage}}%</li>
            </ul>
          </div>
          
          <p>Please review this status change and take any necessary follow-up actions.</p>
          <p>Best regards,<br>KoolSoft Team</p>
        </div>
      </div>
    `,
    bodyText: `
      Sales Lead Status Updated
      
      Hello {{recipientName}},
      
      A sales lead status has been updated in the KoolSoft system.
      
      Lead Details:
      - Customer: {{customerName}}
      - Executive: {{executiveName}}
      - Previous Status: {{oldStatus}}
      - New Status: {{newStatus}}
      - Contact Person: {{eventData.contactPerson}}
      - Prospect Percentage: {{eventData.prospectPercentage}}%
      
      Please review this status change and take any necessary follow-up actions.
      
      Best regards,
      KoolSoft Team
    `,
    category: 'sales',
    variables: ['recipientName', 'customerName', 'executiveName', 'oldStatus', 'newStatus', 'eventData'],
    description: 'Email template for sales lead status change notifications',
  },
  {
    name: 'sales-opportunity-created',
    subject: 'New Sales Opportunity Created - {{customerName}}',
    bodyHtml: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #0F52BA; color: white; padding: 20px; text-align: center;">
          <h1>New Sales Opportunity Created</h1>
        </div>
        <div style="padding: 20px; background-color: #f9f9f9;">
          <p>Hello {{recipientName}},</p>
          <p>A new sales opportunity has been created in the KoolSoft system.</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>Opportunity Details:</h3>
            <ul>
              <li><strong>Customer:</strong> {{customerName}}</li>
              <li><strong>Executive:</strong> {{executiveName}}</li>
              <li><strong>Status:</strong> {{newStatus}}</li>
              <li><strong>Contact Person:</strong> {{eventData.contactPerson}}</li>
              <li><strong>Contact Phone:</strong> {{eventData.contactPhone}}</li>
              <li><strong>Prospect Percentage:</strong> {{eventData.prospectPercentage}}%</li>
            </ul>
          </div>
          
          <p>Please review this opportunity and develop an action plan.</p>
          <p>Best regards,<br>KoolSoft Team</p>
        </div>
      </div>
    `,
    bodyText: `
      New Sales Opportunity Created
      
      Hello {{recipientName}},
      
      A new sales opportunity has been created in the KoolSoft system.
      
      Opportunity Details:
      - Customer: {{customerName}}
      - Executive: {{executiveName}}
      - Status: {{newStatus}}
      - Contact Person: {{eventData.contactPerson}}
      - Contact Phone: {{eventData.contactPhone}}
      - Prospect Percentage: {{eventData.prospectPercentage}}%
      
      Please review this opportunity and develop an action plan.
      
      Best regards,
      KoolSoft Team
    `,
    category: 'sales',
    variables: ['recipientName', 'customerName', 'executiveName', 'newStatus', 'eventData'],
    description: 'Email template for new sales opportunity creation notifications',
  },
  {
    name: 'sales-order-created',
    subject: '🎉 New Sales Order Created - {{customerName}}',
    bodyHtml: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #0F52BA; color: white; padding: 20px; text-align: center;">
          <h1>🎉 New Sales Order Created!</h1>
        </div>
        <div style="padding: 20px; background-color: #f9f9f9;">
          <p>Hello {{recipientName}},</p>
          <p><strong>Great news!</strong> A new sales order has been created in the KoolSoft system.</p>
          
          <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #28a745;">
            <h3>Order Details:</h3>
            <ul>
              <li><strong>Customer:</strong> {{customerName}}</li>
              <li><strong>Executive:</strong> {{executiveName}}</li>
              <li><strong>Status:</strong> {{newStatus}}</li>
              <li><strong>Amount:</strong> ₹{{eventData.amount}}</li>
              <li><strong>Contact Person:</strong> {{eventData.contactPerson}}</li>
              <li><strong>Contact Phone:</strong> {{eventData.contactPhone}}</li>
              <li><strong>Delivery Date:</strong> {{eventData.deliveryDate}}</li>
            </ul>
          </div>
          
          <p>This is a high-priority notification. Please ensure proper order processing and delivery coordination.</p>
          <p>Best regards,<br>KoolSoft Team</p>
        </div>
      </div>
    `,
    bodyText: `
      🎉 New Sales Order Created!
      
      Hello {{recipientName}},
      
      Great news! A new sales order has been created in the KoolSoft system.
      
      Order Details:
      - Customer: {{customerName}}
      - Executive: {{executiveName}}
      - Status: {{newStatus}}
      - Amount: ₹{{eventData.amount}}
      - Contact Person: {{eventData.contactPerson}}
      - Contact Phone: {{eventData.contactPhone}}
      - Delivery Date: {{eventData.deliveryDate}}
      
      This is a high-priority notification. Please ensure proper order processing and delivery coordination.
      
      Best regards,
      KoolSoft Team
    `,
    category: 'sales',
    variables: ['recipientName', 'customerName', 'executiveName', 'newStatus', 'eventData'],
    description: 'Email template for new sales order creation notifications (high priority)',
  },
];

async function seedSalesEmailTemplates() {
  console.log('Seeding sales email templates...');

  for (const template of salesEmailTemplates) {
    try {
      // Check if template already exists
      const existingTemplate = await prisma.emailTemplate.findUnique({
        where: { name: template.name },
      });

      if (existingTemplate) {
        console.log(`Template '${template.name}' already exists, updating...`);
        await prisma.emailTemplate.update({
          where: { name: template.name },
          data: {
            subject: template.subject,
            bodyHtml: template.bodyHtml,
            bodyText: template.bodyText,
            description: template.description,
            variables: template.variables,
            category: template.category,
            isActive: true,
          },
        });
      } else {
        console.log(`Creating template '${template.name}'...`);
        await prisma.emailTemplate.create({
          data: template,
        });
      }
    } catch (error) {
      console.error(`Error processing template '${template.name}':`, error);
    }
  }

  console.log('Sales email templates seeding completed!');
}

// Run the seeding function
seedSalesEmailTemplates()
  .catch((error) => {
    console.error('Error seeding sales email templates:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
