'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { Database } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Reference Data Layout Component
 *
 * This component provides a consistent layout for all reference data-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function ReferenceDataLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Reference Data';
  let categoryTitle = '';

  if (pathname !== '/reference-data') {
    const type = pathname?.split('/').pop();

    // Define category titles
    const categoryInfo: Record<string, string> = {
      territories: 'Territories',
      segments: 'Segments',
      competitors: 'Competitors',
      serviceVisitType: 'Service Visit Types',
      complaintType: 'Complaint Types',
      complaintNatureType: 'Complaint Nature Types',
      failureType: 'Failure Types',
      spareType: 'Spare Types',
      measurementType: 'Measurement Types',
      priorityTypes: 'Priority Types',
      priorityType: 'Priority Types',
      enquiryTypes: 'Enquiry Types',
      enquiryType: 'Enquiry Types',
      deductionTypes: 'Deduction Types',
      deductionType: 'Deduction Types',
      debitDivisions: 'Debit Divisions',
      debitDivision: 'Debit Divisions',
      accountDivisions: 'Account Divisions',
      accountDivision: 'Account Divisions',
      spareParts: 'Spare Parts',
      sparePart: 'Spare Parts',
      taxRates: 'Tax Rates',
      taxRate: 'Tax Rates',
      transitDamageTypes: 'Transit Damage Types',
      transitDamageType: 'Transit Damage Types',
      userGroups: 'User Groups',
      userGroup: 'User Groups',
      uspTypes: 'USP Types',
      uspType: 'USP Types',
      visitTypes: 'Visit Types',
      visitType: 'Visit Types',
      divisions: 'Divisions',
      brands: 'Brands'
    };

    categoryTitle = categoryInfo[type as string] || type || '';
    pageTitle = categoryTitle;
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Reference Data', href: '/reference-data', icon: <Database className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname !== '/reference-data' && categoryTitle) {
    breadcrumbs.push({ label: categoryTitle, href: pathname || '', current: true });
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
