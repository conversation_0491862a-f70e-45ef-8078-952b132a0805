import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { EmailDistributionListRepository } from '@/lib/repositories/email-distribution.repository';
import { updateEmailDistributionListSchema } from '@/lib/validations/email-distribution.schema';
import { z } from 'zod';

/**
 * GET /api/reports/email/distribution-lists/[id]
 * Get a specific email distribution list
 */
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) => {
  try {
    const repository = new EmailDistributionListRepository();
    const distributionList = await repository.findById(params.id);

    if (!distributionList) {
      return NextResponse.json(
        { success: false, error: 'Distribution list not found' },
        { status: 404 }
      );
    }

    // Check if user has access to this distribution list
    if (distributionList.createdBy !== user.id && !['ADMIN', 'MANAGER'].includes(user.role?.toUpperCase())) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: distributionList,
    });

  } catch (error) {
    console.error('Error fetching distribution list:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch distribution list' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/reports/email/distribution-lists/[id]
 * Update an email distribution list
 */
export const PUT = withRoleProtection(['ADMIN', 'MANAGER'], async (
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) => {
  try {
    const body = await request.json();
    const validatedData = updateEmailDistributionListSchema.parse(body);

    const repository = new EmailDistributionListRepository();
    const existingList = await repository.findById(params.id);

    if (!existingList) {
      return NextResponse.json(
        { success: false, error: 'Distribution list not found' },
        { status: 404 }
      );
    }

    // Check if user has access to update this distribution list
    if (existingList.createdBy !== user.id && user.role?.toUpperCase() !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if name already exists for this user (excluding current record)
    if (validatedData.name) {
      const nameExists = await repository.getModel().findFirst({
        where: {
          name: validatedData.name,
          createdBy: user.id,
          id: { not: params.id },
        },
      });

      if (nameExists) {
        return NextResponse.json(
          { success: false, error: 'Distribution list with this name already exists' },
          { status: 409 }
        );
      }
    }

    const updatedList = await repository.update(params.id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedList,
      message: 'Distribution list updated successfully',
    });

  } catch (error) {
    console.error('Error updating distribution list:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to update distribution list' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/reports/email/distribution-lists/[id]
 * Delete an email distribution list
 */
export const DELETE = withRoleProtection(['ADMIN', 'MANAGER'], async (
  request: NextRequest,
  { params, user }: { params: { id: string }; user: any }
) => {
  try {
    const repository = new EmailDistributionListRepository();
    const existingList = await repository.findById(params.id);

    if (!existingList) {
      return NextResponse.json(
        { success: false, error: 'Distribution list not found' },
        { status: 404 }
      );
    }

    // Check if user has access to delete this distribution list
    if (existingList.createdBy !== user.id && user.role?.toUpperCase() !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if distribution list is being used by any email configurations
    const configsUsingList = await repository.getPrismaClient().report_email_configs.count({
      where: { distributionListId: params.id },
    });

    if (configsUsingList > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot delete distribution list. It is being used by ${configsUsingList} email configuration(s).` 
        },
        { status: 409 }
      );
    }

    await repository.delete(params.id);

    return NextResponse.json({
      success: true,
      message: 'Distribution list deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting distribution list:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete distribution list' },
      { status: 500 }
    );
  }
});
