import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesLeadRepository } from '@/lib/repositories';
import { createSalesLeadSchema, salesFilterSchema } from '@/lib/validations/sales.schema';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/sales/leads
 * Get sales leads with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        status: searchParams.get('status') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
        search: searchParams.get('search') || undefined,
        skip: searchParams.get('skip') || undefined,
        take: searchParams.get('take') || undefined,
        sortBy: searchParams.get('sortBy') || undefined,
        sortOrder: searchParams.get('sortOrder') || undefined,
      };

      const validatedFilters = salesFilterSchema.parse(filters);

      const salesLeadRepository = getSalesLeadRepository();
      const result = await salesLeadRepository.findWithFilters(validatedFilters);

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error fetching sales leads:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales leads',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/sales/leads
 * Create a new sales lead
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createSalesLeadSchema.parse(body);

      const salesLeadRepository = getSalesLeadRepository();

      // Create sales lead
      const salesLead = await salesLeadRepository.create({
        customer: { connect: { id: validatedData.customerId } },
        executive: { connect: { id: validatedData.executiveId } },
        leadDate: validatedData.leadDate,
        contactPerson: validatedData.contactPerson,
        contactPhone: validatedData.contactPhone,
        status: validatedData.status,
        prospectPercentage: validatedData.prospectPercentage,
        followUpDate: validatedData.followUpDate,
        nextVisitDate: validatedData.nextVisitDate,
        remarks: validatedData.remarks,
      });

      // Fetch the created lead with relations
      const createdLead = await salesLeadRepository.findById(salesLead.id);

      // Create notification event for lead creation
      try {
        const session = await getServerSession(authOptions);
        const salesNotificationService = getSalesNotificationService();

        await salesNotificationService.createSalesEvent({
          eventType: 'LEAD_CREATED',
          entityType: 'lead',
          entityId: salesLead.id,
          userId: session?.user?.id,
          customerId: validatedData.customerId,
          executiveId: validatedData.executiveId,
          newStatus: validatedData.status,
          eventData: {
            leadDate: validatedData.leadDate,
            contactPerson: validatedData.contactPerson,
            contactPhone: validatedData.contactPhone,
            prospectPercentage: validatedData.prospectPercentage,
          },
        });
      } catch (notificationError) {
        console.error('Error creating lead notification:', notificationError);
        // Don't fail the request if notification fails
      }

      return NextResponse.json({
        success: true,
        data: createdLead,
        message: 'Sales lead created successfully',
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating sales lead:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create sales lead',
        },
        { status: 500 }
      );
    }
  }
);
