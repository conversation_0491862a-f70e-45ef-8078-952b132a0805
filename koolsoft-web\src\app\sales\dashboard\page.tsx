'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  SalesMetricsCards, 
  SalesTrendsChart, 
  SalesPipelineBreakdown, 
  SalesDashboardFilters 
} from '@/components/sales';
import { BarChart4, AlertCircle, RefreshCw, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import { SalesDashboardFilters as FilterType } from '@/lib/validations/sales.schema';

interface DashboardData {
  summary: {
    totalLeads: number;
    totalOpportunities: number;
    totalProspects: number;
    totalOrders: number;
    totalRevenue: number;
    averageOrderValue: number;
    leadToOpportunityRate: number;
    opportunityToProspectRate: number;
    prospectToOrderRate: number;
    overallConversionRate: number;
  };
  breakdown: {
    pipeline: Array<{
      status: string;
      count: number;
      color: string;
    }>;
  };
  trends: {
    monthly: Array<{
      month: string;
      leads: number;
      opportunities: number;
      prospects: number;
      orders: number;
      revenue: number;
    }>;
  };
  period: {
    startDate: string;
    endDate: string;
    period: string;
  };
}

/**
 * Sales Dashboard Page
 *
 * This page provides comprehensive sales metrics and analytics including:
 * - Key performance indicators (KPIs)
 * - Sales trends over time
 * - Pipeline breakdown visualization
 * - Filtering and export capabilities
 *
 * Note: This page uses the Sales layout which provides DashboardLayout,
 * so we don't need to wrap it in DashboardLayout again.
 */
export default function SalesDashboardPage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterType>({
    period: '30d',
  });

  // Fetch dashboard data
  const fetchDashboardData = async (currentFilters: FilterType) => {
    try {
      setIsLoading(true);
      setError(null);

      const searchParams = new URLSearchParams();
      if (currentFilters.customerId) searchParams.set('customerId', currentFilters.customerId);
      if (currentFilters.executiveId) searchParams.set('executiveId', currentFilters.executiveId);
      if (currentFilters.startDate) searchParams.set('startDate', currentFilters.startDate);
      if (currentFilters.endDate) searchParams.set('endDate', currentFilters.endDate);
      if (currentFilters.period) searchParams.set('period', currentFilters.period);

      const response = await fetch(`/api/sales/dashboard?${searchParams.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch dashboard data: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch dashboard data');
      }

      setDashboardData(result.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData(filters);
  }, []);

  // Handle filter changes
  const handleFiltersChange = (newFilters: FilterType) => {
    setFilters(newFilters);
    fetchDashboardData(newFilters);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchDashboardData(filters);
  };

  // Handle export
  const handleExport = async (format: 'CSV' | 'EXCEL') => {
    try {
      const exportData = {
        format,
        ...filters,
      };

      const response = await fetch('/api/sales/dashboard/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(exportData),
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sales_dashboard_${new Date().toISOString().split('T')[0]}.${format.toLowerCase()}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Dashboard data exported as ${format}`);
    } catch (error) {
      console.error('Error exporting dashboard data:', error);
      toast.error('Failed to export dashboard data');
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="bg-primary text-primary-foreground">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart4 className="h-6 w-6" />
                Sales Dashboard
              </CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Comprehensive sales metrics and analytics
              </CardDescription>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <SalesDashboardFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onExport={handleExport}
        isLoading={isLoading}
      />

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Dashboard Content */}
      {dashboardData && (
        <>
          {/* Metrics Cards */}
          <SalesMetricsCards
            data={dashboardData.summary}
            isLoading={isLoading}
          />

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sales Trends Chart */}
            <SalesTrendsChart
              data={dashboardData.trends.monthly}
              isLoading={isLoading}
              className="lg:col-span-1"
            />

            {/* Pipeline Breakdown */}
            <SalesPipelineBreakdown
              data={dashboardData.breakdown.pipeline}
              isLoading={isLoading}
              className="lg:col-span-1"
            />
          </div>

          {/* Period Information */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>
                    Data period: {dashboardData.period.startDate} to {dashboardData.period.endDate}
                  </span>
                </div>
                <span>
                  Last updated: {new Date().toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Loading State */}
      {isLoading && !dashboardData && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                    <Skeleton className="h-12 w-12 rounded-full" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-80 w-full" />
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-80 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
