import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getReportSchedulerService } from '@/lib/services/report-scheduler.service';
import { triggerExecutionSchema } from '@/lib/validations/scheduled-report.schema';

/**
 * POST /api/reports/schedules/[id]/execute
 * Manually trigger execution of a scheduled report
 */
async function executeScheduledReport(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = triggerExecutionSchema.parse({
      scheduledReportId: params.id,
      ...body,
    });
    
    const schedulerService = getReportSchedulerService();
    
    // Execute the report manually
    const result = await schedulerService.executeReport(
      validatedData.scheduledReportId,
      true // manualTrigger = true
    );
    
    return NextResponse.json({
      success: true,
      message: 'Report execution triggered successfully',
      data: result,
    });
  } catch (error) {
    console.error('Error executing scheduled report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to execute scheduled report' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  executeScheduledReport
);
