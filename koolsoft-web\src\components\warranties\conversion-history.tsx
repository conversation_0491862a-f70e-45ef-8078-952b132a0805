'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowRightLeft, 
  Calendar, 
  User, 
  FileText, 
  Search,
  Filter,
  RefreshCw
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface ConversionRecord {
  id: string;
  customerId: string;
  cardNo: number;
  source: string;
  createdAt: string;
  customer: {
    id: string;
    name: string;
  };
  amcContract?: {
    id: string;
    amount: number;
    startDate: string;
    endDate: string;
    status: string;
  };
  inWarranty?: {
    id: string;
    bslNo: string;
    installDate: string;
    warrantyDate: string;
  };
  outWarranty?: {
    id: string;
    startDate: string;
    endDate: string;
  };
}

interface ConversionHistoryProps {
  customerId?: string;
  showFilters?: boolean;
  maxItems?: number;
}

export function ConversionHistory({ 
  customerId, 
  showFilters = true, 
  maxItems = 10 
}: ConversionHistoryProps) {
  const [conversions, setConversions] = useState<ConversionRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    conversionType: '',
    dateFrom: '',
    dateTo: '',
    skip: 0,
    take: maxItems,
  });

  const fetchConversions = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (customerId) {
        params.append('customerId', customerId);
      }
      
      if (filters.conversionType) {
        params.append('conversionType', filters.conversionType);
      }
      
      if (filters.dateFrom) {
        params.append('dateFrom', filters.dateFrom);
      }
      
      if (filters.dateTo) {
        params.append('dateTo', filters.dateTo);
      }
      
      params.append('skip', filters.skip.toString());
      params.append('take', filters.take.toString());

      const response = await fetch(`/api/conversions?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversion history');
      }

      const data = await response.json();
      setConversions(data.data || []);
    } catch (error) {
      console.error('Error fetching conversion history:', error);
      toast({
        title: 'Error',
        description: 'Failed to load conversion history',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConversions();
  }, [customerId, filters]);

  const getConversionTypeLabel = (source: string) => {
    switch (source) {
      case 'WARRANTY_TO_AMC':
        return 'Warranty → AMC';
      case 'AMC_TO_OUT_WARRANTY':
        return 'AMC → Out-Warranty';
      case 'WARRANTY_TO_OUT_WARRANTY':
        return 'Warranty → Out-Warranty';
      default:
        return source;
    }
  };

  const getConversionTypeBadge = (source: string) => {
    switch (source) {
      case 'WARRANTY_TO_AMC':
        return <Badge className="bg-blue-100 text-blue-800">Warranty → AMC</Badge>;
      case 'AMC_TO_OUT_WARRANTY':
        return <Badge className="bg-orange-100 text-orange-800">AMC → Out-Warranty</Badge>;
      case 'WARRANTY_TO_OUT_WARRANTY':
        return <Badge className="bg-purple-100 text-purple-800">Warranty → Out-Warranty</Badge>;
      default:
        return <Badge variant="secondary">{source}</Badge>;
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? '' : value, // Convert "all" to empty string for API
      skip: 0, // Reset pagination when filters change
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRightLeft className="w-5 h-5" />
          Conversion History
          <Button
            variant="outline"
            size="sm"
            onClick={fetchConversions}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {showFilters && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="conversionType">Conversion Type</Label>
                <Select
                  value={filters.conversionType}
                  onValueChange={(value) => handleFilterChange('conversionType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    <SelectItem value="WARRANTY_TO_AMC">Warranty → AMC</SelectItem>
                    <SelectItem value="AMC_TO_OUT_WARRANTY">AMC → Out-Warranty</SelectItem>
                    <SelectItem value="WARRANTY_TO_OUT_WARRANTY">Warranty → Out-Warranty</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="dateFrom">From Date</Label>
                <Input
                  id="dateFrom"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="dateTo">To Date</Label>
                <Input
                  id="dateTo"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                />
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-20 bg-gray-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : conversions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <ArrowRightLeft className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No conversion history found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {conversions.map((conversion) => (
              <div
                key={conversion.id}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getConversionTypeBadge(conversion.source)}
                      <span className="text-sm text-gray-500">
                        HC-{conversion.cardNo}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <User className="w-4 h-4 text-gray-400" />
                          <span className="font-medium">{conversion.customer.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span>{format(new Date(conversion.createdAt), 'PPP')}</span>
                        </div>
                      </div>
                      
                      <div>
                        {conversion.source === 'WARRANTY_TO_AMC' && conversion.amcContract && (
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500">AMC Contract</div>
                            <div>Amount: ₹{conversion.amcContract.amount.toLocaleString()}</div>
                            <div>Period: {format(new Date(conversion.amcContract.startDate), 'MMM yyyy')} - {format(new Date(conversion.amcContract.endDate), 'MMM yyyy')}</div>
                          </div>
                        )}
                        
                        {conversion.inWarranty && (
                          <div className="space-y-1">
                            <div className="text-xs text-gray-500">Source Warranty</div>
                            <div>BSL: {conversion.inWarranty.bslNo}</div>
                            <div>Warranty: {format(new Date(conversion.inWarranty.warrantyDate), 'PPP')}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
