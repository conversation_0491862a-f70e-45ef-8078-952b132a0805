/**
 * Formula Repository for KoolSoft Report Formula Engine
 * 
 * This repository handles CRUD operations for formulas, formula fields,
 * and test cases with proper validation and error handling.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
export const createFormulaSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  formula: z.string().min(1),
  category: z.enum(['MATHEMATICAL', 'STATISTICAL', 'BUSINESS', 'CUSTOM']).default('CUSTOM'),
  isTemplate: z.boolean().default(false),
  variables: z.array(z.string()).optional(),
  returnType: z.enum(['NUMBER', 'STRING', 'BOOLEAN', 'DATE']).default('NUMBER'),
});

export const updateFormulaSchema = createFormulaSchema.partial();

export const createFormulaFieldSchema = z.object({
  reportType: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER']),
  fieldName: z.string().min(1).max(50),
  fieldLabel: z.string().min(1).max(100),
  formulaId: z.string().uuid(),
  sortOrder: z.number().int().min(0).default(0),
});

export const createTestCaseSchema = z.object({
  formulaId: z.string().uuid(),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  inputData: z.record(z.any()),
  expectedResult: z.any(),
});

export interface FormulaFilters {
  category?: string;
  isTemplate?: boolean;
  isActive?: boolean;
  createdBy?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FormulaFieldFilters {
  reportType?: string;
  isActive?: boolean;
  formulaId?: string;
}

export class FormulaRepository {
  /**
   * Create a new formula
   */
  async createFormula(data: z.infer<typeof createFormulaSchema>, createdBy: string) {
    const validatedData = createFormulaSchema.parse(data);

    // Calculate complexity score (placeholder - would use actual formula engine)
    const complexity = Math.min(Math.max(validatedData.formula.length / 10, 1), 100);

    return await prisma.report_formulas.create({
      data: {
        ...validatedData,
        variables: validatedData.variables ? JSON.stringify(validatedData.variables) : undefined,
        complexity: Math.round(complexity),
        createdBy,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        reportFields: true,
        testCases: true,
      },
    });
  }

  /**
   * Get formula by ID
   */
  async getFormulaById(id: string) {
    return await prisma.report_formulas.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        reportFields: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        },
        testCases: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }

  /**
   * Get formulas with filtering and pagination
   */
  async getFormulas(filters: FormulaFilters = {}) {
    const {
      category,
      isTemplate,
      isActive = true,
      createdBy,
      search,
      page = 1,
      limit = 50,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = filters;

    const whereClause: Prisma.report_formulasWhereInput = {
      isActive,
      ...(category && { category }),
      ...(isTemplate !== undefined && { isTemplate }),
      ...(createdBy && { createdBy }),
    };

    // Search filter
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { formula: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.report_formulas.findMany({
        where: whereClause,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              reportFields: true,
              testCases: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.report_formulas.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Update formula
   */
  async updateFormula(id: string, data: z.infer<typeof updateFormulaSchema>) {
    const validatedData = updateFormulaSchema.parse(data);

    // Recalculate complexity if formula changed
    let complexity: number | undefined;
    if (validatedData.formula) {
      complexity = Math.min(Math.max(validatedData.formula.length / 10, 1), 100);
    }

    return await prisma.report_formulas.update({
      where: { id },
      data: {
        ...validatedData,
        ...(validatedData.variables && {
          variables: JSON.stringify(validatedData.variables),
        }),
        ...(complexity && { complexity: Math.round(complexity) }),
        updatedAt: new Date(),
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        reportFields: true,
        testCases: true,
      },
    });
  }

  /**
   * Delete formula
   */
  async deleteFormula(id: string) {
    return await prisma.report_formulas.delete({
      where: { id },
    });
  }

  /**
   * Get template formulas
   */
  async getTemplateFormulas(category?: string) {
    const whereClause: Prisma.report_formulasWhereInput = {
      isTemplate: true,
      isActive: true,
      ...(category && { category }),
    };

    return await prisma.report_formulas.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        description: true,
        formula: true,
        category: true,
        variables: true,
        returnType: true,
        complexity: true,
        usageCount: true,
      },
      orderBy: [
        { category: 'asc' },
        { usageCount: 'desc' },
        { name: 'asc' },
      ],
    });
  }

  /**
   * Increment formula usage count
   */
  async incrementUsageCount(id: string) {
    return await prisma.report_formulas.update({
      where: { id },
      data: {
        usageCount: { increment: 1 },
        lastUsedAt: new Date(),
      },
    });
  }

  /**
   * Create formula field
   */
  async createFormulaField(data: z.infer<typeof createFormulaFieldSchema>) {
    const validatedData = createFormulaFieldSchema.parse(data);

    return await prisma.report_formula_fields.create({
      data: validatedData,
      include: {
        formula: {
          select: {
            id: true,
            name: true,
            formula: true,
            returnType: true,
          },
        },
      },
    });
  }

  /**
   * Get formula fields
   */
  async getFormulaFields(filters: FormulaFieldFilters = {}) {
    const { reportType, isActive = true, formulaId } = filters;

    const whereClause: Prisma.report_formula_fieldsWhereInput = {
      isActive,
      ...(reportType && { reportType }),
      ...(formulaId && { formulaId }),
    };

    return await prisma.report_formula_fields.findMany({
      where: whereClause,
      include: {
        formula: {
          select: {
            id: true,
            name: true,
            formula: true,
            returnType: true,
            complexity: true,
          },
        },
      },
      orderBy: [
        { reportType: 'asc' },
        { sortOrder: 'asc' },
        { fieldName: 'asc' },
      ],
    });
  }

  /**
   * Update formula field
   */
  async updateFormulaField(id: string, data: Partial<z.infer<typeof createFormulaFieldSchema>>) {
    return await prisma.report_formula_fields.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
      include: {
        formula: {
          select: {
            id: true,
            name: true,
            formula: true,
            returnType: true,
          },
        },
      },
    });
  }

  /**
   * Delete formula field
   */
  async deleteFormulaField(id: string) {
    return await prisma.report_formula_fields.delete({
      where: { id },
    });
  }

  /**
   * Create test case
   */
  async createTestCase(data: z.infer<typeof createTestCaseSchema>) {
    const validatedData = createTestCaseSchema.parse(data);

    return await prisma.formula_test_cases.create({
      data: {
        ...validatedData,
        inputData: JSON.stringify(validatedData.inputData),
        expectedResult: JSON.stringify(validatedData.expectedResult),
      },
    });
  }

  /**
   * Get test cases for formula
   */
  async getTestCases(formulaId: string) {
    return await prisma.formula_test_cases.findMany({
      where: { formulaId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Update test case result
   */
  async updateTestCaseResult(id: string, actualResult: any, passed: boolean) {
    return await prisma.formula_test_cases.update({
      where: { id },
      data: {
        actualResult: JSON.stringify(actualResult),
        passed,
        lastRunAt: new Date(),
      },
    });
  }

  /**
   * Delete test case
   */
  async deleteTestCase(id: string) {
    return await prisma.formula_test_cases.delete({
      where: { id },
    });
  }

  /**
   * Get formula statistics
   */
  async getFormulaStatistics() {
    const [
      totalFormulas,
      templateFormulas,
      activeFormulas,
      categoryCounts,
      mostUsedFormulas,
    ] = await Promise.all([
      prisma.report_formulas.count(),
      prisma.report_formulas.count({ where: { isTemplate: true } }),
      prisma.report_formulas.count({ where: { isActive: true } }),
      prisma.report_formulas.groupBy({
        by: ['category'],
        _count: { category: true },
        where: { isActive: true },
      }),
      prisma.report_formulas.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          category: true,
          usageCount: true,
        },
        orderBy: { usageCount: 'desc' },
        take: 10,
      }),
    ]);

    return {
      totalFormulas,
      templateFormulas,
      activeFormulas,
      categoryCounts: categoryCounts.reduce((acc, item) => {
        acc[item.category] = item._count.category;
        return acc;
      }, {} as Record<string, number>),
      mostUsedFormulas,
    };
  }
}
