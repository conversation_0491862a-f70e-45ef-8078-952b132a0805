'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Download, RefreshCw, Filter } from 'lucide-react';
import { ReportParametersForm } from '@/components/reports/report-parameters-form';
import { ReportDataTable } from '@/components/reports/report-data-table';
import { ReportExportDialog } from '@/components/reports/report-export-dialog';

interface ReportData {
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  reportType: string;
  filters: Record<string, any>;
}

interface ReportType {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  permissions: string[];
  availableFilters: string[];
  sortOptions: Array<{ value: string; label: string }>;
  exportFormats: string[];
}

/**
 * Dynamic Report Viewer Page
 * 
 * Displays a specific report type with parameter selection, data table,
 * and export functionality. The report type is determined by the URL parameter.
 */
export default function ReportViewerTypePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  
  const reportType = (params?.type as string)?.toUpperCase();
  
  const [reportConfig, setReportConfig] = useState<ReportType | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [isLoadingConfig, setIsLoadingConfig] = useState(true);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<Record<string, any>>({});
  const [showFilters, setShowFilters] = useState(true);

  // Fetch report configuration
  useEffect(() => {
    const fetchReportConfig = async () => {
      try {
        setIsLoadingConfig(true);
        const response = await fetch('/api/reports/types', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch report configuration');
        }

        const data = await response.json();
        const config = (data.data || []).find(
          (report: ReportType) => report.type === reportType
        );

        if (!config) {
          toast({
            title: 'Error',
            description: 'Report type not found.',
            variant: 'destructive',
          });
          router.push('/reports/viewer');
          return;
        }

        setReportConfig(config);
      } catch (error) {
        console.error('Error fetching report configuration:', error);
        toast({
          title: 'Error',
          description: 'Failed to load report configuration.',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingConfig(false);
      }
    };

    if (reportType) {
      fetchReportConfig();
    }
  }, [reportType, router, toast]);

  // Fetch report data
  const fetchReportData = async (filters: Record<string, any> = {}) => {
    if (!reportType) return;

    try {
      setIsLoadingData(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      params.set('type', reportType);
      
      // Add filters to query parameters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.set(key, String(value));
        }
      });

      const response = await fetch(`/api/reports?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch report data');
      }

      const data = await response.json();
      setReportData(data);
      setCurrentFilters(filters);
      
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load report data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingData(false);
    }
  };

  // Handle parameter form submission
  const handleParametersSubmit = (filters: Record<string, any>) => {
    fetchReportData(filters);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchReportData(currentFilters);
  };

  // Handle export
  const handleExport = () => {
    setShowExportDialog(true);
  };

  // Handle back navigation
  const handleBack = () => {
    router.push('/reports/viewer');
  };

  if (isLoadingConfig) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-64" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!reportConfig) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Report not found</h3>
        <p className="text-gray-500 mb-4">The requested report type could not be found.</p>
        <Button onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Reports
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{reportConfig.name}</h1>
            <p className="text-gray-600">{reportConfig.description}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            {showFilters ? 'Hide' : 'Show'} Filters
          </Button>
          
          {reportData && (
            <>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Parameters Form */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Report Parameters</CardTitle>
            <CardDescription>
              Configure the parameters below and click "Generate Report" to view the data.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReportParametersForm
              reportType={reportType}
              availableFilters={reportConfig.availableFilters}
              sortOptions={reportConfig.sortOptions}
              onSubmit={handleParametersSubmit}
              isLoading={isLoadingData}
            />
          </CardContent>
        </Card>
      )}

      {/* Report Data */}
      {reportData ? (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Report Results</CardTitle>
            <CardDescription>
              Showing {reportData.pagination.total} records
              {reportData.pagination.totalPages > 1 && 
                ` (Page ${reportData.pagination.page} of ${reportData.pagination.totalPages})`
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ReportDataTable
              data={reportData.data}
              reportType={reportType}
              pagination={reportData.pagination}
              onPageChange={(page) => {
                fetchReportData({ ...currentFilters, page });
              }}
              onSortChange={(sortBy, sortOrder) => {
                fetchReportData({ ...currentFilters, sortBy, sortOrder });
              }}
            />
          </CardContent>
        </Card>
      ) : !isLoadingData ? (
        <Card>
          <CardContent className="py-12 text-center">
            <div className="text-gray-500">
              <Filter className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">No Report Generated</h3>
              <p>Configure the parameters above and click "Generate Report" to view the data.</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="py-12">
            <div className="flex items-center justify-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Generating report...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Export Dialog */}
      {showExportDialog && reportData && (
        <ReportExportDialog
          reportType={reportType}
          reportName={reportConfig.name}
          data={reportData.data}
          filters={currentFilters}
          exportFormats={reportConfig.exportFormats}
          onClose={() => setShowExportDialog(false)}
        />
      )}
    </div>
  );
}
