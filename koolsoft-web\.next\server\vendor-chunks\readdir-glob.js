"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/readdir-glob";
exports.ids = ["vendor-chunks/readdir-glob"];
exports.modules = {

/***/ "(rsc)/./node_modules/readdir-glob/index.js":
/*!********************************************!*\
  !*** ./node_modules/readdir-glob/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = readdirGlob;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst {\n  EventEmitter\n} = __webpack_require__(/*! events */ \"events\");\nconst {\n  Minimatch\n} = __webpack_require__(/*! minimatch */ \"(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\");\nconst {\n  resolve\n} = __webpack_require__(/*! path */ \"path\");\nfunction readdir(dir, strict) {\n  return new Promise((resolve, reject) => {\n    fs.readdir(dir, {\n      withFileTypes: true\n    }, (err, files) => {\n      if (err) {\n        switch (err.code) {\n          case 'ENOTDIR':\n            // Not a directory\n            if (strict) {\n              reject(err);\n            } else {\n              resolve([]);\n            }\n            break;\n          case 'ENOTSUP': // Operation not supported\n          case 'ENOENT': // No such file or directory\n          case 'ENAMETOOLONG': // Filename too long\n          case 'UNKNOWN':\n            resolve([]);\n            break;\n          case 'ELOOP': // Too many levels of symbolic links\n          default:\n            reject(err);\n            break;\n        }\n      } else {\n        resolve(files);\n      }\n    });\n  });\n}\nfunction stat(file, followSymlinks) {\n  return new Promise((resolve, reject) => {\n    const statFunc = followSymlinks ? fs.stat : fs.lstat;\n    statFunc(file, (err, stats) => {\n      if (err) {\n        switch (err.code) {\n          case 'ENOENT':\n            if (followSymlinks) {\n              // Fallback to lstat to handle broken links as files\n              resolve(stat(file, false));\n            } else {\n              resolve(null);\n            }\n            break;\n          default:\n            resolve(null);\n            break;\n        }\n      } else {\n        resolve(stats);\n      }\n    });\n  });\n}\nasync function* exploreWalkAsync(dir, path, followSymlinks, useStat, shouldSkip, strict) {\n  let files = await readdir(path + dir, strict);\n  for (const file of files) {\n    let name = file.name;\n    if (name === undefined) {\n      // undefined file.name means the `withFileTypes` options is not supported by node\n      // we have to call the stat function to know if file is directory or not.\n      name = file;\n      useStat = true;\n    }\n    const filename = dir + '/' + name;\n    const relative = filename.slice(1); // Remove the leading /\n    const absolute = path + '/' + relative;\n    let stats = null;\n    if (useStat || followSymlinks) {\n      stats = await stat(absolute, followSymlinks);\n    }\n    if (!stats && file.name !== undefined) {\n      stats = file;\n    }\n    if (stats === null) {\n      stats = {\n        isDirectory: () => false\n      };\n    }\n    if (stats.isDirectory()) {\n      if (!shouldSkip(relative)) {\n        yield {\n          relative,\n          absolute,\n          stats\n        };\n        yield* exploreWalkAsync(filename, path, followSymlinks, useStat, shouldSkip, false);\n      }\n    } else {\n      yield {\n        relative,\n        absolute,\n        stats\n      };\n    }\n  }\n}\nasync function* explore(path, followSymlinks, useStat, shouldSkip) {\n  yield* exploreWalkAsync('', path, followSymlinks, useStat, shouldSkip, true);\n}\nfunction readOptions(options) {\n  return {\n    pattern: options.pattern,\n    dot: !!options.dot,\n    noglobstar: !!options.noglobstar,\n    matchBase: !!options.matchBase,\n    nocase: !!options.nocase,\n    ignore: options.ignore,\n    skip: options.skip,\n    follow: !!options.follow,\n    stat: !!options.stat,\n    nodir: !!options.nodir,\n    mark: !!options.mark,\n    silent: !!options.silent,\n    absolute: !!options.absolute\n  };\n}\nclass ReaddirGlob extends EventEmitter {\n  constructor(cwd, options, cb) {\n    super();\n    if (typeof options === 'function') {\n      cb = options;\n      options = null;\n    }\n    this.options = readOptions(options || {});\n    this.matchers = [];\n    if (this.options.pattern) {\n      const matchers = Array.isArray(this.options.pattern) ? this.options.pattern : [this.options.pattern];\n      this.matchers = matchers.map(m => new Minimatch(m, {\n        dot: this.options.dot,\n        noglobstar: this.options.noglobstar,\n        matchBase: this.options.matchBase,\n        nocase: this.options.nocase\n      }));\n    }\n    this.ignoreMatchers = [];\n    if (this.options.ignore) {\n      const ignorePatterns = Array.isArray(this.options.ignore) ? this.options.ignore : [this.options.ignore];\n      this.ignoreMatchers = ignorePatterns.map(ignore => new Minimatch(ignore, {\n        dot: true\n      }));\n    }\n    this.skipMatchers = [];\n    if (this.options.skip) {\n      const skipPatterns = Array.isArray(this.options.skip) ? this.options.skip : [this.options.skip];\n      this.skipMatchers = skipPatterns.map(skip => new Minimatch(skip, {\n        dot: true\n      }));\n    }\n    this.iterator = explore(resolve(cwd || '.'), this.options.follow, this.options.stat, this._shouldSkipDirectory.bind(this));\n    this.paused = false;\n    this.inactive = false;\n    this.aborted = false;\n    if (cb) {\n      this._matches = [];\n      this.on('match', match => this._matches.push(this.options.absolute ? match.absolute : match.relative));\n      this.on('error', err => cb(err));\n      this.on('end', () => cb(null, this._matches));\n    }\n    setTimeout(() => this._next(), 0);\n  }\n  _shouldSkipDirectory(relative) {\n    //console.log(relative, this.skipMatchers.some(m => m.match(relative)));\n    return this.skipMatchers.some(m => m.match(relative));\n  }\n  _fileMatches(relative, isDirectory) {\n    const file = relative + (isDirectory ? '/' : '');\n    return (this.matchers.length === 0 || this.matchers.some(m => m.match(file))) && !this.ignoreMatchers.some(m => m.match(file)) && (!this.options.nodir || !isDirectory);\n  }\n  _next() {\n    if (!this.paused && !this.aborted) {\n      this.iterator.next().then(obj => {\n        if (!obj.done) {\n          const isDirectory = obj.value.stats.isDirectory();\n          if (this._fileMatches(obj.value.relative, isDirectory)) {\n            let relative = obj.value.relative;\n            let absolute = obj.value.absolute;\n            if (this.options.mark && isDirectory) {\n              relative += '/';\n              absolute += '/';\n            }\n            if (this.options.stat) {\n              this.emit('match', {\n                relative,\n                absolute,\n                stat: obj.value.stats\n              });\n            } else {\n              this.emit('match', {\n                relative,\n                absolute\n              });\n            }\n          }\n          this._next(this.iterator);\n        } else {\n          this.emit('end');\n        }\n      }).catch(err => {\n        this.abort();\n        this.emit('error', err);\n        if (!err.code && !this.options.silent) {\n          console.error(err);\n        }\n      });\n    } else {\n      this.inactive = true;\n    }\n  }\n  abort() {\n    this.aborted = true;\n  }\n  pause() {\n    this.paused = true;\n  }\n  resume() {\n    this.paused = false;\n    if (this.inactive) {\n      this.inactive = false;\n      this._next();\n    }\n  }\n}\nfunction readdirGlob(pattern, options, cb) {\n  return new ReaddirGlob(pattern, options, cb);\n}\nreaddirGlob.ReaddirGlob = ReaddirGlob;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/brace-expansion/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar balanced = __webpack_require__(/*! balanced-match */ \"(rsc)/./node_modules/balanced-match/index.js\");\nmodule.exports = expandTop;\nvar escSlash = '\\0SLASH' + Math.random() + '\\0';\nvar escOpen = '\\0OPEN' + Math.random() + '\\0';\nvar escClose = '\\0CLOSE' + Math.random() + '\\0';\nvar escComma = '\\0COMMA' + Math.random() + '\\0';\nvar escPeriod = '\\0PERIOD' + Math.random() + '\\0';\nfunction numeric(str) {\n  return parseInt(str, 10) == str ? parseInt(str, 10) : str.charCodeAt(0);\n}\nfunction escapeBraces(str) {\n  return str.split('\\\\\\\\').join(escSlash).split('\\\\{').join(escOpen).split('\\\\}').join(escClose).split('\\\\,').join(escComma).split('\\\\.').join(escPeriod);\n}\nfunction unescapeBraces(str) {\n  return str.split(escSlash).join('\\\\').split(escOpen).join('{').split(escClose).join('}').split(escComma).join(',').split(escPeriod).join('.');\n}\n\n// Basically just str.split(\",\"), but handling cases\n// where we have nested braced sections, which should be\n// treated as individual members, like {a,{b,c},d}\nfunction parseCommaParts(str) {\n  if (!str) return [''];\n  var parts = [];\n  var m = balanced('{', '}', str);\n  if (!m) return str.split(',');\n  var pre = m.pre;\n  var body = m.body;\n  var post = m.post;\n  var p = pre.split(',');\n  p[p.length - 1] += '{' + body + '}';\n  var postParts = parseCommaParts(post);\n  if (post.length) {\n    p[p.length - 1] += postParts.shift();\n    p.push.apply(p, postParts);\n  }\n  parts.push.apply(parts, p);\n  return parts;\n}\nfunction expandTop(str) {\n  if (!str) return [];\n\n  // I don't know why Bash 4.3 does this, but it does.\n  // Anything starting with {} will have the first two bytes preserved\n  // but *only* at the top level, so {},a}b will not expand to anything,\n  // but a{},b}c will be expanded to [a}c,abc].\n  // One could argue that this is a bug in Bash, but since the goal of\n  // this module is to match Bash's rules, we escape a leading {}\n  if (str.substr(0, 2) === '{}') {\n    str = '\\\\{\\\\}' + str.substr(2);\n  }\n  return expand(escapeBraces(str), true).map(unescapeBraces);\n}\nfunction embrace(str) {\n  return '{' + str + '}';\n}\nfunction isPadded(el) {\n  return /^-?0\\d/.test(el);\n}\nfunction lte(i, y) {\n  return i <= y;\n}\nfunction gte(i, y) {\n  return i >= y;\n}\nfunction expand(str, isTop) {\n  var expansions = [];\n  var m = balanced('{', '}', str);\n  if (!m) return [str];\n\n  // no need to expand pre, since it is guaranteed to be free of brace-sets\n  var pre = m.pre;\n  var post = m.post.length ? expand(m.post, false) : [''];\n  if (/\\$$/.test(m.pre)) {\n    for (var k = 0; k < post.length; k++) {\n      var expansion = pre + '{' + m.body + '}' + post[k];\n      expansions.push(expansion);\n    }\n  } else {\n    var isNumericSequence = /^-?\\d+\\.\\.-?\\d+(?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isAlphaSequence = /^[a-zA-Z]\\.\\.[a-zA-Z](?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isSequence = isNumericSequence || isAlphaSequence;\n    var isOptions = m.body.indexOf(',') >= 0;\n    if (!isSequence && !isOptions) {\n      // {a},b}\n      if (m.post.match(/,(?!,).*\\}/)) {\n        str = m.pre + '{' + m.body + escClose + m.post;\n        return expand(str);\n      }\n      return [str];\n    }\n    var n;\n    if (isSequence) {\n      n = m.body.split(/\\.\\./);\n    } else {\n      n = parseCommaParts(m.body);\n      if (n.length === 1) {\n        // x{{a,b}}y ==> x{a}y x{b}y\n        n = expand(n[0], false).map(embrace);\n        if (n.length === 1) {\n          return post.map(function (p) {\n            return m.pre + n[0] + p;\n          });\n        }\n      }\n    }\n\n    // at this point, n is the parts, and we know it's not a comma set\n    // with a single entry.\n    var N;\n    if (isSequence) {\n      var x = numeric(n[0]);\n      var y = numeric(n[1]);\n      var width = Math.max(n[0].length, n[1].length);\n      var incr = n.length == 3 ? Math.abs(numeric(n[2])) : 1;\n      var test = lte;\n      var reverse = y < x;\n      if (reverse) {\n        incr *= -1;\n        test = gte;\n      }\n      var pad = n.some(isPadded);\n      N = [];\n      for (var i = x; test(i, y); i += incr) {\n        var c;\n        if (isAlphaSequence) {\n          c = String.fromCharCode(i);\n          if (c === '\\\\') c = '';\n        } else {\n          c = String(i);\n          if (pad) {\n            var need = width - c.length;\n            if (need > 0) {\n              var z = new Array(need + 1).join('0');\n              if (i < 0) c = '-' + z + c.slice(1);else c = z + c;\n            }\n          }\n        }\n        N.push(c);\n      }\n    } else {\n      N = [];\n      for (var j = 0; j < n.length; j++) {\n        N.push.apply(N, expand(n[j], false));\n      }\n    }\n    for (var j = 0; j < N.length; j++) {\n      for (var k = 0; k < post.length; k++) {\n        var expansion = pre + N[j] + post[k];\n        if (!isTop || isSequence || expansion) expansions.push(expansion);\n      }\n    }\n  }\n  return expansions;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js":
/*!**********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/lib/path.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("\n\nconst isWindows = typeof process === 'object' && process && process.platform === 'win32';\nmodule.exports = isWindows ? {\n  sep: '\\\\'\n} : {\n  sep: '/'\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhZGRpci1nbG9iL25vZGVfbW9kdWxlcy9taW5pbWF0Y2gvbGliL3BhdGguanMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxNQUFNQSxTQUFTLEdBQUcsT0FBT0MsT0FBTyxLQUFLLFFBQVEsSUFDM0NBLE9BQU8sSUFDUEEsT0FBTyxDQUFDQyxRQUFRLEtBQUssT0FBTztBQUM5QkMsTUFBTSxDQUFDQyxPQUFPLEdBQUdKLFNBQVMsR0FBRztFQUFFSyxHQUFHLEVBQUU7QUFBSyxDQUFDLEdBQUc7RUFBRUEsR0FBRyxFQUFFO0FBQUksQ0FBQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXHJlYWRkaXItZ2xvYlxcbm9kZV9tb2R1bGVzXFxtaW5pbWF0Y2hcXGxpYlxccGF0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1dpbmRvd3MgPSB0eXBlb2YgcHJvY2VzcyA9PT0gJ29iamVjdCcgJiZcbiAgcHJvY2VzcyAmJlxuICBwcm9jZXNzLnBsYXRmb3JtID09PSAnd2luMzInXG5tb2R1bGUuZXhwb3J0cyA9IGlzV2luZG93cyA/IHsgc2VwOiAnXFxcXCcgfSA6IHsgc2VwOiAnLycgfVxuIl0sIm5hbWVzIjpbImlzV2luZG93cyIsInByb2Nlc3MiLCJwbGF0Zm9ybSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzZXAiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js":
/*!***********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/minimatch.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst minimatch = module.exports = (p, pattern, options = {}) => {\n  assertValidPattern(pattern);\n\n  // shortcut: comments match nothing.\n  if (!options.nocomment && pattern.charAt(0) === '#') {\n    return false;\n  }\n  return new Minimatch(pattern, options).match(p);\n};\nmodule.exports = minimatch;\nconst path = __webpack_require__(/*! ./lib/path.js */ \"(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\");\nminimatch.sep = path.sep;\nconst GLOBSTAR = Symbol('globstar **');\nminimatch.GLOBSTAR = GLOBSTAR;\nconst expand = __webpack_require__(/*! brace-expansion */ \"(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\");\nconst plTypes = {\n  '!': {\n    open: '(?:(?!(?:',\n    close: '))[^/]*?)'\n  },\n  '?': {\n    open: '(?:',\n    close: ')?'\n  },\n  '+': {\n    open: '(?:',\n    close: ')+'\n  },\n  '*': {\n    open: '(?:',\n    close: ')*'\n  },\n  '@': {\n    open: '(?:',\n    close: ')'\n  }\n};\n\n// any single thing other than /\n// don't need to escape / when using new RegExp()\nconst qmark = '[^/]';\n\n// * => any number of characters\nconst star = qmark + '*?';\n\n// ** when dots are allowed.  Anything goes, except .. and .\n// not (^ or / followed by one or two dots followed by $ or /),\n// followed by anything, any number of times.\nconst twoStarDot = '(?:(?!(?:\\\\\\/|^)(?:\\\\.{1,2})($|\\\\\\/)).)*?';\n\n// not a ^ or / followed by a dot,\n// followed by anything, any number of times.\nconst twoStarNoDot = '(?:(?!(?:\\\\\\/|^)\\\\.).)*?';\n\n// \"abc\" -> { a:true, b:true, c:true }\nconst charSet = s => s.split('').reduce((set, c) => {\n  set[c] = true;\n  return set;\n}, {});\n\n// characters that need to be escaped in RegExp.\nconst reSpecials = charSet('().*{}+?[]^$\\\\!');\n\n// characters that indicate we have to add the pattern start\nconst addPatternStartSet = charSet('[.(');\n\n// normalizes slashes.\nconst slashSplit = /\\/+/;\nminimatch.filter = (pattern, options = {}) => (p, i, list) => minimatch(p, pattern, options);\nconst ext = (a, b = {}) => {\n  const t = {};\n  Object.keys(a).forEach(k => t[k] = a[k]);\n  Object.keys(b).forEach(k => t[k] = b[k]);\n  return t;\n};\nminimatch.defaults = def => {\n  if (!def || typeof def !== 'object' || !Object.keys(def).length) {\n    return minimatch;\n  }\n  const orig = minimatch;\n  const m = (p, pattern, options) => orig(p, pattern, ext(def, options));\n  m.Minimatch = class Minimatch extends orig.Minimatch {\n    constructor(pattern, options) {\n      super(pattern, ext(def, options));\n    }\n  };\n  m.Minimatch.defaults = options => orig.defaults(ext(def, options)).Minimatch;\n  m.filter = (pattern, options) => orig.filter(pattern, ext(def, options));\n  m.defaults = options => orig.defaults(ext(def, options));\n  m.makeRe = (pattern, options) => orig.makeRe(pattern, ext(def, options));\n  m.braceExpand = (pattern, options) => orig.braceExpand(pattern, ext(def, options));\n  m.match = (list, pattern, options) => orig.match(list, pattern, ext(def, options));\n  return m;\n};\n\n// Brace expansion:\n// a{b,c}d -> abd acd\n// a{b,}c -> abc ac\n// a{0..3}d -> a0d a1d a2d a3d\n// a{b,c{d,e}f}g -> abg acdfg acefg\n// a{b,c}d{e,f}g -> abdeg acdeg abdeg abdfg\n//\n// Invalid sets are not expanded.\n// a{2..}b -> a{2..}b\n// a{b}c -> a{b}c\nminimatch.braceExpand = (pattern, options) => braceExpand(pattern, options);\nconst braceExpand = (pattern, options = {}) => {\n  assertValidPattern(pattern);\n\n  // Thanks to Yeting Li <https://github.com/yetingli> for\n  // improving this regexp to avoid a ReDOS vulnerability.\n  if (options.nobrace || !/\\{(?:(?!\\{).)*\\}/.test(pattern)) {\n    // shortcut. no need to expand.\n    return [pattern];\n  }\n  return expand(pattern);\n};\nconst MAX_PATTERN_LENGTH = 1024 * 64;\nconst assertValidPattern = pattern => {\n  if (typeof pattern !== 'string') {\n    throw new TypeError('invalid pattern');\n  }\n  if (pattern.length > MAX_PATTERN_LENGTH) {\n    throw new TypeError('pattern is too long');\n  }\n};\n\n// parse a component of the expanded set.\n// At this point, no pattern may contain \"/\" in it\n// so we're going to return a 2d array, where each entry is the full\n// pattern, split on '/', and then turned into a regular expression.\n// A regexp is made at the end which joins each array with an\n// escaped /, and another full one which joins each regexp with |.\n//\n// Following the lead of Bash 4.1, note that \"**\" only has special meaning\n// when it is the *only* thing in a path portion.  Otherwise, any series\n// of * is equivalent to a single *.  Globstar behavior is enabled by\n// default, and can be disabled by setting options.noglobstar.\nconst SUBPARSE = Symbol('subparse');\nminimatch.makeRe = (pattern, options) => new Minimatch(pattern, options || {}).makeRe();\nminimatch.match = (list, pattern, options = {}) => {\n  const mm = new Minimatch(pattern, options);\n  list = list.filter(f => mm.match(f));\n  if (mm.options.nonull && !list.length) {\n    list.push(pattern);\n  }\n  return list;\n};\n\n// replace stuff like \\* with *\nconst globUnescape = s => s.replace(/\\\\(.)/g, '$1');\nconst charUnescape = s => s.replace(/\\\\([^-\\]])/g, '$1');\nconst regExpEscape = s => s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\nconst braExpEscape = s => s.replace(/[[\\]\\\\]/g, '\\\\$&');\nclass Minimatch {\n  constructor(pattern, options) {\n    assertValidPattern(pattern);\n    if (!options) options = {};\n    this.options = options;\n    this.set = [];\n    this.pattern = pattern;\n    this.windowsPathsNoEscape = !!options.windowsPathsNoEscape || options.allowWindowsEscape === false;\n    if (this.windowsPathsNoEscape) {\n      this.pattern = this.pattern.replace(/\\\\/g, '/');\n    }\n    this.regexp = null;\n    this.negate = false;\n    this.comment = false;\n    this.empty = false;\n    this.partial = !!options.partial;\n\n    // make the set of regexps etc.\n    this.make();\n  }\n  debug() {}\n  make() {\n    const pattern = this.pattern;\n    const options = this.options;\n\n    // empty patterns and comments match nothing.\n    if (!options.nocomment && pattern.charAt(0) === '#') {\n      this.comment = true;\n      return;\n    }\n    if (!pattern) {\n      this.empty = true;\n      return;\n    }\n\n    // step 1: figure out negation, etc.\n    this.parseNegate();\n\n    // step 2: expand braces\n    let set = this.globSet = this.braceExpand();\n    if (options.debug) this.debug = (...args) => console.error(...args);\n    this.debug(this.pattern, set);\n\n    // step 3: now we have a set, so turn each one into a series of path-portion\n    // matching patterns.\n    // These will be regexps, except in the case of \"**\", which is\n    // set to the GLOBSTAR object for globstar behavior,\n    // and will not contain any / characters\n    set = this.globParts = set.map(s => s.split(slashSplit));\n    this.debug(this.pattern, set);\n\n    // glob --> regexps\n    set = set.map((s, si, set) => s.map(this.parse, this));\n    this.debug(this.pattern, set);\n\n    // filter out everything that didn't compile properly.\n    set = set.filter(s => s.indexOf(false) === -1);\n    this.debug(this.pattern, set);\n    this.set = set;\n  }\n  parseNegate() {\n    if (this.options.nonegate) return;\n    const pattern = this.pattern;\n    let negate = false;\n    let negateOffset = 0;\n    for (let i = 0; i < pattern.length && pattern.charAt(i) === '!'; i++) {\n      negate = !negate;\n      negateOffset++;\n    }\n    if (negateOffset) this.pattern = pattern.slice(negateOffset);\n    this.negate = negate;\n  }\n\n  // set partial to true to test if, for example,\n  // \"/a/b\" matches the start of \"/*/b/*/d\"\n  // Partial means, if you run out of file before you run\n  // out of pattern, then that's fine, as long as all\n  // the parts match.\n  matchOne(file, pattern, partial) {\n    var options = this.options;\n    this.debug('matchOne', {\n      'this': this,\n      file: file,\n      pattern: pattern\n    });\n    this.debug('matchOne', file.length, pattern.length);\n    for (var fi = 0, pi = 0, fl = file.length, pl = pattern.length; fi < fl && pi < pl; fi++, pi++) {\n      this.debug('matchOne loop');\n      var p = pattern[pi];\n      var f = file[fi];\n      this.debug(pattern, p, f);\n\n      // should be impossible.\n      // some invalid regexp stuff in the set.\n      /* istanbul ignore if */\n      if (p === false) return false;\n      if (p === GLOBSTAR) {\n        this.debug('GLOBSTAR', [pattern, p, f]);\n\n        // \"**\"\n        // a/**/b/**/c would match the following:\n        // a/b/x/y/z/c\n        // a/x/y/z/b/c\n        // a/b/x/b/x/c\n        // a/b/c\n        // To do this, take the rest of the pattern after\n        // the **, and see if it would match the file remainder.\n        // If so, return success.\n        // If not, the ** \"swallows\" a segment, and try again.\n        // This is recursively awful.\n        //\n        // a/**/b/**/c matching a/b/x/y/z/c\n        // - a matches a\n        // - doublestar\n        //   - matchOne(b/x/y/z/c, b/**/c)\n        //     - b matches b\n        //     - doublestar\n        //       - matchOne(x/y/z/c, c) -> no\n        //       - matchOne(y/z/c, c) -> no\n        //       - matchOne(z/c, c) -> no\n        //       - matchOne(c, c) yes, hit\n        var fr = fi;\n        var pr = pi + 1;\n        if (pr === pl) {\n          this.debug('** at the end');\n          // a ** at the end will just swallow the rest.\n          // We have found a match.\n          // however, it will not swallow /.x, unless\n          // options.dot is set.\n          // . and .. are *never* matched by **, for explosively\n          // exponential reasons.\n          for (; fi < fl; fi++) {\n            if (file[fi] === '.' || file[fi] === '..' || !options.dot && file[fi].charAt(0) === '.') return false;\n          }\n          return true;\n        }\n\n        // ok, let's see if we can swallow whatever we can.\n        while (fr < fl) {\n          var swallowee = file[fr];\n          this.debug('\\nglobstar while', file, fr, pattern, pr, swallowee);\n\n          // XXX remove this slice.  Just pass the start index.\n          if (this.matchOne(file.slice(fr), pattern.slice(pr), partial)) {\n            this.debug('globstar found match!', fr, fl, swallowee);\n            // found a match.\n            return true;\n          } else {\n            // can't swallow \".\" or \"..\" ever.\n            // can only swallow \".foo\" when explicitly asked.\n            if (swallowee === '.' || swallowee === '..' || !options.dot && swallowee.charAt(0) === '.') {\n              this.debug('dot detected!', file, fr, pattern, pr);\n              break;\n            }\n\n            // ** swallows a segment, and continue.\n            this.debug('globstar swallow a segment, and continue');\n            fr++;\n          }\n        }\n\n        // no match was found.\n        // However, in partial mode, we can't say this is necessarily over.\n        // If there's more *pattern* left, then\n        /* istanbul ignore if */\n        if (partial) {\n          // ran out of file\n          this.debug('\\n>>> no match, partial?', file, fr, pattern, pr);\n          if (fr === fl) return true;\n        }\n        return false;\n      }\n\n      // something other than **\n      // non-magic patterns just have to match exactly\n      // patterns with magic have been turned into regexps.\n      var hit;\n      if (typeof p === 'string') {\n        hit = f === p;\n        this.debug('string match', p, f, hit);\n      } else {\n        hit = f.match(p);\n        this.debug('pattern match', p, f, hit);\n      }\n      if (!hit) return false;\n    }\n\n    // Note: ending in / means that we'll get a final \"\"\n    // at the end of the pattern.  This can only match a\n    // corresponding \"\" at the end of the file.\n    // If the file ends in /, then it can only match a\n    // a pattern that ends in /, unless the pattern just\n    // doesn't have any more for it. But, a/b/ should *not*\n    // match \"a/b/*\", even though \"\" matches against the\n    // [^/]*? pattern, except in partial mode, where it might\n    // simply not be reached yet.\n    // However, a/b/ should still satisfy a/*\n\n    // now either we fell off the end of the pattern, or we're done.\n    if (fi === fl && pi === pl) {\n      // ran out of pattern and filename at the same time.\n      // an exact hit!\n      return true;\n    } else if (fi === fl) {\n      // ran out of file, but still had pattern left.\n      // this is ok if we're doing the match as part of\n      // a glob fs traversal.\n      return partial;\n    } else /* istanbul ignore else */if (pi === pl) {\n        // ran out of pattern, still have file left.\n        // this is only acceptable if we're on the very last\n        // empty segment of a file with a trailing slash.\n        // a/* should match a/b/\n        return fi === fl - 1 && file[fi] === '';\n      }\n\n    // should be unreachable.\n    /* istanbul ignore next */\n    throw new Error('wtf?');\n  }\n  braceExpand() {\n    return braceExpand(this.pattern, this.options);\n  }\n  parse(pattern, isSub) {\n    assertValidPattern(pattern);\n    const options = this.options;\n\n    // shortcuts\n    if (pattern === '**') {\n      if (!options.noglobstar) return GLOBSTAR;else pattern = '*';\n    }\n    if (pattern === '') return '';\n    let re = '';\n    let hasMagic = false;\n    let escaping = false;\n    // ? => one single character\n    const patternListStack = [];\n    const negativeLists = [];\n    let stateChar;\n    let inClass = false;\n    let reClassStart = -1;\n    let classStart = -1;\n    let cs;\n    let pl;\n    let sp;\n    // . and .. never match anything that doesn't start with .,\n    // even when options.dot is set.  However, if the pattern\n    // starts with ., then traversal patterns can match.\n    let dotTravAllowed = pattern.charAt(0) === '.';\n    let dotFileAllowed = options.dot || dotTravAllowed;\n    const patternStart = () => dotTravAllowed ? '' : dotFileAllowed ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))' : '(?!\\\\.)';\n    const subPatternStart = p => p.charAt(0) === '.' ? '' : options.dot ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))' : '(?!\\\\.)';\n    const clearStateChar = () => {\n      if (stateChar) {\n        // we had some state-tracking character\n        // that wasn't consumed by this pass.\n        switch (stateChar) {\n          case '*':\n            re += star;\n            hasMagic = true;\n            break;\n          case '?':\n            re += qmark;\n            hasMagic = true;\n            break;\n          default:\n            re += '\\\\' + stateChar;\n            break;\n        }\n        this.debug('clearStateChar %j %j', stateChar, re);\n        stateChar = false;\n      }\n    };\n    for (let i = 0, c; i < pattern.length && (c = pattern.charAt(i)); i++) {\n      this.debug('%s\\t%s %s %j', pattern, i, re, c);\n\n      // skip over any that are escaped.\n      if (escaping) {\n        /* istanbul ignore next - completely not allowed, even escaped. */\n        if (c === '/') {\n          return false;\n        }\n        if (reSpecials[c]) {\n          re += '\\\\';\n        }\n        re += c;\n        escaping = false;\n        continue;\n      }\n      switch (c) {\n        /* istanbul ignore next */\n        case '/':\n          {\n            // Should already be path-split by now.\n            return false;\n          }\n        case '\\\\':\n          if (inClass && pattern.charAt(i + 1) === '-') {\n            re += c;\n            continue;\n          }\n          clearStateChar();\n          escaping = true;\n          continue;\n\n        // the various stateChar values\n        // for the \"extglob\" stuff.\n        case '?':\n        case '*':\n        case '+':\n        case '@':\n        case '!':\n          this.debug('%s\\t%s %s %j <-- stateChar', pattern, i, re, c);\n\n          // all of those are literals inside a class, except that\n          // the glob [!a] means [^a] in regexp\n          if (inClass) {\n            this.debug('  in class');\n            if (c === '!' && i === classStart + 1) c = '^';\n            re += c;\n            continue;\n          }\n\n          // if we already have a stateChar, then it means\n          // that there was something like ** or +? in there.\n          // Handle the stateChar, then proceed with this one.\n          this.debug('call clearStateChar %j', stateChar);\n          clearStateChar();\n          stateChar = c;\n          // if extglob is disabled, then +(asdf|foo) isn't a thing.\n          // just clear the statechar *now*, rather than even diving into\n          // the patternList stuff.\n          if (options.noext) clearStateChar();\n          continue;\n        case '(':\n          {\n            if (inClass) {\n              re += '(';\n              continue;\n            }\n            if (!stateChar) {\n              re += '\\\\(';\n              continue;\n            }\n            const plEntry = {\n              type: stateChar,\n              start: i - 1,\n              reStart: re.length,\n              open: plTypes[stateChar].open,\n              close: plTypes[stateChar].close\n            };\n            this.debug(this.pattern, '\\t', plEntry);\n            patternListStack.push(plEntry);\n            // negation is (?:(?!(?:js)(?:<rest>))[^/]*)\n            re += plEntry.open;\n            // next entry starts with a dot maybe?\n            if (plEntry.start === 0 && plEntry.type !== '!') {\n              dotTravAllowed = true;\n              re += subPatternStart(pattern.slice(i + 1));\n            }\n            this.debug('plType %j %j', stateChar, re);\n            stateChar = false;\n            continue;\n          }\n        case ')':\n          {\n            const plEntry = patternListStack[patternListStack.length - 1];\n            if (inClass || !plEntry) {\n              re += '\\\\)';\n              continue;\n            }\n            patternListStack.pop();\n\n            // closing an extglob\n            clearStateChar();\n            hasMagic = true;\n            pl = plEntry;\n            // negation is (?:(?!js)[^/]*)\n            // The others are (?:<pattern>)<type>\n            re += pl.close;\n            if (pl.type === '!') {\n              negativeLists.push(Object.assign(pl, {\n                reEnd: re.length\n              }));\n            }\n            continue;\n          }\n        case '|':\n          {\n            const plEntry = patternListStack[patternListStack.length - 1];\n            if (inClass || !plEntry) {\n              re += '\\\\|';\n              continue;\n            }\n            clearStateChar();\n            re += '|';\n            // next subpattern can start with a dot?\n            if (plEntry.start === 0 && plEntry.type !== '!') {\n              dotTravAllowed = true;\n              re += subPatternStart(pattern.slice(i + 1));\n            }\n            continue;\n          }\n\n        // these are mostly the same in regexp and glob\n        case '[':\n          // swallow any state-tracking char before the [\n          clearStateChar();\n          if (inClass) {\n            re += '\\\\' + c;\n            continue;\n          }\n          inClass = true;\n          classStart = i;\n          reClassStart = re.length;\n          re += c;\n          continue;\n        case ']':\n          //  a right bracket shall lose its special\n          //  meaning and represent itself in\n          //  a bracket expression if it occurs\n          //  first in the list.  -- POSIX.2 2.8.3.2\n          if (i === classStart + 1 || !inClass) {\n            re += '\\\\' + c;\n            continue;\n          }\n\n          // split where the last [ was, make sure we don't have\n          // an invalid re. if so, re-walk the contents of the\n          // would-be class to re-translate any characters that\n          // were passed through as-is\n          // TODO: It would probably be faster to determine this\n          // without a try/catch and a new RegExp, but it's tricky\n          // to do safely.  For now, this is safe and works.\n          cs = pattern.substring(classStart + 1, i);\n          try {\n            RegExp('[' + braExpEscape(charUnescape(cs)) + ']');\n            // looks good, finish up the class.\n            re += c;\n          } catch (er) {\n            // out of order ranges in JS are errors, but in glob syntax,\n            // they're just a range that matches nothing.\n            re = re.substring(0, reClassStart) + '(?:$.)'; // match nothing ever\n          }\n\n          hasMagic = true;\n          inClass = false;\n          continue;\n        default:\n          // swallow any state char that wasn't consumed\n          clearStateChar();\n          if (reSpecials[c] && !(c === '^' && inClass)) {\n            re += '\\\\';\n          }\n          re += c;\n          break;\n      } // switch\n    } // for\n\n    // handle the case where we left a class open.\n    // \"[abc\" is valid, equivalent to \"\\[abc\"\n    if (inClass) {\n      // split where the last [ was, and escape it\n      // this is a huge pita.  We now have to re-walk\n      // the contents of the would-be class to re-translate\n      // any characters that were passed through as-is\n      cs = pattern.slice(classStart + 1);\n      sp = this.parse(cs, SUBPARSE);\n      re = re.substring(0, reClassStart) + '\\\\[' + sp[0];\n      hasMagic = hasMagic || sp[1];\n    }\n\n    // handle the case where we had a +( thing at the *end*\n    // of the pattern.\n    // each pattern list stack adds 3 chars, and we need to go through\n    // and escape any | chars that were passed through as-is for the regexp.\n    // Go through and escape them, taking care not to double-escape any\n    // | chars that were already escaped.\n    for (pl = patternListStack.pop(); pl; pl = patternListStack.pop()) {\n      let tail;\n      tail = re.slice(pl.reStart + pl.open.length);\n      this.debug('setting tail', re, pl);\n      // maybe some even number of \\, then maybe 1 \\, followed by a |\n      tail = tail.replace(/((?:\\\\{2}){0,64})(\\\\?)\\|/g, (_, $1, $2) => {\n        /* istanbul ignore else - should already be done */\n        if (!$2) {\n          // the | isn't already escaped, so escape it.\n          $2 = '\\\\';\n        }\n\n        // need to escape all those slashes *again*, without escaping the\n        // one that we need for escaping the | character.  As it works out,\n        // escaping an even number of slashes can be done by simply repeating\n        // it exactly after itself.  That's why this trick works.\n        //\n        // I am sorry that you have to see this.\n        return $1 + $1 + $2 + '|';\n      });\n      this.debug('tail=%j\\n   %s', tail, tail, pl, re);\n      const t = pl.type === '*' ? star : pl.type === '?' ? qmark : '\\\\' + pl.type;\n      hasMagic = true;\n      re = re.slice(0, pl.reStart) + t + '\\\\(' + tail;\n    }\n\n    // handle trailing things that only matter at the very end.\n    clearStateChar();\n    if (escaping) {\n      // trailing \\\\\n      re += '\\\\\\\\';\n    }\n\n    // only need to apply the nodot start if the re starts with\n    // something that could conceivably capture a dot\n    const addPatternStart = addPatternStartSet[re.charAt(0)];\n\n    // Hack to work around lack of negative lookbehind in JS\n    // A pattern like: *.!(x).!(y|z) needs to ensure that a name\n    // like 'a.xyz.yz' doesn't match.  So, the first negative\n    // lookahead, has to look ALL the way ahead, to the end of\n    // the pattern.\n    for (let n = negativeLists.length - 1; n > -1; n--) {\n      const nl = negativeLists[n];\n      const nlBefore = re.slice(0, nl.reStart);\n      const nlFirst = re.slice(nl.reStart, nl.reEnd - 8);\n      let nlAfter = re.slice(nl.reEnd);\n      const nlLast = re.slice(nl.reEnd - 8, nl.reEnd) + nlAfter;\n\n      // Handle nested stuff like *(*.js|!(*.json)), where open parens\n      // mean that we should *not* include the ) in the bit that is considered\n      // \"after\" the negated section.\n      const closeParensBefore = nlBefore.split(')').length;\n      const openParensBefore = nlBefore.split('(').length - closeParensBefore;\n      let cleanAfter = nlAfter;\n      for (let i = 0; i < openParensBefore; i++) {\n        cleanAfter = cleanAfter.replace(/\\)[+*?]?/, '');\n      }\n      nlAfter = cleanAfter;\n      const dollar = nlAfter === '' && isSub !== SUBPARSE ? '(?:$|\\\\/)' : '';\n      re = nlBefore + nlFirst + nlAfter + dollar + nlLast;\n    }\n\n    // if the re is not \"\" at this point, then we need to make sure\n    // it doesn't match against an empty path part.\n    // Otherwise a/* will match a/, which it should not.\n    if (re !== '' && hasMagic) {\n      re = '(?=.)' + re;\n    }\n    if (addPatternStart) {\n      re = patternStart() + re;\n    }\n\n    // parsing just a piece of a larger pattern.\n    if (isSub === SUBPARSE) {\n      return [re, hasMagic];\n    }\n\n    // if it's nocase, and the lcase/uppercase don't match, it's magic\n    if (options.nocase && !hasMagic) {\n      hasMagic = pattern.toUpperCase() !== pattern.toLowerCase();\n    }\n\n    // skip the regexp for non-magical patterns\n    // unescape anything in it, though, so that it'll be\n    // an exact match against a file etc.\n    if (!hasMagic) {\n      return globUnescape(pattern);\n    }\n    const flags = options.nocase ? 'i' : '';\n    try {\n      return Object.assign(new RegExp('^' + re + '$', flags), {\n        _glob: pattern,\n        _src: re\n      });\n    } catch (er) /* istanbul ignore next - should be impossible */{\n      // If it was an invalid regular expression, then it can't match\n      // anything.  This trick looks for a character after the end of\n      // the string, which is of course impossible, except in multi-line\n      // mode, but it's not a /m regex.\n      return new RegExp('$.');\n    }\n  }\n  makeRe() {\n    if (this.regexp || this.regexp === false) return this.regexp;\n\n    // at this point, this.set is a 2d array of partial\n    // pattern strings, or \"**\".\n    //\n    // It's better to use .match().  This function shouldn't\n    // be used, really, but it's pretty convenient sometimes,\n    // when you just want to work with a regex.\n    const set = this.set;\n    if (!set.length) {\n      this.regexp = false;\n      return this.regexp;\n    }\n    const options = this.options;\n    const twoStar = options.noglobstar ? star : options.dot ? twoStarDot : twoStarNoDot;\n    const flags = options.nocase ? 'i' : '';\n\n    // coalesce globstars and regexpify non-globstar patterns\n    // if it's the only item, then we just do one twoStar\n    // if it's the first, and there are more, prepend (\\/|twoStar\\/)? to next\n    // if it's the last, append (\\/twoStar|) to previous\n    // if it's in the middle, append (\\/|\\/twoStar\\/) to previous\n    // then filter out GLOBSTAR symbols\n    let re = set.map(pattern => {\n      pattern = pattern.map(p => typeof p === 'string' ? regExpEscape(p) : p === GLOBSTAR ? GLOBSTAR : p._src).reduce((set, p) => {\n        if (!(set[set.length - 1] === GLOBSTAR && p === GLOBSTAR)) {\n          set.push(p);\n        }\n        return set;\n      }, []);\n      pattern.forEach((p, i) => {\n        if (p !== GLOBSTAR || pattern[i - 1] === GLOBSTAR) {\n          return;\n        }\n        if (i === 0) {\n          if (pattern.length > 1) {\n            pattern[i + 1] = '(?:\\\\\\/|' + twoStar + '\\\\\\/)?' + pattern[i + 1];\n          } else {\n            pattern[i] = twoStar;\n          }\n        } else if (i === pattern.length - 1) {\n          pattern[i - 1] += '(?:\\\\\\/|' + twoStar + ')?';\n        } else {\n          pattern[i - 1] += '(?:\\\\\\/|\\\\\\/' + twoStar + '\\\\\\/)' + pattern[i + 1];\n          pattern[i + 1] = GLOBSTAR;\n        }\n      });\n      return pattern.filter(p => p !== GLOBSTAR).join('/');\n    }).join('|');\n\n    // must match entire pattern\n    // ending in a * or ** will make it less strict.\n    re = '^(?:' + re + ')$';\n\n    // can match anything, as long as it's not this.\n    if (this.negate) re = '^(?!' + re + ').*$';\n    try {\n      this.regexp = new RegExp(re, flags);\n    } catch (ex) /* istanbul ignore next - should be impossible */{\n      this.regexp = false;\n    }\n    return this.regexp;\n  }\n  match(f, partial = this.partial) {\n    this.debug('match', f, this.pattern);\n    // short-circuit in the case of busted things.\n    // comments, etc.\n    if (this.comment) return false;\n    if (this.empty) return f === '';\n    if (f === '/' && partial) return true;\n    const options = this.options;\n\n    // windows: need to use /, not \\\n    if (path.sep !== '/') {\n      f = f.split(path.sep).join('/');\n    }\n\n    // treat the test path as a set of pathparts.\n    f = f.split(slashSplit);\n    this.debug(this.pattern, 'split', f);\n\n    // just ONE of the pattern sets in this.set needs to match\n    // in order for it to be valid.  If negating, then just one\n    // match means that we have failed.\n    // Either way, return on the first hit.\n\n    const set = this.set;\n    this.debug(this.pattern, 'set', set);\n\n    // Find the basename of the path by looking for the last non-empty segment\n    let filename;\n    for (let i = f.length - 1; i >= 0; i--) {\n      filename = f[i];\n      if (filename) break;\n    }\n    for (let i = 0; i < set.length; i++) {\n      const pattern = set[i];\n      let file = f;\n      if (options.matchBase && pattern.length === 1) {\n        file = [filename];\n      }\n      const hit = this.matchOne(file, pattern, partial);\n      if (hit) {\n        if (options.flipNegate) return true;\n        return !this.negate;\n      }\n    }\n\n    // didn't get any hits.  this is success if it's a negative\n    // pattern, failure otherwise.\n    if (options.flipNegate) return false;\n    return this.negate;\n  }\n  static defaults(def) {\n    return minimatch.defaults(def).Minimatch;\n  }\n}\nminimatch.Minimatch = Minimatch;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\n");

/***/ })

};
;