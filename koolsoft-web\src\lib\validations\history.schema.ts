/**
 * Comprehensive History Tracking Validation Schemas
 * 
 * This file contains all Zod validation schemas for the KoolSoft history tracking system.
 * It provides validation for different types of history records including repairs, 
 * maintenance, warranty, AMC, and audit history.
 */

import { z } from 'zod';

// Base validation schemas
const baseHistorySchema = z.object({
  id: z.string().uuid().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
  originalId: z.number().int().positive().optional(),
});

const customerReferenceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
});

const userReferenceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  email: z.string().email(),
  role: z.string().min(1),
});

// History Card Schemas
export const historyCardSchema = baseHistorySchema.extend({
  cardNo: z.number().int().positive().optional(),
  source: z.enum(['AMC', 'INW', 'OTW', 'SERVICE']).optional(),
  customerId: z.string().uuid('Valid customer ID is required'),
  amcId: z.string().uuid().optional(),
  inWarrantyId: z.string().uuid().optional(),
  outWarrantyId: z.string().uuid().optional(),
  toCardNo: z.number().int().positive().optional(),
  customer: customerReferenceSchema.optional(),
});

export const historySectionSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  sectionCode: z.string().min(1, 'Section code is required').max(50),
  content: z.string().min(1, 'Content is required').max(5000),
});

// Repair Part Schema
const repairPartSchema = z.object({
  id: z.string().uuid().optional(),
  partName: z.string().min(1, 'Part name is required').max(200),
  partNumber: z.string().min(1, 'Part number is required').max(100),
  quantity: z.number().int().min(1, 'Quantity must be at least 1'),
  cost: z.number().min(0).optional(),
  supplier: z.string().max(200).optional(),
  notes: z.string().max(500).optional(),
});

// Audit Finding Schema
const auditFindingSchema = z.object({
  id: z.string().uuid().optional(),
  category: z.enum(['COMPLIANCE', 'SECURITY', 'PERFORMANCE', 'QUALITY', 'SAFETY']),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  description: z.string().min(1, 'Description is required').max(1000),
  recommendation: z.string().max(1000).optional(),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).default('OPEN'),
  assignedTo: z.string().uuid().optional(),
  dueDate: z.coerce.date().optional(),
  notes: z.string().max(500).optional(),
});

// Repair History Schemas
export const historyRepairSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  assetNo: z.number().int().positive().optional(),
  repairDate: z.coerce.date({ message: 'Valid repair date is required' }),
  technicianId: z.string().uuid().optional(),
  description: z.string().min(1, 'Description is required').max(1000),
  partsReplaced: z.array(z.string()).optional(),
  laborHours: z.number().min(0).max(24).optional(),
  cost: z.number().min(0).optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  notes: z.string().max(2000).optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.coerce.date().optional(),
}).refine(
  (data) => {
    // If follow-up is required, follow-up date must be provided
    if (data.followUpRequired && !data.followUpDate) {
      return false;
    }
    return true;
  },
  {
    message: 'Follow-up date is required when follow-up is marked as required',
    path: ['followUpDate'],
  }
);

// Maintenance History Schemas
const maintenanceChecklistItemSchema = z.object({
  id: z.string().uuid().optional(),
  description: z.string().min(1, 'Description is required').max(500),
  completed: z.boolean().default(false),
  notes: z.string().max(1000).optional(),
});

export const historyMaintenanceSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  maintenanceType: z.enum(['PREVENTIVE', 'CORRECTIVE', 'SCHEDULED', 'EMERGENCY']),
  scheduledDate: z.coerce.date({ message: 'Valid scheduled date is required' }),
  completedDate: z.coerce.date().optional(),
  technicianId: z.string().uuid().optional(),
  description: z.string().min(1, 'Description is required').max(1000),
  checklistItems: z.array(maintenanceChecklistItemSchema).optional(),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'OVERDUE']).default('SCHEDULED'),
  cost: z.number().min(0).optional(),
  notes: z.string().max(2000).optional(),
  nextMaintenanceDate: z.coerce.date().optional(),
}).refine(
  (data) => {
    // If completed, completed date must be provided
    if (data.status === 'COMPLETED' && !data.completedDate) {
      return false;
    }
    return true;
  },
  {
    message: 'Completed date is required when status is completed',
    path: ['completedDate'],
  }
);

// Warranty History Schemas
const warrantyDocumentSchema = z.object({
  id: z.string().uuid().optional(),
  fileName: z.string().min(1),
  fileType: z.string().min(1),
  fileSize: z.number().int().positive(),
  uploadedAt: z.coerce.date().optional(),
  uploadedBy: z.string().uuid(),
});

export const historyWarrantySchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  warrantyType: z.enum(['IN_WARRANTY', 'OUT_WARRANTY', 'EXTENDED']),
  claimNumber: z.string().max(50).optional(),
  claimDate: z.coerce.date({ message: 'Valid claim date is required' }),
  issueDescription: z.string().min(1, 'Issue description is required').max(2000),
  resolution: z.string().max(2000).optional(),
  status: z.enum(['SUBMITTED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'COMPLETED']).default('SUBMITTED'),
  coverageStartDate: z.coerce.date({ message: 'Valid coverage start date is required' }),
  coverageEndDate: z.coerce.date({ message: 'Valid coverage end date is required' }),
  claimAmount: z.number().min(0).optional(),
  approvedAmount: z.number().min(0).optional(),
  rejectionReason: z.string().max(1000).optional(),
  documents: z.array(warrantyDocumentSchema).optional(),
}).refine(
  (data) => {
    // Coverage end date must be after start date
    return data.coverageEndDate > data.coverageStartDate;
  },
  {
    message: 'Coverage end date must be after start date',
    path: ['coverageEndDate'],
  }
).refine(
  (data) => {
    // If rejected, rejection reason must be provided
    if (data.status === 'REJECTED' && !data.rejectionReason) {
      return false;
    }
    return true;
  },
  {
    message: 'Rejection reason is required when status is rejected',
    path: ['rejectionReason'],
  }
);

// AMC History Schemas
export const historyAmcDetailSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  amcId: z.string().uuid('Valid AMC ID is required'),
  contractNumber: z.string().min(1, 'Contract number is required').max(50),
  startDate: z.coerce.date({ message: 'Valid start date is required' }),
  endDate: z.coerce.date({ message: 'Valid end date is required' }),
  renewalDate: z.coerce.date().optional(),
  serviceVisits: z.number().int().min(0, 'Service visits must be non-negative'),
  completedVisits: z.number().int().min(0, 'Completed visits must be non-negative'),
  remainingVisits: z.number().int().min(0, 'Remaining visits must be non-negative'),
  contractValue: z.number().min(0, 'Contract value must be non-negative'),
  status: z.enum(['ACTIVE', 'EXPIRED', 'CANCELLED', 'RENEWED']).default('ACTIVE'),
  serviceType: z.string().min(1, 'Service type is required').max(100),
  coverageDetails: z.string().max(2000).optional(),
  notes: z.string().max(2000).optional(),
}).refine(
  (data) => {
    // End date must be after start date
    return data.endDate > data.startDate;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
).refine(
  (data) => {
    // Completed visits cannot exceed total service visits
    return data.completedVisits <= data.serviceVisits;
  },
  {
    message: 'Completed visits cannot exceed total service visits',
    path: ['completedVisits'],
  }
);

// Addon History Schemas
export const historyAddonDetailSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  addonType: z.string().min(1, 'Addon type is required').max(100),
  addonDate: z.coerce.date({ message: 'Valid addon date is required' }),
  description: z.string().min(1, 'Description is required').max(1000),
  cost: z.number().min(0).optional(),
  validFrom: z.coerce.date({ message: 'Valid from date is required' }),
  validTo: z.coerce.date().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'CANCELLED']).default('ACTIVE'),
  notes: z.string().max(2000).optional(),
}).refine(
  (data) => {
    // Valid to date must be after valid from date
    if (data.validTo && data.validTo <= data.validFrom) {
      return false;
    }
    return true;
  },
  {
    message: 'Valid to date must be after valid from date',
    path: ['validTo'],
  }
);

// Water Wash History Schemas
export const historyWaterWashSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  washDate: z.coerce.date({ message: 'Valid wash date is required' }),
  technicianId: z.string().uuid().optional(),
  washType: z.enum(['REGULAR', 'DEEP_CLEAN', 'CHEMICAL', 'MAINTENANCE']).default('REGULAR'),
  duration: z.number().int().min(1).max(480).optional(), // max 8 hours
  chemicalsUsed: z.array(z.string()).optional(),
  cost: z.number().min(0).optional(),
  notes: z.string().max(2000).optional(),
  beforePhotos: z.array(z.string().url()).optional(),
  afterPhotos: z.array(z.string().url()).optional(),
});

// Complaint History Schemas
export const historyComplaintSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  complaintNumber: z.string().min(1, 'Complaint number is required').max(50),
  complaintDate: z.coerce.date({ message: 'Valid complaint date is required' }),
  visitDate: z.coerce.date().optional(),
  assetNo: z.number().int().positive().optional(),
  natureOfComplaint: z.string().min(1, 'Nature of complaint is required').max(200),
  complaintCategory: z.enum(['TECHNICAL', 'SERVICE', 'BILLING', 'PRODUCT', 'OTHER']),
  description: z.string().min(1, 'Description is required').max(2000),
  action: z.string().max(2000).optional(),
  resolution: z.string().max(2000).optional(),
  status: z.enum(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'ESCALATED']).default('OPEN'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
  assignedTo: z.string().uuid().optional(),
  responseTime: z.number().min(0).optional(),
  resolutionTime: z.number().min(0).optional(),
  customerSatisfaction: z.number().int().min(1).max(5).optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.coerce.date().optional(),
  notes: z.string().max(2000).optional(),
}).refine(
  (data) => {
    // If follow-up is required, follow-up date must be provided
    if (data.followUpRequired && !data.followUpDate) {
      return false;
    }
    return true;
  },
  {
    message: 'Follow-up date is required when follow-up is marked as required',
    path: ['followUpDate'],
  }
);

// Audit History Schemas
export const historyAuditSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  auditType: z.enum(['SYSTEM', 'USER_ACTION', 'DATA_CHANGE', 'ACCESS', 'SECURITY']),
  action: z.string().min(1, 'Action is required').max(200),
  entityType: z.string().min(1, 'Entity type is required').max(100),
  entityId: z.string().min(1, 'Entity ID is required').max(100),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  userId: z.string().uuid('Valid user ID is required'),
  ipAddress: z.string().ip().optional(),
  userAgent: z.string().max(500).optional(),
  timestamp: z.coerce.date({ message: 'Valid timestamp is required' }),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('LOW'),
  description: z.string().max(2000).optional(),
});

// Component Replacement History Schemas
export const historyComponentReplacementSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  componentId: z.string().uuid('Valid component ID is required'),
  componentName: z.string().min(1, 'Component name is required').max(200),
  replacementDate: z.coerce.date({ message: 'Valid replacement date is required' }),
  oldComponentSerialNumber: z.string().max(100).optional(),
  newComponentSerialNumber: z.string().max(100).optional(),
  reason: z.enum(['MAINTENANCE', 'FAILURE', 'UPGRADE', 'WARRANTY']),
  technicianId: z.string().uuid().optional(),
  cost: z.number().min(0).optional(),
  warrantyPeriod: z.number().int().min(0).max(120).optional(), // max 10 years
  notes: z.string().max(2000).optional(),
});

// Filter and Query Schemas
export const historyFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  historyType: z.array(z.string()).optional(),
  status: z.array(z.string()).optional(),
  priority: z.array(z.string()).optional(),
  technicianId: z.string().uuid().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
}).refine(
  (data) => {
    // Date to must be after date from
    if (data.dateFrom && data.dateTo && data.dateTo <= data.dateFrom) {
      return false;
    }
    return true;
  },
  {
    message: 'Date to must be after date from',
    path: ['dateTo'],
  }
);

// Create schemas (for POST requests) - using pick instead of omit for ZodEffects
export const createHistoryRepairSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  assetNo: z.number().int().positive().optional(),
  repairDate: z.coerce.date({ message: 'Valid repair date is required' }),
  technicianId: z.string().uuid().optional(),
  description: z.string().min(1, 'Description is required').max(1000),
  partsUsed: z.array(repairPartSchema).optional(),
  laborHours: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  notes: z.string().max(2000).optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.coerce.date().optional(),
});

export const createHistoryMaintenanceSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  maintenanceType: z.enum(['PREVENTIVE', 'CORRECTIVE', 'SCHEDULED', 'EMERGENCY']),
  scheduledDate: z.coerce.date({ message: 'Valid scheduled date is required' }),
  completedDate: z.coerce.date().optional(),
  technicianId: z.string().uuid().optional(),
  checklistItems: z.array(maintenanceChecklistItemSchema).optional(),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'OVERDUE']).default('SCHEDULED'),
  cost: z.number().min(0).optional(),
  notes: z.string().max(2000).optional(),
  nextMaintenanceDate: z.coerce.date().optional(),
});

export const createHistoryWarrantySchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  warrantyType: z.enum(['MANUFACTURER', 'EXTENDED', 'THIRD_PARTY']),
  claimNumber: z.string().min(1, 'Claim number is required').max(100),
  status: z.enum(['SUBMITTED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'CLOSED']).default('SUBMITTED'),
  submissionDate: z.coerce.date({ message: 'Valid submission date is required' }),
  resolutionDate: z.coerce.date().optional(),
  coverageStartDate: z.coerce.date({ message: 'Valid coverage start date is required' }),
  coverageEndDate: z.coerce.date({ message: 'Valid coverage end date is required' }),
  claimAmount: z.number().min(0).optional(),
  approvedAmount: z.number().min(0).optional(),
  rejectionReason: z.string().max(1000).optional(),
  documents: z.array(warrantyDocumentSchema).optional(),
});

export const createHistoryAmcDetailSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  contractNumber: z.string().min(1, 'Contract number is required').max(100),
  startDate: z.coerce.date({ message: 'Valid start date is required' }),
  endDate: z.coerce.date({ message: 'Valid end date is required' }),
  serviceVisits: z.number().int().min(1, 'Service visits must be at least 1'),
  completedVisits: z.number().int().min(0).default(0),
  contractValue: z.number().min(0, 'Contract value must be non-negative'),
  status: z.enum(['ACTIVE', 'EXPIRED', 'CANCELLED', 'RENEWED']).default('ACTIVE'),
  serviceType: z.string().min(1, 'Service type is required').max(100),
  coverageDetails: z.string().max(2000).optional(),
  notes: z.string().max(2000).optional(),
});

export const createHistoryAddonDetailSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  addonType: z.string().min(1, 'Addon type is required').max(100),
  description: z.string().min(1, 'Description is required').max(1000),
  cost: z.number().min(0).optional(),
  validFrom: z.coerce.date({ message: 'Valid from date is required' }),
  validTo: z.coerce.date().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'CANCELLED']).default('ACTIVE'),
  notes: z.string().max(2000).optional(),
});

export const createHistoryWaterWashSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  washDate: z.coerce.date({ message: 'Valid wash date is required' }),
  technicianId: z.string().uuid().optional(),
  waterQuality: z.enum(['EXCELLENT', 'GOOD', 'FAIR', 'POOR']),
  chemicalsUsed: z.array(z.string()).optional(),
  duration: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  notes: z.string().max(1000).optional(),
});

export const createHistoryComplaintSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  complaintType: z.enum(['TECHNICAL', 'SERVICE', 'BILLING', 'OTHER']),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
  description: z.string().min(1, 'Description is required').max(2000),
  reportedDate: z.coerce.date({ message: 'Valid reported date is required' }),
  acknowledgedDate: z.coerce.date().optional(),
  resolvedDate: z.coerce.date().optional(),
  status: z.enum(['OPEN', 'ACKNOWLEDGED', 'IN_PROGRESS', 'RESOLVED', 'CLOSED']).default('OPEN'),
  assignedTo: z.string().uuid().optional(),
  resolution: z.string().max(2000).optional(),
  resolutionTime: z.number().min(0).optional(),
  customerSatisfaction: z.number().int().min(1).max(5).optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.coerce.date().optional(),
  notes: z.string().max(2000).optional(),
});

export const createHistoryAuditSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  auditDate: z.coerce.date({ message: 'Valid audit date is required' }),
  auditorId: z.string().uuid().optional(),
  auditType: z.enum(['INTERNAL', 'EXTERNAL', 'COMPLIANCE', 'QUALITY']),
  findings: z.array(auditFindingSchema).optional(),
  overallScore: z.number().min(0).max(100).optional(),
  status: z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('SCHEDULED'),
  recommendations: z.string().max(2000).optional(),
  followUpDate: z.coerce.date().optional(),
  notes: z.string().max(2000).optional(),
});

export const createHistoryComponentReplacementSchema = baseHistorySchema.extend({
  historyCardId: z.string().uuid('Valid history card ID is required'),
  componentName: z.string().min(1, 'Component name is required').max(200),
  partNumber: z.string().min(1, 'Part number is required').max(100),
  serialNumber: z.string().max(100).optional(),
  replacementDate: z.coerce.date({ message: 'Valid replacement date is required' }),
  reason: z.enum(['FAILURE', 'MAINTENANCE', 'UPGRADE', 'RECALL']),
  technicianId: z.string().uuid().optional(),
  cost: z.number().min(0).optional(),
  warrantyPeriod: z.number().int().min(0).optional(),
  notes: z.string().max(1000).optional(),
});

// Update schemas (for PUT/PATCH requests) - using partial of create schemas
export const updateHistoryRepairSchema = createHistoryRepairSchema.omit({ historyCardId: true }).partial();
export const updateHistoryMaintenanceSchema = createHistoryMaintenanceSchema.omit({ historyCardId: true }).partial();
export const updateHistoryWarrantySchema = createHistoryWarrantySchema.omit({ historyCardId: true }).partial();
export const updateHistoryAmcDetailSchema = createHistoryAmcDetailSchema.omit({ historyCardId: true }).partial();
export const updateHistoryAddonDetailSchema = createHistoryAddonDetailSchema.omit({ historyCardId: true }).partial();
export const updateHistoryWaterWashSchema = createHistoryWaterWashSchema.omit({ historyCardId: true }).partial();
export const updateHistoryComplaintSchema = createHistoryComplaintSchema.omit({ historyCardId: true }).partial();
export const updateHistoryAuditSchema = createHistoryAuditSchema.omit({ historyCardId: true }).partial();
export const updateHistoryComponentReplacementSchema = createHistoryComponentReplacementSchema.omit({ historyCardId: true }).partial();
