'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Calendar as CalendarIcon, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useComponents, Component, CreateComponentData, UpdateComponentData } from '@/lib/hooks/useComponents';
import { 
  ComponentType, 
  ComponentStatus, 
  getComponentTypeLabel, 
  getComponentStatusLabel 
} from '@/lib/validations/component.schema';

// Form validation schema
const componentFormSchema = z.object({
  machineId: z.string().uuid('Valid machine ID is required'),
  componentNo: z.number().int().positive().optional(),
  serialNumber: z.string()
    .min(1, 'Serial number is required')
    .max(50, 'Serial number must be less than 50 characters'),
  warrantyDate: z.date().optional(),
  section: z.string().max(1, 'Section must be a single character').optional(),
  componentType: z.enum([
    ComponentType.COMPRESSOR,
    ComponentType.CONDENSER,
    ComponentType.EVAPORATOR,
    ComponentType.EXPANSION_VALVE,
    ComponentType.FAN_MOTOR,
    ComponentType.CONTROL_BOARD,
    ComponentType.SENSOR,
    ComponentType.FILTER,
    ComponentType.COIL,
    ComponentType.OTHER,
  ]).optional(),
  status: z.enum([
    ComponentStatus.ACTIVE,
    ComponentStatus.REPLACED,
    ComponentStatus.FAILED,
    ComponentStatus.MAINTENANCE,
  ]).default(ComponentStatus.ACTIVE),
  installationDate: z.date().optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
});

type ComponentFormData = z.infer<typeof componentFormSchema>;

interface ComponentFormProps {
  machineId: string;
  component?: Component;
  onSuccess?: (component: Component) => void;
  onCancel?: () => void;
}

export function ComponentForm({ machineId, component, onSuccess, onCancel }: ComponentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serialValidation, setSerialValidation] = useState<{
    isChecking: boolean;
    isValid: boolean;
    message: string;
  }>({ isChecking: false, isValid: true, message: '' });

  const { createComponent, updateComponent, validateSerialNumber } = useComponents();

  const form = useForm<ComponentFormData>({
    resolver: zodResolver(componentFormSchema) as any,
    defaultValues: {
      machineId,
      componentNo: component?.componentNo || undefined,
      serialNumber: component?.serialNumber || '',
      warrantyDate: component?.warrantyDate || undefined,
      section: component?.section || '',
      componentType: ComponentType.COMPRESSOR,
      status: ComponentStatus.ACTIVE,
      installationDate: undefined,
      notes: '',
    },
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch, reset } = form;
  const watchedSerialNumber = watch('serialNumber');

  // Validate serial number on change
  useEffect(() => {
    const validateSerial = async () => {
      if (!watchedSerialNumber || watchedSerialNumber.length < 3) {
        setSerialValidation({ isChecking: false, isValid: true, message: '' });
        return;
      }

      setSerialValidation({ isChecking: true, isValid: true, message: 'Checking...' });

      try {
        const result = await validateSerialNumber(watchedSerialNumber, component?.id);
        setSerialValidation({
          isChecking: false,
          isValid: result.isUnique,
          message: result.message,
        });
      } catch (error) {
        setSerialValidation({
          isChecking: false,
          isValid: false,
          message: 'Failed to validate serial number',
        });
      }
    };

    const timeoutId = setTimeout(validateSerial, 500);
    return () => clearTimeout(timeoutId);
  }, [watchedSerialNumber, validateSerialNumber, component?.id]);

  const onSubmit = async (data: ComponentFormData) => {
    if (!serialValidation.isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      let result: Component;

      if (component) {
        // Update existing component
        const updateData: UpdateComponentData = {
          machineId: data.machineId,
          componentNo: data.componentNo,
          serialNumber: data.serialNumber,
          warrantyDate: data.warrantyDate,
          section: data.section,
        };
        result = await updateComponent(component.id, updateData);
      } else {
        // Create new component
        const createData: CreateComponentData = {
          machineId: data.machineId,
          componentNo: data.componentNo,
          serialNumber: data.serialNumber,
          warrantyDate: data.warrantyDate,
          section: data.section,
        };
        result = await createComponent(createData);
      }

      onSuccess?.(result);
      if (!component) {
        reset();
      }
    } catch (error) {
      console.error('Error saving component:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <CardTitle className="text-white">
          {component ? 'Edit Component' : 'Add New Component'}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
          {/* Component Number */}
          <div className="space-y-2">
            <Label htmlFor="componentNo" className="text-black">
              Component Number
            </Label>
            <Input
              id="componentNo"
              type="number"
              placeholder="Enter component number"
              {...register('componentNo', { valueAsNumber: true })}
              className="w-full"
            />
            {errors.componentNo && (
              <p className="text-sm text-destructive">{errors.componentNo.message}</p>
            )}
          </div>

          {/* Serial Number */}
          <div className="space-y-2">
            <Label htmlFor="serialNumber" className="text-black">
              Serial Number *
            </Label>
            <div className="relative">
              <Input
                id="serialNumber"
                placeholder="Enter serial number"
                {...register('serialNumber')}
                className={cn(
                  'w-full pr-10',
                  !serialValidation.isValid && 'border-destructive'
                )}
              />
              {serialValidation.isChecking && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>
            {serialValidation.message && (
              <p className={cn(
                'text-sm',
                serialValidation.isValid ? 'text-green-600' : 'text-destructive'
              )}>
                {serialValidation.message}
              </p>
            )}
            {errors.serialNumber && (
              <p className="text-sm text-destructive">{errors.serialNumber.message}</p>
            )}
          </div>

          {/* Component Type */}
          <div className="space-y-2">
            <Label htmlFor="componentType" className="text-black">
              Component Type
            </Label>
            <Select onValueChange={(value) => setValue('componentType', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select component type" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ComponentType).map((type) => (
                  <SelectItem key={type} value={type}>
                    {getComponentTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status" className="text-black">
              Status
            </Label>
            <Select onValueChange={(value) => setValue('status', value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ComponentStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {getComponentStatusLabel(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Warranty Date */}
          <div className="space-y-2">
            <Label htmlFor="warrantyDate" className="text-black">
              Warranty Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !watch('warrantyDate') && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watch('warrantyDate') ? (
                    format(watch('warrantyDate')!, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={watch('warrantyDate')}
                  onSelect={(date) => setValue('warrantyDate', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Installation Date */}
          <div className="space-y-2">
            <Label htmlFor="installationDate" className="text-black">
              Installation Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !watch('installationDate') && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watch('installationDate') ? (
                    format(watch('installationDate')!, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={watch('installationDate')}
                  onSelect={(date) => setValue('installationDate', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Section */}
          <div className="space-y-2">
            <Label htmlFor="section" className="text-black">
              Section
            </Label>
            <Input
              id="section"
              placeholder="Enter section (single character)"
              maxLength={1}
              {...register('section')}
              className="w-full"
            />
            {errors.section && (
              <p className="text-sm text-destructive">{errors.section.message}</p>
            )}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-black">
              Notes
            </Label>
            <Textarea
              id="notes"
              placeholder="Enter any additional notes"
              rows={3}
              {...register('notes')}
              className="w-full"
            />
            {errors.notes && (
              <p className="text-sm text-destructive">{errors.notes.message}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              disabled={isSubmitting || !serialValidation.isValid}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {component ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                component ? 'Update Component' : 'Create Component'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
