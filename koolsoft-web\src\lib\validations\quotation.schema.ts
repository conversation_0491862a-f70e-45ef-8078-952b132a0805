import { z } from 'zod';

/**
 * Quotation Status Schema
 */
export const quotationStatusSchema = z.enum([
  'DRAFT',
  'SENT',
  'ACCEPTED',
  'REJECTED',
  'EXPIRED',
]);

/**
 * Discount Type Schema
 */
export const discountTypeSchema = z.enum(['PERCENTAGE', 'FIXED']);

/**
 * Quotation Item Schema
 */
export const quotationItemSchema = z.object({
  id: z.string().uuid().optional(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  description: z.string().min(1, { message: 'Description is required' }).max(500),
  quantity: z.number().int().min(1, { message: 'Quantity must be at least 1' }),
  unitPrice: z.number().min(0, { message: 'Unit price must be non-negative' }),
  totalPrice: z.number().min(0, { message: 'Total price must be non-negative' }),
  taxRate: z.number().min(0).max(100).optional(),
  taxAmount: z.number().min(0).optional(),
  discount: z.number().min(0).optional(),
  discountType: discountTypeSchema.optional(),
  specifications: z.string().max(1000).optional(),
  notes: z.string().max(500).optional(),
  sortOrder: z.number().int().min(0).optional(),
});

/**
 * Base Quotation Schema
 */
export const quotationBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }),
  quotationDate: z.coerce.date({ message: 'Valid quotation date is required' }),
  validUntil: z.coerce.date().optional(),
  status: quotationStatusSchema.default('DRAFT'),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  contactEmail: z.string().email().optional(),
  subject: z.string().max(200).optional(),
  notes: z.string().max(2000).optional(),
  termsConditions: z.string().max(5000).optional(),
  discount: z.number().min(0).optional(),
  discountType: discountTypeSchema.optional(),
});

/**
 * Create Quotation Schema
 */
export const createQuotationSchema = quotationBaseSchema.extend({
  items: z.array(quotationItemSchema).min(1, { message: 'At least one item is required' }),
});

/**
 * Update Quotation Schema
 */
export const updateQuotationSchema = quotationBaseSchema.partial().extend({
  id: z.string().uuid({ message: 'Valid quotation ID is required' }),
  items: z.array(quotationItemSchema).optional(),
});

/**
 * Quotation Filter Schema
 */
export const quotationFilterSchema = z.object({
  search: z.string().optional(),
  status: quotationStatusSchema.optional(),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['quotationDate', 'quotationNumber', 'totalAmount', 'status']).default('quotationDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Quotation Status Update Schema
 */
export const updateQuotationStatusSchema = z.object({
  id: z.string().uuid({ message: 'Valid quotation ID is required' }),
  status: quotationStatusSchema,
  notes: z.string().max(500).optional(),
});

/**
 * Quotation Email Schema
 */
export const quotationEmailSchema = z.object({
  quotationId: z.string().uuid({ message: 'Valid quotation ID is required' }),
  to: z.string().email({ message: 'Valid email address is required' }),
  cc: z.array(z.string().email()).optional(),
  bcc: z.array(z.string().email()).optional(),
  subject: z.string().min(1, { message: 'Subject is required' }).max(200),
  message: z.string().max(2000).optional(),
  attachPdf: z.boolean().default(true),
});

/**
 * Quotation Statistics Schema
 */
export const quotationStatisticsSchema = z.object({
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
});

/**
 * Quotation Export Schema
 */
export const quotationExportSchema = z.object({
  format: z.enum(['PDF', 'EXCEL', 'CSV']).default('PDF'),
  quotationIds: z.array(z.string().uuid()).optional(),
  filters: quotationFilterSchema.omit({ page: true, limit: true }).optional(),
});

/**
 * Quotation Duplicate Schema
 */
export const duplicateQuotationSchema = z.object({
  sourceQuotationId: z.string().uuid({ message: 'Valid source quotation ID is required' }),
  customerId: z.string().uuid().optional(),
  quotationDate: z.coerce.date().optional(),
  validUntil: z.coerce.date().optional(),
  notes: z.string().max(2000).optional(),
});

/**
 * Quotation Conversion Schema
 */
export const convertQuotationSchema = z.object({
  quotationId: z.string().uuid({ message: 'Valid quotation ID is required' }),
  convertTo: z.enum(['SALES_ORDER', 'AMC_CONTRACT', 'WARRANTY']),
  additionalData: z.record(z.any()).optional(),
});

// Type exports
export type QuotationStatus = z.infer<typeof quotationStatusSchema>;
export type DiscountType = z.infer<typeof discountTypeSchema>;
export type QuotationItem = z.infer<typeof quotationItemSchema>;
export type QuotationBase = z.infer<typeof quotationBaseSchema>;
export type CreateQuotation = z.infer<typeof createQuotationSchema>;
export type UpdateQuotation = z.infer<typeof updateQuotationSchema>;
export type QuotationFilter = z.infer<typeof quotationFilterSchema>;
export type UpdateQuotationStatus = z.infer<typeof updateQuotationStatusSchema>;
export type QuotationEmail = z.infer<typeof quotationEmailSchema>;
export type QuotationStatistics = z.infer<typeof quotationStatisticsSchema>;
export type QuotationExport = z.infer<typeof quotationExportSchema>;
export type DuplicateQuotation = z.infer<typeof duplicateQuotationSchema>;
export type ConvertQuotation = z.infer<typeof convertQuotationSchema>;
