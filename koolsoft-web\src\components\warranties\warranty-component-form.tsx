'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CalendarIcon, 
  Save, 
  X, 
  AlertCircle,
  Settings,
  Hash
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Warranty component form validation schema
const warrantyComponentFormSchema = z.object({
  machineId: z.string().uuid('Valid machine ID is required'),
  componentNo: z.number().int().min(1, 'Component number is required'),
  serialNumber: z.string().min(1, 'Serial number is required').max(50),
  warrantyDate: z.date().optional(),
  section: z.string().max(1, 'Section must be a single character').optional(),
});

type WarrantyComponentFormData = z.infer<typeof warrantyComponentFormSchema>;

interface WarrantyComponent {
  id: string;
  machineId: string;
  componentNo: number;
  serialNumber: string;
  warrantyDate?: string;
  section?: string;
  machine?: {
    id: string;
    serialNumber?: string;
    product?: {
      name: string;
    };
    model?: {
      name: string;
    };
    brand?: {
      name: string;
    };
  };
}

interface WarrantyComponentFormProps {
  component?: WarrantyComponent;
  machineId?: string;
  onSuccess: (component: any) => void;
  onCancel: () => void;
}

interface Machine {
  id: string;
  serialNumber?: string;
  product?: {
    name: string;
  };
  model?: {
    name: string;
  };
  brand?: {
    name: string;
  };
}

export function WarrantyComponentForm({ component, machineId, onSuccess, onCancel }: WarrantyComponentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [machines, setMachines] = useState<Machine[]>([]);
  const [loadingMachines, setLoadingMachines] = useState(false);

  const isEditing = !!component;

  // Initialize form
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<WarrantyComponentFormData>({
    resolver: zodResolver(warrantyComponentFormSchema),
    defaultValues: {
      machineId: component?.machineId || machineId || '',
      componentNo: component?.componentNo || undefined,
      serialNumber: component?.serialNumber || '',
      warrantyDate: component?.warrantyDate ? new Date(component.warrantyDate) : undefined,
      section: component?.section || '',
    }
  });

  const watchedValues = watch();

  // Load machines
  useEffect(() => {
    const loadMachines = async () => {
      try {
        setLoadingMachines(true);
        const response = await fetch('/api/warranties/machines', {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setMachines(data.machines || []);
        }
      } catch (error) {
        console.error('Error loading machines:', error);
      } finally {
        setLoadingMachines(false);
      }
    };

    loadMachines();
  }, []);

  // Handle form submission
  const onSubmit = async (data: WarrantyComponentFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Prepare submission data
      const submissionData = {
        ...data,
        warrantyDate: data.warrantyDate?.toISOString() || null,
        section: data.section || null,
      };

      const url = isEditing
        ? `/api/warranties/components/${component?.id}`
        : '/api/warranties/components';
      
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${isEditing ? 'update' : 'create'} component`);
      }

      const result = await response.json();
      onSuccess(result);
    } catch (error: any) {
      console.error('Error submitting component:', error);
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} component`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedMachine = machines.find(m => m.id === watchedValues.machineId);

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <CardTitle className="text-white">
          {isEditing ? 'Edit Warranty Component' : 'Add New Warranty Component'}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        {error && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-black">{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Machine Selection */}
          <div className="space-y-2">
            <Label className="text-black">
              Machine <span className="text-red-500">*</span>
            </Label>
            <Select 
              value={watchedValues.machineId || ''} 
              onValueChange={(value) => setValue('machineId', value)}
              disabled={loadingMachines || isEditing}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select machine" />
              </SelectTrigger>
              <SelectContent>
                {Array.isArray(machines) && machines.length > 0 ? (
                  machines.map((machine) => (
                    <SelectItem key={machine.id} value={machine.id}>
                      <div className="flex items-center">
                        <Settings className="mr-2 h-4 w-4 text-blue-500" />
                        <div>
                          <div className="font-medium">
                            {machine.brand?.name} {machine.product?.name} {machine.model?.name}
                          </div>
                          {machine.serialNumber && (
                            <div className="text-sm text-gray-500">
                              S/N: {machine.serialNumber}
                            </div>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-machines" disabled>
                    No machines available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            {errors.machineId && (
              <p className="text-sm text-destructive">{errors.machineId.message}</p>
            )}
            {selectedMachine && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Selected: {selectedMachine.brand?.name} {selectedMachine.product?.name} {selectedMachine.model?.name}
                {selectedMachine.serialNumber && ` (S/N: ${selectedMachine.serialNumber})`}
              </div>
            )}
          </div>

          {/* Component Number */}
          <div className="space-y-2">
            <Label htmlFor="componentNo" className="text-black">
              Component Number <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="componentNo"
                type="number"
                placeholder="Enter component number"
                {...register('componentNo', { valueAsNumber: true })}
                className="pl-10"
                min="1"
              />
            </div>
            {errors.componentNo && (
              <p className="text-sm text-destructive">{errors.componentNo.message}</p>
            )}
          </div>

          {/* Serial Number */}
          <div className="space-y-2">
            <Label htmlFor="serialNumber" className="text-black">
              Serial Number <span className="text-red-500">*</span>
            </Label>
            <Input
              id="serialNumber"
              placeholder="Enter component serial number"
              {...register('serialNumber')}
              maxLength={50}
            />
            {errors.serialNumber && (
              <p className="text-sm text-destructive">{errors.serialNumber.message}</p>
            )}
          </div>

          {/* Warranty Date */}
          <div className="space-y-2">
            <Label className="text-black">Warranty Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !watchedValues.warrantyDate && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watchedValues.warrantyDate ? (
                    format(watchedValues.warrantyDate, 'PPP')
                  ) : (
                    <span>Pick warranty date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={watchedValues.warrantyDate}
                  onSelect={(date) => setValue('warrantyDate', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {errors.warrantyDate && (
              <p className="text-sm text-destructive">{errors.warrantyDate.message}</p>
            )}
          </div>

          {/* Section */}
          <div className="space-y-2">
            <Label htmlFor="section" className="text-black">Section</Label>
            <Input
              id="section"
              placeholder="Enter section (single character)"
              {...register('section')}
              maxLength={1}
              className="w-20"
            />
            {errors.section && (
              <p className="text-sm text-destructive">{errors.section.message}</p>
            )}
            <p className="text-xs text-gray-500">
              Optional single character to identify the section or location
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90"
            >
              {isSubmitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              {isEditing ? 'Update Component' : 'Add Component'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
