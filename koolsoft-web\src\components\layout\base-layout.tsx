'use client';

import React from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface BaseLayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
}

/**
 * BaseLayout Component
 * 
 * A foundation layout component that handles authentication and authorization.
 * It redirects unauthenticated users to the login page and checks role-based access.
 */
export function BaseLayout({
  children,
  requireAuth = true,
  allowedRoles = [],
}: BaseLayoutProps) {
  const { user, isLoading, isAuthenticated, hasRole } = useAuth();
  const pathname = usePathname();
  const router = useRouter();

  // Redirect to login if authentication is required but user is not authenticated
  useEffect(() => {
    if (requireAuth && !isLoading && !isAuthenticated) {
      const callbackUrl = encodeURIComponent(pathname || '/');
      router.push(`/auth/login?callbackUrl=${callbackUrl}`);
    }
  }, [isLoading, isAuthenticated, pathname, router, requireAuth]);

  // Check role-based access
  const hasAccess = !requireAuth || 
    (isAuthenticated && (allowedRoles.length === 0 || allowedRoles.some(role => hasRole([role]))));

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated, show nothing (will be redirected by useEffect)
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Authentication Required</h2>
          <p className="text-gray-500">Please log in to access this page</p>
        </div>
      </div>
    );
  }

  // If role-based access is required but user doesn't have the required role
  if (requireAuth && isAuthenticated && !hasAccess) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Access Denied</h2>
          <p className="text-gray-500">You don't have permission to access this page</p>
        </div>
      </div>
    );
  }

  // Render children if all checks pass
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  );
}
