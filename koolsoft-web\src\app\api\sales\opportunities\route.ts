import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesOpportunityRepository } from '@/lib/repositories';
import { createSalesOpportunitySchema, salesFilterSchema } from '@/lib/validations/sales.schema';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/sales/opportunities
 * Get sales opportunities with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        status: searchParams.get('status') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
        search: searchParams.get('search') || undefined,
        skip: searchParams.get('skip') || undefined,
        take: searchParams.get('take') || undefined,
        sortBy: searchParams.get('sortBy') || undefined,
        sortOrder: searchParams.get('sortOrder') || undefined,
      };

      const validatedFilters = salesFilterSchema.parse(filters);

      const salesOpportunityRepository = getSalesOpportunityRepository();
      const result = await salesOpportunityRepository.findWithFilters(validatedFilters);

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error fetching sales opportunities:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales opportunities',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/sales/opportunities
 * Create a new sales opportunity
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createSalesOpportunitySchema.parse(body);

      const salesOpportunityRepository = getSalesOpportunityRepository();

      // Create sales opportunity
      const salesOpportunity = await salesOpportunityRepository.create({
        customer: { connect: { id: validatedData.customerId } },
        executive: { connect: { id: validatedData.executiveId } },
        opportunityDate: validatedData.opportunityDate,
        contactPerson: validatedData.contactPerson,
        contactPhone: validatedData.contactPhone,
        status: validatedData.status,
        prospectPercentage: validatedData.prospectPercentage,
        followUpDate: validatedData.followUpDate,
        nextVisitDate: validatedData.nextVisitDate,
        remarks: validatedData.remarks,
      });

      // Fetch the created opportunity with relations
      const createdOpportunity = await salesOpportunityRepository.findById(salesOpportunity.id);

      // Create notification event for opportunity creation
      try {
        const session = await getServerSession(authOptions);
        const salesNotificationService = getSalesNotificationService();

        await salesNotificationService.createSalesEvent({
          eventType: 'OPPORTUNITY_CREATED',
          entityType: 'opportunity',
          entityId: salesOpportunity.id,
          userId: session?.user?.id,
          customerId: validatedData.customerId,
          executiveId: validatedData.executiveId,
          newStatus: validatedData.status,
          eventData: {
            opportunityDate: validatedData.opportunityDate,
            contactPerson: validatedData.contactPerson,
            contactPhone: validatedData.contactPhone,
            prospectPercentage: validatedData.prospectPercentage,
          },
        });
      } catch (notificationError) {
        console.error('Error creating opportunity notification:', notificationError);
        // Don't fail the request if notification fails
      }

      return NextResponse.json({
        success: true,
        data: createdOpportunity,
        message: 'Sales opportunity created successfully',
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating sales opportunity:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create sales opportunity',
        },
        { status: 500 }
      );
    }
  }
);
