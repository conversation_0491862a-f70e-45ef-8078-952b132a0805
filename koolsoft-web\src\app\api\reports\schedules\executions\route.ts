import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ScheduledReportExecutionRepository } from '@/lib/repositories/scheduled-report-execution.repository';
import { listExecutionsSchema } from '@/lib/validations/scheduled-report.schema';

/**
 * GET /api/reports/schedules/executions
 * List scheduled report executions with filtering and pagination
 */
async function getScheduledReportExecutions(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = listExecutionsSchema.parse(params);
    
    const executionRepository = new ScheduledReportExecutionRepository();
    const result = await executionRepository.findMany(validatedParams);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching scheduled report executions:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch scheduled report executions' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getScheduledReportExecutions
);
