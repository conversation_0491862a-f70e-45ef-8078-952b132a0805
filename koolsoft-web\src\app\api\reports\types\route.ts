import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { buildReportResponse } from '@/lib/utils/report-utils';

/**
 * GET /api/reports/types
 * Get available report types and their configurations
 * Accessible by ADMIN, MANAGER, EXECUTIVE, USER roles
 */
async function getReportTypes(request: NextRequest) {
  try {
    const reportTypes = [
      {
        type: 'AMC',
        name: 'AMC Reports',
        description: 'Annual Maintenance Contract reports and analytics',
        category: 'Contracts',
        icon: 'FileContract',
        availableFilters: [
          'startDate',
          'endDate',
          'customerId',
          'executiveId',
          'amcStatus',
          'contractType',
          'amountMin',
          'amountMax',
          'serviceType',
          'search',
        ],
        sortOptions: [
          { value: 'start_date', label: 'Start Date' },
          { value: 'end_date', label: 'End Date' },
          { value: 'amount', label: 'Amount' },
          { value: 'customer_name', label: 'Customer Name' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
      },
      {
        type: 'WARRANTY',
        name: 'Warranty Reports',
        description: 'In-warranty and out-warranty reports',
        category: 'Warranties',
        icon: 'Shield',
        availableFilters: [
          'startDate',
          'endDate',
          'customerId',
          'executiveId',
          'warrantyType',
          'warrantyStatus',
          'productId',
          'modelId',
          'search',
        ],
        sortOptions: [
          { value: 'start_date', label: 'Start Date' },
          { value: 'end_date', label: 'End Date' },
          { value: 'customer_name', label: 'Customer Name' },
          { value: 'product_name', label: 'Product' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
      },
      {
        type: 'SERVICE',
        name: 'Service Reports',
        description: 'Service reports and maintenance tracking',
        category: 'Services',
        icon: 'Wrench',
        availableFilters: [
          'startDate',
          'endDate',
          'customerId',
          'executiveId',
          'serviceStatus',
          'complaintType',
          'priority',
          'technicianId',
          'search',
        ],
        sortOptions: [
          { value: 'sr_date', label: 'Service Date' },
          { value: 'customer_name', label: 'Customer Name' },
          { value: 'status', label: 'Status' },
          { value: 'priority', label: 'Priority' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
      },
      {
        type: 'SALES',
        name: 'Sales Reports',
        description: 'Sales orders, leads, and opportunities',
        category: 'Sales',
        icon: 'TrendingUp',
        availableFilters: [
          'startDate',
          'endDate',
          'customerId',
          'executiveId',
          'salesStage',
          'salesStatus',
          'amountMin',
          'amountMax',
          'source',
          'search',
        ],
        sortOptions: [
          { value: 'order_date', label: 'Order Date' },
          { value: 'amount', label: 'Amount' },
          { value: 'customer_name', label: 'Customer Name' },
          { value: 'status', label: 'Status' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
      },
      {
        type: 'CUSTOMER',
        name: 'Customer Reports',
        description: 'Customer information and analytics',
        category: 'Customers',
        icon: 'Users',
        availableFilters: [
          'customerType',
          'city',
          'state',
          'isActive',
          'search',
        ],
        sortOptions: [
          { value: 'name', label: 'Customer Name' },
          { value: 'city', label: 'City' },
          { value: 'state', label: 'State' },
          { value: 'created_at', label: 'Created Date' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
      },
      {
        type: 'FINANCIAL',
        name: 'Financial Reports',
        description: 'Revenue, payments, and financial analytics',
        category: 'Finance',
        icon: 'DollarSign',
        availableFilters: [
          'startDate',
          'endDate',
          'reportSubType',
          'paymentMode',
          'amountMin',
          'amountMax',
          'search',
        ],
        sortOptions: [
          { value: 'date', label: 'Date' },
          { value: 'amount', label: 'Amount' },
          { value: 'customer_name', label: 'Customer Name' },
          { value: 'payment_mode', label: 'Payment Mode' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER'],
      },
      {
        type: 'ANALYTICS',
        name: 'Analytics Reports',
        description: 'Business intelligence and trend analysis',
        category: 'Analytics',
        icon: 'BarChart',
        availableFilters: [
          'startDate',
          'endDate',
          'analyticsType',
          'groupBy',
          'metrics',
        ],
        sortOptions: [
          { value: 'date', label: 'Date' },
          { value: 'value', label: 'Value' },
          { value: 'metric', label: 'Metric' },
        ],
        exportFormats: ['CSV', 'JSON', 'EXCEL'],
        permissions: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
      },
    ];

    // Filter report types based on user role (this would be enhanced with actual session data)
    const filteredReportTypes = reportTypes; // For now, return all types

    // Get unique categories
    const categorySet = new Set(filteredReportTypes.map(rt => rt.category));
    const categories = Array.from(categorySet);

    return NextResponse.json(
      buildReportResponse(filteredReportTypes, undefined, {
        totalTypes: filteredReportTypes.length,
        categories,
      })
    );

  } catch (error) {
    console.error('Error fetching report types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch report types' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  getReportTypes
);
