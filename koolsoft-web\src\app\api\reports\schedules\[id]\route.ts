import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ScheduledReportRepository } from '@/lib/repositories/scheduled-report.repository';
import { getReportSchedulerService } from '@/lib/services/report-scheduler.service';
import { updateScheduledReportSchema } from '@/lib/validations/scheduled-report.schema';

/**
 * GET /api/reports/schedules/[id]
 * Get a specific scheduled report by ID
 */
async function getScheduledReport(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduledReportRepository = new ScheduledReportRepository();
    const scheduledReport = await scheduledReportRepository.findById(params.id);
    
    if (!scheduledReport) {
      return NextResponse.json(
        { error: 'Scheduled report not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: scheduledReport,
    });
  } catch (error) {
    console.error('Error fetching scheduled report:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scheduled report' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/reports/schedules/[id]
 * Update a scheduled report
 */
async function updateScheduledReport(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = updateScheduledReportSchema.parse(body);
    
    const scheduledReportRepository = new ScheduledReportRepository();
    
    // Check if the scheduled report exists
    const existingReport = await scheduledReportRepository.findById(params.id);
    if (!existingReport) {
      return NextResponse.json(
        { error: 'Scheduled report not found' },
        { status: 404 }
      );
    }
    
    // Update the scheduled report
    const updatedReport = await scheduledReportRepository.update(params.id, validatedData);
    
    // Update the schedule if cron expression or active status changed
    const schedulerService = getReportSchedulerService();
    
    if (validatedData.cronExpression || validatedData.isActive !== undefined) {
      if (updatedReport.isActive && updatedReport.cronExpression) {
        await schedulerService.scheduleReport(params.id, updatedReport.cronExpression);
      } else {
        schedulerService.unscheduleReport(params.id);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Scheduled report updated successfully',
      data: updatedReport,
    });
  } catch (error) {
    console.error('Error updating scheduled report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update scheduled report' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/reports/schedules/[id]
 * Delete a scheduled report
 */
async function deleteScheduledReport(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduledReportRepository = new ScheduledReportRepository();
    
    // Check if the scheduled report exists
    const existingReport = await scheduledReportRepository.findById(params.id);
    if (!existingReport) {
      return NextResponse.json(
        { error: 'Scheduled report not found' },
        { status: 404 }
      );
    }
    
    // Unschedule the report
    const schedulerService = getReportSchedulerService();
    schedulerService.unscheduleReport(params.id);
    
    // Delete the scheduled report
    await scheduledReportRepository.delete(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Scheduled report deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting scheduled report:', error);
    return NextResponse.json(
      { error: 'Failed to delete scheduled report' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getScheduledReport
);

export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  updateScheduledReport
);

export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  deleteScheduledReport
);

/**
 * PATCH /api/reports/schedules/[id]
 * Toggle active status of a scheduled report
 */
async function toggleScheduledReport(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduledReportRepository = new ScheduledReportRepository();

    // Toggle the active status
    const updatedReport = await scheduledReportRepository.toggleActive(params.id);

    // Update the schedule
    const schedulerService = getReportSchedulerService();

    if (updatedReport.isActive) {
      await schedulerService.scheduleReport(params.id, updatedReport.cronExpression);
    } else {
      schedulerService.unscheduleReport(params.id);
    }

    return NextResponse.json({
      success: true,
      message: `Scheduled report ${updatedReport.isActive ? 'activated' : 'deactivated'} successfully`,
      data: updatedReport,
    });
  } catch (error) {
    console.error('Error toggling scheduled report:', error);
    return NextResponse.json(
      { error: 'Failed to toggle scheduled report' },
      { status: 500 }
    );
  }
}

export const PATCH = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  toggleScheduledReport
);
