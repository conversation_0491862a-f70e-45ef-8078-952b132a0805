'use client';

import { useState, useEffect } from 'react';

export interface Brand {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  brandId?: string;
  brand?: Brand;
}

export interface Model {
  id: string;
  name: string;
  description?: string;
  specs?: string;
  tonnage?: number;
  installCharge?: number;
  numberOfComponents?: number;
  isActive: boolean;
  productId?: string;
  product?: Product;
}

export interface Division {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  originalId?: number;
}

export interface ServiceCenter {
  id: string;
  name: string;
  vendor?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReferenceData {
  brands: Brand[];
  products: Product[];
  models: Model[];
  serviceCenters: ServiceCenter[];
}

export function useReferenceData() {
  const [data, setData] = useState<ReferenceData>({
    brands: [],
    products: [],
    models: [],
    serviceCenters: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/reference-data', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch reference data');
        }

        const result = await response.json();
        setData({
          brands: result.brands || [],
          products: result.products || [],
          models: result.models || [],
          serviceCenters: result.serviceCenters || [],
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching reference data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferenceData();
  }, []);

  return { data, isLoading, error };
}

export function useBrands() {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/reference-data?type=brands', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch brands');
        }

        const result = await response.json();
        setBrands(result || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching brands:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBrands();
  }, []);

  return { brands, isLoading, error };
}

export function useProducts(brandId?: string) {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        let url = '/api/reference-data?type=products';
        if (brandId) {
          url += `&brandId=${brandId}`;
        }

        const response = await fetch(url, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }

        const result = await response.json();
        setProducts(result || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching products:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [brandId]);

  return { products, isLoading, error };
}

export function useModels(productId?: string) {
  const [models, setModels] = useState<Model[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        setIsLoading(true);
        setError(null);

        let url = '/api/reference-data?type=models';
        if (productId) {
          url += `&productId=${productId}`;
        }

        const response = await fetch(url, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch models');
        }

        const result = await response.json();
        setModels(result || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching models:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchModels();
  }, [productId]);

  return { models, isLoading, error };
}

export function useDivisions() {
  const [divisions, setDivisions] = useState<Division[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDivisions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/reference-data?type=divisions', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch divisions');
        }

        const result = await response.json();
        setDivisions(result || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching divisions:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDivisions();
  }, []);

  return { divisions, isLoading, error };
}
