'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CalendarIcon, 
  Save, 
  X, 
  AlertCircle,
  Clock,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Service date form validation schema
const serviceDateFormSchema = z.object({
  amcContractId: z.string().uuid('Valid AMC contract ID is required'),
  scheduledDate: z.date({ required_error: 'Scheduled date is required' }),
  completedDate: z.date().optional(),
  status: z.enum(['SCHEDULED', 'COMPLETED', 'MISSED'], {
    errorMap: () => ({ message: 'Status must be one of: SCHEDULED, COMPLETED, MISSED' })
  }).default('SCHEDULED'),
  technicianId: z.string().optional(),
  remarks: z.string().optional(),
});

type ServiceDateFormData = z.infer<typeof serviceDateFormSchema>;

interface ServiceDate {
  id: string;
  amcContractId: string;
  scheduledDate: string;
  completedDate?: string;
  status: 'SCHEDULED' | 'COMPLETED' | 'MISSED';
  technicianId?: string;
  remarks?: string;
  amcContract?: {
    id: string;
    contractNumber?: string;
    customer: {
      name: string;
    };
  };
}

interface ServiceDateFormProps {
  serviceDate?: ServiceDate;
  amcContractId?: string;
  onSuccess: (serviceDate: any) => void;
  onCancel: () => void;
}

interface Technician {
  id: string;
  name: string;
  email?: string;
}

export function ServiceDateForm({ serviceDate, amcContractId, onSuccess, onCancel }: ServiceDateFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loadingTechnicians, setLoadingTechnicians] = useState(false);

  const isEditing = !!serviceDate;

  // Initialize form
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<ServiceDateFormData>({
    resolver: zodResolver(serviceDateFormSchema) as any,
    defaultValues: {
      amcContractId: serviceDate?.amcContractId || amcContractId || '',
      scheduledDate: serviceDate ? new Date(serviceDate.scheduledDate) : undefined,
      completedDate: serviceDate?.completedDate ? new Date(serviceDate.completedDate) : undefined,
      status: serviceDate?.status || 'SCHEDULED',
      technicianId: serviceDate?.technicianId || 'none',
      remarks: serviceDate?.remarks || '',
    }
  });

  const watchedValues = watch();

  // Load technicians
  useEffect(() => {
    const loadTechnicians = async () => {
      try {
        setLoadingTechnicians(true);
        const response = await fetch('/api/users?role=TECHNICIAN', {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setTechnicians(data.users || []);
        }
      } catch (error) {
        console.error('Error loading technicians:', error);
      } finally {
        setLoadingTechnicians(false);
      }
    };

    loadTechnicians();
  }, []);

  // Handle form submission
  const onSubmit = async (data: ServiceDateFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Prepare submission data
      const submissionData = {
        ...data,
        technicianId: data.technicianId === 'none' ? null : data.technicianId,
        scheduledDate: data.scheduledDate.toISOString(),
        completedDate: data.completedDate?.toISOString() || null,
      };

      const url = isEditing 
        ? `/api/amc/service-dates/${serviceDate.id}`
        : '/api/amc/service-dates';
      
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${isEditing ? 'update' : 'create'} service date`);
      }

      const result = await response.json();
      onSuccess(result);
    } catch (error: any) {
      console.error('Error submitting service date:', error);
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} service date`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
        <CardTitle className="text-white">
          {isEditing ? 'Edit Service Date' : 'Schedule New Service'}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        {error && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-black">{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
          {/* Scheduled Date */}
          <div className="space-y-2">
            <Label className="text-black">
              Scheduled Date <span className="text-red-500">*</span>
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !watchedValues.scheduledDate && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watchedValues.scheduledDate ? (
                    format(watchedValues.scheduledDate, 'PPP')
                  ) : (
                    <span>Pick scheduled date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={watchedValues.scheduledDate}
                  onSelect={(date) => date && setValue('scheduledDate', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {errors.scheduledDate && (
              <p className="text-sm text-destructive">{errors.scheduledDate.message}</p>
            )}
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label className="text-black">Status</Label>
            <Select 
              value={watchedValues.status} 
              onValueChange={(value) => setValue('status', value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="SCHEDULED">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-blue-500" />
                    Scheduled
                  </div>
                </SelectItem>
                <SelectItem value="COMPLETED">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-green-500" />
                    Completed
                  </div>
                </SelectItem>
                <SelectItem value="MISSED">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-red-500" />
                    Missed
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-destructive">{errors.status.message}</p>
            )}
          </div>

          {/* Completed Date (only if status is COMPLETED) */}
          {watchedValues.status === 'COMPLETED' && (
            <div className="space-y-2">
              <Label className="text-black">Completed Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !watchedValues.completedDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {watchedValues.completedDate ? (
                      format(watchedValues.completedDate, 'PPP')
                    ) : (
                      <span>Pick completed date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={watchedValues.completedDate}
                    onSelect={(date) => setValue('completedDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          {/* Technician */}
          <div className="space-y-2">
            <Label className="text-black">Assigned Technician</Label>
            <Select 
              value={watchedValues.technicianId || 'none'} 
              onValueChange={(value) => setValue('technicianId', value)}
              disabled={loadingTechnicians}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select technician" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">
                  <div className="flex items-center">
                    <User className="mr-2 h-4 w-4 text-gray-400" />
                    No technician assigned
                  </div>
                </SelectItem>
                {technicians.map((technician) => (
                  <SelectItem key={technician.id} value={technician.id}>
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-blue-500" />
                      {technician.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Remarks */}
          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-black">Remarks</Label>
            <Textarea
              id="remarks"
              placeholder="Enter any remarks or notes about this service..."
              {...register('remarks')}
              className="min-h-[80px]"
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90"
            >
              {isSubmitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              {isEditing ? 'Update Service' : 'Schedule Service'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
