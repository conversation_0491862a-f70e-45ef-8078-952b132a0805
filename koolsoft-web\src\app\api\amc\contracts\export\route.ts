import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getAMCContractRepository } from '@/lib/repositories';
import { logActivity } from '@/lib/activity-logger';

/**
 * Export AMC Contracts
 * 
 * GET /api/amc/contracts/export
 * 
 * Exports AMC contracts in CSV format
 */
async function exportHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'csv';
    
    // Get all contracts for export
    const repository = getAMCContractRepository();
    const contracts = await repository.findWithFilter({
      filter: {},
      skip: 0,
      take: 10000, // Large number to get all contracts
      orderBy: { startDate: 'desc' }
    });

    if (format === 'csv') {
      // Generate CSV content
      const csvHeaders = [
        'Contract ID',
        'Customer Name',
        'Customer City',
        'Contact Person',
        'Executive',
        'Nature of Service',
        'Start Date',
        'End Date',
        'Warning Date',
        'Amount',
        'BSL Debit',
        'Previous Amount',
        'AMC Period',
        'Year of Commencement',
        'Number of Machines',
        'Number of Services',
        'Renewal Flag',
        'Bluestar Flag',
        'Paid Amount',
        'Fresh',
        'Number of Installments',
        'Payment Mode',
        'Total Tonnage',
        'Category',
        'Status',
        'Created At',
        'Updated At'
      ];

      const csvRows = contracts.map((contract: any) => [
        contract.id,
        contract.customer?.name || '',
        contract.customer?.city || '',
        contract.contactPerson?.name || '',
        contract.executive?.name || '',
        contract.natureOfService || '',
        contract.startDate ? new Date(contract.startDate).toLocaleDateString() : '',
        contract.endDate ? new Date(contract.endDate).toLocaleDateString() : '',
        contract.warningDate ? new Date(contract.warningDate).toLocaleDateString() : '',
        contract.amount || 0,
        contract.bslDebit || 0,
        contract.previousAmount || 0,
        contract.amcPeriod || '',
        contract.yearOfCommencement || '',
        contract.numberOfMachines || 0,
        contract.numberOfServices || 0,
        contract.renewalFlag ? 'Yes' : 'No',
        contract.blstrFlag ? 'Yes' : 'No',
        contract.paidAmount || 0,
        contract.fresh ? 'Yes' : 'No',
        contract.numberOfInstallments || 0,
        contract.paymentMode || '',
        contract.totalTonnage || 0,
        contract.category || '',
        contract.status || '',
        contract.createdAt ? new Date(contract.createdAt).toLocaleDateString() : '',
        contract.updatedAt ? new Date(contract.updatedAt).toLocaleDateString() : ''
      ]);

      // Escape CSV values and handle commas/quotes
      const escapeCsvValue = (value: any): string => {
        const stringValue = String(value || '');
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      const csvContent = [
        csvHeaders.map(escapeCsvValue).join(','),
        ...csvRows.map((row: any) => row.map(escapeCsvValue).join(','))
      ].join('\n');

      // Log the export activity
      await logActivity({
        action: 'EXPORT',
        entityType: 'AMC_CONTRACT',
        details: `Exported ${contracts.length} AMC contracts in CSV format`,
        request
      });

      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="amc-contracts-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    }

    // For other formats, return JSON for now
    const jsonData = {
      contracts: contracts.map((contract: any) => ({
        id: contract.id,
        customerName: contract.customer?.name || '',
        customerCity: contract.customer?.city || '',
        contactPerson: contract.contactPerson?.name || '',
        executive: contract.executive?.name || '',
        natureOfService: contract.natureOfService || '',
        startDate: contract.startDate,
        endDate: contract.endDate,
        warningDate: contract.warningDate,
        amount: contract.amount || 0,
        bslDebit: contract.bslDebit || 0,
        previousAmount: contract.previousAmount || 0,
        amcPeriod: contract.amcPeriod || '',
        yearOfCommencement: contract.yearOfCommencement || '',
        numberOfMachines: contract.numberOfMachines || 0,
        numberOfServices: contract.numberOfServices || 0,
        renewalFlag: contract.renewalFlag,
        blstrFlag: contract.blstrFlag,
        paidAmount: contract.paidAmount || 0,
        fresh: contract.fresh,
        numberOfInstallments: contract.numberOfInstallments || 0,
        paymentMode: contract.paymentMode || '',
        totalTonnage: contract.totalTonnage || 0,
        category: contract.category || '',
        status: contract.status || '',
        createdAt: contract.createdAt,
        updatedAt: contract.updatedAt
      })),
      exportedAt: new Date().toISOString(),
      totalCount: contracts.length
    };

    // Log the export activity
    await logActivity({
      action: 'EXPORT',
      entityType: 'AMC_CONTRACT',
      details: `Exported ${contracts.length} AMC contracts in JSON format`,
      request
    });

    return NextResponse.json(jsonData, {
      headers: {
        'Content-Disposition': `attachment; filename="amc-contracts-${new Date().toISOString().split('T')[0]}.json"`
      }
    });

  } catch (error) {
    console.error('Error exporting AMC contracts:', error);
    return NextResponse.json(
      { error: 'Failed to export AMC contracts' },
      { status: 500 }
    );
  }
}

export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], exportHandler);
