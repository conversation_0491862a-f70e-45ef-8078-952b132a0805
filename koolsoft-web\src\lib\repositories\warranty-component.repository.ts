import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Warranty Component Repository
 *
 * This repository handles database operations for the Warranty Component entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class WarrantyComponentRepository extends PrismaRepository<
  Prisma.warranty_componentsGetPayload<{}>,
  string,
  Prisma.warranty_componentsCreateInput,
  Prisma.warranty_componentsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('warranty_components');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find warranty components by machine ID
   * @param machineId Warranty machine ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty components
   */
  async findByMachineId(machineId: string, skip?: number, take?: number): Promise<Prisma.warranty_componentsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { machineId },
      skip,
      take,
      orderBy: { componentNo: 'asc' },
    });
  }

  /**
   * Find warranty component by serial number
   * @param serialNumber Component serial number
   * @returns Promise resolving to the warranty component or null if not found
   */
  async findBySerialNumber(serialNumber: string): Promise<Prisma.warranty_componentsGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { serialNumber },
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: true,
              },
            },
            product: true,
            model: true,
            brand: true,
          },
        },
      },
    });
  }

  /**
   * Find warranty components by component number
   * @param componentNo Component number
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty components
   */
  async findByComponentNo(componentNo: number, skip?: number, take?: number): Promise<Prisma.warranty_componentsGetPayload<{}>[]> {
    return this.model.findMany({
      where: { componentNo },
      skip,
      take,
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: true,
              },
            },
            product: true,
            model: true,
            brand: true,
          },
        },
      },
    });
  }

  /**
   * Find expiring warranty components (warranty date within the next X days)
   * @param days Number of days to look ahead
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of expiring warranty components
   */
  async findExpiring(days: number = 30, skip?: number, take?: number): Promise<Prisma.warranty_componentsGetPayload<{}>[]> {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);

    return this.model.findMany({
      where: {
        warrantyDate: {
          gte: today,
          lte: futureDate,
        },
      },
      skip,
      take,
      orderBy: { warrantyDate: 'asc' },
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: true,
              },
            },
            product: true,
            model: true,
            brand: true,
          },
        },
      },
    });
  }

  /**
   * Find expired warranty components
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of expired warranty components
   */
  async findExpired(skip?: number, take?: number): Promise<Prisma.warranty_componentsGetPayload<{}>[]> {
    const today = new Date();

    return this.model.findMany({
      where: {
        warrantyDate: {
          lt: today,
        },
      },
      skip,
      take,
      orderBy: { warrantyDate: 'desc' },
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: true,
              },
            },
            product: true,
            model: true,
            brand: true,
          },
        },
      },
    });
  }

  /**
   * Find warranty component by ID with basic relations
   * @param id Warranty component ID
   * @returns Promise resolving to the warranty component or null if not found
   */
  async findById(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            product: {
              select: {
                id: true,
                name: true,
              },
            },
            model: {
              select: {
                id: true,
                name: true,
              },
            },
            brand: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find warranty component with all related data
   * @param id Warranty component ID
   * @returns Promise resolving to the warranty component with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        machine: {
          include: {
            warranty: {
              include: {
                customer: true,
                contactPerson: true,
              },
            },
            product: true,
            model: true,
            brand: true,
          },
        },
      },
    });
  }

  /**
   * Find warranty components with filter, pagination, and sorting
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of warranty components
   */
  async findWithFilter(
    filter: any,
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<Prisma.warranty_componentsGetPayload<{}>[]> {
    try {
      if (!this.model) {
        return [];
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { componentNo: 'asc' },
        include: {
          machine: {
            select: {
              id: true,
              serialNumber: true,
              location: true,
              warranty: {
                select: {
                  id: true,
                  bslNo: true,
                  installDate: true,
                  warrantyDate: true,
                  status: true,
                  customer: {
                    select: {
                      id: true,
                      name: true,
                      city: true,
                    },
                  },
                },
              },
              product: {
                select: {
                  id: true,
                  name: true,
                },
              },
              model: {
                select: {
                  id: true,
                  name: true,
                },
              },
              brand: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('WarrantyComponentRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count warranty components with filter
   * @param filter Filter condition
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any): Promise<number> {
    try {
      if (!this.model) {
        return 0;
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      const count = await this.model.count({
        where: filter,
      });

      return count;
    } catch (error) {
      console.error('WarrantyComponentRepository.countWithFilter: Error executing count:', error);
      throw error;
    }
  }

  /**
   * Validate filter to ensure it only contains valid fields
   * @param filter Filter condition
   */
  private validateFilter(filter: any): void {
    if (!filter || typeof filter !== 'object') {
      return;
    }

    const validFields = [
      'id', 'machineId', 'componentNo', 'serialNumber', 'warrantyDate', 'section',
      'originalWarrantyId', 'originalAssetNo', 'originalComponentNo',
      'createdAt', 'updatedAt'
    ];

    for (const key in filter) {
      if (!validFields.includes(key) && !key.includes('.')) {
        console.warn(`WarrantyComponentRepository.validateFilter: Invalid filter field '${key}' removed`);
        delete filter[key];
      }
    }
  }

  /**
   * Create warranty component with validation
   * @param data Warranty component data
   * @returns Promise resolving to the created warranty component
   */
  async createWithValidation(data: any): Promise<any> {
    // Check if serial number already exists
    if (data.serialNumber) {
      const existing = await this.findBySerialNumber(data.serialNumber);
      if (existing) {
        throw new Error(`Component with serial number ${data.serialNumber} already exists in warranty`);
      }
    }

    return this.create(data);
  }

  /**
   * Update warranty component with validation
   * @param id Warranty component ID
   * @param data Updated warranty component data
   * @returns Promise resolving to the updated warranty component
   */
  async updateWithValidation(id: string, data: any): Promise<any> {
    // Check if serial number already exists (excluding current record)
    if (data.serialNumber) {
      const existing = await this.model.findFirst({
        where: {
          serialNumber: data.serialNumber,
          NOT: { id },
        },
      });
      
      if (existing) {
        throw new Error(`Component with serial number ${data.serialNumber} already exists in warranty`);
      }
    }

    return this.update(id, data);
  }

  /**
   * Bulk create warranty components
   * @param components Array of warranty component data
   * @returns Promise resolving to the created warranty components
   */
  async createMany(components: any[]): Promise<any> {
    return this.prisma.$transaction(async (tx) => {
      const results = [];

      for (const component of components) {
        const created = await tx.warranty_components.create({
          data: component,
        });
        results.push(created);
      }

      return results;
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.warranty_componentsGetPayload<{}>,
    string,
    Prisma.warranty_componentsCreateInput,
    Prisma.warranty_componentsUpdateInput
  > {
    const repo = new WarrantyComponentRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
