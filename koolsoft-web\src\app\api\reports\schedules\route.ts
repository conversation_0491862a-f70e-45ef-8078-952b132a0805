import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ScheduledReportRepository } from '@/lib/repositories/scheduled-report.repository';
import { getReportSchedulerService } from '@/lib/services/report-scheduler.service';
import {
  createScheduledReportSchema,
  listScheduledReportsSchema,
} from '@/lib/validations/scheduled-report.schema';
import { getSession } from 'next-auth/react';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

/**
 * GET /api/reports/schedules
 * List scheduled reports with filtering and pagination
 */
async function getScheduledReports(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = listScheduledReportsSchema.parse(params);
    
    const scheduledReportRepository = new ScheduledReportRepository();
    const result = await scheduledReportRepository.findMany(validatedParams);
    
    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching scheduled reports:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch scheduled reports' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports/schedules
 * Create a new scheduled report
 */
async function createScheduledReport(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate request body
    const validatedData = createScheduledReportSchema.parse(body);
    
    const scheduledReportRepository = new ScheduledReportRepository();
    
    // Create the scheduled report
    const newScheduledReport = await scheduledReportRepository.create({
      ...validatedData,
      createdBy: session.user.id,
    });
    
    // Schedule the report with the scheduler service
    const schedulerService = getReportSchedulerService();
    await schedulerService.scheduleReport(
      newScheduledReport.id,
      validatedData.cronExpression
    );
    
    return NextResponse.json({
      success: true,
      message: 'Scheduled report created successfully',
      data: newScheduledReport,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating scheduled report:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create scheduled report' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getScheduledReports
);

export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  createScheduledReport
);
