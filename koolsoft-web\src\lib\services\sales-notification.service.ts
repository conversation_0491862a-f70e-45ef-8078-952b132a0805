import { 
  getNotificationPreferenceRepository,
  getSalesNotificationEventRepository,
  getSalesNotificationQueueRepository,
  getUserRepository
} from '@/lib/repositories';
import { getEmailService } from './email.service';

/**
 * Sales Notification Service
 *
 * This service handles the creation and processing of sales event notifications.
 * It manages the notification workflow from event creation to email delivery.
 */
export class SalesNotificationService {
  private notificationPreferenceRepository = getNotificationPreferenceRepository();
  private salesNotificationEventRepository = getSalesNotificationEventRepository();
  private salesNotificationQueueRepository = getSalesNotificationQueueRepository();
  private userRepository = getUserRepository();
  private emailService = getEmailService();

  /**
   * Create a sales notification event
   * @param eventData Event data
   * @returns Promise resolving to the created event
   */
  async createSalesEvent(eventData: {
    eventType: string;
    entityType: string;
    entityId: string;
    userId?: string;
    customerId?: string;
    executiveId?: string;
    oldStatus?: string;
    newStatus?: string;
    eventData?: any;
  }) {
    try {
      // Create the notification event
      const event = await this.salesNotificationEventRepository.createEvent(eventData);

      // Process the event asynchronously (don't wait for completion)
      this.processEventAsync(event.id).catch(error => {
        console.error('Error processing sales notification event:', error);
      });

      return event;
    } catch (error) {
      console.error('Error creating sales notification event:', error);
      throw error;
    }
  }

  /**
   * Process a notification event asynchronously
   * @param eventId Event ID
   */
  private async processEventAsync(eventId: string) {
    try {
      const event = await this.salesNotificationEventRepository.findById(eventId);
      if (!event) {
        console.error(`Sales notification event ${eventId} not found`);
        return;
      }

      // Determine notification type based on event
      const notificationType = this.getNotificationTypeFromEvent(event.eventType, event.entityType);
      if (!notificationType) {
        console.log(`No notification type mapped for event ${event.eventType} on ${event.entityType}`);
        await this.salesNotificationEventRepository.markAsProcessed([eventId]);
        return;
      }

      // Find users who should receive this notification
      const recipients = await this.findNotificationRecipients(notificationType, event);

      // Create queue items for each recipient
      for (const recipient of recipients) {
        await this.queueNotification(event, recipient, notificationType);
      }

      // Mark event as processed
      await this.salesNotificationEventRepository.markAsProcessed([eventId]);
    } catch (error) {
      console.error(`Error processing sales notification event ${eventId}:`, error);
    }
  }

  /**
   * Get notification type from event type and entity type
   * @param eventType Event type
   * @param entityType Entity type
   * @returns Notification type or null if not mapped
   */
  private getNotificationTypeFromEvent(eventType: string, entityType: string): string | null {
    const eventMap: Record<string, string> = {
      'LEAD_CREATED': 'salesLeadCreated',
      'LEAD_STATUS_CHANGED': 'salesLeadStatusChanged',
      'OPPORTUNITY_CREATED': 'salesOpportunityCreated',
      'OPPORTUNITY_STATUS_CHANGED': 'salesOpportunityStatusChanged',
      'PROSPECT_CREATED': 'salesProspectCreated',
      'PROSPECT_STATUS_CHANGED': 'salesProspectStatusChanged',
      'ORDER_CREATED': 'salesOrderCreated',
      'ORDER_STATUS_CHANGED': 'salesOrderStatusChanged',
      'CONVERSION_EVENT': 'salesConversionEvents',
    };

    return eventMap[eventType] || null;
  }

  /**
   * Find users who should receive a notification
   * @param notificationType Notification type
   * @param event Event data
   * @returns Promise resolving to an array of recipients
   */
  private async findNotificationRecipients(notificationType: string, event: any): Promise<Array<{
    userId: string;
    email: string;
    name: string;
    role: string;
  }>> {
    try {
      // Get users with this notification type enabled
      const recipients = await this.notificationPreferenceRepository.findUsersWithNotificationEnabled(
        notificationType as any,
        {
          isActive: true,
        }
      );

      // Filter based on role and relationship to the event
      const filteredRecipients = recipients.filter(recipient => {
        // Always include admins and managers
        if (['ADMIN', 'MANAGER'].includes(recipient.role.toUpperCase())) {
          return true;
        }

        // Include executives if they are assigned to the entity
        if (recipient.role.toUpperCase() === 'EXECUTIVE') {
          return event.executiveId === recipient.userId || event.userId === recipient.userId;
        }

        // Include users if they created the event
        if (recipient.role.toUpperCase() === 'USER') {
          return event.userId === recipient.userId;
        }

        return false;
      });

      return filteredRecipients;
    } catch (error) {
      console.error('Error finding notification recipients:', error);
      return [];
    }
  }

  /**
   * Queue a notification for delivery
   * @param event Event data
   * @param recipient Recipient data
   * @param notificationType Notification type
   */
  private async queueNotification(event: any, recipient: any, notificationType: string) {
    try {
      // Determine template name and priority
      const templateName = this.getTemplateNameForEvent(event.eventType, event.entityType);
      const priority = this.getPriorityForEvent(event.eventType, event.entityType);

      // Prepare template data
      const templateData = {
        recipientName: recipient.name,
        eventType: event.eventType,
        entityType: event.entityType,
        entityId: event.entityId,
        oldStatus: event.oldStatus,
        newStatus: event.newStatus,
        customerName: event.customer?.name,
        customerEmail: event.customer?.email,
        executiveName: event.executive?.name,
        userName: event.user?.name,
        eventData: event.eventData,
        createdAt: event.createdAt,
      };

      // Add to queue
      await this.salesNotificationQueueRepository.addToQueue({
        eventId: event.id,
        recipientUserId: recipient.userId,
        recipientEmail: recipient.email,
        templateName,
        templateData,
        priority,
      });
    } catch (error) {
      console.error('Error queueing notification:', error);
    }
  }

  /**
   * Get template name for event
   * @param eventType Event type
   * @param entityType Entity type
   * @returns Template name
   */
  private getTemplateNameForEvent(eventType: string, entityType: string): string {
    const templateMap: Record<string, string> = {
      'LEAD_CREATED': 'sales-lead-created',
      'LEAD_STATUS_CHANGED': 'sales-lead-status-changed',
      'OPPORTUNITY_CREATED': 'sales-opportunity-created',
      'OPPORTUNITY_STATUS_CHANGED': 'sales-opportunity-status-changed',
      'PROSPECT_CREATED': 'sales-prospect-created',
      'PROSPECT_STATUS_CHANGED': 'sales-prospect-status-changed',
      'ORDER_CREATED': 'sales-order-created',
      'ORDER_STATUS_CHANGED': 'sales-order-status-changed',
      'CONVERSION_EVENT': 'sales-conversion-event',
    };

    return templateMap[eventType] || 'sales-generic-notification';
  }

  /**
   * Get priority for event
   * @param eventType Event type
   * @param entityType Entity type
   * @returns Priority level
   */
  private getPriorityForEvent(eventType: string, entityType: string): string {
    const highPriorityEvents = ['ORDER_CREATED', 'CONVERSION_EVENT'];
    return highPriorityEvents.includes(eventType) ? 'HIGH' : 'NORMAL';
  }

  /**
   * Process pending notifications in the queue
   * @param limit Maximum number of notifications to process
   * @returns Promise resolving to the number of processed notifications
   */
  async processPendingNotifications(limit: number = 50): Promise<number> {
    try {
      const pendingNotifications = await this.salesNotificationQueueRepository.getPendingNotifications(limit);
      let processedCount = 0;

      for (const notification of pendingNotifications) {
        try {
          // Update status to processing
          await this.salesNotificationQueueRepository.updateStatus(notification.id, 'PROCESSING');

          // Send email
          const emailLog = await this.emailService.sendTemplateEmail(
            notification.templateName,
            notification.templateData as Record<string, any> || {},
            notification.recipientEmail,
            undefined, // cc
            undefined, // bcc
            notification.event.customerId || undefined,
            notification.recipientUserId
          );

          // Update status to sent
          await this.salesNotificationQueueRepository.updateStatus(notification.id, 'SENT', {
            sentAt: new Date(),
            emailLogId: emailLog.id,
          });

          processedCount++;
        } catch (error) {
          console.error(`Error processing notification ${notification.id}:`, error);

          // Increment attempt count
          await this.salesNotificationQueueRepository.incrementAttempt(
            notification.id,
            error instanceof Error ? error.message : String(error)
          );
        }
      }

      return processedCount;
    } catch (error) {
      console.error('Error processing pending notifications:', error);
      return 0;
    }
  }

  /**
   * Create or update notification preferences for a user
   * @param userId User ID
   * @param preferences Notification preferences
   * @returns Promise resolving to the updated preferences
   */
  async updateNotificationPreferences(userId: string, preferences: any) {
    try {
      return await this.notificationPreferenceRepository.upsertByUserId(userId, preferences);
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get notification preferences for a user
   * @param userId User ID
   * @returns Promise resolving to the user's notification preferences
   */
  async getNotificationPreferences(userId: string) {
    try {
      let preferences = await this.notificationPreferenceRepository.findByUserId(userId);

      if (!preferences) {
        // Create default preferences based on user role
        const user = await this.userRepository.findById(userId);
        if (user) {
          const defaultPreferences = this.notificationPreferenceRepository.getDefaultPreferences(user.role);
          preferences = await this.notificationPreferenceRepository.upsertByUserId(userId, defaultPreferences);
        }
      }

      return preferences;
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics
   * @param startDate Start date for statistics
   * @param endDate End date for statistics
   * @returns Promise resolving to notification statistics
   */
  async getNotificationStatistics(startDate?: Date, endDate?: Date) {
    try {
      const [eventStats, queueStats, preferenceStats] = await Promise.all([
        this.salesNotificationEventRepository.getStatistics(startDate, endDate),
        this.salesNotificationQueueRepository.getStatistics(startDate, endDate),
        this.notificationPreferenceRepository.getStatistics(),
      ]);

      return {
        events: eventStats,
        queue: queueStats,
        preferences: preferenceStats,
      };
    } catch (error) {
      console.error('Error getting notification statistics:', error);
      throw error;
    }
  }
}

/**
 * Get the sales notification service instance
 * @returns Sales notification service instance
 */
export function getSalesNotificationService(): SalesNotificationService {
  return new SalesNotificationService();
}
