'use client';

import { useState, useEffect, useRef } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Search,
  X,
  Filter,
  ChevronDown,
  ChevronUp,
  History,
  BookmarkPlus
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Define the filter form schema
const filterFormSchema = z.object({
  name: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pinCode: z.string().optional(),
  location: z.string().optional(),
  isActive: z.string().optional(),
  segment: z.string().optional(),
  createdAfter: z.string().optional(),
  createdBefore: z.string().optional(),
  searchAll: z.string().optional(), // New field for searching across all text fields
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Define the form values type
type FilterFormValues = z.infer<typeof filterFormSchema>;

// Define the component props
interface CustomerFilterFormProps {
  onFilter: (values: FilterFormValues) => void;
  initialValues?: Partial<FilterFormValues>;
}

/**
 * Customer Filter Form Component
 *
 * This component provides a form for filtering and sorting customers.
 * It includes real-time search functionality and advanced filtering options.
 */
export function CustomerFilterForm({ onFilter, initialValues }: CustomerFilterFormProps) {
  const [expanded, setExpanded] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [savedSearches, setSavedSearches] = useState<{name: string, values: FilterFormValues}[]>([]);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<FilterFormValues>({
    resolver: zodResolver(filterFormSchema),
    defaultValues: {
      name: initialValues?.name || '',
      email: initialValues?.email || '',
      phone: initialValues?.phone || '',
      city: initialValues?.city || '',
      state: initialValues?.state || '',
      pinCode: initialValues?.pinCode || '',
      location: initialValues?.location || '',
      isActive: initialValues?.isActive || 'all',
      segment: initialValues?.segment || 'all',
      createdAfter: initialValues?.createdAfter || '',
      createdBefore: initialValues?.createdBefore || '',
      searchAll: initialValues?.searchAll || '',
      sortBy: initialValues?.sortBy || 'name',
      sortOrder: initialValues?.sortOrder || 'asc',
    },
  });

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    const storedSearches = localStorage.getItem('recentCustomerSearches');
    if (storedSearches) {
      setRecentSearches(JSON.parse(storedSearches));
    }

    const storedSavedSearches = localStorage.getItem('savedCustomerSearches');
    if (storedSavedSearches) {
      setSavedSearches(JSON.parse(storedSavedSearches));
    }
  }, []);

  // Focus search input when component mounts
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Handle real-time search with debounce
  const handleSearchChange = (value: string) => {
    // Clear any existing timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Set a new timeout for debouncing
    searchTimeout.current = setTimeout(() => {
      const values = form.getValues();
      values.searchAll = value;
      onFilter(values);

      // Add to recent searches if not empty and not already in the list
      if (value && !recentSearches.includes(value)) {
        const newRecentSearches = [value, ...recentSearches.slice(0, 4)];
        setRecentSearches(newRecentSearches);
        localStorage.setItem('recentCustomerSearches', JSON.stringify(newRecentSearches));
      }
    }, 500); // 500ms debounce
  };

  // Handle form submission
  const onSubmit = (values: FilterFormValues) => {
    // Convert "all" values to empty strings for API filtering
    const apiValues = { ...values };
    if (apiValues.segment === 'all') apiValues.segment = '';
    onFilter(apiValues);
  };

  // Handle form reset
  const handleReset = () => {
    form.reset({
      name: '',
      email: '',
      phone: '',
      city: '',
      state: '',
      pinCode: '',
      location: '',
      isActive: 'all',
      segment: 'all',
      createdAfter: '',
      createdBefore: '',
      searchAll: '',
      sortBy: 'name',
      sortOrder: 'asc',
    });
    onFilter({
      name: '',
      email: '',
      phone: '',
      city: '',
      state: '',
      pinCode: '',
      location: '',
      isActive: 'all',
      segment: 'all',
      createdAfter: '',
      createdBefore: '',
      searchAll: '',
      sortBy: 'name',
      sortOrder: 'asc',
    });
  };

  // Handle saving current search
  const handleSaveSearch = () => {
    const values = form.getValues();
    const searchName = prompt('Enter a name for this search:');
    if (searchName) {
      const newSavedSearches = [...savedSearches, { name: searchName, values }];
      setSavedSearches(newSavedSearches);
      localStorage.setItem('savedCustomerSearches', JSON.stringify(newSavedSearches));
    }
  };

  // Handle applying a saved search
  const applySavedSearch = (values: FilterFormValues) => {
    form.reset(values);
    onFilter(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Global search field */}
          <FormField
            control={form.control}
            name="searchAll"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search customers by name, email, phone, city..."
                      className="pl-9"
                      {...field}
                      ref={searchInputRef}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSearchChange(e.target.value);
                      }}
                      onFocus={() => setShowRecentSearches(true)}
                      onBlur={() => setTimeout(() => setShowRecentSearches(false), 200)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          form.handleSubmit(onSubmit)();
                        }
                      }}
                    />
                    <div className="absolute right-2.5 top-2.5 flex items-center gap-1">
                      {field.value && (
                        <button
                          type="button"
                          onClick={() => {
                            field.onChange('');
                            form.handleSubmit(onSubmit)();
                          }}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              onClick={() => setShowRecentSearches(!showRecentSearches)}
                              className="text-gray-500 hover:text-gray-700 ml-1"
                            >
                              <History className="h-4 w-4" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Recent searches</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              onClick={handleSaveSearch}
                              className="text-gray-500 hover:text-gray-700 ml-1"
                            >
                              <BookmarkPlus className="h-4 w-4" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Save this search</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    {/* Recent searches dropdown */}
                    {showRecentSearches && recentSearches.length > 0 && (
                      <div className="absolute z-10 w-full bg-white border border-gray-200 rounded-md shadow-lg mt-1 max-h-60 overflow-auto">
                        <div className="p-2 text-sm font-medium text-gray-700 border-b">
                          Recent Searches
                        </div>
                        <ul>
                          {recentSearches.map((search, index) => (
                            <li
                              key={index}
                              className="px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                              onClick={() => {
                                field.onChange(search);
                                form.handleSubmit(onSubmit)();
                                setShowRecentSearches(false);
                              }}
                            >
                              <History className="h-3 w-3 mr-2 text-gray-500" />
                              {search}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          <Button
            type="button"
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => setExpanded(!expanded)}
          >
            <Filter className="h-4 w-4" />
            Advanced Filters
            {expanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>

          <Button type="submit" variant="default">
            Apply Filters
          </Button>

          <Button type="button" variant="outline" onClick={handleReset}>
            Reset
          </Button>
        </div>

        {/* Saved searches */}
        {savedSearches.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            <span className="text-sm text-gray-500 mt-1">Saved searches:</span>
            {savedSearches.map((saved, index) => (
              <Button
                key={index}
                type="button"
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => applySavedSearch(saved.values)}
              >
                <BookmarkPlus className="h-3 w-3 mr-1" />
                {saved.name}
              </Button>
            ))}
          </div>
        )}

        {expanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Basic Information Filters */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by name..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by email..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by phone..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="true">Active</SelectItem>
                      <SelectItem value="false">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            {/* Location Filters */}
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by city..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by state..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pinCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pin Code</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by pin code..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="Filter by location..." {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Date Filters */}
            <FormField
              control={form.control}
              name="createdAfter"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Created After</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="createdBefore"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Created Before</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="segment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Segment</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select segment" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All Segments</SelectItem>
                      <SelectItem value="corporate">Corporate</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="government">Government</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            {/* Sorting Options */}
            <FormField
              control={form.control}
              name="sortBy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sort By</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="city">City</SelectItem>
                      <SelectItem value="state">State</SelectItem>
                      <SelectItem value="location">Location</SelectItem>
                      <SelectItem value="createdAt">Created Date</SelectItem>
                      <SelectItem value="updatedAt">Updated Date</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sortOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sort Order</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sort order" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="asc">Ascending</SelectItem>
                      <SelectItem value="desc">Descending</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>
        )}
      </form>
    </Form>
  );
}
