'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, Save, RefreshCw } from 'lucide-react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import { format } from 'date-fns';

// Renewal form schema
const renewalFormSchema = z.object({
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number'
  }).nonnegative('Amount must be a positive number'),
  warningDate: z.string().optional(),
  numberOfServices: z.number().int().nonnegative().optional(),
  remarks: z.string().optional(),
  carryForwardMachines: z.boolean().default(true),
  carryForwardDivisions: z.boolean().default(true),
}).refine(
  (data) => {
    if (data.startDate && data.endDate) {
      return new Date(data.endDate) > new Date(data.startDate);
    }
    return true;
  },
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
);

type RenewalFormData = z.infer<typeof renewalFormSchema>;

interface AMCContract {
  id: string;
  customerId: string;
  customer: {
    id: string;
    name: string;
    email?: string;
  };
  contactPersonId?: string;
  executiveId?: string;
  natureOfService?: string;
  startDate: string;
  endDate: string;
  warningDate?: string;
  amount: number;
  numberOfServices?: number;
  numberOfMachines?: number;
  totalTonnage?: number;
  status: string;
  contractNumber?: string;
  remarks?: string;
  machines?: any[];
  divisions?: any[];
}

interface AMCRenewalFormProps {
  contract: AMCContract;
  onSuccess: (renewedContract: any) => void;
}

/**
 * AMC Renewal Form Component
 * 
 * This component provides a form for renewing AMC contracts with pre-populated data
 * from the existing contract and options to carry forward machines and divisions.
 */
export function AMCRenewalForm({ contract, onSuccess }: AMCRenewalFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate suggested dates (1 year from current end date)
  const currentEndDate = new Date(contract.endDate);
  const suggestedStartDate = new Date(currentEndDate);
  suggestedStartDate.setDate(suggestedStartDate.getDate() + 1); // Start day after current end
  
  const suggestedEndDate = new Date(suggestedStartDate);
  suggestedEndDate.setFullYear(suggestedEndDate.getFullYear() + 1); // 1 year later

  const suggestedWarningDate = new Date(suggestedEndDate);
  suggestedWarningDate.setDate(suggestedWarningDate.getDate() - 30); // 30 days before end

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<RenewalFormData>({
    resolver: zodResolver(renewalFormSchema) as any,
    defaultValues: {
      startDate: format(suggestedStartDate, 'yyyy-MM-dd'),
      endDate: format(suggestedEndDate, 'yyyy-MM-dd'),
      amount: contract.amount,
      warningDate: format(suggestedWarningDate, 'yyyy-MM-dd'),
      numberOfServices: contract.numberOfServices || 4,
      remarks: `Renewed from contract ${contract.contractNumber || contract.id}`,
      carryForwardMachines: true,
      carryForwardDivisions: true,
    }
  });

  const watchedValues = watch();

  // Handle form submission
  const onSubmit = async (data: RenewalFormData) => {
    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/amc/contracts/${contract.id}/renew`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          startDate: data.startDate,
          endDate: data.endDate,
          amount: data.amount,
          warningDate: data.warningDate || undefined,
          numberOfServices: data.numberOfServices || undefined,
          remarks: data.remarks || undefined,
          carryForwardMachines: data.carryForwardMachines,
          carryForwardDivisions: data.carryForwardDivisions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to renew contract');
      }

      const renewedContract = await response.json();
      
      showSuccessToast(
        'Contract Renewed Successfully',
        `New AMC contract has been created for ${contract.customer.name}`
      );

      onSuccess(renewedContract);
    } catch (error) {
      console.error('Error renewing contract:', error);
      showErrorToast(
        'Renewal Failed',
        error instanceof Error ? error.message : 'Failed to renew contract'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
      {/* Current Contract Information */}
      <Card>
        <CardHeader className="pb-3 bg-secondary">
          <CardTitle className="text-black">Current Contract Information</CardTitle>
          <CardDescription className="text-black">
            Review the details of the contract being renewed
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label className="text-black font-medium">Customer</Label>
              <p className="text-black">{contract.customer.name}</p>
            </div>
            <div>
              <Label className="text-black font-medium">Current Period</Label>
              <p className="text-black">
                {format(new Date(contract.startDate), 'MMM dd, yyyy')} - {format(new Date(contract.endDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <Label className="text-black font-medium">Current Amount</Label>
              <p className="text-black">₹{contract.amount.toLocaleString()}</p>
            </div>
            <div>
              <Label className="text-black font-medium">Machines</Label>
              <p className="text-black">{contract.numberOfMachines || contract.machines?.length || 0} machines</p>
            </div>
            <div>
              <Label className="text-black font-medium">Services</Label>
              <p className="text-black">{contract.numberOfServices || 'Not specified'} services</p>
            </div>
            <div>
              <Label className="text-black font-medium">Status</Label>
              <p className="text-black">{contract.status}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Renewal Details */}
      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <CardTitle>New Contract Details</CardTitle>
          <CardDescription className="text-gray-100">
            Configure the details for the renewed contract
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          {/* Contract Dates */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate" className="text-black">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
                className={errors.startDate ? 'border-destructive' : ''}
              />
              {errors.startDate && (
                <p className="text-sm text-destructive">{errors.startDate.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate" className="text-black">End Date *</Label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
                className={errors.endDate ? 'border-destructive' : ''}
              />
              {errors.endDate && (
                <p className="text-sm text-destructive">{errors.endDate.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="warningDate" className="text-black">Warning Date</Label>
              <Input
                id="warningDate"
                type="date"
                {...register('warningDate')}
              />
            </div>
          </div>

          <Separator />

          {/* Contract Terms */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-black">Contract Amount (₹) *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                {...register('amount', { valueAsNumber: true })}
                className={errors.amount ? 'border-destructive' : ''}
              />
              {errors.amount && (
                <p className="text-sm text-destructive">{errors.amount.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="numberOfServices" className="text-black">Number of Services</Label>
              <Input
                id="numberOfServices"
                type="number"
                {...register('numberOfServices', { valueAsNumber: true })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-black">Remarks</Label>
            <Textarea
              id="remarks"
              {...register('remarks')}
              placeholder="Additional notes about the renewal..."
              rows={3}
            />
          </div>

          <Separator />

          {/* Carry Forward Options */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-black">Carry Forward Options</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="carryForwardMachines"
                  checked={watchedValues.carryForwardMachines}
                  onCheckedChange={(checked) => setValue('carryForwardMachines', !!checked)}
                />
                <Label htmlFor="carryForwardMachines" className="text-black">
                  Carry forward machines ({contract.numberOfMachines || contract.machines?.length || 0} machines)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="carryForwardDivisions"
                  checked={watchedValues.carryForwardDivisions}
                  onCheckedChange={(checked) => setValue('carryForwardDivisions', !!checked)}
                />
                <Label htmlFor="carryForwardDivisions" className="text-black">
                  Carry forward division assignments ({contract.divisions?.length || 0} divisions)
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-end space-x-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Renewing...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Renew Contract
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
