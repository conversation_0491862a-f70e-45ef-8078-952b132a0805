import { NextRequest, NextResponse } from 'next/server';
import { getOutWarrantyRepository } from '@/lib/repositories';
import { createOutWarrantySchema, outWarrantyFilterSchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * Query parameters schema for out-warranty filtering (API version)
 */
const outWarrantyApiFilterSchema = z.object({
  skip: z.string().optional().transform(val => val ? parseInt(val) : 0),
  take: z.string().optional().transform(val => val ? parseInt(val) : 50),
  search: z.string().optional(),
  customerId: z.string().optional(),
  executiveId: z.string().optional(),
  source: z.string().optional(),
  isActive: z.string().optional().transform(val => val ? val === 'true' : undefined),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  sortBy: z.string().optional().default('startDate'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

/**
 * GET /api/out-warranties
 * Get out-warranties with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const params = Object.fromEntries(searchParams.entries());
      const validatedParams = outWarrantyApiFilterSchema.parse(params);

      const outWarrantyRepository = getOutWarrantyRepository();

      // Build filter object
      const filter: any = {};

      // Apply filters
      if (validatedParams.customerId) {
        filter.customerId = validatedParams.customerId;
      }

      if (validatedParams.executiveId) {
        filter.executiveId = validatedParams.executiveId;
      }

      if (validatedParams.source) {
        filter.source = validatedParams.source;
      }

      if (validatedParams.isActive !== undefined) {
        filter.isActive = validatedParams.isActive;
      }

      if (validatedParams.startDate) {
        filter.startDate = {
          gte: new Date(validatedParams.startDate),
        };
      }

      if (validatedParams.endDate) {
        filter.endDate = {
          lte: new Date(validatedParams.endDate),
        };
      }

      // Build order by object
      const orderBy: any = {};
      orderBy[validatedParams.sortBy] = validatedParams.sortOrder;

      let outWarranties;
      let total;

      if (validatedParams.search) {
        // Use search method if search term provided
        outWarranties = await outWarrantyRepository.search(
          validatedParams.search,
          validatedParams.skip,
          validatedParams.take
        );
        
        // For search, we need to count separately
        total = outWarranties.length; // This is approximate for search results
      } else {
        // Use regular filtering
        outWarranties = await outWarrantyRepository.findWithRelations(
          filter,
          validatedParams.skip,
          validatedParams.take,
          orderBy
        );

        // Get total count for pagination
        total = await outWarrantyRepository.countByFilter(filter);
      }

      // Transform data for frontend consumption
      const transformedOutWarranties = outWarranties.map(outWarranty => ({
        id: outWarranty.id,
        customer: {
          id: outWarranty.customer.id,
          name: outWarranty.customer.name,
          city: outWarranty.customer.city,
          phone: outWarranty.customer.phone,
        },
        executive: outWarranty.executive ? {
          id: outWarranty.executive.id,
          name: outWarranty.executive.name,
          email: outWarranty.executive.email,
        } : null,
        contactPerson: outWarranty.contactPerson,
        contactPhone: outWarranty.contactPhone,
        startDate: outWarranty.startDate,
        endDate: outWarranty.endDate,
        amount: Number(outWarranty.amount),
        source: outWarranty.source,
        isActive: outWarranty.isActive,
        machines: outWarranty.machines?.map((machine: any) => ({
          id: machine.id,
          serialNumber: machine.serialNumber,
          product: machine.model?.product?.name,
          model: machine.model?.name,
          brand: machine.model?.product?.brand?.name,
          components: machine.components?.length || 0,
        })) || [],
        totalPayments: outWarranty.payments?.reduce((sum: number, payment: any) => 
          sum + Number(payment.amount), 0) || 0,
        balance: Number(outWarranty.amount) - (outWarranty.payments?.reduce((sum: number, payment: any) => 
          sum + Number(payment.amount), 0) || 0),
        lastServiceDate: outWarranty.payments?.length > 0 ? 
          outWarranty.payments[0].paymentDate : null,
        status: outWarranty.isActive ? 'ACTIVE' : 'INACTIVE',
        serviceRequests: outWarranty.payments?.length || 0,
      }));

      return NextResponse.json({
        outWarranties: transformedOutWarranties,
        pagination: {
          total,
          skip: validatedParams.skip,
          take: validatedParams.take,
          hasMore: validatedParams.skip + validatedParams.take < total,
        },
      });

    } catch (error) {
      console.error('Error fetching out-warranties:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch out-warranties' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/out-warranties
 * Create a new out-warranty record
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = createOutWarrantySchema.parse(body);

        const outWarrantyRepository = getOutWarrantyRepository();

        // Create the out-warranty record
        const outWarranty = await outWarrantyRepository.create({
          customer: { connect: { id: validatedData.customerId } },
          executive: { connect: { id: validatedData.executiveId } },
          contactPerson: validatedData.contactPerson,
          contactPhone: validatedData.contactPhone,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          amount: validatedData.amount,
          source: validatedData.source,
          sourceId: validatedData.sourceId,
          // amcSourceId: validatedData.amcSourceId, // TODO: Add amcSourceId field to schema
          inWarrantySourceId: validatedData.inWarrantySourceId,
          isActive: validatedData.isActive,
        });

        // Fetch the created record with relations
        const createdOutWarranty = await outWarrantyRepository.findWithAllRelations(outWarranty.id);

        return NextResponse.json(createdOutWarranty, { status: 201 });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating out-warranty:', error);
      return NextResponse.json(
        { error: 'Failed to create out-warranty' },
        { status: 500 }
      );
    }
  }
);
