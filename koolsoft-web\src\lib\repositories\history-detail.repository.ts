import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * History Detail Repository
 * 
 * This repository handles database operations for the History Detail entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class HistoryDetailRepository extends PrismaRepository<
  Prisma.HistoryAmcDetailGetPayload<{}>,
  string,
  Prisma.HistoryAmcDetailCreateInput,
  Prisma.HistoryAmcDetailUpdateInput
> {
  constructor() {
    super('historyAmcDetail');
  }

  /**
   * Find history details by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history details
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.HistoryAmcDetailGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { date: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find history details by date range
   * @param startDate Start date
   * @param endDate End date
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history details
   */
  async findByDateRange(startDate: Date, endDate: Date, skip?: number, take?: number): Promise<Prisma.HistoryAmcDetailGetPayload<{}>[]> {
    return this.model.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      skip,
      take,
      orderBy: { date: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find history details by user ID
   * @param userId User ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history details
   */
  async findByUserId(userId: string, skip?: number, take?: number): Promise<Prisma.HistoryAmcDetailGetPayload<{}>[]> {
    return this.model.findMany({
      where: { userId },
      skip,
      take,
      orderBy: { date: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find history details by type
   * @param type History detail type
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of history details
   */
  async findByType(type: string, skip?: number, take?: number): Promise<Prisma.HistoryAmcDetailGetPayload<{}>[]> {
    return this.model.findMany({
      where: { type },
      skip,
      take,
      orderBy: { date: 'desc' },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Find history detail with all related data
   * @param id History detail ID
   * @returns Promise resolving to the history detail with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: true,
        user: true,
      },
    });
  }

  /**
   * Create a new repository instance with a transaction client
   * @param tx Transaction client
   * @returns Repository instance with transaction client
   */
  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.HistoryAmcDetailGetPayload<{}>,
    string,
    Prisma.HistoryAmcDetailCreateInput,
    Prisma.HistoryAmcDetailUpdateInput
  > {
    const repo = new HistoryDetailRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
