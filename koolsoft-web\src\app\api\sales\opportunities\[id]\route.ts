import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesOpportunityRepository } from '@/lib/repositories';
import { updateSalesOpportunitySchema } from '@/lib/validations/sales.schema';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/sales/opportunities/[id]
 * Get a specific sales opportunity by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesOpportunityRepository = getSalesOpportunityRepository();
      const salesOpportunity = await salesOpportunityRepository.findById(id);

      if (!salesOpportunity) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales opportunity not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: salesOpportunity,
      });
    } catch (error) {
      console.error('Error fetching sales opportunity:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales opportunity',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/sales/opportunities/[id]
 * Update a specific sales opportunity
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateSalesOpportunitySchema.parse({ ...body, id });

      const salesOpportunityRepository = getSalesOpportunityRepository();

      // Check if sales opportunity exists
      const existingSalesOpportunity = await salesOpportunityRepository.findById(id);
      if (!existingSalesOpportunity) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales opportunity not found',
          },
          { status: 404 }
        );
      }

      // Update sales opportunity
      const updateData: any = {};
      
      if (validatedData.customerId) updateData.customerId = validatedData.customerId;
      if (validatedData.executiveId) updateData.executiveId = validatedData.executiveId;
      if (validatedData.opportunityDate) updateData.opportunityDate = validatedData.opportunityDate;
      if (validatedData.contactPerson !== undefined) updateData.contactPerson = validatedData.contactPerson;
      if (validatedData.contactPhone !== undefined) updateData.contactPhone = validatedData.contactPhone;
      if (validatedData.status) updateData.status = validatedData.status;
      if (validatedData.prospectPercentage !== undefined) updateData.prospectPercentage = validatedData.prospectPercentage;
      if (validatedData.followUpDate !== undefined) updateData.followUpDate = validatedData.followUpDate;
      if (validatedData.nextVisitDate !== undefined) updateData.nextVisitDate = validatedData.nextVisitDate;
      if (validatedData.remarks !== undefined) updateData.remarks = validatedData.remarks;

      const updatedSalesOpportunity = await salesOpportunityRepository.update(id, updateData);

      // Create notification event for status change
      if (validatedData.status && validatedData.status !== existingSalesOpportunity.status) {
        try {
          const session = await getServerSession(authOptions);
          const salesNotificationService = getSalesNotificationService();

          await salesNotificationService.createSalesEvent({
            eventType: 'OPPORTUNITY_STATUS_CHANGED',
            entityType: 'opportunity',
            entityId: id,
            userId: session?.user?.id,
            customerId: existingSalesOpportunity.customerId,
            executiveId: existingSalesOpportunity.executiveId,
            oldStatus: existingSalesOpportunity.status,
            newStatus: validatedData.status,
            eventData: {
              contactPerson: existingSalesOpportunity.contactPerson,
              contactPhone: existingSalesOpportunity.contactPhone,
              prospectPercentage: validatedData.prospectPercentage || existingSalesOpportunity.prospectPercentage,
            },
          });
        } catch (notificationError) {
          console.error('Error creating opportunity status change notification:', notificationError);
          // Don't fail the request if notification fails
        }
      }

      return NextResponse.json({
        success: true,
        data: updatedSalesOpportunity,
        message: 'Sales opportunity updated successfully',
      });
    } catch (error) {
      console.error('Error updating sales opportunity:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update sales opportunity',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/sales/opportunities/[id]
 * Delete a specific sales opportunity
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesOpportunityRepository = getSalesOpportunityRepository();

      // Check if sales opportunity exists
      const existingSalesOpportunity = await salesOpportunityRepository.findById(id);
      if (!existingSalesOpportunity) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales opportunity not found',
          },
          { status: 404 }
        );
      }

      // Delete sales opportunity
      await salesOpportunityRepository.delete(id);

      return NextResponse.json({
        success: true,
        message: 'Sales opportunity deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting sales opportunity:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to delete sales opportunity',
        },
        { status: 500 }
      );
    }
  }
);
