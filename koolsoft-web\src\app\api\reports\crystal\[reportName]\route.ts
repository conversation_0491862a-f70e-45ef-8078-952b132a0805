import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { CrystalReportRepository } from '@/lib/repositories/crystal-report.repository';
import { z } from 'zod';

/**
 * Crystal Report Parameters Schema
 */
const crystalReportParamsSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  customerId: z.string().optional(),
  executiveId: z.string().optional(),
  amcStatus: z.string().optional(),
  warrantyStatus: z.string().optional(),
  serviceStatus: z.string().optional(),
  contractType: z.string().optional(),
  groupBy: z.enum(['customer', 'executive', 'status', 'none']).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => parseInt(val) || 50).optional(),
});

/**
 * GET /api/reports/crystal/[reportName]
 * Get Crystal Report data
 * Accessible by ADMIN, MANAGER, EXECUTIVE roles
 */
async function getCrystalReport(
  request: NextRequest,
  { params }: { params: { reportName: string } }
) {
  try {
    const { reportName } = params;
    const { searchParams } = new URL(request.url);
    
    // Extract and validate parameters
    const rawParams: any = {};
    searchParams.forEach((value, key) => {
      if (value) {
        rawParams[key] = value;
      }
    });
    
    const validatedParams = crystalReportParamsSchema.parse(rawParams);
    
    // Initialize repository
    const crystalReportRepository = new CrystalReportRepository();
    
    // Route to appropriate report method
    let result;
    switch (reportName.toLowerCase()) {
      case 'amc-summary':
        result = await crystalReportRepository.getAMCSummaryReport(validatedParams);
        break;
        
      case 'amc-detail':
        result = await crystalReportRepository.getAMCDetailReport(validatedParams);
        break;
        
      case 'warranty-summary':
        result = await crystalReportRepository.getWarrantySummaryReport(validatedParams);
        break;
        
      case 'warranty-detail':
        result = await crystalReportRepository.getWarrantyDetailReport(validatedParams);
        break;
        
      case 'service-summary':
        result = await crystalReportRepository.getServiceSummaryReport(validatedParams);
        break;
        
      case 'service-detail':
        result = await crystalReportRepository.getServiceDetailReport(validatedParams);
        break;
        
      case 'sales-summary':
        result = await crystalReportRepository.getSalesSummaryReport(validatedParams);
        break;
        
      case 'customer-summary':
        result = await crystalReportRepository.getCustomerSummaryReport(validatedParams);
        break;
        
      default:
        return NextResponse.json(
          { error: `Unsupported Crystal Report: ${reportName}` },
          { status: 400 }
        );
    }
    
    // Add metadata
    const response = {
      ...result,
      metadata: {
        generatedAt: new Date(),
        recordCount: result.data?.length || 0,
        parameters: validatedParams,
        reportName,
      },
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Crystal Report API Error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid parameters', 
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to generate Crystal Report' },
      { status: 500 }
    );
  }
}

/**
 * Crystal Report mapping configuration
 */
const CRYSTAL_REPORT_MAPPING = {
  // AMC Reports
  'amc-summary': {
    originalName: 'AMCSummary.rpt',
    description: 'Summary of all AMC contracts',
    parameters: ['startDate', 'endDate', 'customerId', 'executiveId', 'amcStatus'],
    complexity: 'Medium',
  },
  'amc-detail': {
    originalName: 'AMCDetail.rpt',
    description: 'Detailed report of a specific AMC contract',
    parameters: ['contractId'],
    complexity: 'High',
  },
  'amc-expiry': {
    originalName: 'AMCExpiry.rpt',
    description: 'List of AMC contracts nearing expiry',
    parameters: ['daysToExpiry'],
    complexity: 'Low',
  },
  
  // Warranty Reports
  'warranty-summary': {
    originalName: 'INWSummary.rpt',
    description: 'Summary of all in-warranty products',
    parameters: ['startDate', 'endDate', 'brand'],
    complexity: 'Medium',
  },
  'warranty-detail': {
    originalName: 'INWDetail.rpt',
    description: 'Detailed report of a specific warranty',
    parameters: ['warrantyId'],
    complexity: 'High',
  },
  'warranty-expiry': {
    originalName: 'INWExpiry.rpt',
    description: 'List of warranties nearing expiry',
    parameters: ['daysToExpiry'],
    complexity: 'Low',
  },
  
  // Service Reports
  'service-summary': {
    originalName: 'ServiceSummary.rpt',
    description: 'Summary of all service reports',
    parameters: ['startDate', 'endDate', 'customerId', 'executiveId'],
    complexity: 'Medium',
  },
  'service-detail': {
    originalName: 'ServiceDetail.rpt',
    description: 'Detailed service report',
    parameters: ['serviceReportId'],
    complexity: 'High',
  },
  
  // Sales Reports
  'sales-summary': {
    originalName: 'SalesSummary.rpt',
    description: 'Summary of sales orders and performance',
    parameters: ['startDate', 'endDate', 'executiveId', 'productId'],
    complexity: 'Medium',
  },
  
  // Customer Reports
  'customer-summary': {
    originalName: 'CustomerSummary.rpt',
    description: 'Summary of customer information',
    parameters: ['city', 'state', 'isActive'],
    complexity: 'Low',
  },
};

/**
 * GET /api/reports/crystal/[reportName]/info
 * Get Crystal Report information and mapping
 */
async function getCrystalReportInfo(
  request: NextRequest,
  { params }: { params: { reportName: string } }
) {
  try {
    const { reportName } = params;
    const reportInfo = CRYSTAL_REPORT_MAPPING[reportName.toLowerCase() as keyof typeof CRYSTAL_REPORT_MAPPING];
    
    if (!reportInfo) {
      return NextResponse.json(
        { error: `Crystal Report not found: ${reportName}` },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      reportName,
      ...reportInfo,
      migrationStatus: 'Completed',
      lastUpdated: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('Crystal Report Info API Error:', error);
    return NextResponse.json(
      { error: 'Failed to get Crystal Report information' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  getCrystalReport
);
