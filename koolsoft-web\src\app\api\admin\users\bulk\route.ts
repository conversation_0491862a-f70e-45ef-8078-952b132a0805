import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * POST /api/admin/users/bulk
 *
 * Perform bulk operations on users
 * Only accessible to admin users
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Case-insensitive role check for admin access
    if (session.user.role.toUpperCase() !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await req.json();

    // Validate request body
    const bulkActionSchema = z.object({
      userIds: z.array(z.string()).min(1, 'At least one user ID is required'),
      action: z.enum(['ACTIVATE', 'DEACTIVATE', 'CHANGE_ROLE', 'DELETE']),
      role: z.enum(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']).optional(),
    }).refine(data => {
      // If action is CHANGE_ROLE, role is required
      if (data.action === 'CHANGE_ROLE') {
        return !!data.role;
      }
      return true;
    }, {
      message: "Role is required for CHANGE_ROLE action",
      path: ["role"],
    });

    const { userIds, action, role } = bulkActionSchema.parse(body);

    // Prevent bulk actions on the current user
    if (userIds.includes(session.user.id)) {
      return NextResponse.json(
        { error: 'You cannot perform bulk actions on your own account' },
        { status: 400 }
      );
    }

    let affected = 0;

    // Perform the bulk action
    switch (action) {
      case 'ACTIVATE':
        const activateResult = await prisma.users.updateMany({
          where: {
            id: { in: userIds },
            isActive: false, // Only update users that are currently inactive
          },
          data: {
            isActive: true,
          },
        });
        affected = activateResult.count;
        break;

      case 'DEACTIVATE':
        const deactivateResult = await prisma.users.updateMany({
          where: {
            id: { in: userIds },
            isActive: true, // Only update users that are currently active
          },
          data: {
            isActive: false,
          },
        });
        affected = deactivateResult.count;
        break;

      case 'CHANGE_ROLE':
        if (!role) {
          return NextResponse.json(
            { error: 'Role is required for CHANGE_ROLE action' },
            { status: 400 }
          );
        }

        const changeRoleResult = await prisma.users.updateMany({
          where: {
            id: { in: userIds },
            role: { not: role }, // Only update users that don't already have this role
          },
          data: {
            role,
          },
        });
        affected = changeRoleResult.count;
        break;

      case 'DELETE':
        const deleteResult = await prisma.users.deleteMany({
          where: {
            id: { in: userIds },
          },
        });
        affected = deleteResult.count;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      action,
      affected,
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
