import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesNotificationEventRepository } from '@/lib/repositories';
import { notificationEventFilterSchema } from '@/lib/validations/notification.schema';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/notifications/events
 * Get notification events with filtering and pagination
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const session = await getServerSession(authOptions);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = notificationEventFilterSchema.parse(queryParams);

      const salesNotificationEventRepository = getSalesNotificationEventRepository();

      // For non-admin users, filter to their own events or events they're involved in
      if (session?.user?.role && !['ADMIN', 'MANAGER'].includes(session.user.role.toUpperCase())) {
        // Users can only see events they created or are assigned to
        if (!validatedParams.userId && !validatedParams.executiveId) {
          validatedParams.userId = session.user.id;
        }
      }

      // Build filter object
      const filter: any = {};
      if (validatedParams.eventType) filter.eventType = validatedParams.eventType;
      if (validatedParams.entityType) filter.entityType = validatedParams.entityType;
      if (validatedParams.entityId) filter.entityId = validatedParams.entityId;
      if (validatedParams.userId) filter.userId = validatedParams.userId;
      if (validatedParams.customerId) filter.customerId = validatedParams.customerId;
      if (validatedParams.executiveId) filter.executiveId = validatedParams.executiveId;
      if (validatedParams.processed !== undefined) filter.processed = validatedParams.processed;
      if (validatedParams.startDate || validatedParams.endDate) {
        filter.createdAt = {};
        if (validatedParams.startDate) filter.createdAt.gte = validatedParams.startDate;
        if (validatedParams.endDate) filter.createdAt.lte = validatedParams.endDate;
      }

      // Get events with pagination
      const events = await salesNotificationEventRepository.findMany({
        where: filter,
        skip: validatedParams.skip,
        take: validatedParams.take,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
        },
      });

      // Get total count for pagination
      const totalCount = await salesNotificationEventRepository.count({ where: filter });

      return NextResponse.json({
        success: true,
        data: events,
        pagination: {
          skip: validatedParams.skip,
          take: validatedParams.take,
          total: totalCount,
          hasMore: validatedParams.skip + validatedParams.take < totalCount,
        },
      });
    } catch (error) {
      console.error('Error fetching notification events:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch notification events',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/notifications/events/process
 * Process pending notification events (Admin/Manager only)
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      const limit = body.limit || 50;

      const salesNotificationEventRepository = getSalesNotificationEventRepository();
      
      // Get unprocessed events
      const unprocessedEvents = await salesNotificationEventRepository.findUnprocessedEvents(limit);
      
      if (unprocessedEvents.length === 0) {
        return NextResponse.json({
          success: true,
          message: 'No pending events to process',
          processedCount: 0,
        });
      }

      // Process events using the notification service
      const { getSalesNotificationService } = await import('@/lib/services/sales-notification.service');
      const salesNotificationService = getSalesNotificationService();

      let processedCount = 0;
      for (const event of unprocessedEvents) {
        try {
          // Re-process the event
          await salesNotificationService.createSalesEvent({
            eventType: event.eventType,
            entityType: event.entityType,
            entityId: event.entityId,
            userId: event.userId || undefined,
            customerId: event.customerId || undefined,
            executiveId: event.executiveId || undefined,
            oldStatus: event.oldStatus || undefined,
            newStatus: event.newStatus || undefined,
            eventData: event.eventData,
          });
          processedCount++;
        } catch (eventError) {
          console.error(`Error processing event ${event.id}:`, eventError);
        }
      }

      return NextResponse.json({
        success: true,
        message: `Processed ${processedCount} of ${unprocessedEvents.length} events`,
        processedCount,
        totalEvents: unprocessedEvents.length,
      });
    } catch (error) {
      console.error('Error processing notification events:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to process notification events',
        },
        { status: 500 }
      );
    }
  }
);
