import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getReportSchedulerService } from '@/lib/services/report-scheduler.service';

/**
 * POST /api/reports/schedules/init
 * Initialize the report scheduler service
 * This endpoint can be called to manually initialize or restart the scheduler
 */
async function initializeScheduler(request: NextRequest) {
  try {
    const schedulerService = getReportSchedulerService();
    
    // Shutdown existing schedules first
    schedulerService.shutdown();
    
    // Initialize the scheduler
    await schedulerService.initialize();
    
    // Get status
    const status = schedulerService.getStatus();
    
    return NextResponse.json({
      success: true,
      message: 'Report scheduler initialized successfully',
      data: status,
    });
  } catch (error) {
    console.error('Error initializing report scheduler:', error);
    return NextResponse.json(
      { error: 'Failed to initialize report scheduler' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/reports/schedules/init
 * Get the current status of the report scheduler
 */
async function getSchedulerStatus(request: NextRequest) {
  try {
    const schedulerService = getReportSchedulerService();
    const status = schedulerService.getStatus();
    
    return NextResponse.json({
      success: true,
      data: status,
    });
  } catch (error) {
    console.error('Error getting scheduler status:', error);
    return NextResponse.json(
      { error: 'Failed to get scheduler status' },
      { status: 500 }
    );
  }
}

// Export with role protection
export const POST = withRoleProtection(
  ['ADMIN'],
  initializeScheduler
);

export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  getSchedulerStatus
);
