'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, FileText, Filter, BarChart3, Users, Shield, Wrench, DollarSign } from 'lucide-react';
import Link from 'next/link';

interface CrystalReport {
  id: string;
  name: string;
  title: string;
  description: string;
  category: 'AMC' | 'WARRANTY' | 'SERVICE' | 'SALES' | 'CUSTOMER';
  complexity: 'Low' | 'Medium' | 'High';
  originalFile: string;
  migrationStatus: 'Completed' | 'In Progress' | 'Pending';
  parameters: string[];
  icon: React.ReactNode;
}

/**
 * Crystal Reports Dashboard Page
 * 
 * Provides access to all migrated Crystal Reports with:
 * - Category-based organization
 * - Search and filtering
 * - Migration status tracking
 * - Direct links to report viewers
 */
export default function CrystalReportsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedComplexity, setSelectedComplexity] = useState<string>('all');

  // Crystal Reports inventory
  const crystalReports: CrystalReport[] = [
    // AMC Reports
    {
      id: 'amc-summary',
      name: 'AMC Summary',
      title: 'AMC Summary Report',
      description: 'Comprehensive summary of Annual Maintenance Contracts',
      category: 'AMC',
      complexity: 'Medium',
      originalFile: 'AMCSummary.rpt',
      migrationStatus: 'Completed',
      parameters: ['Date Range', 'Customer', 'Executive', 'Status'],
      icon: <Shield className="h-5 w-5" />,
    },
    {
      id: 'amc-detail',
      name: 'AMC Detail',
      title: 'AMC Detail Report',
      description: 'Detailed report of a specific AMC contract',
      category: 'AMC',
      complexity: 'High',
      originalFile: 'AMCDetail.rpt',
      migrationStatus: 'Completed',
      parameters: ['Contract ID'],
      icon: <Shield className="h-5 w-5" />,
    },
    {
      id: 'amc-expiry',
      name: 'AMC Expiry',
      title: 'AMC Expiry Report',
      description: 'List of AMC contracts nearing expiry',
      category: 'AMC',
      complexity: 'Low',
      originalFile: 'AMCExpiry.rpt',
      migrationStatus: 'Completed',
      parameters: ['Days to Expiry'],
      icon: <Shield className="h-5 w-5" />,
    },

    // Warranty Reports
    {
      id: 'warranty-summary',
      name: 'Warranty Summary',
      title: 'In-Warranty Summary Report',
      description: 'Summary of all in-warranty products',
      category: 'WARRANTY',
      complexity: 'Medium',
      originalFile: 'INWSummary.rpt',
      migrationStatus: 'Completed',
      parameters: ['Date Range', 'Brand'],
      icon: <FileText className="h-5 w-5" />,
    },
    {
      id: 'warranty-detail',
      name: 'Warranty Detail',
      title: 'In-Warranty Detail Report',
      description: 'Detailed report of a specific warranty',
      category: 'WARRANTY',
      complexity: 'High',
      originalFile: 'INWDetail.rpt',
      migrationStatus: 'Completed',
      parameters: ['Warranty ID'],
      icon: <FileText className="h-5 w-5" />,
    },

    // Service Reports
    {
      id: 'service-summary',
      name: 'Service Summary',
      title: 'Service Summary Report',
      description: 'Summary of all service reports',
      category: 'SERVICE',
      complexity: 'Medium',
      originalFile: 'ServiceSummary.rpt',
      migrationStatus: 'Completed',
      parameters: ['Date Range', 'Customer', 'Executive'],
      icon: <Wrench className="h-5 w-5" />,
    },
    {
      id: 'service-detail',
      name: 'Service Detail',
      title: 'Service Detail Report',
      description: 'Detailed service report',
      category: 'SERVICE',
      complexity: 'High',
      originalFile: 'ServiceDetail.rpt',
      migrationStatus: 'Completed',
      parameters: ['Service Report ID'],
      icon: <Wrench className="h-5 w-5" />,
    },

    // Sales Reports
    {
      id: 'sales-summary',
      name: 'Sales Summary',
      title: 'Sales Summary Report',
      description: 'Summary of sales orders and performance',
      category: 'SALES',
      complexity: 'Medium',
      originalFile: 'SalesSummary.rpt',
      migrationStatus: 'Completed',
      parameters: ['Date Range', 'Executive', 'Product'],
      icon: <DollarSign className="h-5 w-5" />,
    },

    // Customer Reports
    {
      id: 'customer-summary',
      name: 'Customer Summary',
      title: 'Customer Summary Report',
      description: 'Summary of customer information',
      category: 'CUSTOMER',
      complexity: 'Low',
      originalFile: 'CustomerSummary.rpt',
      migrationStatus: 'Completed',
      parameters: ['City', 'State', 'Active Status'],
      icon: <Users className="h-5 w-5" />,
    },
  ];

  // Filter reports based on search and filters
  const filteredReports = crystalReports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesComplexity = selectedComplexity === 'all' || report.complexity === selectedComplexity;
    
    return matchesSearch && matchesCategory && matchesComplexity;
  });

  // Group reports by category
  const reportsByCategory = crystalReports.reduce((acc, report) => {
    if (!acc[report.category]) {
      acc[report.category] = [];
    }
    acc[report.category].push(report);
    return acc;
  }, {} as Record<string, CrystalReport[]>);

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'AMC': return <Shield className="h-5 w-5" />;
      case 'WARRANTY': return <FileText className="h-5 w-5" />;
      case 'SERVICE': return <Wrench className="h-5 w-5" />;
      case 'SALES': return <DollarSign className="h-5 w-5" />;
      case 'CUSTOMER': return <Users className="h-5 w-5" />;
      default: return <BarChart3 className="h-5 w-5" />;
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Pending': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary to-primary/80 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Crystal Reports Migration</h1>
            <p className="text-primary-foreground/90">
              Access migrated Crystal Reports with modern web-based functionality
            </p>
          </div>
          <BarChart3 className="h-16 w-16 text-primary-foreground/50" />
        </div>
      </div>

      {/* Migration Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{crystalReports.length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">
                  {crystalReports.filter(r => r.migrationStatus === 'Completed').length}
                </p>
              </div>
              <FileText className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-blue-600">
                  {Object.keys(reportsByCategory).length}
                </p>
              </div>
              <Filter className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Migration Rate</p>
                <p className="text-2xl font-bold text-primary">100%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Find Reports</CardTitle>
          <CardDescription>Search and filter Crystal Reports</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="AMC">AMC Reports</SelectItem>
                <SelectItem value="WARRANTY">Warranty Reports</SelectItem>
                <SelectItem value="SERVICE">Service Reports</SelectItem>
                <SelectItem value="SALES">Sales Reports</SelectItem>
                <SelectItem value="CUSTOMER">Customer Reports</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={selectedComplexity} onValueChange={setSelectedComplexity}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Complexity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Complexity</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Reports Grid */}
      <Tabs defaultValue="grid" className="space-y-4">
        <TabsList>
          <TabsTrigger value="grid">Grid View</TabsTrigger>
          <TabsTrigger value="category">By Category</TabsTrigger>
        </TabsList>
        
        <TabsContent value="grid" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredReports.map((report) => (
              <Card key={report.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      {report.icon}
                      <CardTitle className="text-lg">{report.name}</CardTitle>
                    </div>
                    <Badge className={getStatusColor(report.migrationStatus)}>
                      {report.migrationStatus}
                    </Badge>
                  </div>
                  <CardDescription>{report.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Category:</span>
                      <Badge variant="outline">{report.category}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Complexity:</span>
                      <Badge variant="outline">{report.complexity}</Badge>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">Parameters:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {report.parameters.map((param, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {param}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Link href={`/reports/crystal/${report.id}`}>
                      <Button className="w-full mt-3">
                        View Report
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="category" className="space-y-6">
          {Object.entries(reportsByCategory).map(([category, reports]) => (
            <Card key={category}>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(category)}
                  <CardTitle>{category} Reports</CardTitle>
                  <Badge variant="outline">{reports.length}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {reports.map((report) => (
                    <div key={report.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium">{report.name}</h4>
                        <Badge className={getStatusColor(report.migrationStatus)} variant="secondary">
                          {report.migrationStatus}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{report.description}</p>
                      <Link href={`/reports/crystal/${report.id}`}>
                        <Button size="sm" className="w-full">
                          View Report
                        </Button>
                      </Link>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
