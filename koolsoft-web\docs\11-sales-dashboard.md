# Sales Dashboard Documentation

## Overview

The Sales Dashboard provides comprehensive sales metrics and analytics for the KoolSoft application. It offers real-time visualization of sales performance, conversion rates, and pipeline status across leads, opportunities, prospects, and orders.

## Features

### Key Metrics Display
- **Total Leads**: Count of active sales leads
- **Total Opportunities**: Count of qualified opportunities  
- **Total Prospects**: Count of active prospects
- **Total Orders**: Count of confirmed orders
- **Total Revenue**: Sum of all order amounts
- **Average Order Value**: Mean value per order
- **Conversion Rates**: Lead→Opportunity, Opportunity→Prospect, Prospect→Order, Overall conversion

### Data Visualization
- **Sales Trends Chart**: Interactive line/bar chart showing monthly trends for all sales stages
- **Pipeline Breakdown**: Pie chart displaying distribution across sales pipeline stages
- **Responsive Charts**: Built with Recharts library for smooth interactions

### Filtering & Export
- **Time Period Filters**: 7 days, 30 days, 90 days, 6 months, 1 year
- **Customer Filter**: Filter by specific customer
- **Executive Filter**: Filter by sales executive
- **Date Range Selection**: Custom start and end date picker
- **Export Functionality**: CSV and Excel export of filtered data

## Technical Implementation

### API Endpoints

#### GET /api/sales/dashboard
Retrieves comprehensive dashboard data including metrics, trends, and breakdowns.

**Query Parameters:**
- `customerId` (optional): UUID of customer to filter by
- `executiveId` (optional): UUID of executive to filter by  
- `startDate` (optional): Start date in ISO format
- `endDate` (optional): End date in ISO format
- `period` (optional): Predefined period (7d, 30d, 90d, 6m, 1y)

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalLeads": 150,
      "totalOpportunities": 75,
      "totalProspects": 45,
      "totalOrders": 25,
      "totalRevenue": 500000,
      "averageOrderValue": 20000,
      "leadToOpportunityRate": 50.0,
      "opportunityToProspectRate": 60.0,
      "prospectToOrderRate": 55.6,
      "overallConversionRate": 16.7
    },
    "breakdown": {
      "pipeline": [
        {"status": "Leads", "count": 150, "color": "#3B82F6"},
        {"status": "Opportunities", "count": 75, "color": "#10B981"},
        {"status": "Prospects", "count": 45, "color": "#8B5CF6"},
        {"status": "Orders", "count": 25, "color": "#F59E0B"}
      ]
    },
    "trends": {
      "monthly": [
        {
          "month": "Jan 2025",
          "leads": 25,
          "opportunities": 12,
          "prospects": 8,
          "orders": 4,
          "revenue": 80000
        }
      ]
    },
    "period": {
      "startDate": "2024-11-18T00:00:00.000Z",
      "endDate": "2025-06-17T23:59:59.999Z",
      "period": "30d"
    }
  }
}
```

#### POST /api/sales/dashboard/export
Exports dashboard data in CSV or Excel format.

**Request Body:**
```json
{
  "format": "CSV",
  "customerId": "uuid-optional",
  "executiveId": "uuid-optional", 
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "period": "1y"
}
```

**Response:** File download with appropriate MIME type

### Components

#### SalesMetricsCards
Displays key performance indicators in a responsive grid layout.
- Location: `src/components/sales/dashboard/sales-metrics-cards.tsx`
- Features: Loading states, formatted numbers, color-coded icons

#### SalesTrendsChart  
Interactive chart component for visualizing sales trends over time.
- Location: `src/components/sales/dashboard/sales-trends-chart.tsx`
- Features: Line/bar chart toggle, custom tooltips, responsive design

#### SalesPipelineBreakdown
Pie chart showing distribution across pipeline stages.
- Location: `src/components/sales/dashboard/sales-pipeline-breakdown.tsx`
- Features: Interactive legend, percentage calculations, color coding

#### SalesDashboardFilters
Comprehensive filtering interface with date pickers and dropdowns.
- Location: `src/components/sales/dashboard/sales-dashboard-filters.tsx`
- Features: Date range selection, customer/executive filters, export buttons

### Database Integration

The dashboard integrates with the following PostgreSQL tables via Prisma:
- `sales_leads`: Lead tracking and management
- `sales_opportunities`: Opportunity pipeline
- `sales_prospects`: Prospect management  
- `sales_orders`: Order tracking and revenue
- `customers`: Customer information
- `users`: Executive/user data

### Authentication & Authorization

**Role-Based Access Control:**
- **ADMIN**: Full access to all dashboard features
- **MANAGER**: Full access to all dashboard features
- **EXECUTIVE**: Full access to all dashboard features  
- **USER**: Full access to all dashboard features

**Security Features:**
- JWT-based authentication via NextAuth.js
- Role validation middleware on API routes
- Session-based access control

## Usage

### Accessing the Dashboard
1. Navigate to `/sales/dashboard` in the application
2. Ensure you have appropriate role permissions (ADMIN, MANAGER, EXECUTIVE, or USER)
3. The dashboard will load with default 30-day period data

### Filtering Data
1. Use the **Time Period** dropdown to select predefined ranges
2. Select specific **Customer** or **Executive** from dropdowns
3. Use **Date Range** pickers for custom periods
4. Click **Clear** to reset all filters

### Exporting Data
1. Apply desired filters
2. Click the **Export** button
3. Data will be downloaded as CSV file
4. File includes all sales data matching current filters

### Chart Interactions
- **Trends Chart**: Toggle between line and bar chart views
- **Pipeline Chart**: Hover over segments for detailed information
- **Responsive Design**: Charts adapt to screen size automatically

## Performance Considerations

- **Database Optimization**: Queries use proper indexing on date fields
- **Pagination**: Export functionality limits to 10,000 records
- **Caching**: Consider implementing Redis caching for frequently accessed data
- **Loading States**: Skeleton components provide smooth user experience

## Future Enhancements

- Real-time data updates via WebSocket connections
- Advanced filtering by product categories or regions
- Drill-down capabilities from charts to detailed views
- Scheduled report generation and email delivery
- Integration with external CRM systems
- Mobile-optimized responsive design improvements

## Troubleshooting

### Common Issues

**Dashboard not loading:**
- Verify user authentication and role permissions
- Check browser console for JavaScript errors
- Ensure API endpoints are accessible

**Export not working:**
- Verify user has appropriate permissions
- Check network connectivity
- Ensure data exists for selected filters

**Charts not displaying:**
- Verify Recharts library is properly installed
- Check for JavaScript console errors
- Ensure data format matches expected structure

### Error Codes

- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: Insufficient role permissions  
- **400 Bad Request**: Invalid filter parameters
- **500 Internal Server Error**: Database or server issues

## Related Documentation

- [Sales Pipeline Management](./10-sales-pipeline.md)
- [Authentication & Authorization](./03-authentication.md)
- [API Documentation](./04-api-endpoints.md)
- [Database Schema](./02-database-schema.md)
