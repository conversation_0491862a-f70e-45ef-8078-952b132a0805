-- Seed default formula templates
-- Insert default formula templates for common business calculations

-- Get admin user ID for template creation
DO $$
DECLARE
    admin_user_id TEXT;
BEGIN
    -- Find an admin user
    SELECT id INTO admin_user_id FROM users WHERE UPPER(role) = 'ADMIN' LIMIT 1;
    
    -- If no admin user found, use the first user
    IF admin_user_id IS NULL THEN
        SELECT id INTO admin_user_id FROM users LIMIT 1;
    END IF;
    
    -- Only proceed if we have a user
    IF admin_user_id IS NOT NULL THEN
        -- Insert default formula templates (only if they don't exist)
        INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
        SELECT * FROM (VALUES
            ('Percentage Calculation', 'Calculate percentage of value relative to total', 'PERCENTAGE(value, total)', 'MATHEMATICAL', TRUE, '["value", "total"]'::jsonb, 'NUMBER', admin_user_id),
            ('Growth Rate', 'Calculate growth percentage between current and previous values', 'GROWTH(current, previous)', 'MATHEMATICAL', TRUE, '["current", "previous"]'::jsonb, 'NUMBER', admin_user_id),
            ('Profit Margin', 'Calculate profit margin percentage', 'MARGIN(revenue, cost)', 'BUSINESS', TRUE, '["revenue", "cost"]'::jsonb, 'NUMBER', admin_user_id),
            ('Tax Calculation', 'Calculate tax amount', 'TAX(amount, taxPercent)', 'BUSINESS', TRUE, '["amount", "taxPercent"]'::jsonb, 'NUMBER', admin_user_id),
            ('Discount Amount', 'Calculate discounted amount', 'DISCOUNT(amount, discountPercent)', 'BUSINESS', TRUE, '["amount", "discountPercent"]'::jsonb, 'NUMBER', admin_user_id),
            ('Total with Tax', 'Calculate total amount including tax', 'amount + TAX(amount, taxPercent)', 'BUSINESS', TRUE, '["amount", "taxPercent"]'::jsonb, 'NUMBER', admin_user_id),
            ('Average Calculation', 'Calculate average of multiple values', 'AVERAGE(value1, value2, value3)', 'STATISTICAL', TRUE, '["value1", "value2", "value3"]'::jsonb, 'NUMBER', admin_user_id),
            ('Sum Calculation', 'Calculate sum of multiple values', 'SUM(value1, value2, value3)', 'STATISTICAL', TRUE, '["value1", "value2", "value3"]'::jsonb, 'NUMBER', admin_user_id),
            ('Days Between Dates', 'Calculate days between two dates', 'DAYS(endDate, startDate)', 'MATHEMATICAL', TRUE, '["endDate", "startDate"]'::jsonb, 'NUMBER', admin_user_id),
            ('Contract Duration (Months)', 'Calculate contract duration in months', 'MONTHS(endDate, startDate)', 'MATHEMATICAL', TRUE, '["endDate", "startDate"]'::jsonb, 'NUMBER', admin_user_id),
            ('Status Based on Amount', 'Determine status based on amount threshold', 'IF(amount > 10000, "High Value", "Standard")', 'BUSINESS', TRUE, '["amount"]'::jsonb, 'STRING', admin_user_id),
            ('Priority Based on Days', 'Determine priority based on days remaining', 'IF(daysRemaining < 7, "Urgent", IF(daysRemaining < 30, "High", "Normal"))', 'BUSINESS', TRUE, '["daysRemaining"]'::jsonb, 'STRING', admin_user_id),
            ('AMC Renewal Rate', 'Calculate AMC renewal rate percentage', 'PERCENTAGE(renewedContracts, totalContracts)', 'BUSINESS', TRUE, '["renewedContracts", "totalContracts"]'::jsonb, 'NUMBER', admin_user_id),
            ('Service Efficiency', 'Calculate service efficiency based on completion time', 'IF(actualHours <= plannedHours, 100, ROUND(plannedHours / actualHours * 100, 2))', 'BUSINESS', TRUE, '["actualHours", "plannedHours"]'::jsonb, 'NUMBER', admin_user_id),
            ('Customer Satisfaction Score', 'Calculate weighted customer satisfaction score', 'ROUND((serviceRating * 0.4 + responseRating * 0.3 + qualityRating * 0.3), 2)', 'BUSINESS', TRUE, '["serviceRating", "responseRating", "qualityRating"]'::jsonb, 'NUMBER', admin_user_id)
        ) AS t(name, description, formula, category, is_template, variables, return_type, created_by)
        WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE report_formulas.name = t.name);
        
        RAISE NOTICE 'Formula templates seeded successfully with user ID: %', admin_user_id;
    ELSE
        RAISE NOTICE 'No users found in database. Please create users first.';
    END IF;
END $$;
