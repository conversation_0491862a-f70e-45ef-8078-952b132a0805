"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/in-warranty/page",{

/***/ "(app-pages-browser)/./src/app/warranties/in-warranty/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/warranties/in-warranty/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InWarrantyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Edit,Eye,FileDown,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_warranties_warranty_conversion_actions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/warranties/warranty-conversion-actions */ \"(app-pages-browser)/./src/components/warranties/warranty-conversion-actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * In-Warranty Management Page\n * \n * This page displays and manages products currently under warranty coverage.\n * It includes filtering, searching, and CRUD operations for in-warranty items.\n */ function InWarrantyPage() {\n    _s();\n    _s1();\n    const [warranties, setWarranties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCustomers, setIsLoadingCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [customerFilter, setCustomerFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshTrigger, setRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Load warranties from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InWarrantyPage.useEffect\": ()=>{\n            const loadWarranties = {\n                \"InWarrantyPage.useEffect.loadWarranties\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const response = await fetch('/api/warranties?status=ACTIVE', {\n                            credentials: 'include',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch warranties');\n                        }\n                        const data = await response.json();\n                        setWarranties(data.warranties || []);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading warranties:', err);\n                        setError('Failed to load warranties');\n                        setWarranties([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"InWarrantyPage.useEffect.loadWarranties\"];\n            loadWarranties();\n        }\n    }[\"InWarrantyPage.useEffect\"], [\n        refreshTrigger\n    ]);\n    // Load customers for filter dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InWarrantyPage.useEffect\": ()=>{\n            const loadCustomers = {\n                \"InWarrantyPage.useEffect.loadCustomers\": async ()=>{\n                    try {\n                        setIsLoadingCustomers(true);\n                        const response = await fetch('/api/customers?take=100', {\n                            credentials: 'include',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch customers');\n                        }\n                        const data = await response.json();\n                        setCustomers(data.success ? data.data : data.customers || []);\n                    } catch (err) {\n                        console.error('Error loading customers:', err);\n                        setCustomers([]);\n                    } finally{\n                        setIsLoadingCustomers(false);\n                    }\n                }\n            }[\"InWarrantyPage.useEffect.loadCustomers\"];\n            loadCustomers();\n        }\n    }[\"InWarrantyPage.useEffect\"], []);\n    const getStatusBadge = (status, warrantyDate)=>{\n        const today = new Date();\n        const warranty = new Date(warrantyDate);\n        const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (status === 'EXPIRED') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"destructive\",\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expired\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 14\n            }, this);\n        }\n        if (daysUntilExpiry <= 30) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"secondary\",\n                className: \"flex items-center space-x-1 bg-yellow-100 text-yellow-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expiring Soon\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n            variant: \"secondary\",\n            className: \"flex items-center space-x-1 bg-green-100 text-green-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n            lineNumber: 106,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-IN');\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-IN', {\n            style: 'currency',\n            currency: 'INR',\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const filteredWarranties = warranties.filter((warranty)=>{\n        const matchesSearch = searchTerm === '' || warranty.bslNo.toLowerCase().includes(searchTerm.toLowerCase()) || warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) || warranty.machines.some((machine)=>machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesStatus = statusFilter === 'all' || warranty.status === statusFilter;\n        const matchesCustomer = customerFilter === 'all' || warranty.customerId === customerFilter;\n        return matchesSearch && matchesStatus && matchesCustomer;\n    });\n    const handleDelete = async (warrantyId, bslNo)=>{\n        if (!confirm(\"Are you sure you want to delete warranty \".concat(bslNo, \"? This action cannot be undone.\"))) {\n            return;\n        }\n        try {\n            setDeletingId(warrantyId);\n            const response = await fetch(\"/api/warranties/\".concat(warrantyId), {\n                method: 'DELETE',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({\n                        error: 'Unknown error'\n                    }));\n                throw new Error(errorData.error || 'Failed to delete warranty');\n            }\n            // Remove from local state\n            setWarranties((prev)=>prev.filter((w)=>w.id !== warrantyId));\n            setError(null);\n        } catch (error) {\n            console.error('Error deleting warranty:', error);\n            setError(error.message || 'Failed to delete warranty');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleConversionSuccess = ()=>{\n        // Refresh the warranties list after successful conversion\n        setRefreshTrigger((prev)=>prev + 1);\n    };\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/export?status=ACTIVE&format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export warranties');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"in-warranty-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting warranties:', error);\n            setError('Failed to export warranties');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"In-Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Manage products currently under warranty coverage\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleExport,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                        href: \"/warranties/new?type=in-warranty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"New In-Warranty\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"search\",\n                                            className: \"text-black\",\n                                            children: \"Search\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"search\",\n                                                    placeholder: \"Search by BSL No, customer, or serial number...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"status\",\n                                            className: \"text-black\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: statusFilter,\n                                            onValueChange: setStatusFilter,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    id: \"status\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Statuses\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"ACTIVE\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"EXPIRED\",\n                                                            children: \"Expired\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"PENDING\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"customer\",\n                                            className: \"text-black\",\n                                            children: \"Customer\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                            value: customerFilter,\n                                            onValueChange: setCustomerFilter,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                    id: \"customer\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                        placeholder: \"Select customer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"All Customers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isLoadingCustomers ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"loading\",\n                                                            disabled: true,\n                                                            children: \"Loading customers...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 41\n                                                        }, this) : Array.isArray(customers) && customers.length > 0 ? customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: customer.id,\n                                                                children: [\n                                                                    customer.name,\n                                                                    \" - \",\n                                                                    customer.city || 'Unknown City'\n                                                                ]\n                                                            }, customer.id, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 191\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                            value: \"no-customers\",\n                                                            disabled: true,\n                                                            children: \"No customers available\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 40\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                    className: \"text-black\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"BSL No\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Customer\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Install Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Warranty Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Machines\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-black\",\n                                                    children: \"Convert\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-right text-black\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                        children: isLoading ? // Loading skeleton\n                                        Array.from({\n                                            length: 5\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-32\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                            className: \"h-6 w-16 ml-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"skeleton-\".concat(index), true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 36\n                                            }, this)) : filteredWarranties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                colSpan: 9,\n                                                className: \"text-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"No in-warranty products found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                                                href: \"/warranties/new?type=in-warranty\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Add First In-Warranty Product\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 70\n                                        }, this) : filteredWarranties.map((warranty)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"font-medium text-black\",\n                                                        children: warranty.bslNo\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-black\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: warranty.customer.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: warranty.customer.city\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-black\",\n                                                        children: formatDate(warranty.installDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-black\",\n                                                        children: formatDate(warranty.warrantyDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-black\",\n                                                        children: warranty.numberOfMachines\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-black\",\n                                                        children: formatCurrency(warranty.bslAmount)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: getStatusBadge(warranty.status, warranty.warrantyDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_warranties_warranty_conversion_actions__WEBPACK_IMPORTED_MODULE_12__.WarrantyConversionActions, {\n                                                            warranty: {\n                                                                id: warranty.id,\n                                                                customerId: warranty.customerId,\n                                                                customerName: warranty.customer.name,\n                                                                bslNo: warranty.bslNo,\n                                                                numberOfMachines: warranty.numberOfMachines,\n                                                                status: warranty.status,\n                                                                executiveId: warranty.executiveId,\n                                                                contactPersonId: warranty.contactPersonId\n                                                            },\n                                                            onConversionSuccess: handleConversionSuccess,\n                                                            variant: \"inline\",\n                                                            size: \"sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                                                        href: \"/warranties/\".concat(warranty.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                                                        href: \"/warranties/\".concat(warranty.id, \"/edit\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-destructive hover:text-destructive\",\n                                                                    onClick: ()=>handleDelete(warranty.id, warranty.bslNo),\n                                                                    disabled: deletingId === warranty.id,\n                                                                    children: deletingId === warranty.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 59\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Edit_Eye_FileDown_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 145\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, warranty.id, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 68\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        !isLoading && filteredWarranties.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Showing \",\n                                        filteredWarranties.length,\n                                        \" of \",\n                                        warranties.length,\n                                        \" warranties\",\n                                        customerFilter !== 'all' && ' (filtered by customer)',\n                                        statusFilter !== 'all' && ' (filtered by status)',\n                                        searchTerm && ' (search results)'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Use filters above to narrow down results\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 59\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\in-warranty\\\\page.tsx\",\n        lineNumber: 186,\n        columnNumber: 10\n    }, this);\n}\n_s(InWarrantyPage, \"v7mDkwADRJWa5uLyCjtVktq+vak=\");\n_c1 = InWarrantyPage;\n_s1(InWarrantyPage, \"v7mDkwADRJWa5uLyCjtVktq+vak=\");\n_c = InWarrantyPage;\nvar _c;\n$RefreshReg$(_c, \"InWarrantyPage\");\nvar _c1;\n$RefreshReg$(_c1, \"InWarrantyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/in-warranty/page.tsx\n"));

/***/ })

});