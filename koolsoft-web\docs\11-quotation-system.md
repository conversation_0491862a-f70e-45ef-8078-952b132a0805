# Quotation System Documentation

## Overview

The Quotation System is a comprehensive module for managing quotations in the KoolSoft web application. It provides full CRUD operations, PDF generation, email functionality, and integration with existing customer and product data.

## Features

### Core Functionality
- ✅ **Quotation Management**: Create, read, update, delete quotations
- ✅ **Line Items**: Multiple items per quotation with product/model integration
- ✅ **Status Tracking**: Draft, Sent, Accepted, Rejected, Expired statuses
- ✅ **PDF Generation**: Export quotations as professional PDF documents
- ✅ **Email Integration**: Send quotations via email with PDF attachments
- ✅ **Duplicate Functionality**: Clone existing quotations
- ✅ **Search & Filtering**: Advanced search and filtering capabilities
- ✅ **Role-based Access**: Admin, Manager, Executive, User permissions

### Database Integration
- ✅ **Zero Mock Data**: 100% real database integration
- ✅ **Prisma Models**: Modern ORM with type safety
- ✅ **Repository Pattern**: Consistent data access layer
- ✅ **Transactions**: Atomic operations for data integrity
- ✅ **Auto-numbering**: Automatic quotation number generation

### UI/UX Standards
- ✅ **Consistent Design**: Follows KoolSoft UI patterns
- ✅ **Primary Blue Headers**: #0F52BA color scheme
- ✅ **Responsive Layout**: Works on all screen sizes
- ✅ **Form Validation**: Zod schemas with error handling
- ✅ **Toast Notifications**: User feedback for all actions

## Database Schema

### Tables Created

#### `quotations`
```sql
- id (UUID, Primary Key)
- quotation_number (TEXT, Unique, Auto-generated)
- customer_id (UUID, Foreign Key to customers)
- executive_id (UUID, Foreign Key to users)
- quotation_date (TIMESTAMP)
- valid_until (TIMESTAMP, Optional)
- status (TEXT: DRAFT, SENT, ACCEPTED, REJECTED, EXPIRED)
- contact_person (TEXT, Optional)
- contact_phone (TEXT, Optional)
- contact_email (TEXT, Optional)
- subject (TEXT, Optional)
- notes (TEXT, Optional)
- terms_conditions (TEXT, Optional)
- subtotal (DECIMAL)
- tax_amount (DECIMAL)
- total_amount (DECIMAL)
- discount (DECIMAL, Optional)
- discount_type (TEXT: PERCENTAGE, FIXED)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### `quotation_items`
```sql
- id (UUID, Primary Key)
- quotation_id (UUID, Foreign Key to quotations)
- product_id (UUID, Optional, Foreign Key to products)
- model_id (UUID, Optional, Foreign Key to models)
- brand_id (UUID, Optional, Foreign Key to brands)
- description (TEXT, Required)
- quantity (INTEGER, Default: 1)
- unit_price (DECIMAL)
- total_price (DECIMAL)
- tax_rate (DECIMAL, Default: 0)
- tax_amount (DECIMAL, Default: 0)
- discount (DECIMAL, Default: 0)
- discount_type (TEXT: PERCENTAGE, FIXED)
- specifications (TEXT, Optional)
- notes (TEXT, Optional)
- sort_order (INTEGER, Default: 0)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

### Relationships
- `quotations.customer_id` → `customers.id`
- `quotations.executive_id` → `users.id`
- `quotation_items.quotation_id` → `quotations.id` (CASCADE DELETE)
- `quotation_items.product_id` → `products.id` (OPTIONAL)
- `quotation_items.model_id` → `models.id` (OPTIONAL)
- `quotation_items.brand_id` → `brands.id` (OPTIONAL)

## API Endpoints

### Quotations
- `GET /api/quotations` - List quotations with filtering and pagination
- `POST /api/quotations` - Create new quotation
- `GET /api/quotations/[id]` - Get quotation details
- `PUT /api/quotations/[id]` - Update quotation
- `DELETE /api/quotations/[id]` - Delete quotation
- `PATCH /api/quotations/[id]/status` - Update quotation status
- `POST /api/quotations/[id]/duplicate` - Duplicate quotation
- `GET /api/quotations/[id]/export` - Export quotation as PDF
- `POST /api/quotations/[id]/email` - Send quotation via email
- `GET /api/quotations/statistics` - Get quotation statistics

### Supporting APIs
- `GET /api/products` - List products with filtering
- `GET /api/models` - List models with filtering

## Repository Classes

### QuotationRepository
Located: `src/lib/repositories/quotation.repository.ts`

Key Methods:
- `generateQuotationNumber()` - Auto-generate unique quotation numbers
- `findWithRelations()` - Get quotations with customer/executive/items
- `findWithAllRelations()` - Get complete quotation data
- `search()` - Search quotations by various criteria
- `getStatistics()` - Calculate quotation metrics

### QuotationItemRepository
Located: `src/lib/repositories/quotation-item.repository.ts`

Key Methods:
- `findByQuotationId()` - Get items for a quotation
- `createMany()` - Bulk create quotation items
- `updateMany()` - Bulk update quotation items
- `calculateTotals()` - Calculate quotation totals

## Validation Schemas

Located: `src/lib/validations/quotation.schema.ts`

### Key Schemas
- `createQuotationSchema` - Validation for new quotations
- `updateQuotationSchema` - Validation for quotation updates
- `quotationFilterSchema` - Validation for search/filter parameters
- `quotationEmailSchema` - Validation for email functionality

## Frontend Components

### Pages
- `/quotations` - Quotation list with search and filtering
- `/quotations/new` - Create new quotation form
- `/quotations/[id]` - Quotation detail view
- `/quotations/[id]/edit` - Edit quotation form

### Key Features
- **React Hook Form** with Zod validation
- **Dynamic line items** with add/remove functionality
- **Real-time calculations** for totals and taxes
- **Customer/Executive selection** with searchable dropdowns
- **PDF export** and email functionality
- **Status management** with role-based permissions

## PDF Generation

### Technology
- **PDFKit** for server-side PDF generation
- **Professional layout** with company branding
- **Complete quotation data** including line items and totals
- **Terms and conditions** support

### Features
- Company header with KoolSoft branding
- Customer and executive information
- Detailed line items table
- Subtotal, tax, and total calculations
- Notes and terms & conditions
- Generated timestamp

## Email Integration

### Functionality
- Send quotations via email with PDF attachments
- Customizable subject and message
- CC/BCC support
- Automatic status update to "SENT"
- Integration with existing email service

## Role-based Access Control

### Permissions
- **ADMIN**: Full access to all quotation operations
- **MANAGER**: Full access to all quotation operations
- **EXECUTIVE**: Create, read, update quotations; cannot delete
- **USER**: Read-only access to quotations

## Navigation Integration

### Sidebar Menu
Added to Sales section:
```
Sales
├── Lead Management
├── Sales Pipeline
└── Quotations (NEW)
```

## Testing Results

### ✅ Successful Tests
1. **Database Integration**: All Prisma queries executing correctly
2. **Authentication**: Admin user authenticated successfully
3. **API Endpoints**: All quotation APIs responding with 200 status
4. **Form Validation**: React Hook Form with Zod working properly
5. **Reference Data**: Customers and users loading successfully
6. **Navigation**: Quotations accessible from sidebar menu
7. **Repository Pattern**: QuotationRepository functioning correctly

### 🔧 Issues Resolved
1. **Import Paths**: Fixed useToast import from `@/hooks/use-toast` to `@/components/ui/use-toast`
2. **API Routes**: Created missing `/api/products` and `/api/models` endpoints
3. **Database Schema**: Successfully created quotations and quotation_items tables
4. **Relationships**: Properly configured foreign key relationships

## File Structure

```
src/
├── app/
│   ├── api/
│   │   ├── quotations/
│   │   │   ├── route.ts
│   │   │   ├── [id]/
│   │   │   │   ├── route.ts
│   │   │   │   ├── status/route.ts
│   │   │   │   ├── duplicate/route.ts
│   │   │   │   ├── export/route.ts
│   │   │   │   └── email/route.ts
│   │   │   └── statistics/route.ts
│   │   ├── products/route.ts
│   │   └── models/route.ts
│   └── quotations/
│       ├── page.tsx
│       ├── new/page.tsx
│       └── [id]/
│           ├── page.tsx
│           └── edit/page.tsx
├── lib/
│   ├── repositories/
│   │   ├── quotation.repository.ts
│   │   └── quotation-item.repository.ts
│   ├── validations/
│   │   └── quotation.schema.ts
│   └── utils/
│       └── pdf-generator.ts
└── prisma/
    └── schema.prisma (updated with quotation models)
```

## Future Enhancements

### Potential Improvements
1. **Advanced Reporting**: Quotation analytics and dashboards
2. **Template System**: Predefined quotation templates
3. **Approval Workflow**: Multi-level approval process
4. **Integration**: Connect with inventory management
5. **Mobile App**: Mobile-friendly quotation creation
6. **E-signature**: Digital signature support
7. **Automated Follow-up**: Email reminders for pending quotations

## Maintenance

### Regular Tasks
1. **Database Cleanup**: Archive old quotations periodically
2. **Performance Monitoring**: Monitor query performance
3. **Backup Verification**: Ensure quotation data is backed up
4. **User Training**: Train staff on new features
5. **Security Review**: Regular security audits

## Support

### Common Issues
1. **PDF Generation Errors**: Check PDFKit dependencies
2. **Email Delivery**: Verify email service configuration
3. **Permission Errors**: Check user roles and permissions
4. **Database Errors**: Monitor Prisma connection health

### Troubleshooting
- Check application logs for detailed error messages
- Verify database connectivity and schema integrity
- Ensure all required environment variables are set
- Test with admin credentials: <EMAIL> / Admin@123

---

**Implementation Date**: December 2024  
**Version**: 1.0.0  
**Status**: ✅ Complete and Tested  
**Next Review**: Q1 2025
