'use client';

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Search, CalendarIcon, ChevronDown, ChevronUp } from 'lucide-react';
import { cn, formatDate } from '@/lib/utils';
import { useExecutives } from '@/lib/hooks/useExecutives';

// Define the filter form schema
const filterFormSchema = z.object({
  customerId: z.string().optional(),
  executiveId: z.string().optional(),
  status: z.string().optional(),
  startDateFrom: z.string().optional(),
  startDateTo: z.string().optional(),
  endDateFrom: z.string().optional(),
  endDateTo: z.string().optional(),
  search: z.string().optional(),
  sortField: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Define the form values type
type FilterFormValues = z.infer<typeof filterFormSchema>;

// Define the component props
interface AMCFilterFormProps {
  onFilter: (filters: FilterFormValues) => void;
  initialValues?: FilterFormValues;
}

/**
 * AMC Filter Form Component
 *
 * This component provides a form for filtering AMC contracts.
 */
export function AMCFilterForm({ onFilter, initialValues }: AMCFilterFormProps) {
  const [expanded, setExpanded] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const { executives, isLoading: executivesLoading } = useExecutives();

  // Initialize form with react-hook-form
  const form = useForm<FilterFormValues>({
    resolver: zodResolver(filterFormSchema),
    defaultValues: {
      customerId: initialValues?.customerId || '',
      executiveId: initialValues?.executiveId || 'all',
      status: initialValues?.status || 'all',
      startDateFrom: initialValues?.startDateFrom || '',
      startDateTo: initialValues?.startDateTo || '',
      endDateFrom: initialValues?.endDateFrom || '',
      endDateTo: initialValues?.endDateTo || '',
      search: initialValues?.search || '',
      sortField: initialValues?.sortField || 'startDate',
      sortOrder: initialValues?.sortOrder || 'desc',
    },
  });

  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Handle search input change with debounce
  const handleSearchChange = (value: string) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    setSearchTimeout(
      setTimeout(() => {
        form.handleSubmit(onSubmit)();
      }, 500)
    );
  };

  // Handle form submission
  const onSubmit = (values: FilterFormValues) => {
    // Convert "all" values to empty strings for API filtering
    const apiValues = { ...values };

    // Convert 'all' values to empty strings for API filtering
    if (apiValues.status === 'all') apiValues.status = '';
    if (apiValues.executiveId === 'all') apiValues.executiveId = '';

    // Convert empty strings to undefined to avoid sending them as 'null' strings
    Object.keys(apiValues).forEach(key => {
      if (apiValues[key as keyof FilterFormValues] === '') {
        apiValues[key as keyof FilterFormValues] = undefined as any;
      }
    });

    onFilter(apiValues);
  };

  // Handle form reset
  const handleReset = () => {
    // Reset form values
    form.reset({
      customerId: '',
      executiveId: 'all',
      status: 'all',
      startDateFrom: '',
      startDateTo: '',
      endDateFrom: '',
      endDateTo: '',
      search: '',
      sortField: 'startDate',
      sortOrder: 'desc',
    });

    // Create API values with undefined instead of empty strings
    onFilter({
      customerId: undefined,
      executiveId: undefined,
      status: '',
      startDateFrom: undefined,
      startDateTo: undefined,
      endDateFrom: undefined,
      endDateTo: undefined,
      search: undefined,
      sortField: 'startDate',
      sortOrder: 'desc',
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Global search field */}
          <FormField
            control={form.control}
            name="search"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormControl>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      placeholder="Search contracts by customer name, contract number..."
                      className="pl-9"
                      {...field}
                      ref={searchInputRef}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSearchChange(e.target.value);
                      }}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />

          {/* Status filter */}
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem className="w-full md:w-48">
                <Select
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.handleSubmit(onSubmit)();
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="EXPIRED">Expired</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                    <SelectItem value="RENEWED">Renewed</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          {/* Filter actions */}
          <div className="flex space-x-2">
            <Button type="submit" variant="default">
              Filter
            </Button>
            <Button type="button" variant="outline" onClick={handleReset}>
              Reset
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={() => setExpanded(!expanded)}
              className="px-2"
            >
              {expanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {expanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Executive filter */}
            <FormField
              control={form.control}
              name="executiveId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Executive</FormLabel>
                  <Select
                    value={field.value}
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Executive" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Executives</SelectItem>
                      {executives.map((executive) => (
                        <SelectItem key={executive.id} value={executive.id}>
                          {executive.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            {/* Start Date From filter */}
            <FormField
              control={form.control}
              name="startDateFrom"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Start Date From</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            formatDate(field.value)
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : '');
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />

            {/* Start Date To filter */}
            <FormField
              control={form.control}
              name="startDateTo"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Start Date To</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            formatDate(field.value)
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : '');
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />

            {/* End Date From filter */}
            <FormField
              control={form.control}
              name="endDateFrom"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>End Date From</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            formatDate(field.value)
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : '');
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />

            {/* End Date To filter */}
            <FormField
              control={form.control}
              name="endDateTo"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>End Date To</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            formatDate(field.value)
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          field.onChange(date ? date.toISOString() : '');
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />
          </div>
        )}
      </form>
    </Form>
  );
}
