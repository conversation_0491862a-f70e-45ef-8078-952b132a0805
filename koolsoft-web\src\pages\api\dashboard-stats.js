// API route to get dashboard statistics
import { PrismaClient } from '../../generated/prisma';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }
    
    // Get current date
    const today = new Date();
    
    // Calculate date ranges
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    // Get total counts
    const totalCustomers = await prisma.customer.count();
    const totalAMCContracts = await prisma.amc_contracts.count();
    const totalWarranties = await prisma.warranties.count();
    const totalMachinesResult = await prisma.$queryRaw`
      SELECT
        (SELECT COUNT(*) FROM "amc_machines") +
        (SELECT COUNT(*) FROM "warranty_machines") as total
    `;
    const totalMachines = Number(totalMachinesResult[0].total);
    
    // Get active AMC contracts
    const activeAMCContracts = await prisma.amc_contracts.count({
      where: {
        status: 'ACTIVE',
        endDate: {
          gte: today
        }
      }
    });
    
    // Get expired AMC contracts
    const expiredAMCContracts = await prisma.amc_contracts.count({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: today
        }
      }
    });
    
    // Get active warranties
    const activeWarranties = await prisma.warranties.count({
      where: {
        status: 'ACTIVE',
        warrantyDate: {
          gte: today
        }
      }
    });
    
    // Get expired warranties
    const expiredWarranties = await prisma.warranties.count({
      where: {
        status: 'ACTIVE',
        warrantyDate: {
          lt: today
        }
      }
    });
    
    // Get AMC contracts expiring in the next 30 days
    const amcContractsExpiringSoon = await prisma.amc_contracts.count({
      where: {
        status: 'ACTIVE',
        endDate: {
          gte: today,
          lte: thirtyDaysFromNow
        }
      }
    });
    
    // Get warranties expiring in the next 30 days
    const warrantiesExpiringSoon = await prisma.warranties.count({
      where: {
        status: 'ACTIVE',
        warrantyDate: {
          gte: today,
          lte: thirtyDaysFromNow
        }
      }
    });
    
    // Get new customers in the last 30 days
    const newCustomers = await prisma.customer.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });
    
    // Get new AMC contracts in the last 30 days
    const newAMCContracts = await prisma.amc_contracts.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });
    
    // Get new warranties in the last 30 days
    const newWarranties = await prisma.warranties.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });
    
    // Get new machines in the last 30 days
    const newMachinesResult = await prisma.$queryRaw`
      SELECT
        (SELECT COUNT(*) FROM "amc_machines" WHERE "created_at" >= ${thirtyDaysAgo}) +
        (SELECT COUNT(*) FROM "warranty_machines" WHERE "created_at" >= ${thirtyDaysAgo}) as total
    `;
    const newMachines = Number(newMachinesResult[0].total);
    
    // Get AMC contracts by status
    const amcContractsByStatus = [
      {
        status: 'Active',
        count: activeAMCContracts
      },
      {
        status: 'Expired',
        count: expiredAMCContracts
      }
    ];
    
    // Get warranties by status
    const warrantiesByStatus = [
      {
        status: 'Active',
        count: activeWarranties
      },
      {
        status: 'Expired',
        count: expiredWarranties
      }
    ];
    
    return res.status(200).json({
      totals: {
        customers: totalCustomers,
        amcContracts: totalAMCContracts,
        warranties: totalWarranties,
        machines: totalMachines
      },
      active: {
        amcContracts: activeAMCContracts,
        warranties: activeWarranties
      },
      expired: {
        amcContracts: expiredAMCContracts,
        warranties: expiredWarranties
      },
      expiringSoon: {
        amcContracts: amcContractsExpiringSoon,
        warranties: warrantiesExpiringSoon
      },
      newItems: {
        customers: newCustomers,
        amcContracts: newAMCContracts,
        warranties: newWarranties,
        machines: newMachines
      },
      charts: {
        amcContractsByStatus,
        warrantiesByStatus
      }
    });
  } catch (error) {
    console.error('Error getting dashboard statistics:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
}
