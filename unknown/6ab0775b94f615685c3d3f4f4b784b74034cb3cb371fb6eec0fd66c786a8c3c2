'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { toast } from 'sonner';
import { 
  Plus, 
  Search, 
  Filter, 
  Download,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  BarChart3,
  Users,
  Wrench
} from 'lucide-react';
import { format } from 'date-fns';

interface ServiceReport {
  id: string;
  reportDate: string;
  visitDate?: string;
  completionDate?: string;
  natureOfService: string;
  complaintType: string;
  status: string;
  customer: {
    id: string;
    name: string;
    city: string;
  };
  executive: {
    id: string;
    name: string;
  };
  details: any[];
}

interface ServiceStatistics {
  overview: {
    totalReports: number;
    openReports: number;
    completedReports: number;
    pendingReports: number;
    recentReports: number;
    completionRate: number;
  };
  period: {
    name: string;
    totalReports: number;
    completedReports: number;
    completionRate: number;
  };
}

export default function ServicePage() {
  const router = useRouter();
  const [serviceReports, setServiceReports] = useState<ServiceReport[]>([]);
  const [statistics, setStatistics] = useState<ServiceStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [complaintTypeFilter, setComplaintTypeFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Load service reports and statistics
  useEffect(() => {
    loadServiceReports();
    loadStatistics();
  }, [currentPage, statusFilter, complaintTypeFilter, searchTerm]);

  const loadServiceReports = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(complaintTypeFilter !== 'all' && { complaintType: complaintTypeFilter }),
      });

      const response = await fetch(`/api/service?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setServiceReports(data.serviceReports || []);
        setTotalPages(data.pagination?.totalPages || 1);
      } else {
        toast.error('Failed to load service reports');
      }
    } catch (error) {
      console.error('Error loading service reports:', error);
      toast.error('Failed to load service reports');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/service/statistics', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setStatistics(data.statistics);
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  const handleCreateNew = () => {
    router.push('/service/new');
  };

  const handleViewReport = (reportId: string) => {
    router.push(`/service/${reportId}`);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      OPEN: { variant: 'secondary' as const, icon: AlertCircle, label: 'Open' },
      IN_PROGRESS: { variant: 'default' as const, icon: Clock, label: 'In Progress' },
      COMPLETED: { variant: 'default' as const, icon: CheckCircle, label: 'Completed' },
      CANCELLED: { variant: 'destructive' as const, icon: XCircle, label: 'Cancelled' },
      PENDING: { variant: 'secondary' as const, icon: Clock, label: 'Pending' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.OPEN;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getComplaintTypeBadge = (type: string) => {
    const typeConfig = {
      REPAIR: 'bg-red-100 text-red-800',
      MAINTENANCE: 'bg-blue-100 text-blue-800',
      INSTALLATION: 'bg-green-100 text-green-800',
      INSPECTION: 'bg-yellow-100 text-yellow-800',
      WARRANTY: 'bg-purple-100 text-purple-800',
      OTHER: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeConfig[type as keyof typeof typeConfig] || typeConfig.OTHER}`}>
        {type}
      </span>
    );
  };

  const columns = [
    {
      header: 'Report Date',
      accessorKey: 'reportDate',
      cell: ({ row }: any) => format(new Date(row.original.reportDate), 'MMM dd, yyyy'),
    },
    {
      header: 'Customer',
      accessorKey: 'customer.name',
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.customer.name}</div>
          <div className="text-sm text-muted-foreground">{row.original.customer.city}</div>
        </div>
      ),
    },
    {
      header: 'Nature of Service',
      accessorKey: 'natureOfService',
      cell: ({ row }: any) => (
        <div className="max-w-[200px] truncate" title={row.original.natureOfService}>
          {row.original.natureOfService}
        </div>
      ),
    },
    {
      header: 'Type',
      accessorKey: 'complaintType',
      cell: ({ row }: any) => getComplaintTypeBadge(row.original.complaintType),
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }: any) => getStatusBadge(row.original.status),
    },
    {
      header: 'Executive',
      accessorKey: 'executive.name',
    },
    {
      header: 'Actions',
      id: 'actions',
      cell: ({ row }: any) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleViewReport(row.original.id)}
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Reports</p>
                  <p className="text-2xl font-bold">{statistics.overview.totalReports}</p>
                </div>
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Open Reports</p>
                  <p className="text-2xl font-bold">{statistics.overview.openReports}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold">{statistics.overview.completedReports}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                  <p className="text-2xl font-bold">{statistics.overview.completionRate.toFixed(1)}%</p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Service Reports Table */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Service Reports
            </span>
            <Button
              onClick={handleCreateNew}
              variant="secondary"
              className="bg-white text-primary hover:bg-gray-100"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Service Report
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search service reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={complaintTypeFilter} onValueChange={setComplaintTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="REPAIR">Repair</SelectItem>
                <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
                <SelectItem value="INSTALLATION">Installation</SelectItem>
                <SelectItem value="INSPECTION">Inspection</SelectItem>
                <SelectItem value="WARRANTY">Warranty</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Table */}
          <DataTable
            columns={columns}
            data={serviceReports}
            loading={loading}
            pagination={{
              page: currentPage,
              total: serviceReports.length,
              pageSize: 10,
              onPageChange: setCurrentPage,
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
