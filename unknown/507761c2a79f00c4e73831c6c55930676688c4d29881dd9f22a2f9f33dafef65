'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Download, RefreshCw, Printer, FileText, FileSpreadsheet } from 'lucide-react';
import { format } from 'date-fns';

export interface CrystalReportProps {
  reportName: string;
  reportTitle: string;
  reportDescription?: string;
  parameters: Record<string, any>;
  onParametersChange?: (parameters: Record<string, any>) => void;
  allowExport?: boolean;
  allowPrint?: boolean;
  className?: string;
}

export interface CrystalReportData {
  data: any[];
  summary?: Record<string, any>;
  metadata?: {
    generatedAt: Date;
    recordCount: number;
    parameters: Record<string, any>;
  };
}

/**
 * Base Crystal Report Component
 * 
 * Provides common functionality for all Crystal Reports including:
 * - Data fetching and loading states
 * - Export functionality (PDF, Excel)
 * - Print functionality
 * - Error handling
 * - Professional KoolSoft branding
 */
export function BaseCrystalReport({
  reportName,
  reportTitle,
  reportDescription,
  parameters,
  onParametersChange,
  allowExport = true,
  allowPrint = true,
  className = '',
  children,
}: CrystalReportProps & { children: React.ReactNode }) {
  const [reportData, setReportData] = useState<CrystalReportData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  /**
   * Fetch report data from API
   */
  const fetchReportData = async (params: Record<string, any> = parameters) => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });

      const response = await fetch(`/api/reports/crystal/${reportName}?${queryParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch report data: ${response.statusText}`);
      }

      const data = await response.json();
      setReportData(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load report data';
      setError(errorMessage);
      toast({
        title: 'Error Loading Report',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Export report to specified format
   */
  const exportReport = async (format: 'PDF' | 'EXCEL') => {
    setIsExporting(true);

    try {
      const queryParams = new URLSearchParams();
      Object.entries(parameters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });
      queryParams.append('format', format);

      const response = await fetch(`/api/reports/crystal/${reportName}/export?${queryParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`);
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportName}_${format.toLowerCase()}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${format === 'PDF' ? 'pdf' : 'xlsx'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Export Successful',
        description: `Report exported as ${format} successfully.`,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Export failed';
      toast({
        title: 'Export Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Print report
   */
  const printReport = () => {
    window.print();
  };

  /**
   * Refresh report data
   */
  const refreshReport = () => {
    fetchReportData();
  };

  // Load initial data
  useEffect(() => {
    fetchReportData();
  }, [reportName]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Report Header */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl font-bold">{reportTitle}</CardTitle>
              {reportDescription && (
                <CardDescription className="text-primary-foreground/90 mt-1">
                  {reportDescription}
                </CardDescription>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={refreshReport}
                disabled={isLoading}
                className="bg-white/10 hover:bg-white/20 text-white border-white/20"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              
              {allowExport && (
                <>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => exportReport('PDF')}
                    disabled={isExporting || !reportData}
                    className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    PDF
                  </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => exportReport('EXCEL')}
                    disabled={isExporting || !reportData}
                    className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    Excel
                  </Button>
                </>
              )}
              
              {allowPrint && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={printReport}
                  disabled={!reportData}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        {/* Report Metadata */}
        {reportData?.metadata && (
          <CardContent className="py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                Generated: {format(new Date(reportData.metadata.generatedAt), 'PPpp')}
              </div>
              <div>
                Records: {reportData.metadata.recordCount.toLocaleString()}
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Report Content */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 space-y-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <div className="text-red-600 mb-4">
                <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="font-medium">Error Loading Report</p>
                <p className="text-sm text-gray-600 mt-1">{error}</p>
              </div>
              <Button onClick={refreshReport} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : reportData ? (
            <div className="print:shadow-none">
              {children}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No data available</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Crystal Report Context for sharing data between components
 */
export const CrystalReportContext = React.createContext<{
  reportData: CrystalReportData | null;
  parameters: Record<string, any>;
  isLoading: boolean;
  error: string | null;
}>({
  reportData: null,
  parameters: {},
  isLoading: false,
  error: null,
});
