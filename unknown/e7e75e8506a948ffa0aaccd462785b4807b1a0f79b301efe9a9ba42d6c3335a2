'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Download, FileText, FileSpreadsheet, FileImage } from 'lucide-react';

interface ReportExportDialogProps {
  reportType: string;
  reportName: string;
  data: any[];
  filters: Record<string, any>;
  exportFormats: string[];
  onClose: () => void;
}

interface ExportOption {
  format: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  mimeType: string;
  extension: string;
}

/**
 * Report Export Dialog Component
 * 
 * Provides options for exporting report data in various formats (CSV, Excel, PDF).
 * Handles the export process and provides feedback to the user.
 */
export function ReportExportDialog({
  reportType,
  reportName,
  data,
  filters,
  exportFormats,
  onClose,
}: ReportExportDialogProps) {
  const [selectedFormat, setSelectedFormat] = useState<string>('CSV');
  const [includeFilters, setIncludeFilters] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const exportOptions: ExportOption[] = [
    {
      format: 'CSV',
      label: 'CSV (Comma Separated Values)',
      description: 'Best for data analysis and spreadsheet applications',
      icon: <FileText className="h-5 w-5" />,
      mimeType: 'text/csv',
      extension: 'csv',
    },
    {
      format: 'EXCEL',
      label: 'Excel Spreadsheet',
      description: 'Best for advanced formatting and calculations',
      icon: <FileSpreadsheet className="h-5 w-5" />,
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      extension: 'xlsx',
    },
    {
      format: 'JSON',
      label: 'JSON (JavaScript Object Notation)',
      description: 'Best for developers and API integration',
      icon: <FileText className="h-5 w-5" />,
      mimeType: 'application/json',
      extension: 'json',
    },
    {
      format: 'PDF',
      label: 'PDF Document',
      description: 'Best for sharing and printing',
      icon: <FileImage className="h-5 w-5" />,
      mimeType: 'application/pdf',
      extension: 'pdf',
    },
  ];

  // Filter available export options based on supported formats
  const availableOptions = exportOptions.filter(option => 
    exportFormats.includes(option.format)
  );

  const handleExport = async () => {
    try {
      setIsExporting(true);

      // Build export request body
      const exportRequest = {
        reportType,
        format: selectedFormat,
        includeFilters,
        filters: includeFilters ? filters : {},
        filename: `${reportType.toLowerCase()}_${new Date().toISOString().slice(0, 10)}`,
      };

      // Make export request
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(exportRequest),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Export failed');
      }

      // Get the selected export option for file details
      const exportOption = availableOptions.find(opt => opt.format === selectedFormat);
      if (!exportOption) {
        throw new Error('Invalid export format');
      }

      // Create download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const filename = `${reportName.toLowerCase().replace(/\s+/g, '_')}_${timestamp}.${exportOption.extension}`;
      link.download = filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: `Report exported as ${selectedFormat} format.`,
      });

      onClose();

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export report. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>Export Report</span>
          </DialogTitle>
          <DialogDescription>
            Export "{reportName}" report data in your preferred format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            <RadioGroup
              value={selectedFormat}
              onValueChange={setSelectedFormat}
              className="space-y-3"
            >
              {availableOptions.map((option) => (
                <div key={option.format} className="flex items-start space-x-3">
                  <RadioGroupItem
                    value={option.format}
                    id={option.format}
                    className="mt-1"
                  />
                  <div className="flex-1 space-y-1">
                    <Label
                      htmlFor={option.format}
                      className="flex items-center space-x-2 cursor-pointer"
                    >
                      {option.icon}
                      <span className="font-medium">{option.label}</span>
                    </Label>
                    <p className="text-xs text-gray-500">{option.description}</p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Export Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Options</Label>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeFilters"
                checked={includeFilters}
                onCheckedChange={(checked) => setIncludeFilters(checked === true)}
              />
              <Label
                htmlFor="includeFilters"
                className="text-sm cursor-pointer"
              >
                Include filter information in export
              </Label>
            </div>
          </div>

          {/* Export Summary */}
          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Records to export:</span>
              <span className="font-medium">{data.length}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Report type:</span>
              <span className="font-medium">{reportType}</span>
            </div>
            {includeFilters && Object.keys(filters).length > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Active filters:</span>
                <span className="font-medium">{Object.keys(filters).length}</span>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
