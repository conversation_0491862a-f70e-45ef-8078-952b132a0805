import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

/**
 * Query parameters schema for models
 */
const modelsQuerySchema = z.object({
  search: z.string().optional(),
  productId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  tonnage: z.coerce.number().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'tonnage', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET /api/models
 * Get models with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = modelsQuerySchema.parse(queryParams);

      const {
        search,
        productId,
        brandId,
        tonnage,
        page,
        limit,
        sortBy,
        sortOrder,
      } = validatedParams;

      const skip = (page - 1) * limit;

      // Build filter criteria
      const whereClause: any = {
        AND: [
          // Search filter
          ...(search
            ? [
                {
                  OR: [
                    {
                      name: {
                        contains: search,
                        mode: 'insensitive',
                      },
                    },
                    {
                      description: {
                        contains: search,
                        mode: 'insensitive',
                      },
                    },
                    {
                      specs: {
                        contains: search,
                        mode: 'insensitive',
                      },
                    },
                  ],
                },
              ]
            : []),
          // Product filter
          ...(productId ? [{ productId }] : []),
          // Brand filter (through product relation)
          ...(brandId
            ? [
                {
                  product: {
                    brandId,
                  },
                },
              ]
            : []),
          // Tonnage filter
          ...(tonnage ? [{ tonnage }] : []),
        ],
      };

      // Get models with relations
      const models = await prisma.models.findMany({
        where: whereClause,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              description: true,
              brand: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                },
              },
            },
          },
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
      });

      // Get total count for pagination
      const totalCount = await prisma.models.count({
        where: whereClause,
      });

      const totalPages = Math.ceil(totalCount / limit);

      return NextResponse.json({
        success: true,
        data: models,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      });
    } catch (error) {
      console.error('Error fetching models:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch models',
        },
        { status: 500 }
      );
    }
  }
);
