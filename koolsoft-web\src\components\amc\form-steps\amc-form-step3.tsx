'use client';

import React, { useState, useEffect } from 'react';
import { useAMCForm, AMCFormStep } from '@/contexts/amc-form-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Settings, AlertCircle, Plus } from 'lucide-react';
import { MachineForm } from '@/components/machines/machine-form';
import { MachineList } from '@/components/machines/machine-list';
import { useMachines, type CreateMachineData } from '@/lib/hooks/useMachines';
import { type MachineFormData } from '@/lib/validations/machine';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

interface MachineData {
  id?: string;
  productId?: string;
  modelId?: string;
  brandId?: string;
  serialNumber: string;
  location: string;
  installationDate?: Date;
  tonnage?: number;
  status: 'ACTIVE' | 'INACTIVE';
  originalAmcId?: number;
  originalAssetNo?: number;
}

export function AMCFormStep3() {
  const { state, updateFormData, goToNextStep, goToPreviousStep, dispatch } = useAMCForm();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingMachine, setEditingMachine] = useState<MachineData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get machines from form state
  const machines = state.formData.machines || [];

  // Validate step whenever machines change
  useEffect(() => {
    const isValid = machines.length > 0;
    dispatch({
      type: 'SET_STEP_VALIDATION',
      payload: { step: AMCFormStep.MACHINE_MANAGEMENT, isValid },
    });
  }, [machines, dispatch]);

  const handleAddMachine = async (data: MachineFormData) => {
    try {
      setIsSubmitting(true);

      // Create machine data without amcContractId for form storage
      const machineData: MachineData = {
        id: `temp-${Date.now()}`, // Temporary ID for form state
        productId: data.productId,
        modelId: data.modelId,
        brandId: data.brandId,
        serialNumber: data.serialNumber,
        location: data.location,
        installationDate: data.installationDate,
        tonnage: data.tonnage,
        status: data.status,
        originalAmcId: data.originalAmcId,
        originalAssetNo: data.originalAssetNo,
      };

      // Add to form state
      const updatedMachines = [...machines, machineData];
      updateFormData({ machines: updatedMachines });

      showSuccessToast('Machine added successfully');
      setShowAddForm(false);
    } catch (error) {
      console.error('Error adding machine:', error);
      showErrorToast('Failed to add machine');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditMachine = async (data: MachineFormData) => {
    try {
      setIsSubmitting(true);

      if (!editingMachine?.id) return;

      // Update machine data
      const updatedMachine: MachineData = {
        ...editingMachine,
        productId: data.productId,
        modelId: data.modelId,
        brandId: data.brandId,
        serialNumber: data.serialNumber,
        location: data.location,
        installationDate: data.installationDate,
        tonnage: data.tonnage,
        status: data.status,
        originalAmcId: data.originalAmcId,
        originalAssetNo: data.originalAssetNo,
      };

      // Update in form state
      const updatedMachines = machines.map(m => 
        m.id === editingMachine.id ? updatedMachine : m
      );
      updateFormData({ machines: updatedMachines });

      showSuccessToast('Machine updated successfully');
      setEditingMachine(null);
    } catch (error) {
      console.error('Error updating machine:', error);
      showErrorToast('Failed to update machine');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteMachine = async (machineId: string) => {
    try {
      // Remove from form state
      const updatedMachines = machines.filter(m => m.id !== machineId);
      updateFormData({ machines: updatedMachines });

      showSuccessToast('Machine removed successfully');
    } catch (error) {
      console.error('Error removing machine:', error);
      showErrorToast('Failed to remove machine');
    }
  };

  const handleNext = () => {
    if (machines.length === 0) {
      showErrorToast('Please add at least one machine before proceeding');
      return;
    }
    goToNextStep();
  };

  // Convert form machines to display format
  const displayMachines = machines.map(machine => ({
    id: machine.id || '',
    amcContractId: state.formData.customerId || '',
    productId: machine.productId,
    modelId: machine.modelId,
    brandId: machine.brandId,
    location: machine.location,
    serialNumber: machine.serialNumber,
    assetNo: machine.originalAssetNo,
    historyCardNo: undefined,
    section: undefined,
    originalAmcId: machine.originalAmcId,
    originalAssetNo: machine.originalAssetNo,
    installationDate: machine.installationDate,
    tonnage: machine.tonnage,
    status: machine.status,
    createdAt: new Date(),
    updatedAt: new Date(),
    // Mock relations for display
    product: machine.productId ? { id: machine.productId, name: 'Product' } : undefined,
    model: machine.modelId ? { id: machine.modelId, name: 'Model', tonnage: machine.tonnage } : undefined,
    brand: machine.brandId ? { id: machine.brandId, name: 'Brand' } : undefined,
  }));

  if (showAddForm) {
    return (
      <MachineForm
        amcContractId={state.formData.customerId || ''}
        onSubmit={handleAddMachine}
        onCancel={() => setShowAddForm(false)}
        isSubmitting={isSubmitting}
      />
    );
  }

  if (editingMachine) {
    return (
      <MachineForm
        machine={displayMachines.find(m => m.id === editingMachine.id)}
        amcContractId={state.formData.customerId || ''}
        onSubmit={handleEditMachine}
        onCancel={() => setEditingMachine(null)}
        isSubmitting={isSubmitting}
      />
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Machine Management
        </CardTitle>
        <CardDescription>
          Add and manage machines that will be covered under this AMC contract
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Machine List */}
        <MachineList
          machines={displayMachines}
          onEdit={(machine) => {
            const formMachine = machines.find(m => m.id === machine.id);
            if (formMachine) {
              setEditingMachine(formMachine as any);
            }
          }}
          onDelete={handleDeleteMachine}
          onAdd={() => setShowAddForm(true)}
          title="Contract Machines"
        />

        {/* Validation Alert */}
        {machines.length === 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              At least one machine must be added to proceed to the next step.
            </AlertDescription>
          </Alert>
        )}

        {/* Navigation */}
        <div className="flex justify-between pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={goToPreviousStep}
          >
            Previous: Contract Details
          </Button>
          <Button
            type="button"
            onClick={handleNext}
            disabled={machines.length === 0}
          >
            Next: Service Scheduling
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
