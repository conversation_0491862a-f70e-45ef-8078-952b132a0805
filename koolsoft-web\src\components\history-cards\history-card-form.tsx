'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchableCustomerSelect } from '@/components/ui/searchable-customer-select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Save, X, Plus, Trash2 } from 'lucide-react';

// Validation schema
const historyCardFormSchema = z.object({
  customerId: z.string().uuid('Please select a valid customer'),
  cardNo: z.coerce.number().int().positive().optional(),
  source: z.enum(['AMC', 'INW', 'OTW']).optional(),
  amcId: z.string().uuid().optional().or(z.literal('')),
  inWarrantyId: z.string().uuid().optional().or(z.literal('')),
  outWarrantyId: z.string().uuid().optional().or(z.literal('')),
  toCardNo: z.coerce.number().int().positive().optional(),
  originalId: z.coerce.number().int().positive().optional(),
  sections: z.array(z.object({
    sectionCode: z.string().min(1, 'Section code is required'),
    content: z.string().min(1, 'Content is required'),
  })).optional(),
});

type HistoryCardFormValues = z.infer<typeof historyCardFormSchema>;

interface Customer {
  id: string;
  name: string;
  address?: string;
  city?: string;
}

interface HistoryCard {
  id: string;
  customerId: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  customer?: Customer;
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
  }[];
}

interface HistoryCardFormProps {
  historyCard?: HistoryCard;
  onSuccess?: (historyCard: any) => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function HistoryCardForm({
  historyCard,
  onSuccess,
  onCancel,
  isLoading: externalLoading = false,
}: HistoryCardFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Customer loading is now handled by SearchableCustomerSelect component
  const [sections, setSections] = useState<{ sectionCode: string; content: string }[]>([]);

  const form = useForm<HistoryCardFormValues>({
    resolver: zodResolver(historyCardFormSchema),
    defaultValues: {
      customerId: historyCard?.customerId || '',
      cardNo: historyCard?.cardNo || undefined,
      source: historyCard?.source || undefined,
      amcId: historyCard?.amcId || '',
      inWarrantyId: historyCard?.inWarrantyId || '',
      outWarrantyId: historyCard?.outWarrantyId || '',
      toCardNo: historyCard?.toCardNo || undefined,
      originalId: historyCard?.originalId || undefined,
      sections: historyCard?.sections?.map(s => ({
        sectionCode: s.sectionCode,
        content: s.content,
      })) || [],
    },
  });

  // Customer loading is now handled by SearchableCustomerSelect component

  // Reset form when historyCard prop changes
  useEffect(() => {
    if (historyCard) {
      const formData = {
        customerId: historyCard.customerId || '',
        cardNo: historyCard.cardNo || undefined,
        source: historyCard.source || undefined,
        amcId: historyCard.amcId || '',
        inWarrantyId: historyCard.inWarrantyId || '',
        outWarrantyId: historyCard.outWarrantyId || '',
        toCardNo: historyCard.toCardNo || undefined,
        originalId: historyCard.originalId || undefined,
        sections: historyCard.sections?.map(s => ({
          sectionCode: s.sectionCode,
          content: s.content,
        })) || [],
      };

      form.reset(formData);

      // Update sections state
      const sectionsData = historyCard.sections?.map(s => ({
        sectionCode: s.sectionCode,
        content: s.content,
      })) || [];

      setSections(sectionsData);
    } else {
      // Reset form for create mode
      form.reset({
        customerId: '',
        cardNo: undefined,
        source: undefined,
        amcId: '',
        inWarrantyId: '',
        outWarrantyId: '',
        toCardNo: undefined,
        originalId: undefined,
        sections: [],
      });
      setSections([]);
    }
  }, [historyCard, form]);

  const addSection = () => {
    setSections([...sections, { sectionCode: '', content: '' }]);
  };

  const removeSection = (index: number) => {
    setSections(sections.filter((_, i) => i !== index));
  };

  const updateSection = (index: number, field: 'sectionCode' | 'content', value: string) => {
    const updatedSections = [...sections];
    updatedSections[index][field] = value;
    setSections(updatedSections);
  };

  const onSubmit = async (data: HistoryCardFormValues) => {
    try {
      setIsSubmitting(true);
      
      // Include sections in the submission data
      const submitData = {
        ...data,
        sections: sections.filter(s => s.sectionCode && s.content),
      };
      
      const url = historyCard 
        ? `/api/history-cards/${historyCard.id}`
        : '/api/history-cards';
      
      const method = historyCard ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save history card');
      }
      
      const result = await response.json();
      
      if (result.success) {
        toast({
          title: 'Success',
          description: historyCard 
            ? 'History card updated successfully.'
            : 'History card created successfully.',
        });
        
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        throw new Error(result.error || 'Failed to save history card');
      }
    } catch (error) {
      console.error('Error saving history card:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save history card. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = externalLoading || isSubmitting;

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {historyCard ? 'Edit History Card' : 'Create New History Card'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="customerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer *</FormLabel>
                    <FormControl>
                      <SearchableCustomerSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Search and select a customer..."
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cardNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Card Number</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter card number"
                        disabled={isLoading}
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="source"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Source</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select source" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="AMC">AMC</SelectItem>
                        <SelectItem value="INW">In-Warranty</SelectItem>
                        <SelectItem value="OTW">Out-of-Warranty</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="toCardNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>To Card Number</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter destination card number"
                        disabled={isLoading}
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Reference IDs */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="amcId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>AMC ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter AMC ID"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="inWarrantyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>In-Warranty ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter in-warranty ID"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="outWarrantyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Out-Warranty ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter out-warranty ID"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="originalId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Original ID</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter original ID from legacy system"
                      disabled={isLoading}
                      {...field}
                      value={field.value || ''}
                      onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </FormControl>
                  <FormDescription>
                    Reference ID from the legacy Microsoft Access system
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Sections */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">History Sections</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addSection}
                  disabled={isLoading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Section
                </Button>
              </div>

              {sections.map((section, index) => (
                <Card key={index} className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                    <div>
                      <Label htmlFor={`section-code-${index}`}>Section Code</Label>
                      <Input
                        id={`section-code-${index}`}
                        placeholder="e.g., A, B, C"
                        value={section.sectionCode}
                        onChange={(e) => updateSection(index, 'sectionCode', e.target.value)}
                        disabled={isLoading}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor={`section-content-${index}`}>Content</Label>
                      <Textarea
                        id={`section-content-${index}`}
                        placeholder="Enter section content"
                        value={section.content}
                        onChange={(e) => updateSection(index, 'content', e.target.value)}
                        disabled={isLoading}
                        rows={3}
                      />
                    </div>
                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeSection(index)}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}

              {sections.length === 0 && (
                <Alert>
                  <AlertDescription>
                    No sections added yet. Click "Add Section" to create history sections for this card.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                <Save className="h-4 w-4 mr-2" />
                {historyCard ? 'Update' : 'Create'} History Card
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
