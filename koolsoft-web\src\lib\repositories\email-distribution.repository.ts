import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Email Distribution List Repository
 */
export class EmailDistributionListRepository extends PrismaRepository<
  Prisma.email_distribution_listsGetPayload<{}>,
  string,
  Prisma.email_distribution_listsCreateInput,
  Prisma.email_distribution_listsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('email_distribution_lists');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find distribution lists by user
   */
  async findByUser(
    userId: string,
    options?: {
      page?: number;
      limit?: number;
      search?: string;
      isActive?: boolean;
    }
  ) {
    const { page = 1, limit = 20, search, isActive } = options || {};
    const skip = (page - 1) * limit;

    const where: Prisma.email_distribution_listsWhereInput = {
      createdBy: userId,
    };

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          _count: {
            select: { reportEmailConfigs: true },
          },
        },
      }),
      this.model.count({ where }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Find active distribution lists
   */
  async findActive() {
    return this.model.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' },
      include: {
        user: {
          select: { id: true, name: true, email: true },
        },
      },
    });
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.email_distribution_listsGetPayload<{}>,
    string,
    Prisma.email_distribution_listsCreateInput,
    Prisma.email_distribution_listsUpdateInput
  > {
    const repo = new EmailDistributionListRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}

/**
 * Report Email Configuration Repository
 */
export class ReportEmailConfigRepository extends PrismaRepository<
  Prisma.report_email_configsGetPayload<{}>,
  string,
  Prisma.report_email_configsCreateInput,
  Prisma.report_email_configsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('report_email_configs');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find configurations by report type
   */
  async findByReportType(
    reportType: string,
    options?: {
      page?: number;
      limit?: number;
      search?: string;
      isActive?: boolean;
    }
  ) {
    const { page = 1, limit = 20, search, isActive } = options || {};
    const skip = (page - 1) * limit;

    const where: Prisma.report_email_configsWhereInput = {
      reportType,
    };

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          user: {
            select: { id: true, name: true, email: true },
          },
          emailTemplate: {
            select: { id: true, name: true, subject: true },
          },
          distributionList: {
            select: { id: true, name: true, emails: true },
          },
          _count: {
            select: { emailDeliveries: true },
          },
        },
      }),
      this.model.count({ where }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Find active configurations by report type
   */
  async findActiveByReportType(reportType: string) {
    return this.model.findMany({
      where: {
        reportType,
        isActive: true,
      },
      orderBy: { name: 'asc' },
      include: {
        emailTemplate: {
          select: { id: true, name: true, subject: true, bodyHtml: true },
        },
        distributionList: {
          select: { id: true, name: true, emails: true },
        },
      },
    });
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.report_email_configsGetPayload<{}>,
    string,
    Prisma.report_email_configsCreateInput,
    Prisma.report_email_configsUpdateInput
  > {
    const repo = new ReportEmailConfigRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}

/**
 * Report Email Delivery Repository
 */
export class ReportEmailDeliveryRepository extends PrismaRepository<
  Prisma.report_email_deliveriesGetPayload<{}>,
  string,
  Prisma.report_email_deliveriesCreateInput,
  Prisma.report_email_deliveriesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('report_email_deliveries');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find deliveries with filters
   */
  async findWithFilters(options: {
    page?: number;
    limit?: number;
    configId?: string;
    reportType?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { page = 1, limit = 20, configId, reportType, status, startDate, endDate } = options;
    const skip = (page - 1) * limit;

    const where: Prisma.report_email_deliveriesWhereInput = {};

    if (configId) where.configId = configId;
    if (reportType) where.reportType = reportType;
    if (status) where.status = status;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [data, total] = await Promise.all([
      this.model.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          config: {
            select: { id: true, name: true, reportType: true },
          },
          user: {
            select: { id: true, name: true, email: true },
          },
        },
      }),
      this.model.count({ where }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get delivery statistics
   */
  async getDeliveryStats(options?: {
    configId?: string;
    reportType?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { configId, reportType, startDate, endDate } = options || {};

    const where: Prisma.report_email_deliveriesWhereInput = {};

    if (configId) where.configId = configId;
    if (reportType) where.reportType = reportType;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const stats = await this.model.groupBy({
      by: ['status'],
      where,
      _count: { id: true },
      _sum: { sentCount: true, failedCount: true },
    });

    return stats.reduce((acc: any, stat: any) => {
      acc[stat.status] = {
        count: stat._count.id,
        sentCount: stat._sum.sentCount || 0,
        failedCount: stat._sum.failedCount || 0,
      };
      return acc;
    }, {} as Record<string, { count: number; sentCount: number; failedCount: number }>);
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.report_email_deliveriesGetPayload<{}>,
    string,
    Prisma.report_email_deliveriesCreateInput,
    Prisma.report_email_deliveriesUpdateInput
  > {
    const repo = new ReportEmailDeliveryRepository();
    (repo as any).prisma = tx;
    return repo;
  }
}
