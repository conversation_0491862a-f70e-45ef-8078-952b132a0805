import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * GET /api/reports/email/test
 * Test endpoint to verify email distribution system is working
 */
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    return NextResponse.json({
      success: true,
      message: 'Email distribution system is operational',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      endpoints: {
        distributionLists: '/api/reports/email/distribution-lists',
        configs: '/api/reports/email/configs',
        send: '/api/reports/email/send',
        deliveries: '/api/reports/email/deliveries',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in email distribution test:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Email distribution system test failed',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
});
