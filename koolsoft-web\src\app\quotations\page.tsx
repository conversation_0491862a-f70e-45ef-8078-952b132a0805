'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/data-table';
import { useToast } from '@/components/ui/use-toast';
import {
  Plus,
  Search,
  Filter,
  FileDown,
  Eye,
  Edit,
  Copy,
  Trash2,
  FileText,
} from 'lucide-react';

interface Quotation {
  id: string;
  quotationNumber: string;
  quotationDate: string;
  validUntil?: string;
  status: string;
  totalAmount: number;
  customer: {
    id: string;
    name: string;
    city?: string;
  };
  executive: {
    id: string;
    name: string;
  };
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
}

/**
 * Quotations Page
 *
 * This page displays a list of all quotations with filtering, search, and management capabilities.
 * It follows the established KoolSoft UI patterns with primary blue headers and consistent styling.
 */
export default function QuotationsPage() {
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();

  const pageSize = 10;

  // Fetch quotations
  const fetchQuotations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
      });

      const response = await fetch(`/api/quotations?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quotations');
      }

      const data = await response.json();
      if (data.success) {
        setQuotations(data.data);
        setTotalPages(data.pagination.totalPages);
        setTotalCount(data.pagination.totalCount);
      } else {
        throw new Error(data.error || 'Failed to fetch quotations');
      }
    } catch (error) {
      console.error('Error fetching quotations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch quotations. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQuotations();
  }, [currentPage, searchTerm, statusFilter]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle status filter
  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'excel' = 'csv') => {
    try {
      setExporting(true);

      const params = new URLSearchParams();
      params.append('format', format);
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter) params.append('status', statusFilter);

      const response = await fetch(`/api/quotations/export?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export quotations');
      }

      if (format === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `quotations-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        const data = await response.json();
        if (data.success) {
          // For Excel, we would need a frontend library like xlsx
          // For now, we'll download as CSV
          toast({
            title: 'Info',
            description: 'Excel export will be available soon. Downloaded as CSV instead.',
          });
          handleExport('csv');
          return;
        }
      }

      toast({
        title: 'Success',
        description: 'Quotations exported successfully.',
      });
    } catch (error) {
      console.error('Error exporting quotations:', error);
      toast({
        title: 'Error',
        description: 'Failed to export quotations. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setExporting(false);
    }
  };

  // Handle delete quotation
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this quotation?')) {
      return;
    }

    try {
      const response = await fetch(`/api/quotations/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete quotation');
      }

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Quotation deleted successfully.',
        });
        fetchQuotations();
      } else {
        throw new Error(data.error || 'Failed to delete quotation');
      }
    } catch (error) {
      console.error('Error deleting quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete quotation. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle duplicate quotation
  const handleDuplicate = async (id: string) => {
    try {
      const response = await fetch(`/api/quotations/${id}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate quotation');
      }

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Quotation duplicated successfully.',
        });
        fetchQuotations();
      } else {
        throw new Error(data.error || 'Failed to duplicate quotation');
      }
    } catch (error) {
      console.error('Error duplicating quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to duplicate quotation. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'secondary';
      case 'SENT':
        return 'default';
      case 'ACCEPTED':
        return 'default';
      case 'REJECTED':
        return 'destructive';
      case 'EXPIRED':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Table columns
  const columns = [
    {
      accessorKey: 'quotationNumber',
      header: 'Quotation #',
      cell: ({ row }: any) => (
        <div className="font-medium">{row.original.quotationNumber}</div>
      ),
    },
    {
      accessorKey: 'customer.name',
      header: 'Customer',
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.customer.name}</div>
          {row.original.customer.city && (
            <div className="text-sm text-gray-500">{row.original.customer.city}</div>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'quotationDate',
      header: 'Date',
      cell: ({ row }: any) => (
        <div>{new Date(row.original.quotationDate).toLocaleDateString()}</div>
      ),
    },
    {
      accessorKey: 'validUntil',
      header: 'Valid Until',
      cell: ({ row }: any) => (
        <div>
          {row.original.validUntil
            ? new Date(row.original.validUntil).toLocaleDateString()
            : '-'}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: any) => (
        <Badge variant={getStatusBadgeVariant(row.original.status)}>
          {row.original.status}
        </Badge>
      ),
    },
    {
      accessorKey: 'totalAmount',
      header: 'Amount',
      cell: ({ row }: any) => (
        <div className="font-medium">
          ₹{row.original.totalAmount.toLocaleString()}
        </div>
      ),
    },
    {
      accessorKey: 'executive.name',
      header: 'Executive',
      cell: ({ row }: any) => (
        <div className="text-sm">{row.original.executive.name}</div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Button asChild variant="ghost" size="sm">
            <Link href={`/quotations/${row.original.id}`}>
              <Eye className="h-4 w-4" />
            </Link>
          </Button>
          <Button asChild variant="ghost" size="sm">
            <Link href={`/quotations/${row.original.id}/edit`}>
              <Edit className="h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDuplicate(row.original.id)}
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DashboardLayout title="Quotations" requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}>
      <div className="space-y-6">
        {/* Header Card */}
        <Card>
          <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Quotation Management</span>
              </CardTitle>
              <CardDescription className="text-gray-100">
                Manage quotations, track status, and generate proposals for customers
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handleExport('csv')}
                disabled={exporting}
              >
                <FileDown className="h-4 w-4 mr-2" />
                {exporting ? 'Exporting...' : 'Export'}
              </Button>
              <Button asChild variant="secondary" size="sm">
                <Link href="/quotations/new">
                  <Plus className="h-4 w-4 mr-2" />
                  New Quotation
                </Link>
              </Button>
            </div>
          </CardHeader>
        </Card>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search quotations..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => handleStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Status</option>
                  <option value="DRAFT">Draft</option>
                  <option value="SENT">Sent</option>
                  <option value="ACCEPTED">Accepted</option>
                  <option value="REJECTED">Rejected</option>
                  <option value="EXPIRED">Expired</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card>
          <CardContent className="pt-6">
            <DataTable
              columns={columns}
              data={quotations}
              loading={loading}
              pagination={{
                page: currentPage,
                total: totalCount,
                pageSize,
                onPageChange: setCurrentPage,
              }}
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
