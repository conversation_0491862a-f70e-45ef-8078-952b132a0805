-- Create quotations table
CREATE TABLE IF NOT EXISTS "quotations" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "quotation_number" TEXT UNIQUE NOT NULL,
  "customer_id" TEXT NOT NULL,
  "executive_id" TEXT NOT NULL,
  "quotation_date" TIMESTAMP(3) NOT NULL,
  "valid_until" TIMESTAMP(3),
  "status" TEXT NOT NULL DEFAULT 'DRAFT',
  "contact_person" TEXT,
  "contact_phone" TEXT,
  "contact_email" TEXT,
  "subject" TEXT,
  "notes" TEXT,
  "terms_conditions" TEXT,
  "subtotal" DECIMAL(12,2) NOT NULL DEFAULT 0,
  "tax_amount" DECIMAL(12,2) NOT NULL DEFAULT 0,
  "total_amount" DECIMAL(12,2) NOT NULL DEFAULT 0,
  "discount" DECIMAL(12,2) DEFAULT 0,
  "discount_type" TEXT DEFAULT 'PERCENTAGE',
  "original_id" INTEGER,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "quotations_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT "quotations_executive_id_fkey" FOREIGN KEY ("executive_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- Create quotation_items table
CREATE TABLE IF NOT EXISTS "quotation_items" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "quotation_id" TEXT NOT NULL,
  "product_id" TEXT,
  "model_id" TEXT,
  "brand_id" TEXT,
  "description" TEXT NOT NULL,
  "quantity" INTEGER NOT NULL DEFAULT 1,
  "unit_price" DECIMAL(12,2) NOT NULL,
  "total_price" DECIMAL(12,2) NOT NULL,
  "tax_rate" DECIMAL(5,2) DEFAULT 0,
  "tax_amount" DECIMAL(12,2) DEFAULT 0,
  "discount" DECIMAL(12,2) DEFAULT 0,
  "discount_type" TEXT DEFAULT 'PERCENTAGE',
  "specifications" TEXT,
  "notes" TEXT,
  "sort_order" INTEGER DEFAULT 0,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  CONSTRAINT "quotation_items_quotation_id_fkey" FOREIGN KEY ("quotation_id") REFERENCES "quotations"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT "quotation_items_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT "quotation_items_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "models"("id") ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT "quotation_items_brand_id_fkey" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- Create indexes for quotations table
CREATE INDEX IF NOT EXISTS "quotations_customer_id_idx" ON "quotations"("customer_id");
CREATE INDEX IF NOT EXISTS "quotations_executive_id_idx" ON "quotations"("executive_id");
CREATE INDEX IF NOT EXISTS "quotations_quotation_date_idx" ON "quotations"("quotation_date");
CREATE INDEX IF NOT EXISTS "quotations_status_idx" ON "quotations"("status");
CREATE INDEX IF NOT EXISTS "quotations_quotation_number_idx" ON "quotations"("quotation_number");

-- Create indexes for quotation_items table
CREATE INDEX IF NOT EXISTS "quotation_items_quotation_id_idx" ON "quotation_items"("quotation_id");
CREATE INDEX IF NOT EXISTS "quotation_items_product_id_idx" ON "quotation_items"("product_id");
CREATE INDEX IF NOT EXISTS "quotation_items_model_id_idx" ON "quotation_items"("model_id");
CREATE INDEX IF NOT EXISTS "quotation_items_sort_order_idx" ON "quotation_items"("sort_order");

-- Create function to generate quotation number
CREATE OR REPLACE FUNCTION generate_quotation_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    sequence_num INTEGER;
    quotation_number TEXT;
BEGIN
    -- Get current year
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Get next sequence number for this year
    SELECT COALESCE(MAX(CAST(SUBSTRING(quotation_number FROM 'QT' || current_year || '-(.*)') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM quotations
    WHERE quotation_number LIKE 'QT' || current_year || '-%';
    
    -- Format quotation number as QT2025-0001
    quotation_number := 'QT' || current_year || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN quotation_number;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate quotation number
CREATE OR REPLACE FUNCTION set_quotation_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.quotation_number IS NULL OR NEW.quotation_number = '' THEN
        NEW.quotation_number := generate_quotation_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_quotation_number
    BEFORE INSERT ON quotations
    FOR EACH ROW
    EXECUTE FUNCTION set_quotation_number();

-- Create trigger to update quotation totals when items change
CREATE OR REPLACE FUNCTION update_quotation_totals()
RETURNS TRIGGER AS $$
DECLARE
    quotation_id_to_update TEXT;
    new_subtotal DECIMAL(12,2);
    new_tax_amount DECIMAL(12,2);
    new_total_amount DECIMAL(12,2);
BEGIN
    -- Determine which quotation to update
    IF TG_OP = 'DELETE' THEN
        quotation_id_to_update := OLD.quotation_id;
    ELSE
        quotation_id_to_update := NEW.quotation_id;
    END IF;
    
    -- Calculate new totals
    SELECT 
        COALESCE(SUM(total_price), 0),
        COALESCE(SUM(tax_amount), 0)
    INTO new_subtotal, new_tax_amount
    FROM quotation_items
    WHERE quotation_id = quotation_id_to_update;
    
    new_total_amount := new_subtotal + new_tax_amount;
    
    -- Update quotation totals
    UPDATE quotations
    SET 
        subtotal = new_subtotal,
        tax_amount = new_tax_amount,
        total_amount = new_total_amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = quotation_id_to_update;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_quotation_totals
    AFTER INSERT OR UPDATE OR DELETE ON quotation_items
    FOR EACH ROW
    EXECUTE FUNCTION update_quotation_totals();
