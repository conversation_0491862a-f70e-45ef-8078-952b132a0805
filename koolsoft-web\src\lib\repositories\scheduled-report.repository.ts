import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import {
  CreateScheduledReportData,
  UpdateScheduledReportData,
  ListScheduledReportsParams,
} from '@/lib/validations/scheduled-report.schema';
import { getNextExecutionTime } from '@/lib/utils/cron-parser';

/**
 * Repository for managing scheduled reports
 */
export class ScheduledReportRepository {
  /**
   * Create a new scheduled report
   */
  async create(data: CreateScheduledReportData & { createdBy: string }) {
    return prisma.scheduled_reports.create({
      data: {
        ...data,
        nextRunAt: this.calculateNextRunTime(data.cronExpression),
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
    });
  }

  /**
   * Get scheduled report by ID
   */
  async findById(id: string) {
    return prisma.scheduled_reports.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        executions: {
          orderBy: { createdAt: 'desc' },
          take: 5,
          select: {
            id: true,
            status: true,
            startedAt: true,
            completedAt: true,
            errorMessage: true,
            emailsSent: true,
            recordCount: true,
            executionTime: true,
          },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
    });
  }

  /**
   * List scheduled reports with filtering and pagination
   */
  async findMany(params: ListScheduledReportsParams) {
    const whereClause: Prisma.scheduled_reportsWhereInput = {};

    // Apply filters
    if (params.reportType) {
      whereClause.reportType = params.reportType;
    }

    if (params.isActive !== undefined) {
      whereClause.isActive = params.isActive;
    }

    if (params.search) {
      whereClause.OR = [
        { name: { contains: params.search, mode: 'insensitive' } },
        { description: { contains: params.search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.scheduled_reports.findMany({
        where: whereClause,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              executions: true,
            },
          },
        },
        orderBy: {
          [params.sortBy]: params.sortOrder,
        },
        skip: (params.page - 1) * params.limit,
        take: params.limit,
      }),
      prisma.scheduled_reports.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
      },
    };
  }

  /**
   * Update scheduled report
   */
  async update(id: string, data: UpdateScheduledReportData) {
    const updateData: any = { ...data };
    
    // Recalculate next run time if cron expression changed
    if (data.cronExpression) {
      updateData.nextRunAt = this.calculateNextRunTime(data.cronExpression);
    }

    return prisma.scheduled_reports.update({
      where: { id },
      data: updateData,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            executions: true,
          },
        },
      },
    });
  }

  /**
   * Delete scheduled report
   */
  async delete(id: string) {
    return prisma.scheduled_reports.delete({
      where: { id },
    });
  }

  /**
   * Get reports that are due for execution
   */
  async findDueReports() {
    const now = new Date();
    
    return prisma.scheduled_reports.findMany({
      where: {
        isActive: true,
        nextRunAt: {
          lte: now,
        },
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Update last run time and calculate next run time
   */
  async updateRunTimes(id: string, lastRunAt: Date) {
    const report = await prisma.scheduled_reports.findUnique({
      where: { id },
      select: { cronExpression: true },
    });

    if (!report) {
      throw new Error('Scheduled report not found');
    }

    const nextRunAt = this.calculateNextRunTime(report.cronExpression);

    return prisma.scheduled_reports.update({
      where: { id },
      data: {
        lastRunAt,
        nextRunAt,
      },
    });
  }

  /**
   * Toggle active status
   */
  async toggleActive(id: string) {
    const report = await prisma.scheduled_reports.findUnique({
      where: { id },
      select: { isActive: true, cronExpression: true },
    });

    if (!report) {
      throw new Error('Scheduled report not found');
    }

    const newActiveStatus = !report.isActive;
    const updateData: any = { isActive: newActiveStatus };

    // If activating, calculate next run time
    if (newActiveStatus) {
      updateData.nextRunAt = this.calculateNextRunTime(report.cronExpression);
    }

    return prisma.scheduled_reports.update({
      where: { id },
      data: updateData,
    });
  }

  /**
   * Get statistics for scheduled reports
   */
  async getStatistics() {
    const [total, active, inactive, totalExecutions, failedExecutions] = await Promise.all([
      prisma.scheduled_reports.count(),
      prisma.scheduled_reports.count({ where: { isActive: true } }),
      prisma.scheduled_reports.count({ where: { isActive: false } }),
      prisma.scheduled_report_executions.count(),
      prisma.scheduled_report_executions.count({ where: { status: 'FAILED' } }),
    ]);

    return {
      total,
      active,
      inactive,
      totalExecutions,
      failedExecutions,
      successRate: totalExecutions > 0 ? ((totalExecutions - failedExecutions) / totalExecutions) * 100 : 0,
    };
  }

  /**
   * Calculate next run time based on cron expression
   */
  private calculateNextRunTime(cronExpression: string): Date {
    return getNextExecutionTime(cronExpression);
  }
}
