import { NextRequest, NextResponse } from 'next/server';
import { getAMCComponentRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';
import { componentSearchSchema, createComponentSchema } from '@/lib/validations/component.schema';

/**
 * GET /api/amc/components
 * Get components with optional filtering
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const queryParams = Object.fromEntries(searchParams.entries());

      // Validate query parameters
      const validatedQuery = componentSearchSchema.parse(queryParams);

      const amcComponentRepository = getAMCComponentRepository();

      // Use the search method for comprehensive filtering
      const result = await amcComponentRepository.search({
        query: validatedQuery.query,
        machineId: validatedQuery.machineId,
        componentNo: validatedQuery.componentNo,
        warrantyStatus: validatedQuery.warrantyStatus,
        skip: validatedQuery.skip,
        take: validatedQuery.take,
        orderBy: validatedQuery.orderBy,
        orderDirection: validatedQuery.orderDirection,
      });

      return NextResponse.json({
        components: result.components,
        meta: {
          total: result.total,
          skip: validatedQuery.skip,
          take: validatedQuery.take,
          orderBy: validatedQuery.orderBy,
          orderDirection: validatedQuery.orderDirection,
        },
      });
    } catch (error) {
      console.error('Error fetching components:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid query parameters', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch components' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/amc/components
 * Create a new component
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createComponentSchema.parse(body);

      const amcComponentRepository = getAMCComponentRepository();

      // Check if serial number already exists (if provided)
      if (validatedData.serialNumber) {
        const existingComponent = await amcComponentRepository.findBySerialNumber(validatedData.serialNumber);
        if (existingComponent) {
          return NextResponse.json(
            { error: 'A component with this serial number already exists' },
            { status: 409 }
          );
        }
      }

      // Create the component with proper Prisma relationship
      const component = await amcComponentRepository.create({
        machine: {
          connect: { id: validatedData.machineId }
        },
        componentNo: validatedData.componentNo,
        serialNumber: validatedData.serialNumber,
        warrantyDate: validatedData.warrantyDate,
        section: validatedData.section,
        originalAmcId: validatedData.originalAmcId,
        originalAssetNo: validatedData.originalAssetNo,
        originalComponentNo: validatedData.originalComponentNo,
      });

      return NextResponse.json({
        message: 'Component created successfully',
        component,
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating component:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid request data', details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to create component' },
        { status: 500 }
      );
    }
  }
);
