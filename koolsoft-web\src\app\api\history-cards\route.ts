import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getHistoryCardRepository } from '@/lib/repositories';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

// Validation schemas
const historyCardCreateSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID'),
  cardNo: z.number().int().positive().optional(),
  source: z.enum(['AMC', 'INW', 'OTW']).optional(),
  amcId: z.string().uuid().optional(),
  inWarrantyId: z.string().uuid().optional(),
  outWarrantyId: z.string().uuid().optional(),
  toCardNo: z.number().int().positive().optional(),
  originalId: z.number().int().positive().optional(),
});

const historyCardUpdateSchema = historyCardCreateSchema.partial();

const historyCardQuerySchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(10),
  search: z.string().optional(),
  customerId: z.string().uuid().optional(),
  source: z.enum(['AMC', 'INW', 'OTW']).optional(),
  amcId: z.string().uuid().optional(),
  inWarrantyId: z.string().uuid().optional(),
  outWarrantyId: z.string().uuid().optional(),
  sortBy: z.enum(['createdAt', 'cardNo', 'source']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * GET /api/history-cards
 * Retrieve history cards with pagination, filtering, and search
 */
async function getHistoryCards(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Validate query parameters
    const validatedQuery = historyCardQuerySchema.parse(queryParams);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Calculate pagination
    const skip = (validatedQuery.page - 1) * validatedQuery.limit;
    
    // Build where clause for filtering
    const where: any = {};
    
    if (validatedQuery.customerId) {
      where.customerId = validatedQuery.customerId;
    }
    
    if (validatedQuery.source) {
      where.source = validatedQuery.source;
    }
    
    if (validatedQuery.amcId) {
      where.amcId = validatedQuery.amcId;
    }
    
    if (validatedQuery.inWarrantyId) {
      where.inWarrantyId = validatedQuery.inWarrantyId;
    }
    
    if (validatedQuery.outWarrantyId) {
      where.outWarrantyId = validatedQuery.outWarrantyId;
    }
    
    if (validatedQuery.search) {
      where.OR = [
        {
          customer: {
            name: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
        },
        {
          cardNo: {
            equals: parseInt(validatedQuery.search) || undefined,
          },
        },
      ];
    }
    
    // Get history cards with relations using the model directly
    const [historyCards, total] = await Promise.all([
      (historyCardRepository as any).model.findMany({
        where,
        skip,
        take: validatedQuery.limit,
        orderBy: {
          [validatedQuery.sortBy]: validatedQuery.sortOrder,
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              address: true,
              city: true,
              phone: true,
            },
          },
          sections: {
            select: {
              id: true,
              sectionCode: true,
              content: true,
              createdAt: true,
            },
            orderBy: {
              sectionCode: 'asc',
            },
          },
        },
      }),
      historyCardRepository.count(where),
    ]);
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(total / validatedQuery.limit);
    const hasNextPage = validatedQuery.page < totalPages;
    const hasPreviousPage = validatedQuery.page > 1;
    
    return NextResponse.json({
      success: true,
      data: historyCards,
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    });
  } catch (error) {
    console.error('Error fetching history cards:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history cards',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/history-cards
 * Create a new history card
 */
async function createHistoryCard(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = historyCardCreateSchema.parse(body);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Create the history card using the model directly
    const historyCard = await (historyCardRepository as any).model.create({
      data: validatedData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            address: true,
            city: true,
            phone: true,
          },
        },
        sections: true,
      },
    });
    
    return NextResponse.json({
      success: true,
      data: historyCard,
      message: 'History card created successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating history card:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create history card',
      },
      { status: 500 }
    );
  }
}

// Export handlers with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forHistoryCardRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], getHistoryCards),
  {
    action: 'list_history_cards',
    getEntityId: () => 'list',
  }
);

export const POST = ActivityLoggerMiddlewareFactory.forHistoryCardRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE'], createHistoryCard),
  {
    action: 'create_history_card',
    getEntityId: (req: NextRequest) => {
      try {
        // Note: res parameter not available in this context
        // const responseData = res.json();
        return 'unknown';
      } catch {
        return 'unknown';
      }
    },
  }
);
