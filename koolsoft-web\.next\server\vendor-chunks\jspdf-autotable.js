"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jspdf-autotable";
exports.ids = ["vendor-chunks/jspdf-autotable"];
exports.modules = {

/***/ "(rsc)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cell: () => (/* binding */ Cell),\n/* harmony export */   CellHookData: () => (/* binding */ CellHookData),\n/* harmony export */   Column: () => (/* binding */ Column),\n/* harmony export */   HookData: () => (/* binding */ HookData),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   __createTable: () => (/* binding */ __createTable),\n/* harmony export */   __drawTable: () => (/* binding */ __drawTable),\n/* harmony export */   applyPlugin: () => (/* binding */ applyPlugin),\n/* harmony export */   autoTable: () => (/* binding */ autoTable),\n/* harmony export */   \"default\": () => (/* binding */ autoTable)\n/* harmony export */ });\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText(text, x, y, styles, doc) {\n  styles = styles || {};\n  var PHYSICAL_LINE_HEIGHT = 1.15;\n  var k = doc.internal.scaleFactor;\n  var fontSize = doc.internal.getFontSize() / k;\n  var lineHeightFactor = doc.getLineHeightFactor ? doc.getLineHeightFactor() : PHYSICAL_LINE_HEIGHT;\n  var lineHeight = fontSize * lineHeightFactor;\n  var splitRegex = /\\r\\n|\\r|\\n/g;\n  var splitText = '';\n  var lineCount = 1;\n  if (styles.valign === 'middle' || styles.valign === 'bottom' || styles.halign === 'center' || styles.halign === 'right') {\n    splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n    lineCount = splitText.length || 1;\n  }\n  // Align the top\n  y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n  if (styles.valign === 'middle') y -= lineCount / 2 * lineHeight;else if (styles.valign === 'bottom') y -= lineCount * lineHeight;\n  if (styles.halign === 'center' || styles.halign === 'right') {\n    var alignSize = fontSize;\n    if (styles.halign === 'center') alignSize *= 0.5;\n    if (splitText && lineCount >= 1) {\n      for (var iLine = 0; iLine < splitText.length; iLine++) {\n        doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n        y += lineHeight;\n      }\n      return doc;\n    }\n    x -= doc.getStringUnitWidth(text) * alignSize;\n  }\n  if (styles.halign === 'justify') {\n    doc.text(text, x, y, {\n      maxWidth: styles.maxWidth || 100,\n      align: 'justify'\n    });\n  } else {\n    doc.text(text, x, y);\n  }\n  return doc;\n}\nvar globalDefaults = {};\nvar DocHandler = /** @class */function () {\n  function DocHandler(jsPDFDocument) {\n    this.jsPDFDocument = jsPDFDocument;\n    this.userStyles = {\n      // Black for versions of jspdf without getTextColor\n      textColor: jsPDFDocument.getTextColor ? this.jsPDFDocument.getTextColor() : 0,\n      fontSize: jsPDFDocument.internal.getFontSize(),\n      fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n      font: jsPDFDocument.internal.getFont().fontName,\n      // 0 for versions of jspdf without getLineWidth\n      lineWidth: jsPDFDocument.getLineWidth ? this.jsPDFDocument.getLineWidth() : 0,\n      // Black for versions of jspdf without getDrawColor\n      lineColor: jsPDFDocument.getDrawColor ? this.jsPDFDocument.getDrawColor() : 0\n    };\n  }\n  DocHandler.setDefaults = function (defaults, doc) {\n    if (doc === void 0) {\n      doc = null;\n    }\n    if (doc) {\n      doc.__autoTableDocumentDefaults = defaults;\n    } else {\n      globalDefaults = defaults;\n    }\n  };\n  DocHandler.unifyColor = function (c) {\n    if (Array.isArray(c)) {\n      return c;\n    } else if (typeof c === 'number') {\n      return [c, c, c];\n    } else if (typeof c === 'string') {\n      return [c];\n    } else {\n      return null;\n    }\n  };\n  DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n    // Font style needs to be applied before font\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n    var _a, _b, _c;\n    if (fontOnly === void 0) {\n      fontOnly = false;\n    }\n    if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n      this.jsPDFDocument.setFontStyle(styles.fontStyle);\n    }\n    var _d = this.jsPDFDocument.internal.getFont(),\n      fontStyle = _d.fontStyle,\n      fontName = _d.fontName;\n    if (styles.font) fontName = styles.font;\n    if (styles.fontStyle) {\n      fontStyle = styles.fontStyle;\n      var availableFontStyles = this.getFontList()[fontName];\n      if (availableFontStyles && availableFontStyles.indexOf(fontStyle) === -1 && this.jsPDFDocument.setFontStyle) {\n        // Common issue was that the default bold in headers\n        // made custom fonts not work. For example:\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n        this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n        fontStyle = availableFontStyles[0];\n      }\n    }\n    this.jsPDFDocument.setFont(fontName, fontStyle);\n    if (styles.fontSize) this.jsPDFDocument.setFontSize(styles.fontSize);\n    if (fontOnly) {\n      return; // Performance improvement\n    }\n\n    var color = DocHandler.unifyColor(styles.fillColor);\n    if (color) (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n    color = DocHandler.unifyColor(styles.textColor);\n    if (color) (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n    color = DocHandler.unifyColor(styles.lineColor);\n    if (color) (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n    if (typeof styles.lineWidth === 'number') {\n      this.jsPDFDocument.setLineWidth(styles.lineWidth);\n    }\n  };\n  DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n    return this.jsPDFDocument.splitTextToSize(text, size, opts);\n  };\n  /**\n   * Adds a rectangle to the PDF\n   * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n   * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n   * @param width Width (in units declared at inception of PDF document)\n   * @param height Height (in units declared at inception of PDF document)\n   * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n   */\n  DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n    // null is excluded from fillStyle possible values because it isn't needed\n    // and is prone to bugs as it's used to postpone setting the style\n    // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n    return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n  };\n  DocHandler.prototype.getLastAutoTable = function () {\n    return this.jsPDFDocument.lastAutoTable || null;\n  };\n  DocHandler.prototype.getTextWidth = function (text) {\n    return this.jsPDFDocument.getTextWidth(text);\n  };\n  DocHandler.prototype.getDocument = function () {\n    return this.jsPDFDocument;\n  };\n  DocHandler.prototype.setPage = function (page) {\n    this.jsPDFDocument.setPage(page);\n  };\n  DocHandler.prototype.addPage = function () {\n    return this.jsPDFDocument.addPage();\n  };\n  DocHandler.prototype.getFontList = function () {\n    return this.jsPDFDocument.getFontList();\n  };\n  DocHandler.prototype.getGlobalOptions = function () {\n    return globalDefaults || {};\n  };\n  DocHandler.prototype.getDocumentOptions = function () {\n    return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n  };\n  DocHandler.prototype.pageSize = function () {\n    var pageSize = this.jsPDFDocument.internal.pageSize;\n    // JSPDF 1.4 uses get functions instead of properties on pageSize\n    if (pageSize.width == null) {\n      pageSize = {\n        width: pageSize.getWidth(),\n        height: pageSize.getHeight()\n      };\n    }\n    return pageSize;\n  };\n  DocHandler.prototype.scaleFactor = function () {\n    return this.jsPDFDocument.internal.scaleFactor;\n  };\n  DocHandler.prototype.getLineHeightFactor = function () {\n    var doc = this.jsPDFDocument;\n    return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n  };\n  DocHandler.prototype.getLineHeight = function (fontSize) {\n    return fontSize / this.scaleFactor() * this.getLineHeightFactor();\n  };\n  DocHandler.prototype.pageNumber = function () {\n    var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n    if (!pageInfo) {\n      // Only recent versions of jspdf has pageInfo\n      return this.jsPDFDocument.internal.getNumberOfPages();\n    }\n    return pageInfo.pageNumber;\n  };\n  return DocHandler;\n}();\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nvar HtmlRowInput = /** @class */function (_super) {\n  __extends(HtmlRowInput, _super);\n  function HtmlRowInput(element) {\n    var _this = _super.call(this) || this;\n    _this._element = element;\n    return _this;\n  }\n  return HtmlRowInput;\n}(Array);\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n  return {\n    font: 'helvetica',\n    // helvetica, times, courier\n    fontStyle: 'normal',\n    // normal, bold, italic, bolditalic\n    overflow: 'linebreak',\n    // linebreak, ellipsize, visible or hidden\n    fillColor: false,\n    // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n    textColor: 20,\n    halign: 'left',\n    // left, center, right, justify\n    valign: 'top',\n    // top, middle, bottom\n    fontSize: 10,\n    cellPadding: 5 / scaleFactor,\n    // number or {top,left,right,left,vertical,horizontal}\n    lineColor: 200,\n    lineWidth: 0,\n    cellWidth: 'auto',\n    // 'auto'|'wrap'|number\n    minCellHeight: 0,\n    minCellWidth: 0\n  };\n}\nfunction getTheme(name) {\n  var themes = {\n    striped: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal'\n      },\n      head: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [41, 128, 185],\n        fontStyle: 'bold'\n      },\n      alternateRow: {\n        fillColor: 245\n      }\n    },\n    grid: {\n      table: {\n        fillColor: 255,\n        textColor: 80,\n        fontStyle: 'normal',\n        lineWidth: 0.1\n      },\n      head: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      body: {},\n      foot: {\n        textColor: 255,\n        fillColor: [26, 188, 156],\n        fontStyle: 'bold',\n        lineWidth: 0\n      },\n      alternateRow: {}\n    },\n    plain: {\n      head: {\n        fontStyle: 'bold'\n      },\n      foot: {\n        fontStyle: 'bold'\n      }\n    }\n  };\n  return themes[name];\n}\nfunction getStringWidth(text, styles, doc) {\n  doc.applyStyles(styles, true);\n  var textArr = Array.isArray(text) ? text : [text];\n  var widestLineWidth = textArr.map(function (text) {\n    return doc.getTextWidth(text);\n  }).reduce(function (a, b) {\n    return Math.max(a, b);\n  }, 0);\n  return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n  var lineWidth = table.settings.tableLineWidth;\n  var lineColor = table.settings.tableLineColor;\n  doc.applyStyles({\n    lineWidth: lineWidth,\n    lineColor: lineColor\n  });\n  var fillStyle = getFillStyle(lineWidth, false);\n  if (fillStyle) {\n    doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n  }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n  var drawLine = lineWidth > 0;\n  var drawBackground = fillColor || fillColor === 0;\n  if (drawLine && drawBackground) {\n    return 'DF'; // Fill then stroke\n  } else if (drawLine) {\n    return 'S'; // Only stroke (transparent background)\n  } else if (drawBackground) {\n    return 'F'; // Only fill, no stroke\n  } else {\n    return null;\n  }\n}\nfunction parseSpacing(value, defaultValue) {\n  var _a, _b, _c, _d;\n  value = value || defaultValue;\n  if (Array.isArray(value)) {\n    if (value.length >= 4) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[3]\n      };\n    } else if (value.length === 3) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[2],\n        left: value[1]\n      };\n    } else if (value.length === 2) {\n      return {\n        top: value[0],\n        right: value[1],\n        bottom: value[0],\n        left: value[1]\n      };\n    } else if (value.length === 1) {\n      value = value[0];\n    } else {\n      value = defaultValue;\n    }\n  }\n  if (typeof value === 'object') {\n    if (typeof value.vertical === 'number') {\n      value.top = value.vertical;\n      value.bottom = value.vertical;\n    }\n    if (typeof value.horizontal === 'number') {\n      value.right = value.horizontal;\n      value.left = value.horizontal;\n    }\n    return {\n      left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n      top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n      right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n      bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue\n    };\n  }\n  if (typeof value !== 'number') {\n    value = defaultValue;\n  }\n  return {\n    top: value,\n    right: value,\n    bottom: value,\n    left: value\n  };\n}\nfunction getPageAvailableWidth(doc, table) {\n  var margins = parseSpacing(table.settings.margin, 0);\n  return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n  var result = {};\n  var pxScaleFactor = 96 / 72;\n  var backgroundColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['backgroundColor'];\n  });\n  if (backgroundColor != null) result.fillColor = backgroundColor;\n  var textColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)['color'];\n  });\n  if (textColor != null) result.textColor = textColor;\n  var padding = parsePadding(style, scaleFactor);\n  if (padding) result.cellPadding = padding;\n  var borderColorSide = 'borderTopColor';\n  var finalScaleFactor = pxScaleFactor * scaleFactor;\n  var btw = style.borderTopWidth;\n  if (style.borderBottomWidth === btw && style.borderRightWidth === btw && style.borderLeftWidth === btw) {\n    var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n    if (borderWidth) result.lineWidth = borderWidth;\n  } else {\n    result.lineWidth = {\n      top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n      right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n      bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n      left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor\n    };\n    // Choose border color of first available side\n    // could be improved by supporting object as lineColor\n    if (!result.lineWidth.top) {\n      if (result.lineWidth.right) {\n        borderColorSide = 'borderRightColor';\n      } else if (result.lineWidth.bottom) {\n        borderColorSide = 'borderBottomColor';\n      } else if (result.lineWidth.left) {\n        borderColorSide = 'borderLeftColor';\n      }\n    }\n  }\n  var borderColor = parseColor(element, function (elem) {\n    return window.getComputedStyle(elem)[borderColorSide];\n  });\n  if (borderColor != null) result.lineColor = borderColor;\n  var accepted = ['left', 'right', 'center', 'justify'];\n  if (accepted.indexOf(style.textAlign) !== -1) {\n    result.halign = style.textAlign;\n  }\n  accepted = ['middle', 'bottom', 'top'];\n  if (accepted.indexOf(style.verticalAlign) !== -1) {\n    result.valign = style.verticalAlign;\n  }\n  var res = parseInt(style.fontSize || '');\n  if (!isNaN(res)) result.fontSize = res / pxScaleFactor;\n  var fontStyle = parseFontStyle(style);\n  if (fontStyle) result.fontStyle = fontStyle;\n  var font = (style.fontFamily || '').toLowerCase();\n  if (supportedFonts.indexOf(font) !== -1) {\n    result.font = font;\n  }\n  return result;\n}\nfunction parseFontStyle(style) {\n  var res = '';\n  if (style.fontWeight === 'bold' || style.fontWeight === 'bolder' || parseInt(style.fontWeight) >= 700) {\n    res = 'bold';\n  }\n  if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n    res += 'italic';\n  }\n  return res;\n}\nfunction parseColor(element, styleGetter) {\n  var cssColor = realColor(element, styleGetter);\n  if (!cssColor) return null;\n  var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n  if (!rgba || !Array.isArray(rgba)) {\n    return null;\n  }\n  var color = [parseInt(rgba[1]), parseInt(rgba[2]), parseInt(rgba[3])];\n  var alpha = parseInt(rgba[4]);\n  if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n    return null;\n  }\n  return color;\n}\nfunction realColor(elem, styleGetter) {\n  var bg = styleGetter(elem);\n  if (bg === 'rgba(0, 0, 0, 0)' || bg === 'transparent' || bg === 'initial' || bg === 'inherit') {\n    if (elem.parentElement == null) {\n      return null;\n    }\n    return realColor(elem.parentElement, styleGetter);\n  } else {\n    return bg;\n  }\n}\nfunction parsePadding(style, scaleFactor) {\n  var val = [style.paddingTop, style.paddingRight, style.paddingBottom, style.paddingLeft];\n  var pxScaleFactor = 96 / (72 / scaleFactor);\n  var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n  var inputPadding = val.map(function (n) {\n    return parseInt(n || '0') / pxScaleFactor;\n  });\n  var padding = parseSpacing(inputPadding, 0);\n  if (linePadding > padding.top) {\n    padding.top = linePadding;\n  }\n  if (linePadding > padding.bottom) {\n    padding.bottom = linePadding;\n  }\n  return padding;\n}\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n  var _a, _b;\n  if (includeHiddenHtml === void 0) {\n    includeHiddenHtml = false;\n  }\n  if (useCss === void 0) {\n    useCss = false;\n  }\n  var tableElement;\n  if (typeof input === 'string') {\n    tableElement = window.document.querySelector(input);\n  } else {\n    tableElement = input;\n  }\n  var supportedFonts = Object.keys(doc.getFontList());\n  var scaleFactor = doc.scaleFactor();\n  var head = [],\n    body = [],\n    foot = [];\n  if (!tableElement) {\n    console.error('Html table could not be found with input: ', input);\n    return {\n      head: head,\n      body: body,\n      foot: foot\n    };\n  }\n  for (var i = 0; i < tableElement.rows.length; i++) {\n    var element = tableElement.rows[i];\n    var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n    var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n    if (!row) continue;\n    if (tagName === 'thead') {\n      head.push(row);\n    } else if (tagName === 'tfoot') {\n      foot.push(row);\n    } else {\n      // Add to body both if parent is tbody or table\n      body.push(row);\n    }\n  }\n  return {\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n  var resultRow = new HtmlRowInput(row);\n  for (var i = 0; i < row.cells.length; i++) {\n    var cell = row.cells[i];\n    var style_1 = window.getComputedStyle(cell);\n    if (includeHidden || style_1.display !== 'none') {\n      var cellStyles = void 0;\n      if (useCss) {\n        cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n      }\n      resultRow.push({\n        rowSpan: cell.rowSpan,\n        colSpan: cell.colSpan,\n        styles: cellStyles,\n        _element: cell,\n        content: parseCellContent(cell)\n      });\n    }\n  }\n  var style = window.getComputedStyle(row);\n  if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n    return resultRow;\n  }\n}\nfunction parseCellContent(orgCell) {\n  // Work on cloned node to make sure no changes are applied to html table\n  var cell = orgCell.cloneNode(true);\n  // Remove extra space and line breaks in markup to make it more similar to\n  // what would be shown in html\n  cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n  // Preserve <br> tags as line breaks in the pdf\n  cell.innerHTML = cell.innerHTML.split(/<br.*?>/) //start with '<br' and ends with '>'.\n  .map(function (part) {\n    return part.trim();\n  }).join('\\n');\n  // innerText for ie\n  return cell.innerText || cell.textContent || '';\n}\nfunction validateInput(global, document, current) {\n  for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n    var options = _a[_i];\n    if (options && typeof options !== 'object') {\n      console.error('The options parameter should be of type object, is: ' + typeof options);\n    }\n    if (options.startY && typeof options.startY !== 'number') {\n      console.error('Invalid value for startY option', options.startY);\n      delete options.startY;\n    }\n  }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n  if (target == null) {\n    throw new TypeError('Cannot convert undefined or null to object');\n  }\n  var to = Object(target);\n  for (var index = 1; index < arguments.length; index++) {\n    // eslint-disable-next-line prefer-rest-params\n    var nextSource = arguments[index];\n    if (nextSource != null) {\n      // Skip over if undefined or null\n      for (var nextKey in nextSource) {\n        // Avoid bugs when hasOwnProperty is shadowed\n        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n          to[nextKey] = nextSource[nextKey];\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction parseInput(d, current) {\n  var doc = new DocHandler(d);\n  var document = doc.getDocumentOptions();\n  var global = doc.getGlobalOptions();\n  validateInput(global, document, current);\n  var options = assign({}, global, document, current);\n  var win;\n  if (false) {}\n  var styles = parseStyles(global, document, current);\n  var hooks = parseHooks(global, document, current);\n  var settings = parseSettings(doc, options);\n  var content = parseContent$1(doc, options, win);\n  return {\n    id: current.tableId,\n    content: content,\n    hooks: hooks,\n    styles: styles,\n    settings: settings\n  };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n  var styleOptions = {\n    styles: {},\n    headStyles: {},\n    bodyStyles: {},\n    footStyles: {},\n    alternateRowStyles: {},\n    columnStyles: {}\n  };\n  var _loop_1 = function (prop) {\n    if (prop === 'columnStyles') {\n      var global_1 = gInput[prop];\n      var document_1 = dInput[prop];\n      var current = cInput[prop];\n      styleOptions.columnStyles = assign({}, global_1, document_1, current);\n    } else {\n      var allOptions = [gInput, dInput, cInput];\n      var styles = allOptions.map(function (opts) {\n        return opts[prop] || {};\n      });\n      styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n    }\n  };\n  for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n    var prop = _a[_i];\n    _loop_1(prop);\n  }\n  return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n  var allOptions = [global, document, current];\n  var result = {\n    didParseCell: [],\n    willDrawCell: [],\n    didDrawCell: [],\n    willDrawPage: [],\n    didDrawPage: []\n  };\n  for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n    var options = allOptions_1[_i];\n    if (options.didParseCell) result.didParseCell.push(options.didParseCell);\n    if (options.willDrawCell) result.willDrawCell.push(options.willDrawCell);\n    if (options.didDrawCell) result.didDrawCell.push(options.didDrawCell);\n    if (options.willDrawPage) result.willDrawPage.push(options.willDrawPage);\n    if (options.didDrawPage) result.didDrawPage.push(options.didDrawPage);\n  }\n  return result;\n}\nfunction parseSettings(doc, options) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n  var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n  var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n  var showFoot;\n  if (options.showFoot === true) {\n    showFoot = 'everyPage';\n  } else if (options.showFoot === false) {\n    showFoot = 'never';\n  } else {\n    showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n  }\n  var showHead;\n  if (options.showHead === true) {\n    showHead = 'everyPage';\n  } else if (options.showHead === false) {\n    showHead = 'never';\n  } else {\n    showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n  }\n  var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n  var theme = options.theme || (useCss ? 'plain' : 'striped');\n  var horizontalPageBreak = !!options.horizontalPageBreak;\n  var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n  return {\n    includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n    useCss: useCss,\n    theme: theme,\n    startY: startY,\n    margin: margin,\n    pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n    rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n    tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n    showHead: showHead,\n    showFoot: showFoot,\n    tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n    tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n    horizontalPageBreak: horizontalPageBreak,\n    horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n    horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows'\n  };\n}\nfunction getStartY(doc, userStartY) {\n  var previous = doc.getLastAutoTable();\n  var sf = doc.scaleFactor();\n  var currentPage = doc.pageNumber();\n  var isSamePageAsPreviousTable = false;\n  if (previous && previous.startPageNumber) {\n    var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n    isSamePageAsPreviousTable = endingPage === currentPage;\n  }\n  if (typeof userStartY === 'number') {\n    return userStartY;\n  } else if (userStartY == null || userStartY === false) {\n    if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n      // Some users had issues with overlapping tables when they used multiple\n      // tables without setting startY so setting it here to a sensible default.\n      return previous.finalY + 20 / sf;\n    }\n  }\n  return null;\n}\nfunction parseContent$1(doc, options, window) {\n  var head = options.head || [];\n  var body = options.body || [];\n  var foot = options.foot || [];\n  if (options.html) {\n    var hidden = options.includeHiddenHtml;\n    if (window) {\n      var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n      head = htmlContent.head || head;\n      body = htmlContent.body || head;\n      foot = htmlContent.foot || head;\n    } else {\n      console.error('Cannot parse html in non browser environment');\n    }\n  }\n  var columns = options.columns || parseColumns(head, body, foot);\n  return {\n    columns: columns,\n    head: head,\n    body: body,\n    foot: foot\n  };\n}\nfunction parseColumns(head, body, foot) {\n  var firstRow = head[0] || body[0] || foot[0] || [];\n  var result = [];\n  Object.keys(firstRow).filter(function (key) {\n    return key !== '_element';\n  }).forEach(function (key) {\n    var colSpan = 1;\n    var input;\n    if (Array.isArray(firstRow)) {\n      input = firstRow[parseInt(key)];\n    } else {\n      input = firstRow[key];\n    }\n    if (typeof input === 'object' && !Array.isArray(input)) {\n      colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n    }\n    for (var i = 0; i < colSpan; i++) {\n      var id = void 0;\n      if (Array.isArray(firstRow)) {\n        id = result.length;\n      } else {\n        id = key + (i > 0 ? \"_\".concat(i) : '');\n      }\n      var rowResult = {\n        dataKey: id\n      };\n      result.push(rowResult);\n    }\n  });\n  return result;\n}\nvar HookData = /** @class */function () {\n  function HookData(doc, table, cursor) {\n    this.table = table;\n    this.pageNumber = table.pageNumber;\n    this.settings = table.settings;\n    this.cursor = cursor;\n    this.doc = doc.getDocument();\n  }\n  return HookData;\n}();\nvar CellHookData = /** @class */function (_super) {\n  __extends(CellHookData, _super);\n  function CellHookData(doc, table, cell, row, column, cursor) {\n    var _this = _super.call(this, doc, table, cursor) || this;\n    _this.cell = cell;\n    _this.row = row;\n    _this.column = column;\n    _this.section = row.section;\n    return _this;\n  }\n  return CellHookData;\n}(HookData);\nvar Table = /** @class */function () {\n  function Table(input, content) {\n    this.pageNumber = 1;\n    this.id = input.id;\n    this.settings = input.settings;\n    this.styles = input.styles;\n    this.hooks = input.hooks;\n    this.columns = content.columns;\n    this.head = content.head;\n    this.body = content.body;\n    this.foot = content.foot;\n  }\n  Table.prototype.getHeadHeight = function (columns) {\n    return this.head.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.getFootHeight = function (columns) {\n    return this.foot.reduce(function (acc, row) {\n      return acc + row.getMaxCellHeight(columns);\n    }, 0);\n  };\n  Table.prototype.allRows = function () {\n    return this.head.concat(this.body).concat(this.foot);\n  };\n  Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n    for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n      var handler = handlers_1[_i];\n      var data = new CellHookData(doc, this, cell, row, column, cursor);\n      var result = handler(data) === false;\n      // Make sure text is always string[] since user can assign string\n      cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n      if (result) {\n        return false;\n      }\n    }\n    return true;\n  };\n  Table.prototype.callEndPageHooks = function (doc, cursor) {\n    doc.applyStyles(doc.userStyles);\n    for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n    for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n      var handler = _a[_i];\n      handler(new HookData(doc, this, cursor));\n    }\n  };\n  Table.prototype.getWidth = function (pageWidth) {\n    if (typeof this.settings.tableWidth === 'number') {\n      return this.settings.tableWidth;\n    } else if (this.settings.tableWidth === 'wrap') {\n      var wrappedWidth = this.columns.reduce(function (total, col) {\n        return total + col.wrappedWidth;\n      }, 0);\n      return wrappedWidth;\n    } else {\n      var margin = this.settings.margin;\n      return pageWidth - margin.left - margin.right;\n    }\n  };\n  return Table;\n}();\nvar Row = /** @class */function () {\n  function Row(raw, index, section, cells, spansMultiplePages) {\n    if (spansMultiplePages === void 0) {\n      spansMultiplePages = false;\n    }\n    this.height = 0;\n    this.raw = raw;\n    if (raw instanceof HtmlRowInput) {\n      this.raw = raw._element;\n      this.element = raw._element;\n    }\n    this.index = index;\n    this.section = section;\n    this.cells = cells;\n    this.spansMultiplePages = spansMultiplePages;\n  }\n  Row.prototype.getMaxCellHeight = function (columns) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var _a;\n      return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0);\n    }, 0);\n  };\n  Row.prototype.hasRowSpan = function (columns) {\n    var _this = this;\n    return columns.filter(function (column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return false;\n      return cell.rowSpan > 1;\n    }).length > 0;\n  };\n  Row.prototype.canEntireRowFit = function (height, columns) {\n    return this.getMaxCellHeight(columns) <= height;\n  };\n  Row.prototype.getMinimumRowHeight = function (columns, doc) {\n    var _this = this;\n    return columns.reduce(function (acc, column) {\n      var cell = _this.cells[column.index];\n      if (!cell) return 0;\n      var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n      var vPadding = cell.padding('vertical');\n      var oneRowHeight = vPadding + lineHeight;\n      return oneRowHeight > acc ? oneRowHeight : acc;\n    }, 0);\n  };\n  return Row;\n}();\nvar Cell = /** @class */function () {\n  function Cell(raw, styles, section) {\n    var _a;\n    this.contentHeight = 0;\n    this.contentWidth = 0;\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.height = 0;\n    this.x = 0;\n    this.y = 0;\n    this.styles = styles;\n    this.section = section;\n    this.raw = raw;\n    var content = raw;\n    if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n      this.rowSpan = raw.rowSpan || 1;\n      this.colSpan = raw.colSpan || 1;\n      content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n      if (raw._element) {\n        this.raw = raw._element;\n      }\n    } else {\n      this.rowSpan = 1;\n      this.colSpan = 1;\n    }\n    // Stringify 0 and false, but not undefined or null\n    var text = content != null ? '' + content : '';\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    this.text = text.split(splitRegex);\n  }\n  Cell.prototype.getTextPos = function () {\n    var y;\n    if (this.styles.valign === 'top') {\n      y = this.y + this.padding('top');\n    } else if (this.styles.valign === 'bottom') {\n      y = this.y + this.height - this.padding('bottom');\n    } else {\n      var netHeight = this.height - this.padding('vertical');\n      y = this.y + netHeight / 2 + this.padding('top');\n    }\n    var x;\n    if (this.styles.halign === 'right') {\n      x = this.x + this.width - this.padding('right');\n    } else if (this.styles.halign === 'center') {\n      var netWidth = this.width - this.padding('horizontal');\n      x = this.x + netWidth / 2 + this.padding('left');\n    } else {\n      x = this.x + this.padding('left');\n    }\n    return {\n      x: x,\n      y: y\n    };\n  };\n  // TODO (v4): replace parameters with only (lineHeight)\n  Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n    if (lineHeightFactor === void 0) {\n      lineHeightFactor = 1.15;\n    }\n    var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n    var lineHeight = this.styles.fontSize / scaleFactor * lineHeightFactor;\n    var height = lineCount * lineHeight + this.padding('vertical');\n    return Math.max(height, this.styles.minCellHeight);\n  };\n  Cell.prototype.padding = function (name) {\n    var padding = parseSpacing(this.styles.cellPadding, 0);\n    if (name === 'vertical') {\n      return padding.top + padding.bottom;\n    } else if (name === 'horizontal') {\n      return padding.left + padding.right;\n    } else {\n      return padding[name];\n    }\n  };\n  return Cell;\n}();\nvar Column = /** @class */function () {\n  function Column(dataKey, raw, index) {\n    this.wrappedWidth = 0;\n    this.minReadableWidth = 0;\n    this.minWidth = 0;\n    this.width = 0;\n    this.dataKey = dataKey;\n    this.raw = raw;\n    this.index = index;\n  }\n  Column.prototype.getMaxCustomCellWidth = function (table) {\n    var max = 0;\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n      var row = _a[_i];\n      var cell = row.cells[this.index];\n      if (cell && typeof cell.styles.cellWidth === 'number') {\n        max = Math.max(max, cell.styles.cellWidth);\n      }\n    }\n    return max;\n  };\n  return Column;\n}();\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n  calculate(doc, table);\n  var resizableColumns = [];\n  var initialTableWidth = 0;\n  table.columns.forEach(function (column) {\n    var customWidth = column.getMaxCustomCellWidth(table);\n    if (customWidth) {\n      // final column width\n      column.width = customWidth;\n    } else {\n      // initial column width (will be resized)\n      column.width = column.wrappedWidth;\n      resizableColumns.push(column);\n    }\n    initialTableWidth += column.width;\n  });\n  // width difference that needs to be distributed\n  var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n  // first resize attempt: with respect to minReadableWidth and minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return Math.max(column.minReadableWidth, column.minWidth);\n    });\n  }\n  // second resize attempt: ignore minReadableWidth but respect minWidth\n  if (resizeWidth) {\n    resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n      return column.minWidth;\n    });\n  }\n  resizeWidth = Math.abs(resizeWidth);\n  if (!table.settings.horizontalPageBreak && resizeWidth > 0.1 / doc.scaleFactor()) {\n    // Table can't get smaller due to custom-width or minWidth restrictions\n    // We can't really do much here. Up to user to for example\n    // reduce font size, increase page size or remove custom cell widths\n    // to allow more columns to be reduced in size\n    resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n    console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n  }\n  applyColSpans(table);\n  fitContent(table, doc);\n  applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n  var sf = doc.scaleFactor();\n  var horizontalPageBreak = table.settings.horizontalPageBreak;\n  var availablePageWidth = getPageAvailableWidth(doc, table);\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      var hooks = table.hooks.didParseCell;\n      table.callCellHooks(doc, hooks, cell, row, column, null);\n      var padding = cell.padding('horizontal');\n      cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n      // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n      // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n      // them in the split process to ensure correct word separation and width\n      // calculation.\n      var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n      cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n      if (typeof cell.styles.cellWidth === 'number') {\n        cell.minWidth = cell.styles.cellWidth;\n        cell.wrappedWidth = cell.styles.cellWidth;\n      } else if (cell.styles.cellWidth === 'wrap' || horizontalPageBreak === true) {\n        // cell width should not be more than available page width\n        if (cell.contentWidth > availablePageWidth) {\n          cell.minWidth = availablePageWidth;\n          cell.wrappedWidth = availablePageWidth;\n        } else {\n          cell.minWidth = cell.contentWidth;\n          cell.wrappedWidth = cell.contentWidth;\n        }\n      } else {\n        // auto\n        var defaultMinWidth = 10 / sf;\n        cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n        cell.wrappedWidth = cell.contentWidth;\n        if (cell.minWidth > cell.wrappedWidth) {\n          cell.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n  table.allRows().forEach(function (row) {\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var cell = row.cells[column.index];\n      // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n      // Could probably be improved upon however.\n      if (cell && cell.colSpan === 1) {\n        column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n        column.minWidth = Math.max(column.minWidth, cell.minWidth);\n        column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n      } else {\n        // Respect cellWidth set in columnStyles even if there is no cells for this column\n        // or if the column only have colspan cells. Since the width of colspan cells\n        // does not affect the width of columns, setting columnStyles cellWidth enables the\n        // user to at least do it manually.\n        // Note that this is not perfect for now since for example row and table styles are\n        // not accounted for\n        var columnStyles = table.styles.columnStyles[column.dataKey] || table.styles.columnStyles[column.index] || {};\n        var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n        if (cellWidth && typeof cellWidth === 'number') {\n          column.minWidth = cellWidth;\n          column.wrappedWidth = cellWidth;\n        }\n      }\n      if (cell) {\n        // Make sure all columns get at least min width even though width calculations are not based on them\n        if (cell.colSpan > 1 && !column.minWidth) {\n          column.minWidth = cell.minWidth;\n        }\n        if (cell.colSpan > 1 && !column.wrappedWidth) {\n          column.wrappedWidth = cell.minWidth;\n        }\n      }\n    }\n  });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n  var initialResizeWidth = resizeWidth;\n  var sumWrappedWidth = columns.reduce(function (acc, column) {\n    return acc + column.wrappedWidth;\n  }, 0);\n  for (var i = 0; i < columns.length; i++) {\n    var column = columns[i];\n    var ratio = column.wrappedWidth / sumWrappedWidth;\n    var suggestedChange = initialResizeWidth * ratio;\n    var suggestedWidth = column.width + suggestedChange;\n    var minWidth = getMinWidth(column);\n    var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n    resizeWidth -= newWidth - column.width;\n    column.width = newWidth;\n  }\n  resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n  // Run the resizer again if there's remaining width needs\n  // to be distributed and there're columns that can be resized\n  if (resizeWidth) {\n    var resizableColumns = columns.filter(function (column) {\n      return resizeWidth < 0 ? column.width > getMinWidth(column) // check if column can shrink\n      : true; // check if column can grow\n    });\n\n    if (resizableColumns.length) {\n      resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n    }\n  }\n  return resizeWidth;\n}\nfunction applyRowSpans(table) {\n  var rowSpanCells = {};\n  var colRowSpansLeft = 1;\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n      var column = _a[_i];\n      var data = rowSpanCells[column.index];\n      if (colRowSpansLeft > 1) {\n        colRowSpansLeft--;\n        delete row.cells[column.index];\n      } else if (data) {\n        data.cell.height += row.height;\n        colRowSpansLeft = data.cell.colSpan;\n        delete row.cells[column.index];\n        data.left--;\n        if (data.left <= 1) {\n          delete rowSpanCells[column.index];\n        }\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) {\n          continue;\n        }\n        cell.height = row.height;\n        if (cell.rowSpan > 1) {\n          var remaining = all.length - rowIndex;\n          var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n          rowSpanCells[column.index] = {\n            cell: cell,\n            left: left,\n            row: row\n          };\n        }\n      }\n    }\n  }\n}\nfunction applyColSpans(table) {\n  var all = table.allRows();\n  for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n    var row = all[rowIndex];\n    var colSpanCell = null;\n    var combinedColSpanWidth = 0;\n    var colSpansLeft = 0;\n    for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n      var column = table.columns[columnIndex];\n      // Width and colspan\n      colSpansLeft -= 1;\n      if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n        combinedColSpanWidth += column.width;\n        delete row.cells[column.index];\n      } else if (colSpanCell) {\n        var cell = colSpanCell;\n        delete row.cells[column.index];\n        colSpanCell = null;\n        cell.width = column.width + combinedColSpanWidth;\n      } else {\n        var cell = row.cells[column.index];\n        if (!cell) continue;\n        colSpansLeft = cell.colSpan;\n        combinedColSpanWidth = 0;\n        if (cell.colSpan > 1) {\n          colSpanCell = cell;\n          combinedColSpanWidth += column.width;\n          continue;\n        }\n        cell.width = column.width + combinedColSpanWidth;\n      }\n    }\n  }\n}\nfunction fitContent(table, doc) {\n  var rowSpanHeight = {\n    count: 0,\n    height: 0\n  };\n  for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n    var row = _a[_i];\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n      var column = _c[_b];\n      var cell = row.cells[column.index];\n      if (!cell) continue;\n      doc.applyStyles(cell.styles, true);\n      var textSpace = cell.width - cell.padding('horizontal');\n      if (cell.styles.overflow === 'linebreak') {\n        // Add one pt to textSpace to fix rounding error\n        cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), {\n          fontSize: cell.styles.fontSize\n        });\n      } else if (cell.styles.overflow === 'ellipsize') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n      } else if (cell.styles.overflow === 'hidden') {\n        cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n      } else if (typeof cell.styles.overflow === 'function') {\n        var result = cell.styles.overflow(cell.text, textSpace);\n        if (typeof result === 'string') {\n          cell.text = [result];\n        } else {\n          cell.text = result;\n        }\n      }\n      cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n      var realContentHeight = cell.contentHeight / cell.rowSpan;\n      if (cell.rowSpan > 1 && rowSpanHeight.count * rowSpanHeight.height < realContentHeight * cell.rowSpan) {\n        rowSpanHeight = {\n          height: realContentHeight,\n          count: cell.rowSpan\n        };\n      } else if (rowSpanHeight && rowSpanHeight.count > 0) {\n        if (rowSpanHeight.height > realContentHeight) {\n          realContentHeight = rowSpanHeight.height;\n        }\n      }\n      if (realContentHeight > row.height) {\n        row.height = realContentHeight;\n      }\n    }\n    rowSpanHeight.count--;\n  }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n  return text.map(function (str) {\n    return ellipsizeStr(str, width, styles, doc, overflow);\n  });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n  var precision = 10000 * doc.scaleFactor();\n  width = Math.ceil(width * precision) / precision;\n  if (width >= getStringWidth(text, styles, doc)) {\n    return text;\n  }\n  while (width < getStringWidth(text + overflow, styles, doc)) {\n    if (text.length <= 1) {\n      break;\n    }\n    text = text.substring(0, text.length - 1);\n  }\n  return text.trim() + overflow;\n}\nfunction createTable(jsPDFDoc, input) {\n  var doc = new DocHandler(jsPDFDoc);\n  var content = parseContent(input, doc.scaleFactor());\n  var table = new Table(input, content);\n  calculateWidths(doc, table);\n  doc.applyStyles(doc.userStyles);\n  return table;\n}\nfunction parseContent(input, sf) {\n  var content = input.content;\n  var columns = createColumns(content.columns);\n  // If no head or foot is set, try generating it with content from columns\n  if (content.head.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'head');\n    if (sectionRow) content.head.push(sectionRow);\n  }\n  if (content.foot.length === 0) {\n    var sectionRow = generateSectionRow(columns, 'foot');\n    if (sectionRow) content.foot.push(sectionRow);\n  }\n  var theme = input.settings.theme;\n  var styles = input.styles;\n  return {\n    columns: columns,\n    head: parseSection('head', content.head, columns, styles, theme, sf),\n    body: parseSection('body', content.body, columns, styles, theme, sf),\n    foot: parseSection('foot', content.foot, columns, styles, theme, sf)\n  };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n  var rowSpansLeftForColumn = {};\n  var result = sectionRows.map(function (rawRow, rowIndex) {\n    var skippedRowForRowSpans = 0;\n    var cells = {};\n    var colSpansAdded = 0;\n    var columnSpansLeft = 0;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n      var column = columns_1[_i];\n      if (rowSpansLeftForColumn[column.index] == null || rowSpansLeftForColumn[column.index].left === 0) {\n        if (columnSpansLeft === 0) {\n          var rawCell = void 0;\n          if (Array.isArray(rawRow)) {\n            rawCell = rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n          } else {\n            rawCell = rawRow[column.dataKey];\n          }\n          var cellInputStyles = {};\n          if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n            cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n          }\n          var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n          var cell = new Cell(rawCell, styles, sectionName);\n          // dataKey is not used internally no more but keep for\n          // backwards compat in hooks\n          cells[column.dataKey] = cell;\n          cells[column.index] = cell;\n          columnSpansLeft = cell.colSpan - 1;\n          rowSpansLeftForColumn[column.index] = {\n            left: cell.rowSpan - 1,\n            times: columnSpansLeft\n          };\n        } else {\n          columnSpansLeft--;\n          colSpansAdded++;\n        }\n      } else {\n        rowSpansLeftForColumn[column.index].left--;\n        columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n        skippedRowForRowSpans++;\n      }\n    }\n    return new Row(rawRow, rowIndex, sectionName, cells);\n  });\n  return result;\n}\nfunction generateSectionRow(columns, section) {\n  var sectionRow = {};\n  columns.forEach(function (col) {\n    if (col.raw != null) {\n      var title = getSectionTitle(section, col.raw);\n      if (title != null) sectionRow[col.dataKey] = title;\n    }\n  });\n  return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n  if (section === 'head') {\n    if (typeof column === 'object') {\n      return column.header || null;\n    } else if (typeof column === 'string' || typeof column === 'number') {\n      return column;\n    }\n  } else if (section === 'foot' && typeof column === 'object') {\n    return column.footer;\n  }\n  return null;\n}\nfunction createColumns(columns) {\n  return columns.map(function (input, index) {\n    var _a;\n    var key;\n    if (typeof input === 'object') {\n      key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n    } else {\n      key = index;\n    }\n    return new Column(key, input, index);\n  });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n  var theme = getTheme(themeName);\n  var sectionStyles;\n  if (sectionName === 'head') {\n    sectionStyles = styles.headStyles;\n  } else if (sectionName === 'body') {\n    sectionStyles = styles.bodyStyles;\n  } else if (sectionName === 'foot') {\n    sectionStyles = styles.footStyles;\n  }\n  var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n  var columnStyles = styles.columnStyles[column.dataKey] || styles.columnStyles[column.index] || {};\n  var colStyles = sectionName === 'body' ? columnStyles : {};\n  var rowStyles = sectionName === 'body' && rowIndex % 2 === 0 ? assign({}, theme.alternateRow, styles.alternateRowStyles) : {};\n  var defaultStyle = defaultStyles(scaleFactor);\n  var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n  return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n  var _a;\n  if (config === void 0) {\n    config = {};\n  }\n  // Get page width\n  var remainingWidth = getPageAvailableWidth(doc, table);\n  // Get column data key to repeat\n  var repeatColumnsMap = new Map();\n  var colIndexes = [];\n  var columns = [];\n  var horizontalPageBreakRepeat = [];\n  if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n    horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n    // It can be a single value of type string or number (even number: 0)\n  } else if (typeof table.settings.horizontalPageBreakRepeat === 'string' || typeof table.settings.horizontalPageBreakRepeat === 'number') {\n    horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n  }\n  // Code to repeat the given column in split pages\n  horizontalPageBreakRepeat.forEach(function (field) {\n    var col = table.columns.find(function (item) {\n      return item.dataKey === field || item.index === field;\n    });\n    if (col && !repeatColumnsMap.has(col.index)) {\n      repeatColumnsMap.set(col.index, true);\n      colIndexes.push(col.index);\n      columns.push(table.columns[col.index]);\n      remainingWidth -= col.wrappedWidth;\n    }\n  });\n  var first = true;\n  var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n  while (i < table.columns.length) {\n    // Prevent duplicates\n    if (repeatColumnsMap.has(i)) {\n      i++;\n      continue;\n    }\n    var colWidth = table.columns[i].wrappedWidth;\n    // Take at least one column even if it doesn't fit\n    if (first || remainingWidth >= colWidth) {\n      first = false;\n      colIndexes.push(i);\n      columns.push(table.columns[i]);\n      remainingWidth -= colWidth;\n    } else {\n      break;\n    }\n    i++;\n  }\n  return {\n    colIndexes: colIndexes,\n    columns: columns,\n    lastIndex: i - 1\n  };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n  var allResults = [];\n  for (var i = 0; i < table.columns.length; i++) {\n    var result = getColumnsCanFitInPage(doc, table, {\n      start: i\n    });\n    if (result.columns.length) {\n      allResults.push(result);\n      i = result.lastIndex;\n    }\n  }\n  return allResults;\n}\nfunction drawTable(jsPDFDoc, table) {\n  var settings = table.settings;\n  var startY = settings.startY;\n  var margin = settings.margin;\n  var cursor = {\n    x: margin.left,\n    y: startY\n  };\n  var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n  if (settings.pageBreak === 'avoid') {\n    var rows = table.body;\n    var tableHeight = rows.reduce(function (acc, row) {\n      return acc + row.height;\n    }, 0);\n    minTableBottomPos += tableHeight;\n  }\n  var doc = new DocHandler(jsPDFDoc);\n  if (settings.pageBreak === 'always' || settings.startY != null && minTableBottomPos > doc.pageSize().height) {\n    nextPage(doc);\n    cursor.y = margin.top;\n  }\n  table.callWillDrawPageHooks(doc, cursor);\n  var startPos = assign({}, cursor);\n  table.startPageNumber = doc.pageNumber();\n  if (settings.horizontalPageBreak) {\n    // managed flow for split columns\n    printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n  } else {\n    // normal flow\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n      table.head.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n      var isLastRow = index === table.body.length - 1;\n      printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n    });\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n      table.foot.forEach(function (row) {\n        return printRow(doc, table, row, cursor, table.columns);\n      });\n    }\n  }\n  addTableBorder(doc, table, startPos, cursor);\n  table.callEndPageHooks(doc, cursor);\n  table.finalY = cursor.y;\n  jsPDFDoc.lastAutoTable = table;\n  doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n  // calculate width of columns and render only those which can fit into page\n  var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n  var settings = table.settings;\n  if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n    allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n      doc.applyStyles(doc.userStyles);\n      // add page to print next columns in new page\n      if (index > 0) {\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n      } else {\n        // print head for selected columns\n        printHead(doc, table, cursor, colsAndIndexes.columns);\n      }\n      // print body & footer for selected columns\n      printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n      printFoot(doc, table, cursor, colsAndIndexes.columns);\n    });\n  } else {\n    var lastRowIndexOfLastPage_1 = -1;\n    var firstColumnsToFitResult = allColumnsCanFitResult[0];\n    var _loop_1 = function () {\n      // Print the first columns, taking note of the last row printed\n      var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n      if (firstColumnsToFitResult) {\n        doc.applyStyles(doc.userStyles);\n        var firstColumnsToFit = firstColumnsToFitResult.columns;\n        if (lastRowIndexOfLastPage_1 >= 0) {\n          // When adding a page here, make sure not to print the footers\n          // because they were already printed before on this same loop\n          addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n        } else {\n          printHead(doc, table, cursor, firstColumnsToFit);\n        }\n        lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n        printFoot(doc, table, cursor, firstColumnsToFit);\n      }\n      // Check how many rows were printed, so that the next columns would not print more rows than that\n      var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n      // Print the next columns, never exceding maxNumberOfRows\n      allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n        doc.applyStyles(doc.userStyles);\n        // When adding a page here, make sure not to print the footers\n        // because they were already printed before on this same loop\n        addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n        printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n        printFoot(doc, table, cursor, colsAndIndexes.columns);\n      });\n      lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n    };\n    while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n      _loop_1();\n    }\n  }\n}\nfunction printHead(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n  doc.applyStyles(doc.userStyles);\n  table.body.forEach(function (row, index) {\n    var isLastRow = index === table.body.length - 1;\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n  doc.applyStyles(doc.userStyles);\n  maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n  var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n  var lastPrintedRowIndex = -1;\n  table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n    var isLastRow = startRowIndex + index === table.body.length - 1;\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n      printRow(doc, table, row, cursor, columns);\n      lastPrintedRowIndex = startRowIndex + index;\n    }\n  });\n  return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n  var settings = table.settings;\n  doc.applyStyles(doc.userStyles);\n  if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n  var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n  var vPadding = cell.padding('vertical');\n  var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n  return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n  var cells = {};\n  row.spansMultiplePages = true;\n  row.height = 0;\n  var rowHeight = 0;\n  for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n    var column = _a[_i];\n    var cell = row.cells[column.index];\n    if (!cell) continue;\n    if (!Array.isArray(cell.text)) {\n      cell.text = [cell.text];\n    }\n    var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n    remainderCell = assign(remainderCell, cell);\n    remainderCell.text = [];\n    var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n    if (cell.text.length > remainingLineCount) {\n      remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n    }\n    var scaleFactor = doc.scaleFactor();\n    var lineHeightFactor = doc.getLineHeightFactor();\n    cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (cell.contentHeight >= remainingPageSpace) {\n      cell.contentHeight = remainingPageSpace;\n      remainderCell.styles.minCellHeight -= remainingPageSpace;\n    }\n    if (cell.contentHeight > row.height) {\n      row.height = cell.contentHeight;\n    }\n    remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n    if (remainderCell.contentHeight > rowHeight) {\n      rowHeight = remainderCell.contentHeight;\n    }\n    cells[column.index] = remainderCell;\n  }\n  var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n  remainderRow.height = rowHeight;\n  for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n    var column = _c[_b];\n    var remainderCell = remainderRow.cells[column.index];\n    if (remainderCell) {\n      remainderCell.height = remainderRow.height;\n    }\n    var cell = row.cells[column.index];\n    if (cell) {\n      cell.height = row.height;\n    }\n  }\n  return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n  var pageHeight = doc.pageSize().height;\n  var margin = table.settings.margin;\n  var marginHeight = margin.top + margin.bottom;\n  var maxRowHeight = pageHeight - marginHeight;\n  if (row.section === 'body') {\n    // Should also take into account that head and foot is not\n    // on every page with some settings\n    maxRowHeight -= table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n  }\n  var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n  var minRowFits = minRowHeight < remainingPageSpace;\n  if (minRowHeight > maxRowHeight) {\n    console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n    return true;\n  }\n  if (!minRowFits) {\n    return false;\n  }\n  var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n  var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n  if (rowHigherThanPage) {\n    if (rowHasRowSpanCell) {\n      console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n    }\n    return true;\n  }\n  if (rowHasRowSpanCell) {\n    // Currently a new page is required whenever a rowspan row don't fit a page.\n    return false;\n  }\n  if (table.settings.rowPageBreak === 'avoid') {\n    return false;\n  }\n  // In all other cases print the row on current page\n  return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n  var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n  if (row.canEntireRowFit(remainingSpace, columns)) {\n    // The row fits in the current page\n    printRow(doc, table, row, cursor, columns);\n  } else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n    // The row gets split in two here, each piece in one page\n    var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n    printRow(doc, table, row, cursor, columns);\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n  } else {\n    // The row get printed entirelly on the next page\n    addPage(doc, table, startPos, cursor, columns);\n    printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n  }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n  cursor.x = table.settings.margin.left;\n  for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n    var column = columns_1[_i];\n    var cell = row.cells[column.index];\n    if (!cell) {\n      cursor.x += column.width;\n      continue;\n    }\n    doc.applyStyles(cell.styles);\n    cell.x = cursor.x;\n    cell.y = cursor.y;\n    var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n    if (result === false) {\n      cursor.x += column.width;\n      continue;\n    }\n    drawCellRect(doc, cell, cursor);\n    var textPos = cell.getTextPos();\n    autoTableText(cell.text, textPos.x, textPos.y, {\n      halign: cell.styles.halign,\n      valign: cell.styles.valign,\n      maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right'))\n    }, doc.getDocument());\n    table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n    cursor.x += column.width;\n  }\n  cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n  var cellStyles = cell.styles;\n  // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n  // TODO (v4): better solution?\n  doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n  if (typeof cellStyles.lineWidth === 'number') {\n    // Draw cell background with normal borders\n    var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n    if (fillStyle) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n    }\n  } else if (typeof cellStyles.lineWidth === 'object') {\n    // Draw cell background\n    if (cellStyles.fillColor) {\n      doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n    }\n    // Draw cell individual borders\n    drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n  }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n  var x1, y1, x2, y2;\n  if (lineWidth.top) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.top, x1, y1, x2, y2);\n  }\n  if (lineWidth.bottom) {\n    x1 = cursor.x;\n    y1 = cursor.y + cell.height;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.right) {\n      x2 += 0.5 * lineWidth.right;\n    }\n    if (lineWidth.left) {\n      x1 -= 0.5 * lineWidth.left;\n    }\n    drawLine(lineWidth.bottom, x1, y1, x2, y2);\n  }\n  if (lineWidth.left) {\n    x1 = cursor.x;\n    y1 = cursor.y;\n    x2 = cursor.x;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.left, x1, y1, x2, y2);\n  }\n  if (lineWidth.right) {\n    x1 = cursor.x + cell.width;\n    y1 = cursor.y;\n    x2 = cursor.x + cell.width;\n    y2 = cursor.y + cell.height;\n    if (lineWidth.top) {\n      y1 -= 0.5 * lineWidth.top;\n    }\n    if (lineWidth.bottom) {\n      y2 += 0.5 * lineWidth.bottom;\n    }\n    drawLine(lineWidth.right, x1, y1, x2, y2);\n  }\n  function drawLine(width, x1, y1, x2, y2) {\n    doc.getDocument().setLineWidth(width);\n    doc.getDocument().line(x1, y1, x2, y2, 'S');\n  }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n  var bottomContentHeight = table.settings.margin.bottom;\n  var showFoot = table.settings.showFoot;\n  if (showFoot === 'everyPage' || showFoot === 'lastPage' && isLastRow) {\n    bottomContentHeight += table.getFootHeight(table.columns);\n  }\n  return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n  if (columns === void 0) {\n    columns = [];\n  }\n  if (suppressFooter === void 0) {\n    suppressFooter = false;\n  }\n  doc.applyStyles(doc.userStyles);\n  if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n    table.foot.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n  }\n  // Add user content just before adding new page ensure it will\n  // be drawn above other things on the page\n  table.callEndPageHooks(doc, cursor);\n  var margin = table.settings.margin;\n  addTableBorder(doc, table, startPos, cursor);\n  nextPage(doc);\n  table.pageNumber++;\n  cursor.x = margin.left;\n  cursor.y = margin.top;\n  startPos.y = margin.top;\n  // call didAddPage hooks before any content is added to the page\n  table.callWillDrawPageHooks(doc, cursor);\n  if (table.settings.showHead === 'everyPage') {\n    table.head.forEach(function (row) {\n      return printRow(doc, table, row, cursor, columns);\n    });\n    doc.applyStyles(doc.userStyles);\n  }\n}\nfunction nextPage(doc) {\n  var current = doc.pageNumber();\n  doc.setPage(current + 1);\n  var newCurrent = doc.pageNumber();\n  if (newCurrent === current) {\n    doc.addPage();\n    return true;\n  }\n  return false;\n}\nfunction applyPlugin(jsPDF) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  jsPDF.API.autoTable = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var options = args[0];\n    var input = parseInput(this, options);\n    var table = createTable(this, input);\n    drawTable(this, table);\n    return this;\n  };\n  // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n  jsPDF.API.lastAutoTable = false;\n  jsPDF.API.autoTableText = function (text, x, y, styles) {\n    autoTableText(text, x, y, styles, this);\n  };\n  jsPDF.API.autoTableSetDefaults = function (defaults) {\n    DocHandler.setDefaults(defaults, this);\n    return this;\n  };\n  jsPDF.autoTableSetDefaults = function (defaults, doc) {\n    DocHandler.setDefaults(defaults, doc);\n  };\n  jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n    var _a;\n    if (includeHiddenElements === void 0) {\n      includeHiddenElements = false;\n    }\n    if (true) {\n      console.error('Cannot run autoTableHtmlToJson in non browser environment');\n      return null;\n    }\n    var doc = new DocHandler(this);\n    var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false),\n      head = _b.head,\n      body = _b.body;\n    var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) {\n      return c.content;\n    })) || [];\n    return {\n      columns: columns,\n      rows: body,\n      data: body\n    };\n  };\n}\nvar _a;\nfunction autoTable(d, options) {\n  var input = parseInput(d, options);\n  var table = createTable(d, input);\n  drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n  var input = parseInput(d, options);\n  return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n  drawTable(d, table);\n}\ntry {\n  if (false) { var jsPDF, anyWindow; }\n} catch (error) {\n  console.error('Could not apply autoTable plugin', error);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\n");

/***/ })

};
;