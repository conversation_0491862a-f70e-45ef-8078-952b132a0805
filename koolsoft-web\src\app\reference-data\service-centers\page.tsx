'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  MapPin,
  Search,
  Plus,
  Edit,
  Trash,
  Loader2,
  FileDown,
  Filter
} from 'lucide-react';

// Define the form schema for service centers
const serviceCenterFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be at most 100 characters'),
  vendor: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pincode: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email('Invalid email format').optional().or(z.literal('')),
  contactPerson: z.string().optional(),
  active: z.boolean().default(true)
});

type ServiceCenterFormValues = z.infer<typeof serviceCenterFormSchema>;

interface ServiceCenter {
  id: string;
  name: string;
  vendor?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Service Centers Reference Data Page
 */
export default function ServiceCentersPage() {
  const router = useRouter();
  const { toast } = useToast();

  // State for service centers
  const [serviceCenters, setServiceCenters] = useState<ServiceCenter[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editItem, setEditItem] = useState<ServiceCenter | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [vendorFilter, setVendorFilter] = useState('all');
  const [cityFilter, setCityFilter] = useState('all');
  const [activeFilter, setActiveFilter] = useState('true');
  const [availableVendors, setAvailableVendors] = useState<string[]>([]);
  const [availableCities, setAvailableCities] = useState<string[]>([]);

  // Initialize form with react-hook-form
  const form = useForm<ServiceCenterFormValues>({
    resolver: zodResolver(serviceCenterFormSchema) as any,
    defaultValues: {
      name: '',
      vendor: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      phone: '',
      email: '',
      contactPerson: '',
      active: true
    }
  });

  // Fetch service centers
  const fetchServiceCenters = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const skip = (currentPage - 1) * pageSize;
      const queryParams = new URLSearchParams({
        skip: skip.toString(),
        take: pageSize.toString(),
        vendor: vendorFilter,
        city: cityFilter,
        active: activeFilter
      });

      // Add search term if provided
      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      // Fetch data from API
      const response = await fetch(`/api/reference/serviceCenters?${queryParams.toString()}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch service centers');
      }

      const data = await response.json();

      // Update state with fetched data
      setServiceCenters(data.data || []);
      setTotalItems(data.pagination?.total || 0);
      setTotalPages(data.pagination?.totalPages || 1);
      setAvailableVendors(data.filters?.vendors || []);
      setAvailableCities(data.filters?.cities || []);

    } catch (error: any) {
      console.error('Error fetching service centers:', error);
      setError(error.message || 'Failed to load service centers');
      toast({
        title: 'Error',
        description: 'Failed to load service centers. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on initial load and when filters or pagination changes
  useEffect(() => {
    fetchServiceCenters();
  }, [currentPage, pageSize, searchTerm, vendorFilter, cityFilter, activeFilter]);

  // Reset form when dialog opens or edit item changes
  useEffect(() => {
    if (isDialogOpen) {
      if (editItem) {
        form.reset({
          name: editItem.name || '',
          vendor: editItem.vendor || '',
          address: editItem.address || '',
          city: editItem.city || '',
          state: editItem.state || '',
          pincode: editItem.pincode || '',
          phone: editItem.phone || '',
          email: editItem.email || '',
          contactPerson: editItem.contactPerson || '',
          active: editItem.active
        });
      } else {
        form.reset({
          name: '',
          vendor: '',
          address: '',
          city: '',
          state: '',
          pincode: '',
          phone: '',
          email: '',
          contactPerson: '',
          active: true
        });
      }
    }
  }, [isDialogOpen, editItem, form]);

  // Handle search
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchServiceCenters();
  };

  // Handle export
  const handleExport = async () => {
    try {
      const response = await fetch('/api/service-centers?export=true', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to export service centers');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `service-centers-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Success',
        description: 'Service centers exported successfully'
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to export service centers',
        variant: 'destructive'
      });
    }
  };

  // Handle form submission
  const onSubmit = async (data: ServiceCenterFormValues) => {
    setIsSubmitting(true);

    try {
      let response;

      if (editItem) {
        // Update existing service center
        response = await fetch(`/api/reference/serviceCenters/${editItem.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });
      } else {
        // Create new service center
        response = await fetch('/api/reference/serviceCenters', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data),
          credentials: 'include'
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || (editItem ? 'Failed to update service center' : 'Failed to create service center'));
      }

      // Close dialog and refresh data
      setIsDialogOpen(false);
      fetchServiceCenters();

      toast({
        title: 'Success',
        description: editItem ? 'Service center updated successfully' : 'Service center created successfully'
      });
    } catch (error: any) {
      console.error('Error saving service center:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save service center. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      setEditItem(null);
    }
  };

  // Handle delete service center
  const handleDeleteItem = async () => {
    if (!deleteItemId) return;

    try {
      setIsDeleting(true);

      const response = await fetch(`/api/reference/serviceCenters/${deleteItemId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service center');
      }

      // Refresh the list after deletion
      fetchServiceCenters();

      toast({
        title: 'Success',
        description: 'Service center deleted successfully'
      });
    } catch (error: any) {
      console.error('Error deleting service center:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete service center. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsDeleting(false);
      setDeleteItemId(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/reference-data">Reference Data</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>Service Centers</BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Service Centers</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Authorized service centers and vendors
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="secondary" onClick={() => setEditItem(null)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Service Center
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{editItem ? 'Edit' : 'Add'} Service Center</DialogTitle>
                  <DialogDescription>
                    {editItem
                      ? 'Update the details for this service center.'
                      : 'Add a new service center to the system.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={form.handleSubmit(onSubmit as any)} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name *</Label>
                      <Input
                        id="name"
                        {...form.register('name')}
                        placeholder="Service center name"
                      />
                      {form.formState.errors.name && (
                        <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="vendor">Vendor</Label>
                      <Input
                        id="vendor"
                        {...form.register('vendor')}
                        placeholder="e.g., BLUESTAR, GENERAL"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      {...form.register('address')}
                      placeholder="Full address"
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        {...form.register('city')}
                        placeholder="City"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        {...form.register('state')}
                        placeholder="State"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pincode">Pincode</Label>
                      <Input
                        id="pincode"
                        {...form.register('pincode')}
                        placeholder="Pincode"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        {...form.register('phone')}
                        placeholder="Phone number"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        {...form.register('email')}
                        placeholder="Email address"
                      />
                      {form.formState.errors.email && (
                        <p className="text-sm text-red-600">{form.formState.errors.email.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPerson">Contact Person</Label>
                    <Input
                      id="contactPerson"
                      {...form.register('contactPerson')}
                      placeholder="Contact person name"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="active"
                      {...form.register('active')}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="active">Active</Label>
                  </div>

                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {editItem ? 'Update' : 'Create'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            <form onSubmit={handleSearch} className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search service centers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button type="submit" variant="outline">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </form>

            <div className="flex gap-4">
              <Select value={vendorFilter} onValueChange={setVendorFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by vendor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Vendors</SelectItem>
                  {availableVendors.map((vendor) => (
                    <SelectItem key={vendor} value={vendor}>
                      {vendor}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={cityFilter} onValueChange={setCityFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by city" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cities</SelectItem>
                  {availableCities.map((city) => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={activeFilter} onValueChange={setActiveFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Service Centers Table */}
          {error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : serviceCenters.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              No service centers found. Try adjusting your search or add a new service center.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>City</TableHead>
                  <TableHead>Contact Person</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {serviceCenters.map((serviceCenter) => (
                  <TableRow key={serviceCenter.id}>
                    <TableCell className="font-medium">{serviceCenter.name}</TableCell>
                    <TableCell>
                      {serviceCenter.vendor && (
                        <Badge variant="outline">{serviceCenter.vendor}</Badge>
                      )}
                    </TableCell>
                    <TableCell>{serviceCenter.city || '-'}</TableCell>
                    <TableCell>{serviceCenter.contactPerson || '-'}</TableCell>
                    <TableCell>{serviceCenter.phone || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={serviceCenter.active ? 'default' : 'secondary'}>
                        {serviceCenter.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditItem(serviceCenter);
                            setIsDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeleteItemId(serviceCenter.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteItemId} onOpenChange={() => setDeleteItemId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will deactivate the service center. It will no longer appear in active lists
              but historical data will be preserved.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteItem}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
