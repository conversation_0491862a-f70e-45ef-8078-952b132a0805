"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/balanced-match";
exports.ids = ["vendor-chunks/balanced-match"];
exports.modules = {

/***/ "(rsc)/./node_modules/balanced-match/index.js":
/*!**********************************************!*\
  !*** ./node_modules/balanced-match/index.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = balanced;\nfunction balanced(a, b, str) {\n  if (a instanceof RegExp) a = maybeMatch(a, str);\n  if (b instanceof RegExp) b = maybeMatch(b, str);\n  var r = range(a, b, str);\n  return r && {\n    start: r[0],\n    end: r[1],\n    pre: str.slice(0, r[0]),\n    body: str.slice(r[0] + a.length, r[1]),\n    post: str.slice(r[1] + b.length)\n  };\n}\nfunction maybeMatch(reg, str) {\n  var m = str.match(reg);\n  return m ? m[0] : null;\n}\nbalanced.range = range;\nfunction range(a, b, str) {\n  var begs, beg, left, right, result;\n  var ai = str.indexOf(a);\n  var bi = str.indexOf(b, ai + 1);\n  var i = ai;\n  if (ai >= 0 && bi > 0) {\n    if (a === b) {\n      return [ai, bi];\n    }\n    begs = [];\n    left = str.length;\n    while (i >= 0 && !result) {\n      if (i == ai) {\n        begs.push(i);\n        ai = str.indexOf(a, i + 1);\n      } else if (begs.length == 1) {\n        result = [begs.pop(), bi];\n      } else {\n        beg = begs.pop();\n        if (beg < left) {\n          left = beg;\n          right = bi;\n        }\n        bi = str.indexOf(b, i + 1);\n      }\n      i = ai < bi && ai >= 0 ? ai : bi;\n    }\n    if (begs.length) {\n      result = [left, right];\n    }\n  }\n  return result;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/balanced-match/index.js\n");

/***/ })

};
;