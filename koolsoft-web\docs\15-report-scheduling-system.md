# Report Scheduling System Implementation

## Overview

The Report Scheduling System (Task 12.5) provides automated report generation and delivery capabilities for KoolSoft. This system allows users to create, manage, and automatically execute scheduled reports with email delivery.

## Features

### Core Functionality
- **Automated Report Generation**: Schedule reports to run at specific intervals
- **Multiple Report Types**: Support for AMC, WARRANTY, SERVICE, SALES, and CUSTOMER reports
- **Flexible Scheduling**: Daily, weekly, monthly, quarterly, yearly, and custom cron expressions
- **Email Delivery**: Automatic email delivery to multiple recipients
- **Multiple Export Formats**: PDF, Excel, and CSV export options
- **Execution Tracking**: Comprehensive logging and monitoring of report executions
- **Manual Execution**: Ability to trigger reports manually
- **Role-Based Access**: Admin/Manager can create/edit, Executive can view

### Technical Features
- **Node-cron Integration**: Reliable job scheduling using node-cron library
- **Database Persistence**: All schedules and execution history stored in PostgreSQL
- **Error Handling**: Comprehensive error handling and logging
- **File Management**: Automatic cleanup of old report files
- **Performance Monitoring**: Execution time tracking and statistics

## Database Schema

### scheduled_reports Table
```sql
CREATE TABLE scheduled_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  report_type VARCHAR(50) NOT NULL, -- AMC, WARRANTY, SERVICE, SALES, CUSTOMER
  cron_expression VARCHAR(100) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  parameters JSONB,
  email_recipients TEXT[] NOT NULL,
  email_subject VARCHAR(255),
  email_body TEXT,
  export_format VARCHAR(20) DEFAULT 'PDF', -- PDF, EXCEL, CSV
  created_by UUID NOT NULL REFERENCES users(id),
  last_run_at TIMESTAMP,
  next_run_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### scheduled_report_executions Table
```sql
CREATE TABLE scheduled_report_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scheduled_report_id UUID NOT NULL REFERENCES scheduled_reports(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, RUNNING, COMPLETED, FAILED
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  error_message TEXT,
  report_data JSONB,
  file_path VARCHAR(500),
  emails_sent INTEGER DEFAULT 0,
  email_errors TEXT[],
  execution_time INTEGER, -- milliseconds
  record_count INTEGER,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Scheduled Reports Management
- `GET /api/reports/schedules` - List scheduled reports with filtering
- `POST /api/reports/schedules` - Create new scheduled report
- `GET /api/reports/schedules/[id]` - Get specific scheduled report
- `PUT /api/reports/schedules/[id]` - Update scheduled report
- `DELETE /api/reports/schedules/[id]` - Delete scheduled report
- `PATCH /api/reports/schedules/[id]` - Toggle active status
- `POST /api/reports/schedules/[id]/execute` - Manual execution

### Execution Management
- `GET /api/reports/schedules/executions` - List executions with filtering
- `GET /api/reports/schedules/statistics` - Get statistics and metrics

### System Management
- `GET /api/reports/schedules/init` - Get scheduler status
- `POST /api/reports/schedules/init` - Initialize/restart scheduler

## Frontend Components

### Pages
- `/reports/schedules` - Main scheduled reports management page
- `/reports/schedules/new` - Create new scheduled report form
- `/reports/schedules/[id]` - Edit scheduled report (to be implemented)

### Key Components
- **ScheduledReportsPage**: Main listing with filters, search, and actions
- **NewScheduledReportPage**: Comprehensive form for creating schedules
- **Cron Expression Builder**: User-friendly interface for scheduling
- **Email Configuration**: Multi-recipient email setup
- **Report Parameters**: Dynamic parameter configuration per report type

## Cron Expression Support

### Predefined Schedules
- **Daily**: `0 9 * * *` (9:00 AM daily)
- **Weekly**: `0 9 * * 1` (9:00 AM every Monday)
- **Monthly**: `0 9 1 * *` (9:00 AM on 1st of month)
- **Quarterly**: `0 9 1 1,4,7,10 *` (9:00 AM on 1st of quarter)
- **Yearly**: `0 9 1 1 *` (9:00 AM on January 1st)

### Custom Expressions
Users can define custom cron expressions with validation and human-readable descriptions.

## File Structure

```
src/
├── lib/
│   ├── validations/
│   │   └── scheduled-report.schema.ts      # Zod validation schemas
│   ├── repositories/
│   │   ├── scheduled-report.repository.ts   # Database operations
│   │   └── scheduled-report-execution.repository.ts
│   ├── services/
│   │   └── report-scheduler.service.ts     # Core scheduling service
│   ├── utils/
│   │   ├── cron-parser.ts                  # Cron expression utilities
│   │   └── report-generator.ts             # File generation utilities
│   └── jobs/
│       └── cleanup-reports.ts              # Cleanup job
├── app/
│   ├── api/reports/schedules/              # API endpoints
│   └── reports/schedules/                  # Frontend pages
└── components/
    └── reports/                            # Report-related components
```

## Security & Access Control

### Role-Based Permissions
- **ADMIN**: Full access to all scheduling features
- **MANAGER**: Create, edit, delete, and execute schedules
- **EXECUTIVE**: View schedules and execution history only

### Data Protection
- All API endpoints protected with authentication middleware
- Input validation using Zod schemas
- SQL injection prevention through Prisma ORM
- File access controls for generated reports

## Performance Considerations

### Optimization Features
- Automatic cleanup of old execution records (90 days retention)
- Automatic cleanup of old report files (7 days retention)
- Efficient database queries with proper indexing
- Background job execution to prevent blocking

### Monitoring
- Execution time tracking
- Success/failure rate monitoring
- Email delivery tracking
- Resource usage monitoring

## Error Handling

### Comprehensive Error Management
- Graceful handling of cron expression errors
- Email delivery failure tracking
- Report generation error logging
- Database connection error handling
- File system error management

### Recovery Mechanisms
- Automatic retry for transient failures
- Detailed error logging for debugging
- Status tracking for failed executions
- Manual retry capabilities

## Integration Points

### Email System
- Integrates with existing KoolSoft email service
- Custom email templates and subjects
- Multiple recipient support
- Delivery confirmation tracking

### Reporting System
- Leverages existing report repositories
- Supports all current report types
- Maintains existing report parameters
- Compatible with current export formats

### Authentication System
- Uses existing NextAuth.js authentication
- Integrates with current role-based access control
- Maintains session management consistency

## Deployment Notes

### Dependencies
- `node-cron`: Job scheduling
- `jspdf`: PDF generation
- `exceljs`: Excel file generation
- `@types/node-cron`: TypeScript definitions

### Environment Setup
- No additional environment variables required
- Uses existing database connection
- Leverages current email configuration

### Initialization
The scheduler service automatically initializes when the application starts and loads all active scheduled reports.

## Future Enhancements

### Planned Features
- Report template customization
- Advanced filtering options
- Dashboard widgets for scheduling metrics
- Webhook notifications
- Report sharing capabilities
- Advanced cron expression builder UI

### Scalability Considerations
- Redis integration for distributed scheduling
- Queue-based job processing
- Load balancing for report generation
- Horizontal scaling support

## Troubleshooting

### Common Issues
1. **Scheduler not starting**: Check database connectivity and cron expression validation
2. **Reports not generating**: Verify report repository integration and parameters
3. **Email delivery failures**: Check email service configuration and recipient addresses
4. **File generation errors**: Ensure proper file system permissions and disk space

### Debugging
- Check execution logs in `scheduled_report_executions` table
- Monitor application logs for scheduler service messages
- Verify cron expression validity using built-in validator
- Test email delivery using manual execution feature

## Testing

### Test Coverage
- Unit tests for cron parser utilities
- Integration tests for API endpoints
- End-to-end tests for scheduling workflow
- Performance tests for report generation

### Test Data
- Sample scheduled reports for each report type
- Test cron expressions for validation
- Mock email recipients for testing
- Sample execution scenarios

This implementation provides a robust, scalable, and user-friendly report scheduling system that integrates seamlessly with the existing KoolSoft infrastructure.
