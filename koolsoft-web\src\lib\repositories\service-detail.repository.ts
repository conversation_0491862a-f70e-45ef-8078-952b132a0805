import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Service Detail Repository
 *
 * This repository handles database operations for the Service Detail entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class ServiceDetailRepository extends PrismaRepository<
  Prisma.service_detailsGetPayload<{}>,
  string,
  Prisma.service_detailsCreateInput,
  Prisma.service_detailsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('service_details');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: PrismaClient): BaseRepository<
    Prisma.service_detailsGetPayload<{}>,
    string,
    Prisma.service_detailsCreateInput,
    Prisma.service_detailsUpdateInput
  > {
    return new ServiceDetailRepository(tx);
  }

  /**
   * Find service details by service report ID
   * @param serviceReportId Service report ID
   * @returns Promise resolving to an array of service details
   */
  async findByServiceReportId(serviceReportId: string): Promise<any[]> {
    return this.model.findMany({
      where: { serviceReportId },
      orderBy: { createdAt: 'desc' },
      include: {
        serviceReport: {
          select: {
            id: true,
            reportDate: true,
            status: true,
            customer: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find service details by serial number
   * @param serialNumber Machine serial number
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service details
   */
  async findBySerialNumber(serialNumber: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { 
        serialNumber: {
          contains: serialNumber,
          mode: 'insensitive',
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        serviceReport: {
          select: {
            id: true,
            reportDate: true,
            visitDate: true,
            completionDate: true,
            status: true,
            customer: {
              select: {
                id: true,
                name: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find service details by machine type
   * @param machineType Machine type
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of service details
   */
  async findByMachineType(machineType: string, skip?: number, take?: number): Promise<any[]> {
    return this.model.findMany({
      where: { 
        machineType: {
          contains: machineType,
          mode: 'insensitive',
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
      include: {
        serviceReport: {
          select: {
            id: true,
            reportDate: true,
            status: true,
            customer: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Find service details with filtering and pagination
   * @param filter Filter criteria
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Order by criteria
   * @returns Promise resolving to filtered service details
   */
  async findWithFilter(
    filter: any = {},
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<any[]> {
    try {
      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);
      
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { createdAt: 'desc' },
        include: {
          serviceReport: {
            select: {
              id: true,
              reportDate: true,
              visitDate: true,
              completionDate: true,
              status: true,
              natureOfService: true,
              complaintType: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  city: true,
                  phone: true,
                },
              },
              executive: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                },
              },
            },
          },
        },
      });

      return result;
    } catch (error) {
      console.error('ServiceDetailRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count service details with filter
   * @param filter Filter criteria
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any = {}): Promise<number> {
    try {
      this.validateFilter(filter);
      return this.model.count({
        where: filter,
      });
    } catch (error) {
      console.error('ServiceDetailRepository.countWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Get service detail statistics
   * @returns Promise resolving to service detail statistics
   */
  async getStatistics(): Promise<any> {
    const [
      totalDetails,
      detailsWithParts,
      uniqueMachineTypes,
      uniqueSerialNumbers,
    ] = await Promise.all([
      this.model.count(),
      this.model.count({
        where: {
          partReplaced: {
            not: null,
          },
        },
      }),
      this.model.groupBy({
        by: ['machineType'],
        where: {
          machineType: {
            not: null,
          },
        },
      }).then((result: any) => result.length),
      this.model.groupBy({
        by: ['serialNumber'],
        where: {
          serialNumber: {
            not: null,
          },
        },
      }).then((result: any) => result.length),
    ]);

    return {
      totalDetails,
      detailsWithParts,
      uniqueMachineTypes,
      uniqueSerialNumbers,
      partReplacementRate: totalDetails > 0 ? (detailsWithParts / totalDetails) * 100 : 0,
    };
  }

  /**
   * Get most common problems
   * @param limit Number of problems to return
   * @returns Promise resolving to most common problems
   */
  async getMostCommonProblems(limit: number = 10): Promise<any[]> {
    const result = await this.model.groupBy({
      by: ['problem'],
      where: {
        problem: {
          not: null,
        },
      },
      _count: {
        problem: true,
      },
      orderBy: {
        _count: {
          problem: 'desc',
        },
      },
      take: limit,
    });

    return result.map((item: any) => ({
      problem: item.problem,
      count: item._count.problem,
    }));
  }

  /**
   * Get most replaced parts
   * @param limit Number of parts to return
   * @returns Promise resolving to most replaced parts
   */
  async getMostReplacedParts(limit: number = 10): Promise<any[]> {
    const result = await this.model.groupBy({
      by: ['partReplaced'],
      where: {
        partReplaced: {
          not: null,
        },
      },
      _count: {
        partReplaced: true,
      },
      orderBy: {
        _count: {
          partReplaced: 'desc',
        },
      },
      take: limit,
    });

    return result.map((item: any) => ({
      part: item.partReplaced,
      count: item._count.partReplaced,
    }));
  }

  /**
   * Create multiple service details
   * @param data Array of service detail data
   * @returns Promise resolving to created service details
   */
  async createMany(data: Prisma.service_detailsCreateInput[]): Promise<Prisma.service_detailsGetPayload<{}>[]> {
    await this.model.createMany({
      data,
    });

    // Return the created details - get the last created ones
    return this.model.findMany({
      where: {
        serviceReport: data[0]?.serviceReport ? data[0].serviceReport : undefined,
      },
      orderBy: { createdAt: 'desc' },
      take: data.length,
    });
  }

  /**
   * Create multiple service details for a specific service report
   * @param serviceReportId Service report ID
   * @param details Array of service detail data
   * @returns Promise resolving to created service details
   */
  async createManyForServiceReport(serviceReportId: string, details: any[]): Promise<any[]> {
    const detailsWithReportId = details.map(detail => ({
      ...detail,
      serviceReportId,
    }));

    await this.model.createMany({
      data: detailsWithReportId,
    });

    // Return the created details
    return this.findByServiceReportId(serviceReportId);
  }

  /**
   * Delete service details by service report ID
   * @param serviceReportId Service report ID
   * @returns Promise resolving to the number of deleted records
   */
  async deleteByServiceReportId(serviceReportId: string): Promise<number> {
    const result = await this.model.deleteMany({
      where: { serviceReportId },
    });

    return result.count;
  }

  /**
   * Validate filter object to prevent injection attacks
   * @param filter Filter object to validate
   */
  private validateFilter(filter: any): void {
    const allowedFields = [
      'id',
      'serviceReportId',
      'machineType',
      'serialNumber',
      'problem',
      'solution',
      'partReplaced',
    ];

    for (const key in filter) {
      if (!allowedFields.includes(key)) {
        throw new Error(`Invalid filter field: ${key}`);
      }
    }
  }
}
