"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-array/src/ascending.js":
/*!************************************************!*\
  !*** ./node_modules/d3-array/src/ascending.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ascending)\n/* harmony export */ });\nfunction ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2FzY2VuZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsU0FBU0EsQ0FBQ0MsQ0FBQyxFQUFFQyxDQUFDLEVBQUU7RUFDdEMsT0FBT0QsQ0FBQyxJQUFJLElBQUksSUFBSUMsQ0FBQyxJQUFJLElBQUksR0FBR0MsR0FBRyxHQUFHRixDQUFDLEdBQUdDLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBR0QsQ0FBQyxHQUFHQyxDQUFDLEdBQUcsQ0FBQyxHQUFHRCxDQUFDLElBQUlDLENBQUMsR0FBRyxDQUFDLEdBQUdDLEdBQUc7QUFDakYiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxhc2NlbmRpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gYXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU4gOiBhIDwgYiA/IC0xIDogYSA+IGIgPyAxIDogYSA+PSBiID8gMCA6IE5hTjtcbn1cbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJhIiwiYiIsIk5hTiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ascending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisect.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/bisect.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bisectCenter: () => (/* binding */ bisectCenter),\n/* harmony export */   bisectLeft: () => (/* binding */ bisectLeft),\n/* harmony export */   bisectRight: () => (/* binding */ bisectRight),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _bisector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bisector.js */ \"(ssr)/./node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n\n\n\nconst ascendingBisect = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = (0,_bisector_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_number_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).center;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bisectRight);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Jpc2VjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXVDO0FBQ0Y7QUFDSjtBQUVqQyxNQUFNRyxlQUFlLEdBQUdGLHdEQUFRLENBQUNELHFEQUFTLENBQUM7QUFDcEMsTUFBTUksV0FBVyxHQUFHRCxlQUFlLENBQUNFLEtBQUs7QUFDekMsTUFBTUMsVUFBVSxHQUFHSCxlQUFlLENBQUNJLElBQUk7QUFDdkMsTUFBTUMsWUFBWSxHQUFHUCx3REFBUSxDQUFDQyxrREFBTSxDQUFDLENBQUNPLE1BQU07QUFDbkQsaUVBQWVMLFdBQVciLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxiaXNlY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFzY2VuZGluZyBmcm9tIFwiLi9hc2NlbmRpbmcuanNcIjtcbmltcG9ydCBiaXNlY3RvciBmcm9tIFwiLi9iaXNlY3Rvci5qc1wiO1xuaW1wb3J0IG51bWJlciBmcm9tIFwiLi9udW1iZXIuanNcIjtcblxuY29uc3QgYXNjZW5kaW5nQmlzZWN0ID0gYmlzZWN0b3IoYXNjZW5kaW5nKTtcbmV4cG9ydCBjb25zdCBiaXNlY3RSaWdodCA9IGFzY2VuZGluZ0Jpc2VjdC5yaWdodDtcbmV4cG9ydCBjb25zdCBiaXNlY3RMZWZ0ID0gYXNjZW5kaW5nQmlzZWN0LmxlZnQ7XG5leHBvcnQgY29uc3QgYmlzZWN0Q2VudGVyID0gYmlzZWN0b3IobnVtYmVyKS5jZW50ZXI7XG5leHBvcnQgZGVmYXVsdCBiaXNlY3RSaWdodDtcbiJdLCJuYW1lcyI6WyJhc2NlbmRpbmciLCJiaXNlY3RvciIsIm51bWJlciIsImFzY2VuZGluZ0Jpc2VjdCIsImJpc2VjdFJpZ2h0IiwicmlnaHQiLCJiaXNlY3RMZWZ0IiwibGVmdCIsImJpc2VjdENlbnRlciIsImNlbnRlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/bisector.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/bisector.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ bisector)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _descending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./descending.js */ \"(ssr)/./node_modules/d3-array/src/descending.js\");\n\n\nfunction bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    compare2 = (d, x) => (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] || f === _descending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction zero() {\n  return 0;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/bisector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/descending.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-array/src/descending.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ descending)\n/* harmony export */ });\nfunction descending(a, b) {\n  return a == null || b == null ? NaN : b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL2Rlc2NlbmRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFVBQVVBLENBQUNDLENBQUMsRUFBRUMsQ0FBQyxFQUFFO0VBQ3ZDLE9BQU9ELENBQUMsSUFBSSxJQUFJLElBQUlDLENBQUMsSUFBSSxJQUFJLEdBQUdDLEdBQUcsR0FDL0JELENBQUMsR0FBR0QsQ0FBQyxHQUFHLENBQUMsQ0FBQyxHQUNWQyxDQUFDLEdBQUdELENBQUMsR0FBRyxDQUFDLEdBQ1RDLENBQUMsSUFBSUQsQ0FBQyxHQUFHLENBQUMsR0FDVkUsR0FBRztBQUNUIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcZDMtYXJyYXlcXHNyY1xcZGVzY2VuZGluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBkZXNjZW5kaW5nKGEsIGIpIHtcbiAgcmV0dXJuIGEgPT0gbnVsbCB8fCBiID09IG51bGwgPyBOYU5cbiAgICA6IGIgPCBhID8gLTFcbiAgICA6IGIgPiBhID8gMVxuICAgIDogYiA+PSBhID8gMFxuICAgIDogTmFOO1xufVxuIl0sIm5hbWVzIjpbImRlc2NlbmRpbmciLCJhIiwiYiIsIk5hTiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/descending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/greatest.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/greatest.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ greatest)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n\nfunction greatest(values, compare = _ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, maxValue) > 0 : (0,_ascending_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, max) > 0 : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/greatest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/max.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/max.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ max)\n/* harmony export */ });\nfunction max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21heC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsR0FBR0EsQ0FBQ0MsTUFBTSxFQUFFQyxPQUFPLEVBQUU7RUFDM0MsSUFBSUYsR0FBRztFQUNQLElBQUlFLE9BQU8sS0FBS0MsU0FBUyxFQUFFO0lBQ3pCLEtBQUssTUFBTUMsS0FBSyxJQUFJSCxNQUFNLEVBQUU7TUFDMUIsSUFBSUcsS0FBSyxJQUFJLElBQUksS0FDVEosR0FBRyxHQUFHSSxLQUFLLElBQUtKLEdBQUcsS0FBS0csU0FBUyxJQUFJQyxLQUFLLElBQUlBLEtBQU0sQ0FBQyxFQUFFO1FBQzdESixHQUFHLEdBQUdJLEtBQUs7TUFDYjtJQUNGO0VBQ0YsQ0FBQyxNQUFNO0lBQ0wsSUFBSUMsS0FBSyxHQUFHLENBQUMsQ0FBQztJQUNkLEtBQUssSUFBSUQsS0FBSyxJQUFJSCxNQUFNLEVBQUU7TUFDeEIsSUFBSSxDQUFDRyxLQUFLLEdBQUdGLE9BQU8sQ0FBQ0UsS0FBSyxFQUFFLEVBQUVDLEtBQUssRUFBRUosTUFBTSxDQUFDLEtBQUssSUFBSSxLQUM3Q0QsR0FBRyxHQUFHSSxLQUFLLElBQUtKLEdBQUcsS0FBS0csU0FBUyxJQUFJQyxLQUFLLElBQUlBLEtBQU0sQ0FBQyxFQUFFO1FBQzdESixHQUFHLEdBQUdJLEtBQUs7TUFDYjtJQUNGO0VBQ0Y7RUFDQSxPQUFPSixHQUFHO0FBQ1oiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxtYXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWF4KHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWF4O1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1heCA8IHZhbHVlIHx8IChtYXggPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1heCA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWF4O1xufVxuIl0sIm5hbWVzIjpbIm1heCIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/max.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/maxIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/maxIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ maxIndex)\n/* harmony export */ });\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/maxIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/min.js":
/*!******************************************!*\
  !*** ./node_modules/d3-array/src/min.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ min)\n/* harmony export */ });\nfunction min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21pbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsR0FBR0EsQ0FBQ0MsTUFBTSxFQUFFQyxPQUFPLEVBQUU7RUFDM0MsSUFBSUYsR0FBRztFQUNQLElBQUlFLE9BQU8sS0FBS0MsU0FBUyxFQUFFO0lBQ3pCLEtBQUssTUFBTUMsS0FBSyxJQUFJSCxNQUFNLEVBQUU7TUFDMUIsSUFBSUcsS0FBSyxJQUFJLElBQUksS0FDVEosR0FBRyxHQUFHSSxLQUFLLElBQUtKLEdBQUcsS0FBS0csU0FBUyxJQUFJQyxLQUFLLElBQUlBLEtBQU0sQ0FBQyxFQUFFO1FBQzdESixHQUFHLEdBQUdJLEtBQUs7TUFDYjtJQUNGO0VBQ0YsQ0FBQyxNQUFNO0lBQ0wsSUFBSUMsS0FBSyxHQUFHLENBQUMsQ0FBQztJQUNkLEtBQUssSUFBSUQsS0FBSyxJQUFJSCxNQUFNLEVBQUU7TUFDeEIsSUFBSSxDQUFDRyxLQUFLLEdBQUdGLE9BQU8sQ0FBQ0UsS0FBSyxFQUFFLEVBQUVDLEtBQUssRUFBRUosTUFBTSxDQUFDLEtBQUssSUFBSSxLQUM3Q0QsR0FBRyxHQUFHSSxLQUFLLElBQUtKLEdBQUcsS0FBS0csU0FBUyxJQUFJQyxLQUFLLElBQUlBLEtBQU0sQ0FBQyxFQUFFO1FBQzdESixHQUFHLEdBQUdJLEtBQUs7TUFDYjtJQUNGO0VBQ0Y7RUFDQSxPQUFPSixHQUFHO0FBQ1oiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxtaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWluKHZhbHVlcywgdmFsdWVvZikge1xuICBsZXQgbWluO1xuICBpZiAodmFsdWVvZiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICgodmFsdWUgPSB2YWx1ZW9mKHZhbHVlLCArK2luZGV4LCB2YWx1ZXMpKSAhPSBudWxsXG4gICAgICAgICAgJiYgKG1pbiA+IHZhbHVlIHx8IChtaW4gPT09IHVuZGVmaW5lZCAmJiB2YWx1ZSA+PSB2YWx1ZSkpKSB7XG4gICAgICAgIG1pbiA9IHZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWluO1xufVxuIl0sIm5hbWVzIjpbIm1pbiIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/min.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/minIndex.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/minIndex.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ minIndex)\n/* harmony export */ });\nfunction minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null && (min > value || min === undefined && value >= value)) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (min > value || min === undefined && value >= value)) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/minIndex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-array/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number),\n/* harmony export */   numbers: () => (/* binding */ numbers)\n/* harmony export */ });\nfunction number(x) {\n  return x === null ? NaN : +x;\n}\nfunction* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFlLFNBQVNBLE1BQU1BLENBQUNDLENBQUMsRUFBRTtFQUNoQyxPQUFPQSxDQUFDLEtBQUssSUFBSSxHQUFHQyxHQUFHLEdBQUcsQ0FBQ0QsQ0FBQztBQUM5QjtBQUVPLFVBQVVFLE9BQU9BLENBQUNDLE1BQU0sRUFBRUMsT0FBTyxFQUFFO0VBQ3hDLElBQUlBLE9BQU8sS0FBS0MsU0FBUyxFQUFFO0lBQ3pCLEtBQUssSUFBSUMsS0FBSyxJQUFJSCxNQUFNLEVBQUU7TUFDeEIsSUFBSUcsS0FBSyxJQUFJLElBQUksSUFBSSxDQUFDQSxLQUFLLEdBQUcsQ0FBQ0EsS0FBSyxLQUFLQSxLQUFLLEVBQUU7UUFDOUMsTUFBTUEsS0FBSztNQUNiO0lBQ0Y7RUFDRixDQUFDLE1BQU07SUFDTCxJQUFJQyxLQUFLLEdBQUcsQ0FBQyxDQUFDO0lBQ2QsS0FBSyxJQUFJRCxLQUFLLElBQUlILE1BQU0sRUFBRTtNQUN4QixJQUFJLENBQUNHLEtBQUssR0FBR0YsT0FBTyxDQUFDRSxLQUFLLEVBQUUsRUFBRUMsS0FBSyxFQUFFSixNQUFNLENBQUMsS0FBSyxJQUFJLElBQUksQ0FBQ0csS0FBSyxHQUFHLENBQUNBLEtBQUssS0FBS0EsS0FBSyxFQUFFO1FBQ2xGLE1BQU1BLEtBQUs7TUFDYjtJQUNGO0VBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXG51bWJlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBudW1iZXIoeCkge1xuICByZXR1cm4geCA9PT0gbnVsbCA/IE5hTiA6ICt4O1xufVxuXG5leHBvcnQgZnVuY3Rpb24qIG51bWJlcnModmFsdWVzLCB2YWx1ZW9mKSB7XG4gIGlmICh2YWx1ZW9mID09PSB1bmRlZmluZWQpIHtcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmICh2YWx1ZSA9ICt2YWx1ZSkgPj0gdmFsdWUpIHtcbiAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGxldCBpbmRleCA9IC0xO1xuICAgIGZvciAobGV0IHZhbHVlIG9mIHZhbHVlcykge1xuICAgICAgaWYgKCh2YWx1ZSA9IHZhbHVlb2YodmFsdWUsICsraW5kZXgsIHZhbHVlcykpICE9IG51bGwgJiYgKHZhbHVlID0gK3ZhbHVlKSA+PSB2YWx1ZSkge1xuICAgICAgICB5aWVsZCB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJudW1iZXIiLCJ4IiwiTmFOIiwibnVtYmVycyIsInZhbHVlcyIsInZhbHVlb2YiLCJ1bmRlZmluZWQiLCJ2YWx1ZSIsImluZGV4Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/permute.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-array/src/permute.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ permute)\n/* harmony export */ });\nfunction permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3Blcm11dGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLE9BQU9BLENBQUNDLE1BQU0sRUFBRUMsSUFBSSxFQUFFO0VBQzVDLE9BQU9DLEtBQUssQ0FBQ0MsSUFBSSxDQUFDRixJQUFJLEVBQUVHLEdBQUcsSUFBSUosTUFBTSxDQUFDSSxHQUFHLENBQUMsQ0FBQztBQUM3QyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHBlcm11dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcGVybXV0ZShzb3VyY2UsIGtleXMpIHtcbiAgcmV0dXJuIEFycmF5LmZyb20oa2V5cywga2V5ID0+IHNvdXJjZVtrZXldKTtcbn1cbiJdLCJuYW1lcyI6WyJwZXJtdXRlIiwic291cmNlIiwia2V5cyIsIkFycmF5IiwiZnJvbSIsImtleSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/permute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-array/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile),\n/* harmony export */   quantileIndex: () => (/* binding */ quantileIndex),\n/* harmony export */   quantileSorted: () => (/* binding */ quantileSorted)\n/* harmony export */ });\n/* harmony import */ var _max_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./max.js */ \"(ssr)/./node_modules/d3-array/src/max.js\");\n/* harmony import */ var _maxIndex_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./maxIndex.js */ \"(ssr)/./node_modules/d3-array/src/maxIndex.js\");\n/* harmony import */ var _min_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./min.js */ \"(ssr)/./node_modules/d3-array/src/min.js\");\n/* harmony import */ var _minIndex_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./minIndex.js */ \"(ssr)/./node_modules/d3-array/src/minIndex.js\");\n/* harmony import */ var _quickselect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quickselect.js */ \"(ssr)/./node_modules/d3-array/src/quickselect.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-array/src/number.js\");\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n/* harmony import */ var _greatest_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./greatest.js */ \"(ssr)/./node_modules/d3-array/src/greatest.js\");\n\n\n\n\n\n\n\n\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from((0,_number_js__WEBPACK_IMPORTED_MODULE_0__.numbers)(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values);\n  if (p >= 1) return (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = (0,_max_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(values, i0).subarray(0, i0 + 1)),\n    value1 = (0,_min_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileSorted(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n    i = (n - 1) * p,\n    i0 = Math.floor(i),\n    value0 = +valueof(values[i0], i0, values),\n    value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\nfunction quantileIndex(values, p, valueof = _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => (0,_number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(valueof(values[i], i, values)));\n  if (p <= 0) return (0,_minIndex_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(numbers);\n  if (p >= 1) return (0,_maxIndex_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(numbers);\n  var numbers,\n    index = Uint32Array.from(values, (_, i) => i),\n    j = numbers.length - 1,\n    i = Math.floor(j * p);\n  (0,_quickselect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(index, i, 0, j, (i, j) => (0,_sort_js__WEBPACK_IMPORTED_MODULE_6__.ascendingDefined)(numbers[i], numbers[j]));\n  i = (0,_greatest_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(index.subarray(0, i + 1), i => numbers[i]);\n  return i >= 0 ? i : -1;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3F1YW50aWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQTJCO0FBQ1U7QUFDVjtBQUNVO0FBQ007QUFDQztBQUNEO0FBQ047QUFFdEIsU0FBU1MsUUFBUUEsQ0FBQ0MsTUFBTSxFQUFFQyxDQUFDLEVBQUVDLE9BQU8sRUFBRTtFQUNuREYsTUFBTSxHQUFHRyxZQUFZLENBQUNDLElBQUksQ0FBQ1IsbURBQU8sQ0FBQ0ksTUFBTSxFQUFFRSxPQUFPLENBQUMsQ0FBQztFQUNwRCxJQUFJLEVBQUVHLENBQUMsR0FBR0wsTUFBTSxDQUFDTSxNQUFNLENBQUMsSUFBSUMsS0FBSyxDQUFDTixDQUFDLEdBQUcsQ0FBQ0EsQ0FBQyxDQUFDLEVBQUU7RUFDM0MsSUFBSUEsQ0FBQyxJQUFJLENBQUMsSUFBSUksQ0FBQyxHQUFHLENBQUMsRUFBRSxPQUFPYixtREFBRyxDQUFDUSxNQUFNLENBQUM7RUFDdkMsSUFBSUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxPQUFPWCxtREFBRyxDQUFDVSxNQUFNLENBQUM7RUFDOUIsSUFBSUssQ0FBQztJQUNERyxDQUFDLEdBQUcsQ0FBQ0gsQ0FBQyxHQUFHLENBQUMsSUFBSUosQ0FBQztJQUNmUSxFQUFFLEdBQUdDLElBQUksQ0FBQ0MsS0FBSyxDQUFDSCxDQUFDLENBQUM7SUFDbEJJLE1BQU0sR0FBR3RCLG1EQUFHLENBQUNJLDJEQUFXLENBQUNNLE1BQU0sRUFBRVMsRUFBRSxDQUFDLENBQUNJLFFBQVEsQ0FBQyxDQUFDLEVBQUVKLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztJQUN6REssTUFBTSxHQUFHdEIsbURBQUcsQ0FBQ1EsTUFBTSxDQUFDYSxRQUFRLENBQUNKLEVBQUUsR0FBRyxDQUFDLENBQUMsQ0FBQztFQUN6QyxPQUFPRyxNQUFNLEdBQUcsQ0FBQ0UsTUFBTSxHQUFHRixNQUFNLEtBQUtKLENBQUMsR0FBR0MsRUFBRSxDQUFDO0FBQzlDO0FBRU8sU0FBU00sY0FBY0EsQ0FBQ2YsTUFBTSxFQUFFQyxDQUFDLEVBQUVDLE9BQU8sR0FBR1Asa0RBQU0sRUFBRTtFQUMxRCxJQUFJLEVBQUVVLENBQUMsR0FBR0wsTUFBTSxDQUFDTSxNQUFNLENBQUMsSUFBSUMsS0FBSyxDQUFDTixDQUFDLEdBQUcsQ0FBQ0EsQ0FBQyxDQUFDLEVBQUU7RUFDM0MsSUFBSUEsQ0FBQyxJQUFJLENBQUMsSUFBSUksQ0FBQyxHQUFHLENBQUMsRUFBRSxPQUFPLENBQUNILE9BQU8sQ0FBQ0YsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRUEsTUFBTSxDQUFDO0VBQzFELElBQUlDLENBQUMsSUFBSSxDQUFDLEVBQUUsT0FBTyxDQUFDQyxPQUFPLENBQUNGLE1BQU0sQ0FBQ0ssQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFQSxDQUFDLEdBQUcsQ0FBQyxFQUFFTCxNQUFNLENBQUM7RUFDekQsSUFBSUssQ0FBQztJQUNERyxDQUFDLEdBQUcsQ0FBQ0gsQ0FBQyxHQUFHLENBQUMsSUFBSUosQ0FBQztJQUNmUSxFQUFFLEdBQUdDLElBQUksQ0FBQ0MsS0FBSyxDQUFDSCxDQUFDLENBQUM7SUFDbEJJLE1BQU0sR0FBRyxDQUFDVixPQUFPLENBQUNGLE1BQU0sQ0FBQ1MsRUFBRSxDQUFDLEVBQUVBLEVBQUUsRUFBRVQsTUFBTSxDQUFDO0lBQ3pDYyxNQUFNLEdBQUcsQ0FBQ1osT0FBTyxDQUFDRixNQUFNLENBQUNTLEVBQUUsR0FBRyxDQUFDLENBQUMsRUFBRUEsRUFBRSxHQUFHLENBQUMsRUFBRVQsTUFBTSxDQUFDO0VBQ3JELE9BQU9ZLE1BQU0sR0FBRyxDQUFDRSxNQUFNLEdBQUdGLE1BQU0sS0FBS0osQ0FBQyxHQUFHQyxFQUFFLENBQUM7QUFDOUM7QUFFTyxTQUFTTyxhQUFhQSxDQUFDaEIsTUFBTSxFQUFFQyxDQUFDLEVBQUVDLE9BQU8sR0FBR1Asa0RBQU0sRUFBRTtFQUN6RCxJQUFJWSxLQUFLLENBQUNOLENBQUMsR0FBRyxDQUFDQSxDQUFDLENBQUMsRUFBRTtFQUNuQkwsT0FBTyxHQUFHTyxZQUFZLENBQUNDLElBQUksQ0FBQ0osTUFBTSxFQUFFLENBQUNpQixDQUFDLEVBQUVULENBQUMsS0FBS2Isc0RBQU0sQ0FBQ08sT0FBTyxDQUFDRixNQUFNLENBQUNRLENBQUMsQ0FBQyxFQUFFQSxDQUFDLEVBQUVSLE1BQU0sQ0FBQyxDQUFDLENBQUM7RUFDcEYsSUFBSUMsQ0FBQyxJQUFJLENBQUMsRUFBRSxPQUFPUix3REFBUSxDQUFDRyxPQUFPLENBQUM7RUFDcEMsSUFBSUssQ0FBQyxJQUFJLENBQUMsRUFBRSxPQUFPVix3REFBUSxDQUFDSyxPQUFPLENBQUM7RUFDcEMsSUFBSUEsT0FBTztJQUNQc0IsS0FBSyxHQUFHQyxXQUFXLENBQUNmLElBQUksQ0FBQ0osTUFBTSxFQUFFLENBQUNpQixDQUFDLEVBQUVULENBQUMsS0FBS0EsQ0FBQyxDQUFDO0lBQzdDWSxDQUFDLEdBQUd4QixPQUFPLENBQUNVLE1BQU0sR0FBRyxDQUFDO0lBQ3RCRSxDQUFDLEdBQUdFLElBQUksQ0FBQ0MsS0FBSyxDQUFDUyxDQUFDLEdBQUduQixDQUFDLENBQUM7RUFDekJQLDJEQUFXLENBQUN3QixLQUFLLEVBQUVWLENBQUMsRUFBRSxDQUFDLEVBQUVZLENBQUMsRUFBRSxDQUFDWixDQUFDLEVBQUVZLENBQUMsS0FBS3ZCLDBEQUFnQixDQUFDRCxPQUFPLENBQUNZLENBQUMsQ0FBQyxFQUFFWixPQUFPLENBQUN3QixDQUFDLENBQUMsQ0FBQyxDQUFDO0VBQy9FWixDQUFDLEdBQUdWLHdEQUFRLENBQUNvQixLQUFLLENBQUNMLFFBQVEsQ0FBQyxDQUFDLEVBQUVMLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBR0EsQ0FBQyxJQUFLWixPQUFPLENBQUNZLENBQUMsQ0FBQyxDQUFDO0VBQ3pELE9BQU9BLENBQUMsSUFBSSxDQUFDLEdBQUdBLENBQUMsR0FBRyxDQUFDLENBQUM7QUFDeEIiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcbm9kZV9tb2R1bGVzXFxkMy1hcnJheVxcc3JjXFxxdWFudGlsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbWF4IGZyb20gXCIuL21heC5qc1wiO1xuaW1wb3J0IG1heEluZGV4IGZyb20gXCIuL21heEluZGV4LmpzXCI7XG5pbXBvcnQgbWluIGZyb20gXCIuL21pbi5qc1wiO1xuaW1wb3J0IG1pbkluZGV4IGZyb20gXCIuL21pbkluZGV4LmpzXCI7XG5pbXBvcnQgcXVpY2tzZWxlY3QgZnJvbSBcIi4vcXVpY2tzZWxlY3QuanNcIjtcbmltcG9ydCBudW1iZXIsIHtudW1iZXJzfSBmcm9tIFwiLi9udW1iZXIuanNcIjtcbmltcG9ydCB7YXNjZW5kaW5nRGVmaW5lZH0gZnJvbSBcIi4vc29ydC5qc1wiO1xuaW1wb3J0IGdyZWF0ZXN0IGZyb20gXCIuL2dyZWF0ZXN0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHF1YW50aWxlKHZhbHVlcywgcCwgdmFsdWVvZikge1xuICB2YWx1ZXMgPSBGbG9hdDY0QXJyYXkuZnJvbShudW1iZXJzKHZhbHVlcywgdmFsdWVvZikpO1xuICBpZiAoIShuID0gdmFsdWVzLmxlbmd0aCkgfHwgaXNOYU4ocCA9ICtwKSkgcmV0dXJuO1xuICBpZiAocCA8PSAwIHx8IG4gPCAyKSByZXR1cm4gbWluKHZhbHVlcyk7XG4gIGlmIChwID49IDEpIHJldHVybiBtYXgodmFsdWVzKTtcbiAgdmFyIG4sXG4gICAgICBpID0gKG4gLSAxKSAqIHAsXG4gICAgICBpMCA9IE1hdGguZmxvb3IoaSksXG4gICAgICB2YWx1ZTAgPSBtYXgocXVpY2tzZWxlY3QodmFsdWVzLCBpMCkuc3ViYXJyYXkoMCwgaTAgKyAxKSksXG4gICAgICB2YWx1ZTEgPSBtaW4odmFsdWVzLnN1YmFycmF5KGkwICsgMSkpO1xuICByZXR1cm4gdmFsdWUwICsgKHZhbHVlMSAtIHZhbHVlMCkgKiAoaSAtIGkwKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHF1YW50aWxlU29ydGVkKHZhbHVlcywgcCwgdmFsdWVvZiA9IG51bWJlcikge1xuICBpZiAoIShuID0gdmFsdWVzLmxlbmd0aCkgfHwgaXNOYU4ocCA9ICtwKSkgcmV0dXJuO1xuICBpZiAocCA8PSAwIHx8IG4gPCAyKSByZXR1cm4gK3ZhbHVlb2YodmFsdWVzWzBdLCAwLCB2YWx1ZXMpO1xuICBpZiAocCA+PSAxKSByZXR1cm4gK3ZhbHVlb2YodmFsdWVzW24gLSAxXSwgbiAtIDEsIHZhbHVlcyk7XG4gIHZhciBuLFxuICAgICAgaSA9IChuIC0gMSkgKiBwLFxuICAgICAgaTAgPSBNYXRoLmZsb29yKGkpLFxuICAgICAgdmFsdWUwID0gK3ZhbHVlb2YodmFsdWVzW2kwXSwgaTAsIHZhbHVlcyksXG4gICAgICB2YWx1ZTEgPSArdmFsdWVvZih2YWx1ZXNbaTAgKyAxXSwgaTAgKyAxLCB2YWx1ZXMpO1xuICByZXR1cm4gdmFsdWUwICsgKHZhbHVlMSAtIHZhbHVlMCkgKiAoaSAtIGkwKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHF1YW50aWxlSW5kZXgodmFsdWVzLCBwLCB2YWx1ZW9mID0gbnVtYmVyKSB7XG4gIGlmIChpc05hTihwID0gK3ApKSByZXR1cm47XG4gIG51bWJlcnMgPSBGbG9hdDY0QXJyYXkuZnJvbSh2YWx1ZXMsIChfLCBpKSA9PiBudW1iZXIodmFsdWVvZih2YWx1ZXNbaV0sIGksIHZhbHVlcykpKTtcbiAgaWYgKHAgPD0gMCkgcmV0dXJuIG1pbkluZGV4KG51bWJlcnMpO1xuICBpZiAocCA+PSAxKSByZXR1cm4gbWF4SW5kZXgobnVtYmVycyk7XG4gIHZhciBudW1iZXJzLFxuICAgICAgaW5kZXggPSBVaW50MzJBcnJheS5mcm9tKHZhbHVlcywgKF8sIGkpID0+IGkpLFxuICAgICAgaiA9IG51bWJlcnMubGVuZ3RoIC0gMSxcbiAgICAgIGkgPSBNYXRoLmZsb29yKGogKiBwKTtcbiAgcXVpY2tzZWxlY3QoaW5kZXgsIGksIDAsIGosIChpLCBqKSA9PiBhc2NlbmRpbmdEZWZpbmVkKG51bWJlcnNbaV0sIG51bWJlcnNbal0pKTtcbiAgaSA9IGdyZWF0ZXN0KGluZGV4LnN1YmFycmF5KDAsIGkgKyAxKSwgKGkpID0+IG51bWJlcnNbaV0pO1xuICByZXR1cm4gaSA+PSAwID8gaSA6IC0xO1xufVxuIl0sIm5hbWVzIjpbIm1heCIsIm1heEluZGV4IiwibWluIiwibWluSW5kZXgiLCJxdWlja3NlbGVjdCIsIm51bWJlciIsIm51bWJlcnMiLCJhc2NlbmRpbmdEZWZpbmVkIiwiZ3JlYXRlc3QiLCJxdWFudGlsZSIsInZhbHVlcyIsInAiLCJ2YWx1ZW9mIiwiRmxvYXQ2NEFycmF5IiwiZnJvbSIsIm4iLCJsZW5ndGgiLCJpc05hTiIsImkiLCJpMCIsIk1hdGgiLCJmbG9vciIsInZhbHVlMCIsInN1YmFycmF5IiwidmFsdWUxIiwicXVhbnRpbGVTb3J0ZWQiLCJxdWFudGlsZUluZGV4IiwiXyIsImluZGV4IiwiVWludDMyQXJyYXkiLCJqIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/quickselect.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-array/src/quickselect.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quickselect)\n/* harmony export */ });\n/* harmony import */ var _sort_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sort.js */ \"(ssr)/./node_modules/d3-array/src/sort.js\");\n\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n  if (!(left <= k && k <= right)) return array;\n  compare = compare === undefined ? _sort_js__WEBPACK_IMPORTED_MODULE_0__.ascendingDefined : (0,_sort_js__WEBPACK_IMPORTED_MODULE_0__.compareDefined)(compare);\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n    const t = array[k];\n    let i = left;\n    let j = right;\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n    if (compare(array[left], t) === 0) swap(array, left, j);else ++j, swap(array, j, right);\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/quickselect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/range.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/range.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ range)\n/* harmony export */ });\nfunction range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n  var i = -1,\n    n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n    range = new Array(n);\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n  return range;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxLQUFLQSxDQUFDQyxLQUFLLEVBQUVDLElBQUksRUFBRUMsSUFBSSxFQUFFO0VBQy9DRixLQUFLLEdBQUcsQ0FBQ0EsS0FBSyxFQUFFQyxJQUFJLEdBQUcsQ0FBQ0EsSUFBSSxFQUFFQyxJQUFJLEdBQUcsQ0FBQ0MsQ0FBQyxHQUFHQyxTQUFTLENBQUNDLE1BQU0sSUFBSSxDQUFDLElBQUlKLElBQUksR0FBR0QsS0FBSyxFQUFFQSxLQUFLLEdBQUcsQ0FBQyxFQUFFLENBQUMsSUFBSUcsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQ0QsSUFBSTtFQUVsSCxJQUFJSSxDQUFDLEdBQUcsQ0FBQyxDQUFDO0lBQ05ILENBQUMsR0FBR0ksSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxFQUFFRCxJQUFJLENBQUNFLElBQUksQ0FBQyxDQUFDUixJQUFJLEdBQUdELEtBQUssSUFBSUUsSUFBSSxDQUFDLENBQUMsR0FBRyxDQUFDO0lBQ3JESCxLQUFLLEdBQUcsSUFBSVcsS0FBSyxDQUFDUCxDQUFDLENBQUM7RUFFeEIsT0FBTyxFQUFFRyxDQUFDLEdBQUdILENBQUMsRUFBRTtJQUNkSixLQUFLLENBQUNPLENBQUMsQ0FBQyxHQUFHTixLQUFLLEdBQUdNLENBQUMsR0FBR0osSUFBSTtFQUM3QjtFQUVBLE9BQU9ILEtBQUs7QUFDZCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGQzLWFycmF5XFxzcmNcXHJhbmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJhbmdlKHN0YXJ0LCBzdG9wLCBzdGVwKSB7XG4gIHN0YXJ0ID0gK3N0YXJ0LCBzdG9wID0gK3N0b3AsIHN0ZXAgPSAobiA9IGFyZ3VtZW50cy5sZW5ndGgpIDwgMiA/IChzdG9wID0gc3RhcnQsIHN0YXJ0ID0gMCwgMSkgOiBuIDwgMyA/IDEgOiArc3RlcDtcblxuICB2YXIgaSA9IC0xLFxuICAgICAgbiA9IE1hdGgubWF4KDAsIE1hdGguY2VpbCgoc3RvcCAtIHN0YXJ0KSAvIHN0ZXApKSB8IDAsXG4gICAgICByYW5nZSA9IG5ldyBBcnJheShuKTtcblxuICB3aGlsZSAoKytpIDwgbikge1xuICAgIHJhbmdlW2ldID0gc3RhcnQgKyBpICogc3RlcDtcbiAgfVxuXG4gIHJldHVybiByYW5nZTtcbn1cbiJdLCJuYW1lcyI6WyJyYW5nZSIsInN0YXJ0Iiwic3RvcCIsInN0ZXAiLCJuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiaSIsIk1hdGgiLCJtYXgiLCJjZWlsIiwiQXJyYXkiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/range.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/sort.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/sort.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ascendingDefined: () => (/* binding */ ascendingDefined),\n/* harmony export */   compareDefined: () => (/* binding */ compareDefined),\n/* harmony export */   \"default\": () => (/* binding */ sort)\n/* harmony export */ });\n/* harmony import */ var _ascending_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ascending.js */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _permute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./permute.js */ \"(ssr)/./node_modules/d3-array/src/permute.js\");\n\n\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if (f && f.length !== 2 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return (0,_permute_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\nfunction compareDefined(compare = _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n  if (compare === _ascending_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\nfunction ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/sort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/ticks.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/ticks.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ticks),\n/* harmony export */   tickIncrement: () => (/* binding */ tickIncrement),\n/* harmony export */   tickStep: () => (/* binding */ tickStep)\n/* harmony export */ });\nconst e10 = Math.sqrt(50),\n  e5 = Math.sqrt(10),\n  e2 = Math.sqrt(2);\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n    power = Math.floor(Math.log10(step)),\n    error = step / Math.pow(10, power),\n    factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\nfunction ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start,\n    [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1,\n    ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\nfunction tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\nfunction tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start,\n    inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/ticks.js\n");

/***/ })

};
;