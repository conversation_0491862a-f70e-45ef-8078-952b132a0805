import * as cron from 'node-cron';
import { ScheduledReportRepository } from '@/lib/repositories/scheduled-report.repository';
import { ScheduledReportExecutionRepository } from '@/lib/repositories/scheduled-report-execution.repository';
import { ReportRepository } from '@/lib/repositories/report.repository';
import { getEmailService } from '@/lib/services/email.service';
import { generateReportFile } from '@/lib/utils/report-generator';
import { ExportFormat } from '@/lib/validations/scheduled-report.schema';

/**
 * Service for managing report scheduling and execution
 */
export class ReportSchedulerService {
  private scheduledReportRepository: ScheduledReportRepository;
  private executionRepository: ScheduledReportExecutionRepository;
  private reportRepository: ReportRepository;
  private emailService: any;
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();

  constructor() {
    this.scheduledReportRepository = new ScheduledReportRepository();
    this.executionRepository = new ScheduledReportExecutionRepository();
    this.reportRepository = new ReportRepository();
    this.emailService = getEmailService();
  }

  /**
   * Initialize the scheduler and load all active scheduled reports
   */
  async initialize() {
    console.log('Initializing Report Scheduler Service...');
    
    try {
      // Load all active scheduled reports
      const activeReports = await this.scheduledReportRepository.findMany({
        page: 1,
        limit: 1000, // Get all active reports
        isActive: true,
        sortBy: 'createdAt',
        sortOrder: 'asc',
      });

      // Schedule each active report
      for (const report of activeReports.data) {
        await this.scheduleReport(report.id, report.cronExpression);
      }

      console.log(`Initialized ${activeReports.data.length} scheduled reports`);
    } catch (error) {
      console.error('Error initializing Report Scheduler Service:', error);
    }
  }

  /**
   * Schedule a report with cron
   */
  async scheduleReport(reportId: string, cronExpression: string) {
    try {
      // Remove existing schedule if it exists
      this.unscheduleReport(reportId);

      // Validate cron expression
      if (!cron.validate(cronExpression)) {
        throw new Error(`Invalid cron expression: ${cronExpression}`);
      }

      // Create new scheduled task
      const task = cron.schedule(cronExpression, async () => {
        await this.executeReport(reportId);
      }, {
        timezone: 'UTC', // Use UTC for consistency
      });

      // Store the task
      this.scheduledTasks.set(reportId, task);
      
      console.log(`Scheduled report ${reportId} with cron: ${cronExpression}`);
    } catch (error) {
      console.error(`Error scheduling report ${reportId}:`, error);
      throw error;
    }
  }

  /**
   * Unschedule a report
   */
  unscheduleReport(reportId: string) {
    const existingTask = this.scheduledTasks.get(reportId);
    if (existingTask) {
      existingTask.stop();
      existingTask.destroy();
      this.scheduledTasks.delete(reportId);
      console.log(`Unscheduled report ${reportId}`);
    }
  }

  /**
   * Execute a scheduled report
   */
  async executeReport(reportId: string, manualTrigger: boolean = false) {
    console.log(`Executing scheduled report ${reportId}...`);
    
    let executionId: string | null = null;
    const startTime = Date.now();

    try {
      // Get the scheduled report details
      const scheduledReport = await this.scheduledReportRepository.findById(reportId);
      if (!scheduledReport) {
        throw new Error(`Scheduled report ${reportId} not found`);
      }

      if (!scheduledReport.isActive && !manualTrigger) {
        console.log(`Skipping inactive report ${reportId}`);
        return;
      }

      // Create execution record
      const execution = await this.executionRepository.create({
        scheduledReportId: reportId,
        status: 'PENDING',
        emailsSent: 0,
        emailErrors: [],
      });
      executionId = execution.id;

      // Mark as started
      await this.executionRepository.markAsStarted(executionId);

      // Generate the report
      const reportData = await this.generateReport(
        scheduledReport.reportType,
        scheduledReport.parameters || {}
      );

      // Generate file
      const filePath = await generateReportFile(
        reportData,
        scheduledReport.exportFormat as ExportFormat,
        scheduledReport.name
      );

      // Send emails
      let emailsSent = 0;
      const emailErrors: string[] = [];

      if (scheduledReport.emailRecipients.length > 0) {
        for (const recipient of scheduledReport.emailRecipients) {
          try {
            await this.sendReportEmail(
              recipient,
              scheduledReport,
              filePath,
              reportData
            );
            emailsSent++;
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            emailErrors.push(`${recipient}: ${errorMessage}`);
            console.error(`Error sending email to ${recipient}:`, error);
          }
        }
      }

      // Calculate execution time
      const executionTime = Date.now() - startTime;

      // Mark as completed
      await this.executionRepository.markAsCompleted(executionId, {
        reportData: {
          recordCount: reportData.data?.length || 0,
          filters: scheduledReport.parameters,
        },
        filePath,
        emailsSent,
        recordCount: reportData.data?.length || 0,
        executionTime,
      });

      // Update last run time for the scheduled report
      if (!manualTrigger) {
        await this.scheduledReportRepository.updateRunTimes(reportId, new Date());
      }

      console.log(`Successfully executed report ${reportId} in ${executionTime}ms`);
      
      return {
        success: true,
        executionId,
        recordCount: reportData.data?.length || 0,
        emailsSent,
        emailErrors,
        executionTime,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Error executing report ${reportId}:`, error);

      if (executionId) {
        await this.executionRepository.markAsFailed(executionId, errorMessage);
      }

      throw error;
    }
  }

  /**
   * Generate report data based on type and parameters
   */
  private async generateReport(reportType: string, parameters: any) {
    switch (reportType.toUpperCase()) {
      case 'AMC':
        return this.reportRepository.getAMCReports({
          page: 1,
          limit: 10000, // Get all records for scheduled reports
          sortBy: 'startDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'WARRANTY':
        return this.reportRepository.getWarrantyReports({
          page: 1,
          limit: 10000,
          sortBy: 'installDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'SERVICE':
        return this.reportRepository.getServiceReports({
          page: 1,
          limit: 10000,
          sortBy: 'reportDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'SALES':
        return this.reportRepository.getSalesReports({
          page: 1,
          limit: 10000,
          sortBy: 'orderDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'CUSTOMER':
        return this.reportRepository.getCustomerReports({
          page: 1,
          limit: 10000,
          sortBy: 'name',
          sortOrder: 'asc',
          ...parameters,
        });

      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }
  }

  /**
   * Send report via email
   */
  private async sendReportEmail(
    recipient: string,
    scheduledReport: any,
    filePath: string,
    reportData: any
  ) {
    const subject = scheduledReport.emailSubject || 
      `Scheduled Report: ${scheduledReport.name}`;
    
    const body = scheduledReport.emailBody || 
      `Please find attached the scheduled report "${scheduledReport.name}".
      
Report Details:
- Type: ${scheduledReport.reportType}
- Generated: ${new Date().toLocaleString()}
- Records: ${reportData.data?.length || 0}

This is an automated email from KoolSoft Report Scheduler.`;

    await this.emailService.sendEmail({
      to: recipient,
      subject,
      text: body,
      html: body.replace(/\n/g, '<br>'),
      attachments: [{
        filename: `${scheduledReport.name}.${scheduledReport.exportFormat.toLowerCase()}`,
        path: filePath,
      }],
    });
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      activeSchedules: this.scheduledTasks.size,
      scheduledReports: Array.from(this.scheduledTasks.keys()),
    };
  }

  /**
   * Shutdown the scheduler
   */
  shutdown() {
    console.log('Shutting down Report Scheduler Service...');
    
    this.scheduledTasks.forEach((task, reportId) => {
      task.stop();
      task.destroy();
    });
    
    this.scheduledTasks.clear();
    console.log('Report Scheduler Service shutdown complete');
  }
}

// Singleton instance
let reportSchedulerService: ReportSchedulerService | null = null;

/**
 * Get the singleton instance of ReportSchedulerService
 */
export function getReportSchedulerService(): ReportSchedulerService {
  if (!reportSchedulerService) {
    reportSchedulerService = new ReportSchedulerService();
  }
  return reportSchedulerService;
}
