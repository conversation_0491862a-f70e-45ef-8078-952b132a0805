import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

/**
 * Customer update schema
 */
const updateCustomerSchema = z.object({
  name: z.string().min(2).max(100).optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pinCode: z.string().optional(),
  phone: z.string().optional(),
  phone1: z.string().optional(),
  phone2: z.string().optional(),
  phone3: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email().optional(),
  mobile: z.string().optional(),
  website: z.string().url().optional(),
  location: z.string().optional(),
  isActive: z.boolean().optional(),
  contacts: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string().min(2).max(100),
      designation: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      isPrimary: z.boolean().default(false),
    })
  ).optional(),
});

/**
 * GET /api/customers/[id]
 * Get a specific customer by ID
 */
async function getCustomer(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    const customerRepository = getCustomerRepository();

    // Get customer with all related data
    const customer = await customerRepository.findWithRelations(id);

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/customers/[id]
 * Update a specific customer
 */
async function updateCustomer(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    const body = await request.json();

    // Validate request body
    const validatedData = updateCustomerSchema.parse(body);

    const customerRepository = getCustomerRepository();

    // Check if customer exists
    const existingCustomer = await customerRepository.findById(id);

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Extract contacts from validated data
    const { contacts, ...customerData } = validatedData;

    // Use transaction to update customer and contacts
    const customer = await customerRepository.transaction(async (txRepo) => {
      // Update customer
      const updatedCustomer = await txRepo.update(id, customerData);

      // Update contacts if provided
      if (contacts && contacts.length > 0) {
        // Get existing contacts
        const existingContacts = await (txRepo as any).model.contact.findMany({
          where: { customerId: id },
        });

        // Create a map of existing contacts by ID
        const existingContactsMap = new Map();
        existingContacts.forEach((contact: any) => {
          existingContactsMap.set(contact.id, contact);
        });

        // Process each contact
        for (const contact of contacts) {
          if (contact.id && existingContactsMap.has(contact.id)) {
            // Update existing contact
            await (txRepo as any).model.contact.update({
              where: { id: contact.id },
              data: {
                name: contact.name,
                designation: contact.designation,
                phone: contact.phone,
                email: contact.email,
                isPrimary: contact.isPrimary,
              },
            });

            // Remove from map to track which ones were processed
            existingContactsMap.delete(contact.id);
          } else {
            // Create new contact
            await (txRepo as any).model.contact.create({
              data: {
                name: contact.name,
                designation: contact.designation,
                phone: contact.phone,
                email: contact.email,
                isPrimary: contact.isPrimary,
                customerId: id,
              },
            });
          }
        }

        // Delete contacts that were not included in the update
        for (const [contactId] of existingContactsMap) {
          await (txRepo as any).model.contact.delete({
            where: { id: contactId },
          });
        }
      }

      // Return customer with contacts
      return (txRepo as any).model.customer.findUnique({
        where: { id },
        include: { contacts: true },
      });
    });

    return NextResponse.json(customer);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating customer:', error);
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/customers/[id]
 * Delete a specific customer
 */
async function deleteCustomer(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const id = pathParts[pathParts.length - 1];

    const customerRepository = getCustomerRepository();

    // Check if customer exists
    const existingCustomer = await customerRepository.findById(id);

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Check for related records that would prevent deletion
    const hasRelatedRecords = await customerRepository.hasRelatedRecords(id);

    if (hasRelatedRecords) {
      return NextResponse.json(
        { error: 'Cannot delete customer with related records' },
        { status: 409 }
      );
    }

    // Delete customer
    await customerRepository.delete(id);

    return NextResponse.json(
      { message: 'Customer deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    );
  }
}

// Export handlers with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], getCustomer),
  {
    action: 'view_customer',
    getEntityId: (req) => {
      try {
        // Extract ID from URL path
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      } catch (error) {
        console.error('Error getting entity ID:', error);
        return 'unknown';
      }
    },
  }
);

export const PUT = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(['ADMIN', 'MANAGER'], updateCustomer),
  {
    action: 'update_customer',
    getEntityId: (req) => {
      try {
        // Extract ID from URL path
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      } catch (error) {
        console.error('Error getting entity ID:', error);
        return 'unknown';
      }
    },
  }
);

export const DELETE = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(['ADMIN'], deleteCustomer),
  {
    action: 'delete_customer',
    getEntityId: (req) => {
      try {
        // Extract ID from URL path
        const url = new URL(req.url);
        const pathParts = url.pathname.split('/');
        return pathParts[pathParts.length - 1];
      } catch (error) {
        console.error('Error getting entity ID:', error);
        return 'unknown';
      }
    },
  }
);
