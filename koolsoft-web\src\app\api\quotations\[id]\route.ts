import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository, getQuotationItemRepository } from '@/lib/repositories';
import { updateQuotationSchema } from '@/lib/validations/quotation.schema';
import { z } from 'zod';

/**
 * GET /api/quotations/[id]
 * Get a specific quotation by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const quotationRepository = getQuotationRepository();
      const quotation = await quotationRepository.findWithAllRelations(id);

      if (!quotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: quotation,
      });
    } catch (error) {
      console.error('Error fetching quotation:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch quotation',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/quotations/[id]
 * Update a specific quotation
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateQuotationSchema.parse({ ...body, id });

      const quotationRepository = getQuotationRepository();
      const quotationItemRepository = getQuotationItemRepository();

      // Check if quotation exists
      const existingQuotation = await quotationRepository.findById(id);
      if (!existingQuotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      // Calculate totals if items are provided
      let subtotal = existingQuotation.subtotal;
      let taxAmount = existingQuotation.taxAmount;
      let totalAmount = existingQuotation.totalAmount;

      if (validatedData.items) {
        subtotal = 0;
        taxAmount = 0;
        
        validatedData.items.forEach((item) => {
          const itemTotalPrice = item.quantity * item.unitPrice;
          const itemTaxAmount = (itemTotalPrice * (item.taxRate || 0)) / 100;
          
          subtotal += itemTotalPrice;
          taxAmount += itemTaxAmount;
        });

        totalAmount = subtotal + taxAmount;
      }

      // Update quotation with items in a transaction
      const result = await quotationRepository.executeInTransaction(async (tx: any) => {
        // Update quotation
        const quotation = await tx.quotations.update({
          where: { id },
          data: {
            customerId: validatedData.customerId,
            executiveId: validatedData.executiveId,
            quotationDate: validatedData.quotationDate,
            validUntil: validatedData.validUntil,
            status: validatedData.status,
            contactPerson: validatedData.contactPerson,
            contactPhone: validatedData.contactPhone,
            contactEmail: validatedData.contactEmail,
            subject: validatedData.subject,
            notes: validatedData.notes,
            termsConditions: validatedData.termsConditions,
            subtotal,
            taxAmount,
            totalAmount,
            discount: validatedData.discount,
            discountType: validatedData.discountType,
          },
        });

        // Update items if provided
        if (validatedData.items) {
          // Delete existing items
          await tx.quotation_items.deleteMany({
            where: { quotationId: id },
          });

          // Create new items
          const items = await Promise.all(
            validatedData.items.map((item, index) => {
              const itemTotalPrice = item.quantity * item.unitPrice;
              const itemTaxAmount = (itemTotalPrice * (item.taxRate || 0)) / 100;

              return tx.quotation_items.create({
                data: {
                  quotationId: id,
                  productId: item.productId,
                  modelId: item.modelId,
                  brandId: item.brandId,
                  description: item.description,
                  quantity: item.quantity,
                  unitPrice: item.unitPrice,
                  totalPrice: itemTotalPrice,
                  taxRate: item.taxRate || 0,
                  taxAmount: itemTaxAmount,
                  discount: item.discount || 0,
                  discountType: item.discountType || 'PERCENTAGE',
                  specifications: item.specifications,
                  notes: item.notes,
                  sortOrder: item.sortOrder || index,
                },
              });
            })
          );

          return { quotation, items };
        }

        return { quotation };
      });

      // Fetch the complete updated quotation with relations
      const completeQuotation = await quotationRepository.findWithAllRelations(id);

      return NextResponse.json({
        success: true,
        data: completeQuotation,
        message: 'Quotation updated successfully',
      });
    } catch (error) {
      console.error('Error updating quotation:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid quotation data',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update quotation',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/quotations/[id]
 * Delete a specific quotation
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const quotationRepository = getQuotationRepository();

      // Check if quotation exists
      const existingQuotation = await quotationRepository.findById(id);
      if (!existingQuotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      // Delete quotation (items will be deleted automatically due to cascade)
      await quotationRepository.delete(id);

      return NextResponse.json({
        success: true,
        message: 'Quotation deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting quotation:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to delete quotation',
        },
        { status: 500 }
      );
    }
  }
);
