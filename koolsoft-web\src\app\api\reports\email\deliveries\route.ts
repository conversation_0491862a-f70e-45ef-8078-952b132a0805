import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportEmailDeliveryRepository } from '@/lib/repositories/email-distribution.repository';
import { EmailDistributionService } from '@/lib/services/email-distribution.service';
import { 
  listReportEmailDeliveriesSchema,
  retryEmailDeliverySchema,
} from '@/lib/validations/email-distribution.schema';
import { z } from 'zod';

/**
 * GET /api/reports/email/deliveries
 * List email deliveries with filtering
 */
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      configId: searchParams.get('configId') || undefined,
      reportType: searchParams.get('reportType') || undefined,
      status: searchParams.get('status') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
    };

    const validatedParams = listReportEmailDeliveriesSchema.parse(queryParams);
    const repository = new ReportEmailDeliveryRepository();

    const options = {
      ...validatedParams,
      startDate: validatedParams.startDate ? new Date(validatedParams.startDate) : undefined,
      endDate: validatedParams.endDate ? new Date(validatedParams.endDate) : undefined,
    };

    const result = await repository.findWithFilters(options);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });

  } catch (error) {
    console.error('Error fetching email deliveries:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid parameters', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch email deliveries' 
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/reports/email/deliveries/retry
 * Retry failed email delivery
 */
export const POST = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const validatedData = retryEmailDeliverySchema.parse(body);

    const emailDistributionService = new EmailDistributionService();

    // Retry the email delivery
    const result = await emailDistributionService.retryEmailDelivery(
      validatedData.deliveryId,
      validatedData.recipients
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          deliveryId: result.deliveryId,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
        },
        message: `Email retry completed. Sent to ${result.sentCount} recipient(s)${result.failedCount > 0 ? ` (${result.failedCount} failed)` : ''}`,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to retry email delivery',
        details: {
          deliveryId: result.deliveryId,
          sentCount: result.sentCount,
          failedCount: result.failedCount,
          errors: result.errors,
        },
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error retrying email delivery:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to retry email delivery'
      },
      { status: 500 }
    );
  }
});
