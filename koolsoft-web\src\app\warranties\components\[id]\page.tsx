'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  AlertTriangle,
  Settings,
  Calendar,
  DollarSign,
  User,
  Building
} from 'lucide-react';
import { toast } from 'sonner';

interface Component {
  id: string;
  machineId: string;
  componentNo?: number;
  serialNumber?: string;
  warrantyDate?: string;
  section?: string;
  createdAt: string;
  updatedAt: string;
  // Relations
  machine?: {
    id: string;
    warranty?: {
      id: string;
      bslNo: string;
      customer: {
        name: string;
      };
    };
    product?: {
      id: string;
      name: string;
    };
    model?: {
      id: string;
      name: string;
    };
    brand?: {
      id: string;
      name: string;
    };
  };
}

export default function ComponentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const componentId = params?.id as string;

  const [component, setComponent] = useState<Component | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadComponent();
  }, [componentId]);

  const loadComponent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/warranties/components/${componentId}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Component not found');
        } else if (response.status === 401) {
          throw new Error('Authentication required. Please log in.');
        }
        throw new Error(`Failed to load component: ${response.statusText}`);
      }

      const data = await response.json();
      setComponent(data);
    } catch (error: any) {
      console.error('Error loading component:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!component) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete component ${component.serialNumber}? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setIsDeleting(true);

      const response = await fetch(`/api/warranties/components/${componentId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to delete component');
      }

      toast.success('Component deleted successfully');
      router.push('/warranties/components');
    } catch (error: any) {
      console.error('Error deleting component:', error);
      toast.error(error.message || 'Failed to delete component');
    } finally {
      setIsDeleting(false);
    }
  };

  const getWarrantyStatus = (warrantyDate?: string) => {
    if (!warrantyDate) return { status: 'Unknown', color: 'bg-gray-100 text-gray-800' };

    const today = new Date();
    const warranty = new Date(warrantyDate);

    if (warranty > today) {
      return { status: 'Active', color: 'bg-green-100 text-green-800' };
    } else {
      return { status: 'Expired', color: 'bg-red-100 text-red-800' };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !component) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-black mb-2">Error Loading Component</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button asChild>
              <Link href="/warranties/components">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Components
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Component {component.serialNumber || component.componentNo || component.id}</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              {component.machine?.product?.name} - {component.machine?.model?.name}
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" asChild>
              <Link href={`/warranties/components/${component.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </>
              )}
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Component Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Component Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Serial Number</p>
                <p className="text-black">{component.serialNumber || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Component Number</p>
                <p className="text-black">{component.componentNo || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Warranty Status</p>
                {(() => {
                  const { status, color } = getWarrantyStatus(component.warrantyDate);
                  return <Badge className={color}>{status}</Badge>;
                })()}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Section</p>
                <p className="text-black">{component.section || 'N/A'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Warranty Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Warranty Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {component.warrantyDate && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Warranty Date</p>
                  <p className="text-black">{formatDate(component.warrantyDate)}</p>
                </div>
              )}
              <div>
                <p className="text-sm font-medium text-gray-500">Created</p>
                <p className="text-black">{formatDate(component.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Last Updated</p>
                <p className="text-black">{formatDate(component.updatedAt)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Related Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Machine Information */}
        {component.machine && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Machine Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Product</p>
                <p className="text-black">{component.machine.product?.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Model</p>
                <p className="text-black">{component.machine.model?.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Brand</p>
                <p className="text-black">{component.machine.brand?.name || 'N/A'}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Warranty Information */}
        {component.machine?.warranty && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Warranty Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500">BSL Number</p>
                <Button variant="link" className="p-0 h-auto" asChild>
                  <Link href={`/warranties/${component.machine.warranty.id}`}>
                    {component.machine.warranty.bslNo}
                  </Link>
                </Button>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Customer</p>
                <p className="text-black">{component.machine.warranty.customer?.name || 'N/A'}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
