import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import {
  BaseReportFilter,
  AMCReportFilter,
  WarrantyReportFilter,
  ServiceReportFilter,
  SalesReportFilter,
  CustomerReportFilter,
  FinancialReportFilter,
  AnalyticsReportFilter,
} from '@/lib/validations/report.schema';

/**
 * Report Repository
 * Handles all report data operations with proper filtering and pagination
 */
export class ReportRepository {
  /**
   * Get AMC Reports
   */
  async getAMCReports(filters: AMCReportFilter) {
    const whereClause: Prisma.amc_contractsWhereInput = {};

    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.startDate = {};
      if (filters.startDate) whereClause.startDate.gte = filters.startDate;
      if (filters.endDate) whereClause.startDate.lte = filters.endDate;
    }

    // Customer filter
    if (filters.customerId) {
      whereClause.customerId = filters.customerId;
    }

    // Executive filter
    if (filters.executiveId) {
      whereClause.executiveId = filters.executiveId;
    }

    // Amount filters
    if (filters.amountMin || filters.amountMax) {
      whereClause.amount = {};
      if (filters.amountMin) whereClause.amount.gte = filters.amountMin;
      if (filters.amountMax) whereClause.amount.lte = filters.amountMax;
    }

    // Status filter
    if (filters.amcStatus) {
      whereClause.status = filters.amcStatus;
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customer: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.amc_contracts.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'startDate']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.amc_contracts.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Warranty Reports
   */
  async getWarrantyReports(filters: WarrantyReportFilter) {
    const whereClause: Prisma.warrantiesWhereInput = {};

    // Date filters (using installDate for warranties)
    if (filters.startDate || filters.endDate) {
      whereClause.installDate = {};
      if (filters.startDate) whereClause.installDate.gte = filters.startDate;
      if (filters.endDate) whereClause.installDate.lte = filters.endDate;
    }

    // Customer filter
    if (filters.customerId) {
      whereClause.customerId = filters.customerId;
    }

    // Executive filter
    if (filters.executiveId) {
      whereClause.executiveId = filters.executiveId;
    }

    // Warranty status filter
    if (filters.warrantyStatus) {
      whereClause.status = filters.warrantyStatus;
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customer: { name: { contains: filters.search, mode: 'insensitive' } } },
        { users: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.warranties.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'installDate']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.warranties.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Service Reports
   */
  async getServiceReports(filters: ServiceReportFilter) {
    const whereClause: Prisma.service_reportsWhereInput = {};

    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.reportDate = {};
      if (filters.startDate) whereClause.reportDate.gte = filters.startDate;
      if (filters.endDate) whereClause.reportDate.lte = filters.endDate;
    }

    // Customer filter
    if (filters.customerId) {
      whereClause.customerId = filters.customerId;
    }

    // Executive filter
    if (filters.executiveId) {
      whereClause.executiveId = filters.executiveId;
    }

    // Service status filter
    if (filters.serviceStatus) {
      whereClause.status = filters.serviceStatus;
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customer: { name: { contains: filters.search, mode: 'insensitive' } } },
        { executive: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.service_reports.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'reportDate']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.service_reports.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Sales Reports
   */
  async getSalesReports(filters: SalesReportFilter) {
    const whereClause: Prisma.sales_ordersWhereInput = {};

    // Date filters
    if (filters.startDate || filters.endDate) {
      whereClause.orderDate = {};
      if (filters.startDate) whereClause.orderDate.gte = filters.startDate;
      if (filters.endDate) whereClause.orderDate.lte = filters.endDate;
    }

    // Customer filter
    if (filters.customerId) {
      whereClause.customerId = filters.customerId;
    }

    // Executive filter
    if (filters.executiveId) {
      whereClause.executiveId = filters.executiveId;
    }

    // Amount filters
    if (filters.amountMin || filters.amountMax) {
      whereClause.amount = {};
      if (filters.amountMin) whereClause.amount.gte = filters.amountMin;
      if (filters.amountMax) whereClause.amount.lte = filters.amountMax;
    }

    // Status filter
    if (filters.salesStatus) {
      whereClause.status = filters.salesStatus;
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { customer: { name: { contains: filters.search, mode: 'insensitive' } } },
        { executive: { name: { contains: filters.search, mode: 'insensitive' } } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.sales_orders.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [filters.sortBy || 'orderDate']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.sales_orders.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }
  
  /**
   * Get Customer Reports
   */
  async getCustomerReports(filters: CustomerReportFilter) {
    const whereClause: Prisma.CustomerWhereInput = {};

    // Active filter
    if (filters.isActive !== undefined) {
      whereClause.isActive = filters.isActive;
    }

    // City filter
    if (filters.city) {
      whereClause.city = { contains: filters.city, mode: 'insensitive' };
    }

    // State filter
    if (filters.state) {
      whereClause.state = { contains: filters.state, mode: 'insensitive' };
    }

    // Search filter
    if (filters.search) {
      whereClause.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
        { phone: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      prisma.customer.findMany({
        where: whereClause,
        orderBy: {
          [filters.sortBy || 'name']: filters.sortOrder,
        },
        skip: (filters.page - 1) * filters.limit,
        take: filters.limit,
      }),
      prisma.customer.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      },
    };
  }

  /**
   * Get Report Statistics
   */
  async getReportStatistics(reportType: string, period: string, startDate?: Date, endDate?: Date) {
    const dateFilter = this.buildDateFilter(period, startDate, endDate);

    switch (reportType.toUpperCase()) {
      case 'AMC':
        return this.getAMCStatistics(dateFilter);
      case 'WARRANTY':
        return this.getWarrantyStatistics(dateFilter);
      case 'SERVICE':
        return this.getServiceStatistics(dateFilter);
      case 'SALES':
        return this.getSalesStatistics(dateFilter);
      case 'CUSTOMER':
        return this.getCustomerStatistics(dateFilter);
      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }
  }

  /**
   * Build date filter based on period
   */
  private buildDateFilter(period: string, startDate?: Date, endDate?: Date) {
    if (period === 'CUSTOM' && startDate && endDate) {
      return { gte: startDate, lte: endDate };
    }

    const now = new Date();
    const filter: { gte: Date; lte?: Date } = { gte: now };

    switch (period) {
      case 'TODAY':
        filter.gte = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'WEEK':
        filter.gte = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'MONTH':
        filter.gte = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'QUARTER':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        filter.gte = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'YEAR':
        filter.gte = new Date(now.getFullYear(), 0, 1);
        break;
    }

    return filter;
  }

  /**
   * Get AMC Statistics
   */
  private async getAMCStatistics(dateFilter: any) {
    const [total, active, expired, revenue] = await Promise.all([
      prisma.amc_contracts.count(),
      prisma.amc_contracts.count({
        where: {
          endDate: { gte: new Date() },
        },
      }),
      prisma.amc_contracts.count({
        where: {
          endDate: { lt: new Date() },
        },
      }),
      prisma.amc_contracts.aggregate({
        _sum: { amount: true },
        where: {
          startDate: dateFilter,
        },
      }),
    ]);

    return {
      total,
      active,
      expired,
      revenue: revenue._sum.amount || 0,
      period: dateFilter,
    };
  }

  /**
   * Get Warranty Statistics
   */
  private async getWarrantyStatistics(dateFilter: any) {
    const [total, active, expired] = await Promise.all([
      prisma.warranties.count(),
      prisma.warranties.count({
        where: {
          warrantyDate: { gte: new Date() },
        },
      }),
      prisma.warranties.count({
        where: {
          warrantyDate: { lt: new Date() },
        },
      }),
    ]);

    return {
      total,
      active,
      expired,
      period: dateFilter,
    };
  }

  /**
   * Get Service Statistics
   */
  private async getServiceStatistics(dateFilter: any) {
    const [total, completed, pending] = await Promise.all([
      prisma.service_reports.count(),
      prisma.service_reports.count({
        where: {
          status: 'COMPLETED',
        },
      }),
      prisma.service_reports.count({
        where: {
          status: { in: ['OPEN', 'IN_PROGRESS', 'PENDING'] },
        },
      }),
    ]);

    return {
      total,
      completed,
      pending,
      period: dateFilter,
    };
  }

  /**
   * Get Sales Statistics
   */
  private async getSalesStatistics(dateFilter: any) {
    const [total, revenue, leads, opportunities] = await Promise.all([
      prisma.sales_orders.count(),
      prisma.sales_orders.aggregate({
        _sum: { amount: true },
        where: {
          orderDate: dateFilter,
        },
      }),
      prisma.sales_leads.count({
        where: {
          leadDate: dateFilter,
        },
      }),
      prisma.sales_opportunities.count({
        where: {
          opportunityDate: dateFilter,
        },
      }),
    ]);

    return {
      total,
      revenue: revenue._sum.amount || 0,
      leads,
      opportunities,
      period: dateFilter,
    };
  }

  /**
   * Get Customer Statistics
   */
  private async getCustomerStatistics(dateFilter: any) {
    const [total, active, inactive] = await Promise.all([
      prisma.customer.count(),
      prisma.customer.count({
        where: { isActive: true },
      }),
      prisma.customer.count({
        where: { isActive: false },
      }),
    ]);

    return {
      total,
      active,
      inactive,
      period: dateFilter,
    };
  }
}
