import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as ExcelJS from 'exceljs';
import { format } from 'date-fns';

export interface CrystalReportExportParams {
  reportName: string;
  reportTitle: string;
  data: any[];
  summary?: Record<string, any>;
  parameters?: Record<string, any>;
  includeCharts?: boolean;
  includeSummary?: boolean;
}

/**
 * Generate Crystal Report PDF
 */
export async function generateCrystalReportPDF(params: CrystalReportExportParams): Promise<Buffer> {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
  });

  let yPos = 20;

  // Add KoolSoft header
  doc.setFillColor(15, 82, 186); // Primary blue
  doc.rect(0, 0, 210, 25, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('KoolSoft', 20, 15);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Professional Service Management', 20, 20);

  yPos = 35;

  // Report title
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text(params.reportTitle, 20, yPos);
  yPos += 10;

  // Generation info
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Generated on: ${format(new Date(), 'PPpp')}`, 20, yPos);
  doc.text(`Records: ${params.data.length}`, 120, yPos);
  yPos += 10;

  // Parameters section
  if (params.parameters && Object.keys(params.parameters).length > 0) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Report Parameters:', 20, yPos);
    yPos += 5;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    Object.entries(params.parameters).forEach(([key, value]) => {
      if (value) {
        doc.text(`${key}: ${value}`, 25, yPos);
        yPos += 4;
      }
    });
    yPos += 5;
  }

  // Summary section
  if (params.includeSummary && params.summary) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Summary:', 20, yPos);
    yPos += 8;

    const summaryData = Object.entries(params.summary)
      .filter(([key, value]) => typeof value === 'number' || typeof value === 'string')
      .map(([key, value]) => [
        key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
        formatValue(value, key),
      ]);

    if (summaryData.length > 0) {
      autoTable(doc, {
        startY: yPos,
        head: [['Metric', 'Value']],
        body: summaryData,
        theme: 'grid',
        headStyles: {
          fillColor: [15, 82, 186],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        styles: {
          fontSize: 9,
          cellPadding: 2,
        },
        columnStyles: {
          0: { cellWidth: 80 },
          1: { cellWidth: 60, halign: 'right' },
        },
      });
      yPos = (doc as any).lastAutoTable.finalY + 10;
    }
  }

  // Data table
  if (params.data && params.data.length > 0) {
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Report Data:', 20, yPos);
    yPos += 8;

    const tableData = prepareTableDataForPDF(params.data, params.reportName);
    
    autoTable(doc, {
      startY: yPos,
      head: [tableData.headers],
      body: tableData.rows,
      theme: 'striped',
      headStyles: {
        fillColor: [15, 82, 186],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      columnStyles: getColumnStylesForReport(params.reportName),
    });
  }

  // Footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(128, 128, 128);
    doc.text(`Page ${i} of ${pageCount}`, 20, 285);
    doc.text('KoolSoft - Professional Service Management', 120, 285);
  }

  return Buffer.from(doc.output('arraybuffer'));
}

/**
 * Generate Crystal Report Excel
 */
export async function generateCrystalReportExcel(params: CrystalReportExportParams): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook();
  
  // Set workbook properties
  workbook.creator = 'KoolSoft';
  workbook.lastModifiedBy = 'KoolSoft';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Main data sheet
  const dataSheet = workbook.addWorksheet('Report Data');
  
  // Add header
  dataSheet.mergeCells('A1:H1');
  const headerCell = dataSheet.getCell('A1');
  headerCell.value = 'KoolSoft - ' + params.reportTitle;
  headerCell.font = { size: 16, bold: true, color: { argb: 'FFFFFFFF' } };
  headerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0F52BA' } };
  headerCell.alignment = { horizontal: 'center', vertical: 'middle' };
  dataSheet.getRow(1).height = 25;

  // Add generation info
  dataSheet.getCell('A3').value = 'Generated on:';
  dataSheet.getCell('B3').value = format(new Date(), 'PPpp');
  dataSheet.getCell('D3').value = 'Records:';
  dataSheet.getCell('E3').value = params.data.length;

  let currentRow = 5;

  // Add parameters
  if (params.parameters && Object.keys(params.parameters).length > 0) {
    dataSheet.getCell(`A${currentRow}`).value = 'Parameters:';
    dataSheet.getCell(`A${currentRow}`).font = { bold: true };
    currentRow++;

    Object.entries(params.parameters).forEach(([key, value]) => {
      if (value) {
        dataSheet.getCell(`A${currentRow}`).value = key + ':';
        dataSheet.getCell(`B${currentRow}`).value = value;
        currentRow++;
      }
    });
    currentRow++;
  }

  // Add summary
  if (params.includeSummary && params.summary) {
    dataSheet.getCell(`A${currentRow}`).value = 'Summary:';
    dataSheet.getCell(`A${currentRow}`).font = { bold: true };
    currentRow++;

    Object.entries(params.summary).forEach(([key, value]) => {
      if (typeof value === 'number' || typeof value === 'string') {
        dataSheet.getCell(`A${currentRow}`).value = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        dataSheet.getCell(`B${currentRow}`).value = value;
        currentRow++;
      }
    });
    currentRow += 2;
  }

  // Add data table
  if (params.data && params.data.length > 0) {
    const tableData = prepareTableDataForExcel(params.data, params.reportName);
    
    // Add headers
    tableData.headers.forEach((header: string, index: number) => {
      const cell = dataSheet.getCell(currentRow, index + 1);
      cell.value = header;
      cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF0F52BA' } };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
    });
    currentRow++;

    // Add data rows
    tableData.rows.forEach(row => {
      row.forEach((cellValue: any, index: number) => {
        const cell = dataSheet.getCell(currentRow, index + 1);
        cell.value = cellValue;

        // Apply formatting based on data type
        if (typeof cellValue === 'number') {
          if (tableData.headers[index].toLowerCase().includes('amount')) {
            cell.numFmt = '₹#,##0.00';
          } else {
            cell.numFmt = '#,##0';
          }
        } else if (cellValue instanceof Date) {
          cell.numFmt = 'dd-mmm-yyyy';
        }
      });
      currentRow++;
    });

    // Auto-fit columns
    dataSheet.columns.forEach(column => {
      column.width = 15;
    });
  }

  // Generate buffer
  const buffer = await workbook.xlsx.writeBuffer();
  return Buffer.from(buffer);
}

/**
 * Prepare table data for PDF export
 */
function prepareTableDataForPDF(data: any[], reportName: string) {
  const config = getTableConfigForReport(reportName);
  
  const headers = config.columns.map((col: TableColumn) => col.label);
  const rows = data.map(item =>
    config.columns.map((col: TableColumn) => formatValue(item[col.key], col.key, col.type))
  );

  return { headers, rows };
}

/**
 * Prepare table data for Excel export
 */
function prepareTableDataForExcel(data: any[], reportName: string) {
  const config = getTableConfigForReport(reportName);
  
  const headers = config.columns.map((col: TableColumn) => col.label);
  const rows = data.map(item =>
    config.columns.map((col: TableColumn) => {
      const value = item[col.key];
      if (col.type === 'date' && value) {
        return new Date(value);
      }
      return value;
    })
  );

  return { headers, rows };
}

/**
 * Get table configuration for specific report
 */
interface TableColumn {
  key: string;
  label: string;
  type: string;
}

interface TableConfig {
  columns: TableColumn[];
}

function getTableConfigForReport(reportName: string): TableConfig {
  const configs: Record<string, TableConfig> = {
    'amc-summary': {
      columns: [
        { key: 'contractNo', label: 'Contract No.', type: 'text' },
        { key: 'customerName', label: 'Customer', type: 'text' },
        { key: 'executiveName', label: 'Executive', type: 'text' },
        { key: 'startDate', label: 'Start Date', type: 'date' },
        { key: 'endDate', label: 'End Date', type: 'date' },
        { key: 'amount', label: 'Amount', type: 'currency' },
        { key: 'status', label: 'Status', type: 'text' },
      ],
    },
    'warranty-summary': {
      columns: [
        { key: 'warrantyNo', label: 'Warranty No.', type: 'text' },
        { key: 'customerName', label: 'Customer', type: 'text' },
        { key: 'productName', label: 'Product', type: 'text' },
        { key: 'installDate', label: 'Install Date', type: 'date' },
        { key: 'warrantyDate', label: 'Warranty Date', type: 'date' },
        { key: 'status', label: 'Status', type: 'text' },
      ],
    },
    'service-summary': {
      columns: [
        { key: 'reportNo', label: 'Report No.', type: 'text' },
        { key: 'customerName', label: 'Customer', type: 'text' },
        { key: 'executiveName', label: 'Executive', type: 'text' },
        { key: 'reportDate', label: 'Report Date', type: 'date' },
        { key: 'visitDate', label: 'Visit Date', type: 'date' },
        { key: 'status', label: 'Status', type: 'text' },
      ],
    },
  };

  return configs[reportName] || configs['amc-summary'];
}

/**
 * Get column styles for PDF
 */
function getColumnStylesForReport(reportName: string) {
  return {
    0: { cellWidth: 25 },
    1: { cellWidth: 40 },
    2: { cellWidth: 30 },
    3: { cellWidth: 25, halign: 'center' as const },
    4: { cellWidth: 25, halign: 'center' as const },
    5: { cellWidth: 25, halign: 'right' as const },
    6: { cellWidth: 20, halign: 'center' as const },
  };
}

/**
 * Format value for display
 */
function formatValue(value: any, key: string, type?: string): string {
  if (value === null || value === undefined) return '';

  if (key.toLowerCase().includes('amount') || type === 'currency') {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(Number(value) || 0);
  }

  if (type === 'date' && value) {
    return format(new Date(value), 'dd-MMM-yyyy');
  }

  if (typeof value === 'number') {
    return value.toLocaleString('en-IN');
  }

  return String(value);
}
