"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/inherits";
exports.ids = ["vendor-chunks/inherits"];
exports.modules = {

/***/ "(rsc)/./node_modules/inherits/inherits.js":
/*!*******************************************!*\
  !*** ./node_modules/inherits/inherits.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\ntry {\n  var util = __webpack_require__(/*! util */ \"util\");\n  /* istanbul ignore next */\n  if (typeof util.inherits !== 'function') throw '';\n  module.exports = util.inherits;\n} catch (e) {\n  /* istanbul ignore next */\n  module.exports = __webpack_require__(/*! ./inherits_browser.js */ \"(rsc)/./node_modules/inherits/inherits_browser.js\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaW5oZXJpdHMvaW5oZXJpdHMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQSxJQUFJO0VBQ0YsSUFBSUEsSUFBSSxHQUFHQyxtQkFBTyxDQUFDLGtCQUFNLENBQUM7RUFDMUI7RUFDQSxJQUFJLE9BQU9ELElBQUksQ0FBQ0UsUUFBUSxLQUFLLFVBQVUsRUFBRSxNQUFNLEVBQUU7RUFDakRDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHSixJQUFJLENBQUNFLFFBQVE7QUFDaEMsQ0FBQyxDQUFDLE9BQU9HLENBQUMsRUFBRTtFQUNWO0VBQ0FGLHNIQUFpRDtBQUNuRCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXGluaGVyaXRzXFxpbmhlcml0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ0cnkge1xuICB2YXIgdXRpbCA9IHJlcXVpcmUoJ3V0aWwnKTtcbiAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgaWYgKHR5cGVvZiB1dGlsLmluaGVyaXRzICE9PSAnZnVuY3Rpb24nKSB0aHJvdyAnJztcbiAgbW9kdWxlLmV4cG9ydHMgPSB1dGlsLmluaGVyaXRzO1xufSBjYXRjaCAoZSkge1xuICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vaW5oZXJpdHNfYnJvd3Nlci5qcycpO1xufVxuIl0sIm5hbWVzIjpbInV0aWwiLCJyZXF1aXJlIiwiaW5oZXJpdHMiLCJtb2R1bGUiLCJleHBvcnRzIiwiZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/inherits/inherits.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/inherits/inherits_browser.js":
/*!***************************************************!*\
  !*** ./node_modules/inherits/inherits_browser.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nif (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      var TempCtor = function () {};\n      TempCtor.prototype = superCtor.prototype;\n      ctor.prototype = new TempCtor();\n      ctor.prototype.constructor = ctor;\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/inherits/inherits_browser.js\n");

/***/ })

};
;