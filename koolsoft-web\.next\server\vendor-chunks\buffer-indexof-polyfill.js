"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-indexof-polyfill";
exports.ids = ["vendor-chunks/buffer-indexof-polyfill"];
exports.modules = {

/***/ "(rsc)/./node_modules/buffer-indexof-polyfill/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar initBuffer = __webpack_require__(/*! ./init-buffer */ \"(rsc)/./node_modules/buffer-indexof-polyfill/init-buffer.js\");\nif (!Buffer.prototype.indexOf) {\n  Buffer.prototype.indexOf = function (value, offset) {\n    offset = offset || 0;\n\n    // Always wrap the input as a Buffer so that this method will support any\n    // data type such as array octet, string or buffer.\n    if (typeof value === \"string\" || value instanceof String) {\n      value = initBuffer(value);\n    } else if (typeof value === \"number\" || value instanceof Number) {\n      value = initBuffer([value]);\n    }\n    var len = value.length;\n    for (var i = offset; i <= this.length - len; i++) {\n      var mismatch = false;\n      for (var j = 0; j < len; j++) {\n        if (this[i + j] != value[j]) {\n          mismatch = true;\n          break;\n        }\n      }\n      if (!mismatch) {\n        return i;\n      }\n    }\n    return -1;\n  };\n}\nfunction bufferLastIndexOf(value, offset) {\n  // Always wrap the input as a Buffer so that this method will support any\n  // data type such as array octet, string or buffer.\n  if (typeof value === \"string\" || value instanceof String) {\n    value = initBuffer(value);\n  } else if (typeof value === \"number\" || value instanceof Number) {\n    value = initBuffer([value]);\n  }\n  var len = value.length;\n  offset = offset || this.length - len;\n  for (var i = offset; i >= 0; i--) {\n    var mismatch = false;\n    for (var j = 0; j < len; j++) {\n      if (this[i + j] != value[j]) {\n        mismatch = true;\n        break;\n      }\n    }\n    if (!mismatch) {\n      return i;\n    }\n  }\n  return -1;\n}\nif (Buffer.prototype.lastIndexOf) {\n  // check Buffer#lastIndexOf is usable: https://github.com/nodejs/node/issues/4604\n  if (initBuffer(\"ABC\").lastIndexOf(\"ABC\") === -1) Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n} else {\n  Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/buffer-indexof-polyfill/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/buffer-indexof-polyfill/init-buffer.js":
/*!*************************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/init-buffer.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function initBuffer(val) {\n  // assume old version\n  var nodeVersion = process && process.version ? process.version : \"v5.0.0\";\n  var major = nodeVersion.split(\".\")[0].replace(\"v\", \"\");\n  return major < 6 ? new Buffer(val) : Buffer.from(val);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWluZGV4b2YtcG9seWZpbGwvaW5pdC1idWZmZXIuanMiLCJtYXBwaW5ncyI6Ijs7QUFBQUEsTUFBTSxDQUFDQyxPQUFPLEdBQUcsU0FBU0MsVUFBVUEsQ0FBQ0MsR0FBRyxFQUFFO0VBQ3hDO0VBQ0UsSUFBSUMsV0FBVyxHQUFHQyxPQUFPLElBQUlBLE9BQU8sQ0FBQ0MsT0FBTyxHQUFHRCxPQUFPLENBQUNDLE9BQU8sR0FBRyxRQUFRO0VBQ3pFLElBQUlDLEtBQUssR0FBR0gsV0FBVyxDQUFDSSxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUNDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxDQUFDO0VBQ3RELE9BQU9GLEtBQUssR0FBRyxDQUFDLEdBQ1osSUFBSUcsTUFBTSxDQUFDUCxHQUFHLENBQUMsR0FDZk8sTUFBTSxDQUFDQyxJQUFJLENBQUNSLEdBQUcsQ0FBQztBQUN4QixDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcYnVmZmVyLWluZGV4b2YtcG9seWZpbGxcXGluaXQtYnVmZmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gaW5pdEJ1ZmZlcih2YWwpIHtcbiAgLy8gYXNzdW1lIG9sZCB2ZXJzaW9uXG4gICAgdmFyIG5vZGVWZXJzaW9uID0gcHJvY2VzcyAmJiBwcm9jZXNzLnZlcnNpb24gPyBwcm9jZXNzLnZlcnNpb24gOiBcInY1LjAuMFwiO1xuICAgIHZhciBtYWpvciA9IG5vZGVWZXJzaW9uLnNwbGl0KFwiLlwiKVswXS5yZXBsYWNlKFwidlwiLCBcIlwiKTtcbiAgICByZXR1cm4gbWFqb3IgPCA2XG4gICAgICA/IG5ldyBCdWZmZXIodmFsKVxuICAgICAgOiBCdWZmZXIuZnJvbSh2YWwpO1xufTsiXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsImluaXRCdWZmZXIiLCJ2YWwiLCJub2RlVmVyc2lvbiIsInByb2Nlc3MiLCJ2ZXJzaW9uIiwibWFqb3IiLCJzcGxpdCIsInJlcGxhY2UiLCJCdWZmZXIiLCJmcm9tIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/buffer-indexof-polyfill/init-buffer.js\n");

/***/ })

};
;