'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { BaseLayout } from './base-layout';
import { PageHeader } from './page-header';
import {
  Users,
  Activity,
  Mail,
  ChevronLeft,
  ChevronRight,
  Settings,
  LayoutDashboard,
  FileText,
  Database,
  Shield,
  BarChart4,
  Wrench,
  Cog
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

// Interface for navigation items
interface NavItem {
  name: string;
  href: string;
  icon?: React.ReactNode;
  exact?: boolean;
  children?: {
    name: string;
    href: string;
  }[];
}

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
  actions?: React.ReactNode;
}

/**
 * AdminLayout Component
 *
 * A specialized layout for admin pages with a collapsible sidebar.
 */
export function AdminLayout({
  children,
  title,
  actions,
}: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();

  // Navigation items for the sidebar
  const navItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: <LayoutDashboard className="h-5 w-5" />,
      exact: true,
    },
    {
      name: 'User Management',
      href: '/admin/users',
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: 'Activity Logs',
      href: '/admin/activity-logs',
      icon: <Activity className="h-5 w-5" />,
    },
    {
      name: 'Email Management',
      href: '/admin/email',
      icon: <Mail className="h-5 w-5" />,
      children: [
        {
          name: 'Templates',
          href: '/admin/email/templates',
        },
        {
          name: 'Preview',
          href: '/admin/email/preview',
        },
        {
          name: 'Create Template',
          href: '/admin/email/templates/create',
        },
      ],
    },
    {
      name: 'System Settings',
      href: '/admin/settings',
      icon: <Cog className="h-5 w-5" />,
    },
    {
      name: 'Reference Data',
      href: '/reference-data',
      icon: <Database className="h-5 w-5" />,
      children: [
        {
          name: 'Territories',
          href: '/reference-data/territories',
        },
        {
          name: 'Segments',
          href: '/reference-data/segments',
        },
        {
          name: 'Complaint Types',
          href: '/reference-data/complaintType',
        },
        {
          name: 'Service Visit Types',
          href: '/reference-data/serviceVisitType',
        },
      ],
    },
    {
      name: 'Visit Cards',
      href: '/visit-cards',
      icon: <FileText className="h-5 w-5" />,
    },
  ];

  // Check if a nav item is active
  const isActive = (href: string, exact = false) => {
    if (exact) {
      return pathname === href;
    }
    return pathname?.startsWith(href) || false;
  };

  return (
    <BaseLayout requireAuth={true} allowedRoles={['ADMIN']}>
      <div className="flex h-screen bg-gray-100">
        {/* Sidebar */}
        <div
          className={cn(
            "bg-white h-full shadow-md transition-all duration-300 flex flex-col",
            sidebarCollapsed ? "w-16" : "w-64"
          )}
        >
          {/* Sidebar Header */}
          <div className="p-4 flex items-center justify-between border-b">
            {!sidebarCollapsed && (
              <h2 className="text-lg font-semibold text-gray-800">Admin</h2>
            )}
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              aria-label={sidebarCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
              className="border border-gray-200 bg-white shadow-sm"
            >
              {sidebarCollapsed ? (
                <ChevronRight className="h-5 w-5" />
              ) : (
                <ChevronLeft className="h-5 w-5" />
              )}
            </Button>
          </div>

          {/* Sidebar Navigation */}
          <nav className="flex-1 py-4">
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.href}>
                  {item.children ? (
                    <div>
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center px-4 py-2 text-sm font-medium",
                          pathname?.startsWith(item.href)
                            ? "bg-blue-50 text-primary"
                            : "text-gray-700 hover:bg-gray-100",
                          sidebarCollapsed && "justify-center"
                        )}
                      >
                        <span className={sidebarCollapsed ? "" : "mr-3"}>
                          {item.icon}
                        </span>
                        {!sidebarCollapsed && <span>{item.name}</span>}
                      </Link>
                      {!sidebarCollapsed && pathname?.startsWith(item.href) && (
                        <ul className="mt-1 pl-8 space-y-1">
                          {item.children.map((child) => (
                            <li key={child.href}>
                              <Link
                                href={child.href}
                                className={cn(
                                  "block px-4 py-2 text-sm font-medium",
                                  pathname === child.href
                                    ? "text-primary"
                                    : "text-gray-600 hover:text-primary"
                                )}
                              >
                                {child.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center px-4 py-2 text-sm font-medium",
                        isActive(item.href, item.exact)
                          ? "bg-blue-50 text-primary"
                          : "text-gray-700 hover:bg-gray-100",
                        sidebarCollapsed && "justify-center"
                      )}
                    >
                      <span className={sidebarCollapsed ? "" : "mr-3"}>
                        {item.icon}
                      </span>
                      {!sidebarCollapsed && <span>{item.name}</span>}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>

          {/* Sidebar Footer */}
          <div className="p-4 border-t">
            <Link
              href="/dashboard"
              className={cn(
                "flex items-center text-sm font-medium text-gray-700 hover:text-primary",
                sidebarCollapsed && "justify-center"
              )}
            >
              <LayoutDashboard className="h-5 w-5" />
              {!sidebarCollapsed && <span className="ml-3">Back to Dashboard</span>}
            </Link>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <PageHeader
            title={title}
            actions={actions}
            showDashboardLink={true}
            showAdminLink={false}
          />

          <main className="flex-1 overflow-y-auto p-6 bg-gray-50">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </BaseLayout>
  );
}
