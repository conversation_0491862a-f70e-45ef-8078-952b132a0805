"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/big-integer";
exports.ids = ["vendor-chunks/big-integer"];
exports.modules = {

/***/ "(rsc)/./node_modules/big-integer/BigInteger.js":
/*!************************************************!*\
  !*** ./node_modules/big-integer/BigInteger.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\nvar __WEBPACK_AMD_DEFINE_RESULT__;\n\nvar bigInt = function (undefined) {\n  \"use strict\";\n\n  var BASE = 1e7,\n    LOG_BASE = 7,\n    MAX_INT = 9007199254740992,\n    MAX_INT_ARR = smallToArray(MAX_INT),\n    DEFAULT_ALPHABET = \"0123456789abcdefghijklmnopqrstuvwxyz\";\n  var supportsNativeBigInt = typeof BigInt === \"function\";\n  function Integer(v, radix, alphabet, caseSensitive) {\n    if (typeof v === \"undefined\") return Integer[0];\n    if (typeof radix !== \"undefined\") return +radix === 10 && !alphabet ? parseValue(v) : parseBase(v, radix, alphabet, caseSensitive);\n    return parseValue(v);\n  }\n  function BigInteger(value, sign) {\n    this.value = value;\n    this.sign = sign;\n    this.isSmall = false;\n  }\n  BigInteger.prototype = Object.create(Integer.prototype);\n  function SmallInteger(value) {\n    this.value = value;\n    this.sign = value < 0;\n    this.isSmall = true;\n  }\n  SmallInteger.prototype = Object.create(Integer.prototype);\n  function NativeBigInt(value) {\n    this.value = value;\n  }\n  NativeBigInt.prototype = Object.create(Integer.prototype);\n  function isPrecise(n) {\n    return -MAX_INT < n && n < MAX_INT;\n  }\n  function smallToArray(n) {\n    // For performance reasons doesn't reference BASE, need to change this function if BASE changes\n    if (n < 1e7) return [n];\n    if (n < 1e14) return [n % 1e7, Math.floor(n / 1e7)];\n    return [n % 1e7, Math.floor(n / 1e7) % 1e7, Math.floor(n / 1e14)];\n  }\n  function arrayToSmall(arr) {\n    // If BASE changes this function may need to change\n    trim(arr);\n    var length = arr.length;\n    if (length < 4 && compareAbs(arr, MAX_INT_ARR) < 0) {\n      switch (length) {\n        case 0:\n          return 0;\n        case 1:\n          return arr[0];\n        case 2:\n          return arr[0] + arr[1] * BASE;\n        default:\n          return arr[0] + (arr[1] + arr[2] * BASE) * BASE;\n      }\n    }\n    return arr;\n  }\n  function trim(v) {\n    var i = v.length;\n    while (v[--i] === 0);\n    v.length = i + 1;\n  }\n  function createArray(length) {\n    // function shamelessly stolen from Yaffle's library https://github.com/Yaffle/BigInteger\n    var x = new Array(length);\n    var i = -1;\n    while (++i < length) {\n      x[i] = 0;\n    }\n    return x;\n  }\n  function truncate(n) {\n    if (n > 0) return Math.floor(n);\n    return Math.ceil(n);\n  }\n  function add(a, b) {\n    // assumes a and b are arrays with a.length >= b.length\n    var l_a = a.length,\n      l_b = b.length,\n      r = new Array(l_a),\n      carry = 0,\n      base = BASE,\n      sum,\n      i;\n    for (i = 0; i < l_b; i++) {\n      sum = a[i] + b[i] + carry;\n      carry = sum >= base ? 1 : 0;\n      r[i] = sum - carry * base;\n    }\n    while (i < l_a) {\n      sum = a[i] + carry;\n      carry = sum === base ? 1 : 0;\n      r[i++] = sum - carry * base;\n    }\n    if (carry > 0) r.push(carry);\n    return r;\n  }\n  function addAny(a, b) {\n    if (a.length >= b.length) return add(a, b);\n    return add(b, a);\n  }\n  function addSmall(a, carry) {\n    // assumes a is array, carry is number with 0 <= carry < MAX_INT\n    var l = a.length,\n      r = new Array(l),\n      base = BASE,\n      sum,\n      i;\n    for (i = 0; i < l; i++) {\n      sum = a[i] - base + carry;\n      carry = Math.floor(sum / base);\n      r[i] = sum - carry * base;\n      carry += 1;\n    }\n    while (carry > 0) {\n      r[i++] = carry % base;\n      carry = Math.floor(carry / base);\n    }\n    return r;\n  }\n  BigInteger.prototype.add = function (v) {\n    var n = parseValue(v);\n    if (this.sign !== n.sign) {\n      return this.subtract(n.negate());\n    }\n    var a = this.value,\n      b = n.value;\n    if (n.isSmall) {\n      return new BigInteger(addSmall(a, Math.abs(b)), this.sign);\n    }\n    return new BigInteger(addAny(a, b), this.sign);\n  };\n  BigInteger.prototype.plus = BigInteger.prototype.add;\n  SmallInteger.prototype.add = function (v) {\n    var n = parseValue(v);\n    var a = this.value;\n    if (a < 0 !== n.sign) {\n      return this.subtract(n.negate());\n    }\n    var b = n.value;\n    if (n.isSmall) {\n      if (isPrecise(a + b)) return new SmallInteger(a + b);\n      b = smallToArray(Math.abs(b));\n    }\n    return new BigInteger(addSmall(b, Math.abs(a)), a < 0);\n  };\n  SmallInteger.prototype.plus = SmallInteger.prototype.add;\n  NativeBigInt.prototype.add = function (v) {\n    return new NativeBigInt(this.value + parseValue(v).value);\n  };\n  NativeBigInt.prototype.plus = NativeBigInt.prototype.add;\n  function subtract(a, b) {\n    // assumes a and b are arrays with a >= b\n    var a_l = a.length,\n      b_l = b.length,\n      r = new Array(a_l),\n      borrow = 0,\n      base = BASE,\n      i,\n      difference;\n    for (i = 0; i < b_l; i++) {\n      difference = a[i] - borrow - b[i];\n      if (difference < 0) {\n        difference += base;\n        borrow = 1;\n      } else borrow = 0;\n      r[i] = difference;\n    }\n    for (i = b_l; i < a_l; i++) {\n      difference = a[i] - borrow;\n      if (difference < 0) difference += base;else {\n        r[i++] = difference;\n        break;\n      }\n      r[i] = difference;\n    }\n    for (; i < a_l; i++) {\n      r[i] = a[i];\n    }\n    trim(r);\n    return r;\n  }\n  function subtractAny(a, b, sign) {\n    var value;\n    if (compareAbs(a, b) >= 0) {\n      value = subtract(a, b);\n    } else {\n      value = subtract(b, a);\n      sign = !sign;\n    }\n    value = arrayToSmall(value);\n    if (typeof value === \"number\") {\n      if (sign) value = -value;\n      return new SmallInteger(value);\n    }\n    return new BigInteger(value, sign);\n  }\n  function subtractSmall(a, b, sign) {\n    // assumes a is array, b is number with 0 <= b < MAX_INT\n    var l = a.length,\n      r = new Array(l),\n      carry = -b,\n      base = BASE,\n      i,\n      difference;\n    for (i = 0; i < l; i++) {\n      difference = a[i] + carry;\n      carry = Math.floor(difference / base);\n      difference %= base;\n      r[i] = difference < 0 ? difference + base : difference;\n    }\n    r = arrayToSmall(r);\n    if (typeof r === \"number\") {\n      if (sign) r = -r;\n      return new SmallInteger(r);\n    }\n    return new BigInteger(r, sign);\n  }\n  BigInteger.prototype.subtract = function (v) {\n    var n = parseValue(v);\n    if (this.sign !== n.sign) {\n      return this.add(n.negate());\n    }\n    var a = this.value,\n      b = n.value;\n    if (n.isSmall) return subtractSmall(a, Math.abs(b), this.sign);\n    return subtractAny(a, b, this.sign);\n  };\n  BigInteger.prototype.minus = BigInteger.prototype.subtract;\n  SmallInteger.prototype.subtract = function (v) {\n    var n = parseValue(v);\n    var a = this.value;\n    if (a < 0 !== n.sign) {\n      return this.add(n.negate());\n    }\n    var b = n.value;\n    if (n.isSmall) {\n      return new SmallInteger(a - b);\n    }\n    return subtractSmall(b, Math.abs(a), a >= 0);\n  };\n  SmallInteger.prototype.minus = SmallInteger.prototype.subtract;\n  NativeBigInt.prototype.subtract = function (v) {\n    return new NativeBigInt(this.value - parseValue(v).value);\n  };\n  NativeBigInt.prototype.minus = NativeBigInt.prototype.subtract;\n  BigInteger.prototype.negate = function () {\n    return new BigInteger(this.value, !this.sign);\n  };\n  SmallInteger.prototype.negate = function () {\n    var sign = this.sign;\n    var small = new SmallInteger(-this.value);\n    small.sign = !sign;\n    return small;\n  };\n  NativeBigInt.prototype.negate = function () {\n    return new NativeBigInt(-this.value);\n  };\n  BigInteger.prototype.abs = function () {\n    return new BigInteger(this.value, false);\n  };\n  SmallInteger.prototype.abs = function () {\n    return new SmallInteger(Math.abs(this.value));\n  };\n  NativeBigInt.prototype.abs = function () {\n    return new NativeBigInt(this.value >= 0 ? this.value : -this.value);\n  };\n  function multiplyLong(a, b) {\n    var a_l = a.length,\n      b_l = b.length,\n      l = a_l + b_l,\n      r = createArray(l),\n      base = BASE,\n      product,\n      carry,\n      i,\n      a_i,\n      b_j;\n    for (i = 0; i < a_l; ++i) {\n      a_i = a[i];\n      for (var j = 0; j < b_l; ++j) {\n        b_j = b[j];\n        product = a_i * b_j + r[i + j];\n        carry = Math.floor(product / base);\n        r[i + j] = product - carry * base;\n        r[i + j + 1] += carry;\n      }\n    }\n    trim(r);\n    return r;\n  }\n  function multiplySmall(a, b) {\n    // assumes a is array, b is number with |b| < BASE\n    var l = a.length,\n      r = new Array(l),\n      base = BASE,\n      carry = 0,\n      product,\n      i;\n    for (i = 0; i < l; i++) {\n      product = a[i] * b + carry;\n      carry = Math.floor(product / base);\n      r[i] = product - carry * base;\n    }\n    while (carry > 0) {\n      r[i++] = carry % base;\n      carry = Math.floor(carry / base);\n    }\n    return r;\n  }\n  function shiftLeft(x, n) {\n    var r = [];\n    while (n-- > 0) r.push(0);\n    return r.concat(x);\n  }\n  function multiplyKaratsuba(x, y) {\n    var n = Math.max(x.length, y.length);\n    if (n <= 30) return multiplyLong(x, y);\n    n = Math.ceil(n / 2);\n    var b = x.slice(n),\n      a = x.slice(0, n),\n      d = y.slice(n),\n      c = y.slice(0, n);\n    var ac = multiplyKaratsuba(a, c),\n      bd = multiplyKaratsuba(b, d),\n      abcd = multiplyKaratsuba(addAny(a, b), addAny(c, d));\n    var product = addAny(addAny(ac, shiftLeft(subtract(subtract(abcd, ac), bd), n)), shiftLeft(bd, 2 * n));\n    trim(product);\n    return product;\n  }\n\n  // The following function is derived from a surface fit of a graph plotting the performance difference\n  // between long multiplication and karatsuba multiplication versus the lengths of the two arrays.\n  function useKaratsuba(l1, l2) {\n    return -0.012 * l1 - 0.012 * l2 + 0.000015 * l1 * l2 > 0;\n  }\n  BigInteger.prototype.multiply = function (v) {\n    var n = parseValue(v),\n      a = this.value,\n      b = n.value,\n      sign = this.sign !== n.sign,\n      abs;\n    if (n.isSmall) {\n      if (b === 0) return Integer[0];\n      if (b === 1) return this;\n      if (b === -1) return this.negate();\n      abs = Math.abs(b);\n      if (abs < BASE) {\n        return new BigInteger(multiplySmall(a, abs), sign);\n      }\n      b = smallToArray(abs);\n    }\n    if (useKaratsuba(a.length, b.length))\n      // Karatsuba is only faster for certain array sizes\n      return new BigInteger(multiplyKaratsuba(a, b), sign);\n    return new BigInteger(multiplyLong(a, b), sign);\n  };\n  BigInteger.prototype.times = BigInteger.prototype.multiply;\n  function multiplySmallAndArray(a, b, sign) {\n    // a >= 0\n    if (a < BASE) {\n      return new BigInteger(multiplySmall(b, a), sign);\n    }\n    return new BigInteger(multiplyLong(b, smallToArray(a)), sign);\n  }\n  SmallInteger.prototype._multiplyBySmall = function (a) {\n    if (isPrecise(a.value * this.value)) {\n      return new SmallInteger(a.value * this.value);\n    }\n    return multiplySmallAndArray(Math.abs(a.value), smallToArray(Math.abs(this.value)), this.sign !== a.sign);\n  };\n  BigInteger.prototype._multiplyBySmall = function (a) {\n    if (a.value === 0) return Integer[0];\n    if (a.value === 1) return this;\n    if (a.value === -1) return this.negate();\n    return multiplySmallAndArray(Math.abs(a.value), this.value, this.sign !== a.sign);\n  };\n  SmallInteger.prototype.multiply = function (v) {\n    return parseValue(v)._multiplyBySmall(this);\n  };\n  SmallInteger.prototype.times = SmallInteger.prototype.multiply;\n  NativeBigInt.prototype.multiply = function (v) {\n    return new NativeBigInt(this.value * parseValue(v).value);\n  };\n  NativeBigInt.prototype.times = NativeBigInt.prototype.multiply;\n  function square(a) {\n    //console.assert(2 * BASE * BASE < MAX_INT);\n    var l = a.length,\n      r = createArray(l + l),\n      base = BASE,\n      product,\n      carry,\n      i,\n      a_i,\n      a_j;\n    for (i = 0; i < l; i++) {\n      a_i = a[i];\n      carry = 0 - a_i * a_i;\n      for (var j = i; j < l; j++) {\n        a_j = a[j];\n        product = 2 * (a_i * a_j) + r[i + j] + carry;\n        carry = Math.floor(product / base);\n        r[i + j] = product - carry * base;\n      }\n      r[i + l] = carry;\n    }\n    trim(r);\n    return r;\n  }\n  BigInteger.prototype.square = function () {\n    return new BigInteger(square(this.value), false);\n  };\n  SmallInteger.prototype.square = function () {\n    var value = this.value * this.value;\n    if (isPrecise(value)) return new SmallInteger(value);\n    return new BigInteger(square(smallToArray(Math.abs(this.value))), false);\n  };\n  NativeBigInt.prototype.square = function (v) {\n    return new NativeBigInt(this.value * this.value);\n  };\n  function divMod1(a, b) {\n    // Left over from previous version. Performs faster than divMod2 on smaller input sizes.\n    var a_l = a.length,\n      b_l = b.length,\n      base = BASE,\n      result = createArray(b.length),\n      divisorMostSignificantDigit = b[b_l - 1],\n      // normalization\n      lambda = Math.ceil(base / (2 * divisorMostSignificantDigit)),\n      remainder = multiplySmall(a, lambda),\n      divisor = multiplySmall(b, lambda),\n      quotientDigit,\n      shift,\n      carry,\n      borrow,\n      i,\n      l,\n      q;\n    if (remainder.length <= a_l) remainder.push(0);\n    divisor.push(0);\n    divisorMostSignificantDigit = divisor[b_l - 1];\n    for (shift = a_l - b_l; shift >= 0; shift--) {\n      quotientDigit = base - 1;\n      if (remainder[shift + b_l] !== divisorMostSignificantDigit) {\n        quotientDigit = Math.floor((remainder[shift + b_l] * base + remainder[shift + b_l - 1]) / divisorMostSignificantDigit);\n      }\n      // quotientDigit <= base - 1\n      carry = 0;\n      borrow = 0;\n      l = divisor.length;\n      for (i = 0; i < l; i++) {\n        carry += quotientDigit * divisor[i];\n        q = Math.floor(carry / base);\n        borrow += remainder[shift + i] - (carry - q * base);\n        carry = q;\n        if (borrow < 0) {\n          remainder[shift + i] = borrow + base;\n          borrow = -1;\n        } else {\n          remainder[shift + i] = borrow;\n          borrow = 0;\n        }\n      }\n      while (borrow !== 0) {\n        quotientDigit -= 1;\n        carry = 0;\n        for (i = 0; i < l; i++) {\n          carry += remainder[shift + i] - base + divisor[i];\n          if (carry < 0) {\n            remainder[shift + i] = carry + base;\n            carry = 0;\n          } else {\n            remainder[shift + i] = carry;\n            carry = 1;\n          }\n        }\n        borrow += carry;\n      }\n      result[shift] = quotientDigit;\n    }\n    // denormalization\n    remainder = divModSmall(remainder, lambda)[0];\n    return [arrayToSmall(result), arrayToSmall(remainder)];\n  }\n  function divMod2(a, b) {\n    // Implementation idea shamelessly stolen from Silent Matt's library http://silentmatt.com/biginteger/\n    // Performs faster than divMod1 on larger input sizes.\n    var a_l = a.length,\n      b_l = b.length,\n      result = [],\n      part = [],\n      base = BASE,\n      guess,\n      xlen,\n      highx,\n      highy,\n      check;\n    while (a_l) {\n      part.unshift(a[--a_l]);\n      trim(part);\n      if (compareAbs(part, b) < 0) {\n        result.push(0);\n        continue;\n      }\n      xlen = part.length;\n      highx = part[xlen - 1] * base + part[xlen - 2];\n      highy = b[b_l - 1] * base + b[b_l - 2];\n      if (xlen > b_l) {\n        highx = (highx + 1) * base;\n      }\n      guess = Math.ceil(highx / highy);\n      do {\n        check = multiplySmall(b, guess);\n        if (compareAbs(check, part) <= 0) break;\n        guess--;\n      } while (guess);\n      result.push(guess);\n      part = subtract(part, check);\n    }\n    result.reverse();\n    return [arrayToSmall(result), arrayToSmall(part)];\n  }\n  function divModSmall(value, lambda) {\n    var length = value.length,\n      quotient = createArray(length),\n      base = BASE,\n      i,\n      q,\n      remainder,\n      divisor;\n    remainder = 0;\n    for (i = length - 1; i >= 0; --i) {\n      divisor = remainder * base + value[i];\n      q = truncate(divisor / lambda);\n      remainder = divisor - q * lambda;\n      quotient[i] = q | 0;\n    }\n    return [quotient, remainder | 0];\n  }\n  function divModAny(self, v) {\n    var value,\n      n = parseValue(v);\n    if (supportsNativeBigInt) {\n      return [new NativeBigInt(self.value / n.value), new NativeBigInt(self.value % n.value)];\n    }\n    var a = self.value,\n      b = n.value;\n    var quotient;\n    if (b === 0) throw new Error(\"Cannot divide by zero\");\n    if (self.isSmall) {\n      if (n.isSmall) {\n        return [new SmallInteger(truncate(a / b)), new SmallInteger(a % b)];\n      }\n      return [Integer[0], self];\n    }\n    if (n.isSmall) {\n      if (b === 1) return [self, Integer[0]];\n      if (b == -1) return [self.negate(), Integer[0]];\n      var abs = Math.abs(b);\n      if (abs < BASE) {\n        value = divModSmall(a, abs);\n        quotient = arrayToSmall(value[0]);\n        var remainder = value[1];\n        if (self.sign) remainder = -remainder;\n        if (typeof quotient === \"number\") {\n          if (self.sign !== n.sign) quotient = -quotient;\n          return [new SmallInteger(quotient), new SmallInteger(remainder)];\n        }\n        return [new BigInteger(quotient, self.sign !== n.sign), new SmallInteger(remainder)];\n      }\n      b = smallToArray(abs);\n    }\n    var comparison = compareAbs(a, b);\n    if (comparison === -1) return [Integer[0], self];\n    if (comparison === 0) return [Integer[self.sign === n.sign ? 1 : -1], Integer[0]];\n\n    // divMod1 is faster on smaller input sizes\n    if (a.length + b.length <= 200) value = divMod1(a, b);else value = divMod2(a, b);\n    quotient = value[0];\n    var qSign = self.sign !== n.sign,\n      mod = value[1],\n      mSign = self.sign;\n    if (typeof quotient === \"number\") {\n      if (qSign) quotient = -quotient;\n      quotient = new SmallInteger(quotient);\n    } else quotient = new BigInteger(quotient, qSign);\n    if (typeof mod === \"number\") {\n      if (mSign) mod = -mod;\n      mod = new SmallInteger(mod);\n    } else mod = new BigInteger(mod, mSign);\n    return [quotient, mod];\n  }\n  BigInteger.prototype.divmod = function (v) {\n    var result = divModAny(this, v);\n    return {\n      quotient: result[0],\n      remainder: result[1]\n    };\n  };\n  NativeBigInt.prototype.divmod = SmallInteger.prototype.divmod = BigInteger.prototype.divmod;\n  BigInteger.prototype.divide = function (v) {\n    return divModAny(this, v)[0];\n  };\n  NativeBigInt.prototype.over = NativeBigInt.prototype.divide = function (v) {\n    return new NativeBigInt(this.value / parseValue(v).value);\n  };\n  SmallInteger.prototype.over = SmallInteger.prototype.divide = BigInteger.prototype.over = BigInteger.prototype.divide;\n  BigInteger.prototype.mod = function (v) {\n    return divModAny(this, v)[1];\n  };\n  NativeBigInt.prototype.mod = NativeBigInt.prototype.remainder = function (v) {\n    return new NativeBigInt(this.value % parseValue(v).value);\n  };\n  SmallInteger.prototype.remainder = SmallInteger.prototype.mod = BigInteger.prototype.remainder = BigInteger.prototype.mod;\n  BigInteger.prototype.pow = function (v) {\n    var n = parseValue(v),\n      a = this.value,\n      b = n.value,\n      value,\n      x,\n      y;\n    if (b === 0) return Integer[1];\n    if (a === 0) return Integer[0];\n    if (a === 1) return Integer[1];\n    if (a === -1) return n.isEven() ? Integer[1] : Integer[-1];\n    if (n.sign) {\n      return Integer[0];\n    }\n    if (!n.isSmall) throw new Error(\"The exponent \" + n.toString() + \" is too large.\");\n    if (this.isSmall) {\n      if (isPrecise(value = Math.pow(a, b))) return new SmallInteger(truncate(value));\n    }\n    x = this;\n    y = Integer[1];\n    while (true) {\n      if (b & 1 === 1) {\n        y = y.times(x);\n        --b;\n      }\n      if (b === 0) break;\n      b /= 2;\n      x = x.square();\n    }\n    return y;\n  };\n  SmallInteger.prototype.pow = BigInteger.prototype.pow;\n  NativeBigInt.prototype.pow = function (v) {\n    var n = parseValue(v);\n    var a = this.value,\n      b = n.value;\n    var _0 = BigInt(0),\n      _1 = BigInt(1),\n      _2 = BigInt(2);\n    if (b === _0) return Integer[1];\n    if (a === _0) return Integer[0];\n    if (a === _1) return Integer[1];\n    if (a === BigInt(-1)) return n.isEven() ? Integer[1] : Integer[-1];\n    if (n.isNegative()) return new NativeBigInt(_0);\n    var x = this;\n    var y = Integer[1];\n    while (true) {\n      if ((b & _1) === _1) {\n        y = y.times(x);\n        --b;\n      }\n      if (b === _0) break;\n      b /= _2;\n      x = x.square();\n    }\n    return y;\n  };\n  BigInteger.prototype.modPow = function (exp, mod) {\n    exp = parseValue(exp);\n    mod = parseValue(mod);\n    if (mod.isZero()) throw new Error(\"Cannot take modPow with modulus 0\");\n    var r = Integer[1],\n      base = this.mod(mod);\n    if (exp.isNegative()) {\n      exp = exp.multiply(Integer[-1]);\n      base = base.modInv(mod);\n    }\n    while (exp.isPositive()) {\n      if (base.isZero()) return Integer[0];\n      if (exp.isOdd()) r = r.multiply(base).mod(mod);\n      exp = exp.divide(2);\n      base = base.square().mod(mod);\n    }\n    return r;\n  };\n  NativeBigInt.prototype.modPow = SmallInteger.prototype.modPow = BigInteger.prototype.modPow;\n  function compareAbs(a, b) {\n    if (a.length !== b.length) {\n      return a.length > b.length ? 1 : -1;\n    }\n    for (var i = a.length - 1; i >= 0; i--) {\n      if (a[i] !== b[i]) return a[i] > b[i] ? 1 : -1;\n    }\n    return 0;\n  }\n  BigInteger.prototype.compareAbs = function (v) {\n    var n = parseValue(v),\n      a = this.value,\n      b = n.value;\n    if (n.isSmall) return 1;\n    return compareAbs(a, b);\n  };\n  SmallInteger.prototype.compareAbs = function (v) {\n    var n = parseValue(v),\n      a = Math.abs(this.value),\n      b = n.value;\n    if (n.isSmall) {\n      b = Math.abs(b);\n      return a === b ? 0 : a > b ? 1 : -1;\n    }\n    return -1;\n  };\n  NativeBigInt.prototype.compareAbs = function (v) {\n    var a = this.value;\n    var b = parseValue(v).value;\n    a = a >= 0 ? a : -a;\n    b = b >= 0 ? b : -b;\n    return a === b ? 0 : a > b ? 1 : -1;\n  };\n  BigInteger.prototype.compare = function (v) {\n    // See discussion about comparison with Infinity:\n    // https://github.com/peterolson/BigInteger.js/issues/61\n    if (v === Infinity) {\n      return -1;\n    }\n    if (v === -Infinity) {\n      return 1;\n    }\n    var n = parseValue(v),\n      a = this.value,\n      b = n.value;\n    if (this.sign !== n.sign) {\n      return n.sign ? 1 : -1;\n    }\n    if (n.isSmall) {\n      return this.sign ? -1 : 1;\n    }\n    return compareAbs(a, b) * (this.sign ? -1 : 1);\n  };\n  BigInteger.prototype.compareTo = BigInteger.prototype.compare;\n  SmallInteger.prototype.compare = function (v) {\n    if (v === Infinity) {\n      return -1;\n    }\n    if (v === -Infinity) {\n      return 1;\n    }\n    var n = parseValue(v),\n      a = this.value,\n      b = n.value;\n    if (n.isSmall) {\n      return a == b ? 0 : a > b ? 1 : -1;\n    }\n    if (a < 0 !== n.sign) {\n      return a < 0 ? -1 : 1;\n    }\n    return a < 0 ? 1 : -1;\n  };\n  SmallInteger.prototype.compareTo = SmallInteger.prototype.compare;\n  NativeBigInt.prototype.compare = function (v) {\n    if (v === Infinity) {\n      return -1;\n    }\n    if (v === -Infinity) {\n      return 1;\n    }\n    var a = this.value;\n    var b = parseValue(v).value;\n    return a === b ? 0 : a > b ? 1 : -1;\n  };\n  NativeBigInt.prototype.compareTo = NativeBigInt.prototype.compare;\n  BigInteger.prototype.equals = function (v) {\n    return this.compare(v) === 0;\n  };\n  NativeBigInt.prototype.eq = NativeBigInt.prototype.equals = SmallInteger.prototype.eq = SmallInteger.prototype.equals = BigInteger.prototype.eq = BigInteger.prototype.equals;\n  BigInteger.prototype.notEquals = function (v) {\n    return this.compare(v) !== 0;\n  };\n  NativeBigInt.prototype.neq = NativeBigInt.prototype.notEquals = SmallInteger.prototype.neq = SmallInteger.prototype.notEquals = BigInteger.prototype.neq = BigInteger.prototype.notEquals;\n  BigInteger.prototype.greater = function (v) {\n    return this.compare(v) > 0;\n  };\n  NativeBigInt.prototype.gt = NativeBigInt.prototype.greater = SmallInteger.prototype.gt = SmallInteger.prototype.greater = BigInteger.prototype.gt = BigInteger.prototype.greater;\n  BigInteger.prototype.lesser = function (v) {\n    return this.compare(v) < 0;\n  };\n  NativeBigInt.prototype.lt = NativeBigInt.prototype.lesser = SmallInteger.prototype.lt = SmallInteger.prototype.lesser = BigInteger.prototype.lt = BigInteger.prototype.lesser;\n  BigInteger.prototype.greaterOrEquals = function (v) {\n    return this.compare(v) >= 0;\n  };\n  NativeBigInt.prototype.geq = NativeBigInt.prototype.greaterOrEquals = SmallInteger.prototype.geq = SmallInteger.prototype.greaterOrEquals = BigInteger.prototype.geq = BigInteger.prototype.greaterOrEquals;\n  BigInteger.prototype.lesserOrEquals = function (v) {\n    return this.compare(v) <= 0;\n  };\n  NativeBigInt.prototype.leq = NativeBigInt.prototype.lesserOrEquals = SmallInteger.prototype.leq = SmallInteger.prototype.lesserOrEquals = BigInteger.prototype.leq = BigInteger.prototype.lesserOrEquals;\n  BigInteger.prototype.isEven = function () {\n    return (this.value[0] & 1) === 0;\n  };\n  SmallInteger.prototype.isEven = function () {\n    return (this.value & 1) === 0;\n  };\n  NativeBigInt.prototype.isEven = function () {\n    return (this.value & BigInt(1)) === BigInt(0);\n  };\n  BigInteger.prototype.isOdd = function () {\n    return (this.value[0] & 1) === 1;\n  };\n  SmallInteger.prototype.isOdd = function () {\n    return (this.value & 1) === 1;\n  };\n  NativeBigInt.prototype.isOdd = function () {\n    return (this.value & BigInt(1)) === BigInt(1);\n  };\n  BigInteger.prototype.isPositive = function () {\n    return !this.sign;\n  };\n  SmallInteger.prototype.isPositive = function () {\n    return this.value > 0;\n  };\n  NativeBigInt.prototype.isPositive = SmallInteger.prototype.isPositive;\n  BigInteger.prototype.isNegative = function () {\n    return this.sign;\n  };\n  SmallInteger.prototype.isNegative = function () {\n    return this.value < 0;\n  };\n  NativeBigInt.prototype.isNegative = SmallInteger.prototype.isNegative;\n  BigInteger.prototype.isUnit = function () {\n    return false;\n  };\n  SmallInteger.prototype.isUnit = function () {\n    return Math.abs(this.value) === 1;\n  };\n  NativeBigInt.prototype.isUnit = function () {\n    return this.abs().value === BigInt(1);\n  };\n  BigInteger.prototype.isZero = function () {\n    return false;\n  };\n  SmallInteger.prototype.isZero = function () {\n    return this.value === 0;\n  };\n  NativeBigInt.prototype.isZero = function () {\n    return this.value === BigInt(0);\n  };\n  BigInteger.prototype.isDivisibleBy = function (v) {\n    var n = parseValue(v);\n    if (n.isZero()) return false;\n    if (n.isUnit()) return true;\n    if (n.compareAbs(2) === 0) return this.isEven();\n    return this.mod(n).isZero();\n  };\n  NativeBigInt.prototype.isDivisibleBy = SmallInteger.prototype.isDivisibleBy = BigInteger.prototype.isDivisibleBy;\n  function isBasicPrime(v) {\n    var n = v.abs();\n    if (n.isUnit()) return false;\n    if (n.equals(2) || n.equals(3) || n.equals(5)) return true;\n    if (n.isEven() || n.isDivisibleBy(3) || n.isDivisibleBy(5)) return false;\n    if (n.lesser(49)) return true;\n    // we don't know if it's prime: let the other functions figure it out\n  }\n\n  function millerRabinTest(n, a) {\n    var nPrev = n.prev(),\n      b = nPrev,\n      r = 0,\n      d,\n      t,\n      i,\n      x;\n    while (b.isEven()) b = b.divide(2), r++;\n    next: for (i = 0; i < a.length; i++) {\n      if (n.lesser(a[i])) continue;\n      x = bigInt(a[i]).modPow(b, n);\n      if (x.isUnit() || x.equals(nPrev)) continue;\n      for (d = r - 1; d != 0; d--) {\n        x = x.square().mod(n);\n        if (x.isUnit()) return false;\n        if (x.equals(nPrev)) continue next;\n      }\n      return false;\n    }\n    return true;\n  }\n\n  // Set \"strict\" to true to force GRH-supported lower bound of 2*log(N)^2\n  BigInteger.prototype.isPrime = function (strict) {\n    var isPrime = isBasicPrime(this);\n    if (isPrime !== undefined) return isPrime;\n    var n = this.abs();\n    var bits = n.bitLength();\n    if (bits <= 64) return millerRabinTest(n, [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]);\n    var logN = Math.log(2) * bits.toJSNumber();\n    var t = Math.ceil(strict === true ? 2 * Math.pow(logN, 2) : logN);\n    for (var a = [], i = 0; i < t; i++) {\n      a.push(bigInt(i + 2));\n    }\n    return millerRabinTest(n, a);\n  };\n  NativeBigInt.prototype.isPrime = SmallInteger.prototype.isPrime = BigInteger.prototype.isPrime;\n  BigInteger.prototype.isProbablePrime = function (iterations, rng) {\n    var isPrime = isBasicPrime(this);\n    if (isPrime !== undefined) return isPrime;\n    var n = this.abs();\n    var t = iterations === undefined ? 5 : iterations;\n    for (var a = [], i = 0; i < t; i++) {\n      a.push(bigInt.randBetween(2, n.minus(2), rng));\n    }\n    return millerRabinTest(n, a);\n  };\n  NativeBigInt.prototype.isProbablePrime = SmallInteger.prototype.isProbablePrime = BigInteger.prototype.isProbablePrime;\n  BigInteger.prototype.modInv = function (n) {\n    var t = bigInt.zero,\n      newT = bigInt.one,\n      r = parseValue(n),\n      newR = this.abs(),\n      q,\n      lastT,\n      lastR;\n    while (!newR.isZero()) {\n      q = r.divide(newR);\n      lastT = t;\n      lastR = r;\n      t = newT;\n      r = newR;\n      newT = lastT.subtract(q.multiply(newT));\n      newR = lastR.subtract(q.multiply(newR));\n    }\n    if (!r.isUnit()) throw new Error(this.toString() + \" and \" + n.toString() + \" are not co-prime\");\n    if (t.compare(0) === -1) {\n      t = t.add(n);\n    }\n    if (this.isNegative()) {\n      return t.negate();\n    }\n    return t;\n  };\n  NativeBigInt.prototype.modInv = SmallInteger.prototype.modInv = BigInteger.prototype.modInv;\n  BigInteger.prototype.next = function () {\n    var value = this.value;\n    if (this.sign) {\n      return subtractSmall(value, 1, this.sign);\n    }\n    return new BigInteger(addSmall(value, 1), this.sign);\n  };\n  SmallInteger.prototype.next = function () {\n    var value = this.value;\n    if (value + 1 < MAX_INT) return new SmallInteger(value + 1);\n    return new BigInteger(MAX_INT_ARR, false);\n  };\n  NativeBigInt.prototype.next = function () {\n    return new NativeBigInt(this.value + BigInt(1));\n  };\n  BigInteger.prototype.prev = function () {\n    var value = this.value;\n    if (this.sign) {\n      return new BigInteger(addSmall(value, 1), true);\n    }\n    return subtractSmall(value, 1, this.sign);\n  };\n  SmallInteger.prototype.prev = function () {\n    var value = this.value;\n    if (value - 1 > -MAX_INT) return new SmallInteger(value - 1);\n    return new BigInteger(MAX_INT_ARR, true);\n  };\n  NativeBigInt.prototype.prev = function () {\n    return new NativeBigInt(this.value - BigInt(1));\n  };\n  var powersOfTwo = [1];\n  while (2 * powersOfTwo[powersOfTwo.length - 1] <= BASE) powersOfTwo.push(2 * powersOfTwo[powersOfTwo.length - 1]);\n  var powers2Length = powersOfTwo.length,\n    highestPower2 = powersOfTwo[powers2Length - 1];\n  function shift_isSmall(n) {\n    return Math.abs(n) <= BASE;\n  }\n  BigInteger.prototype.shiftLeft = function (v) {\n    var n = parseValue(v).toJSNumber();\n    if (!shift_isSmall(n)) {\n      throw new Error(String(n) + \" is too large for shifting.\");\n    }\n    if (n < 0) return this.shiftRight(-n);\n    var result = this;\n    if (result.isZero()) return result;\n    while (n >= powers2Length) {\n      result = result.multiply(highestPower2);\n      n -= powers2Length - 1;\n    }\n    return result.multiply(powersOfTwo[n]);\n  };\n  NativeBigInt.prototype.shiftLeft = SmallInteger.prototype.shiftLeft = BigInteger.prototype.shiftLeft;\n  BigInteger.prototype.shiftRight = function (v) {\n    var remQuo;\n    var n = parseValue(v).toJSNumber();\n    if (!shift_isSmall(n)) {\n      throw new Error(String(n) + \" is too large for shifting.\");\n    }\n    if (n < 0) return this.shiftLeft(-n);\n    var result = this;\n    while (n >= powers2Length) {\n      if (result.isZero() || result.isNegative() && result.isUnit()) return result;\n      remQuo = divModAny(result, highestPower2);\n      result = remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0];\n      n -= powers2Length - 1;\n    }\n    remQuo = divModAny(result, powersOfTwo[n]);\n    return remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0];\n  };\n  NativeBigInt.prototype.shiftRight = SmallInteger.prototype.shiftRight = BigInteger.prototype.shiftRight;\n  function bitwise(x, y, fn) {\n    y = parseValue(y);\n    var xSign = x.isNegative(),\n      ySign = y.isNegative();\n    var xRem = xSign ? x.not() : x,\n      yRem = ySign ? y.not() : y;\n    var xDigit = 0,\n      yDigit = 0;\n    var xDivMod = null,\n      yDivMod = null;\n    var result = [];\n    while (!xRem.isZero() || !yRem.isZero()) {\n      xDivMod = divModAny(xRem, highestPower2);\n      xDigit = xDivMod[1].toJSNumber();\n      if (xSign) {\n        xDigit = highestPower2 - 1 - xDigit; // two's complement for negative numbers\n      }\n\n      yDivMod = divModAny(yRem, highestPower2);\n      yDigit = yDivMod[1].toJSNumber();\n      if (ySign) {\n        yDigit = highestPower2 - 1 - yDigit; // two's complement for negative numbers\n      }\n\n      xRem = xDivMod[0];\n      yRem = yDivMod[0];\n      result.push(fn(xDigit, yDigit));\n    }\n    var sum = fn(xSign ? 1 : 0, ySign ? 1 : 0) !== 0 ? bigInt(-1) : bigInt(0);\n    for (var i = result.length - 1; i >= 0; i -= 1) {\n      sum = sum.multiply(highestPower2).add(bigInt(result[i]));\n    }\n    return sum;\n  }\n  BigInteger.prototype.not = function () {\n    return this.negate().prev();\n  };\n  NativeBigInt.prototype.not = SmallInteger.prototype.not = BigInteger.prototype.not;\n  BigInteger.prototype.and = function (n) {\n    return bitwise(this, n, function (a, b) {\n      return a & b;\n    });\n  };\n  NativeBigInt.prototype.and = SmallInteger.prototype.and = BigInteger.prototype.and;\n  BigInteger.prototype.or = function (n) {\n    return bitwise(this, n, function (a, b) {\n      return a | b;\n    });\n  };\n  NativeBigInt.prototype.or = SmallInteger.prototype.or = BigInteger.prototype.or;\n  BigInteger.prototype.xor = function (n) {\n    return bitwise(this, n, function (a, b) {\n      return a ^ b;\n    });\n  };\n  NativeBigInt.prototype.xor = SmallInteger.prototype.xor = BigInteger.prototype.xor;\n  var LOBMASK_I = 1 << 30,\n    LOBMASK_BI = (BASE & -BASE) * (BASE & -BASE) | LOBMASK_I;\n  function roughLOB(n) {\n    // get lowestOneBit (rough)\n    // SmallInteger: return Min(lowestOneBit(n), 1 << 30)\n    // BigInteger: return Min(lowestOneBit(n), 1 << 14) [BASE=1e7]\n    var v = n.value,\n      x = typeof v === \"number\" ? v | LOBMASK_I : typeof v === \"bigint\" ? v | BigInt(LOBMASK_I) : v[0] + v[1] * BASE | LOBMASK_BI;\n    return x & -x;\n  }\n  function integerLogarithm(value, base) {\n    if (base.compareTo(value) <= 0) {\n      var tmp = integerLogarithm(value, base.square(base));\n      var p = tmp.p;\n      var e = tmp.e;\n      var t = p.multiply(base);\n      return t.compareTo(value) <= 0 ? {\n        p: t,\n        e: e * 2 + 1\n      } : {\n        p: p,\n        e: e * 2\n      };\n    }\n    return {\n      p: bigInt(1),\n      e: 0\n    };\n  }\n  BigInteger.prototype.bitLength = function () {\n    var n = this;\n    if (n.compareTo(bigInt(0)) < 0) {\n      n = n.negate().subtract(bigInt(1));\n    }\n    if (n.compareTo(bigInt(0)) === 0) {\n      return bigInt(0);\n    }\n    return bigInt(integerLogarithm(n, bigInt(2)).e).add(bigInt(1));\n  };\n  NativeBigInt.prototype.bitLength = SmallInteger.prototype.bitLength = BigInteger.prototype.bitLength;\n  function max(a, b) {\n    a = parseValue(a);\n    b = parseValue(b);\n    return a.greater(b) ? a : b;\n  }\n  function min(a, b) {\n    a = parseValue(a);\n    b = parseValue(b);\n    return a.lesser(b) ? a : b;\n  }\n  function gcd(a, b) {\n    a = parseValue(a).abs();\n    b = parseValue(b).abs();\n    if (a.equals(b)) return a;\n    if (a.isZero()) return b;\n    if (b.isZero()) return a;\n    var c = Integer[1],\n      d,\n      t;\n    while (a.isEven() && b.isEven()) {\n      d = min(roughLOB(a), roughLOB(b));\n      a = a.divide(d);\n      b = b.divide(d);\n      c = c.multiply(d);\n    }\n    while (a.isEven()) {\n      a = a.divide(roughLOB(a));\n    }\n    do {\n      while (b.isEven()) {\n        b = b.divide(roughLOB(b));\n      }\n      if (a.greater(b)) {\n        t = b;\n        b = a;\n        a = t;\n      }\n      b = b.subtract(a);\n    } while (!b.isZero());\n    return c.isUnit() ? a : a.multiply(c);\n  }\n  function lcm(a, b) {\n    a = parseValue(a).abs();\n    b = parseValue(b).abs();\n    return a.divide(gcd(a, b)).multiply(b);\n  }\n  function randBetween(a, b, rng) {\n    a = parseValue(a);\n    b = parseValue(b);\n    var usedRNG = rng || Math.random;\n    var low = min(a, b),\n      high = max(a, b);\n    var range = high.subtract(low).add(1);\n    if (range.isSmall) return low.add(Math.floor(usedRNG() * range));\n    var digits = toBase(range, BASE).value;\n    var result = [],\n      restricted = true;\n    for (var i = 0; i < digits.length; i++) {\n      var top = restricted ? digits[i] + (i + 1 < digits.length ? digits[i + 1] / BASE : 0) : BASE;\n      var digit = truncate(usedRNG() * top);\n      result.push(digit);\n      if (digit < digits[i]) restricted = false;\n    }\n    return low.add(Integer.fromArray(result, BASE, false));\n  }\n  var parseBase = function (text, base, alphabet, caseSensitive) {\n    alphabet = alphabet || DEFAULT_ALPHABET;\n    text = String(text);\n    if (!caseSensitive) {\n      text = text.toLowerCase();\n      alphabet = alphabet.toLowerCase();\n    }\n    var length = text.length;\n    var i;\n    var absBase = Math.abs(base);\n    var alphabetValues = {};\n    for (i = 0; i < alphabet.length; i++) {\n      alphabetValues[alphabet[i]] = i;\n    }\n    for (i = 0; i < length; i++) {\n      var c = text[i];\n      if (c === \"-\") continue;\n      if (c in alphabetValues) {\n        if (alphabetValues[c] >= absBase) {\n          if (c === \"1\" && absBase === 1) continue;\n          throw new Error(c + \" is not a valid digit in base \" + base + \".\");\n        }\n      }\n    }\n    base = parseValue(base);\n    var digits = [];\n    var isNegative = text[0] === \"-\";\n    for (i = isNegative ? 1 : 0; i < text.length; i++) {\n      var c = text[i];\n      if (c in alphabetValues) digits.push(parseValue(alphabetValues[c]));else if (c === \"<\") {\n        var start = i;\n        do {\n          i++;\n        } while (text[i] !== \">\" && i < text.length);\n        digits.push(parseValue(text.slice(start + 1, i)));\n      } else throw new Error(c + \" is not a valid character\");\n    }\n    return parseBaseFromArray(digits, base, isNegative);\n  };\n  function parseBaseFromArray(digits, base, isNegative) {\n    var val = Integer[0],\n      pow = Integer[1],\n      i;\n    for (i = digits.length - 1; i >= 0; i--) {\n      val = val.add(digits[i].times(pow));\n      pow = pow.times(base);\n    }\n    return isNegative ? val.negate() : val;\n  }\n  function stringify(digit, alphabet) {\n    alphabet = alphabet || DEFAULT_ALPHABET;\n    if (digit < alphabet.length) {\n      return alphabet[digit];\n    }\n    return \"<\" + digit + \">\";\n  }\n  function toBase(n, base) {\n    base = bigInt(base);\n    if (base.isZero()) {\n      if (n.isZero()) return {\n        value: [0],\n        isNegative: false\n      };\n      throw new Error(\"Cannot convert nonzero numbers to base 0.\");\n    }\n    if (base.equals(-1)) {\n      if (n.isZero()) return {\n        value: [0],\n        isNegative: false\n      };\n      if (n.isNegative()) return {\n        value: [].concat.apply([], Array.apply(null, Array(-n.toJSNumber())).map(Array.prototype.valueOf, [1, 0])),\n        isNegative: false\n      };\n      var arr = Array.apply(null, Array(n.toJSNumber() - 1)).map(Array.prototype.valueOf, [0, 1]);\n      arr.unshift([1]);\n      return {\n        value: [].concat.apply([], arr),\n        isNegative: false\n      };\n    }\n    var neg = false;\n    if (n.isNegative() && base.isPositive()) {\n      neg = true;\n      n = n.abs();\n    }\n    if (base.isUnit()) {\n      if (n.isZero()) return {\n        value: [0],\n        isNegative: false\n      };\n      return {\n        value: Array.apply(null, Array(n.toJSNumber())).map(Number.prototype.valueOf, 1),\n        isNegative: neg\n      };\n    }\n    var out = [];\n    var left = n,\n      divmod;\n    while (left.isNegative() || left.compareAbs(base) >= 0) {\n      divmod = left.divmod(base);\n      left = divmod.quotient;\n      var digit = divmod.remainder;\n      if (digit.isNegative()) {\n        digit = base.minus(digit).abs();\n        left = left.next();\n      }\n      out.push(digit.toJSNumber());\n    }\n    out.push(left.toJSNumber());\n    return {\n      value: out.reverse(),\n      isNegative: neg\n    };\n  }\n  function toBaseString(n, base, alphabet) {\n    var arr = toBase(n, base);\n    return (arr.isNegative ? \"-\" : \"\") + arr.value.map(function (x) {\n      return stringify(x, alphabet);\n    }).join('');\n  }\n  BigInteger.prototype.toArray = function (radix) {\n    return toBase(this, radix);\n  };\n  SmallInteger.prototype.toArray = function (radix) {\n    return toBase(this, radix);\n  };\n  NativeBigInt.prototype.toArray = function (radix) {\n    return toBase(this, radix);\n  };\n  BigInteger.prototype.toString = function (radix, alphabet) {\n    if (radix === undefined) radix = 10;\n    if (radix !== 10 || alphabet) return toBaseString(this, radix, alphabet);\n    var v = this.value,\n      l = v.length,\n      str = String(v[--l]),\n      zeros = \"0000000\",\n      digit;\n    while (--l >= 0) {\n      digit = String(v[l]);\n      str += zeros.slice(digit.length) + digit;\n    }\n    var sign = this.sign ? \"-\" : \"\";\n    return sign + str;\n  };\n  SmallInteger.prototype.toString = function (radix, alphabet) {\n    if (radix === undefined) radix = 10;\n    if (radix != 10 || alphabet) return toBaseString(this, radix, alphabet);\n    return String(this.value);\n  };\n  NativeBigInt.prototype.toString = SmallInteger.prototype.toString;\n  NativeBigInt.prototype.toJSON = BigInteger.prototype.toJSON = SmallInteger.prototype.toJSON = function () {\n    return this.toString();\n  };\n  BigInteger.prototype.valueOf = function () {\n    return parseInt(this.toString(), 10);\n  };\n  BigInteger.prototype.toJSNumber = BigInteger.prototype.valueOf;\n  SmallInteger.prototype.valueOf = function () {\n    return this.value;\n  };\n  SmallInteger.prototype.toJSNumber = SmallInteger.prototype.valueOf;\n  NativeBigInt.prototype.valueOf = NativeBigInt.prototype.toJSNumber = function () {\n    return parseInt(this.toString(), 10);\n  };\n  function parseStringValue(v) {\n    if (isPrecise(+v)) {\n      var x = +v;\n      if (x === truncate(x)) return supportsNativeBigInt ? new NativeBigInt(BigInt(x)) : new SmallInteger(x);\n      throw new Error(\"Invalid integer: \" + v);\n    }\n    var sign = v[0] === \"-\";\n    if (sign) v = v.slice(1);\n    var split = v.split(/e/i);\n    if (split.length > 2) throw new Error(\"Invalid integer: \" + split.join(\"e\"));\n    if (split.length === 2) {\n      var exp = split[1];\n      if (exp[0] === \"+\") exp = exp.slice(1);\n      exp = +exp;\n      if (exp !== truncate(exp) || !isPrecise(exp)) throw new Error(\"Invalid integer: \" + exp + \" is not a valid exponent.\");\n      var text = split[0];\n      var decimalPlace = text.indexOf(\".\");\n      if (decimalPlace >= 0) {\n        exp -= text.length - decimalPlace - 1;\n        text = text.slice(0, decimalPlace) + text.slice(decimalPlace + 1);\n      }\n      if (exp < 0) throw new Error(\"Cannot include negative exponent part for integers\");\n      text += new Array(exp + 1).join(\"0\");\n      v = text;\n    }\n    var isValid = /^([0-9][0-9]*)$/.test(v);\n    if (!isValid) throw new Error(\"Invalid integer: \" + v);\n    if (supportsNativeBigInt) {\n      return new NativeBigInt(BigInt(sign ? \"-\" + v : v));\n    }\n    var r = [],\n      max = v.length,\n      l = LOG_BASE,\n      min = max - l;\n    while (max > 0) {\n      r.push(+v.slice(min, max));\n      min -= l;\n      if (min < 0) min = 0;\n      max -= l;\n    }\n    trim(r);\n    return new BigInteger(r, sign);\n  }\n  function parseNumberValue(v) {\n    if (supportsNativeBigInt) {\n      return new NativeBigInt(BigInt(v));\n    }\n    if (isPrecise(v)) {\n      if (v !== truncate(v)) throw new Error(v + \" is not an integer.\");\n      return new SmallInteger(v);\n    }\n    return parseStringValue(v.toString());\n  }\n  function parseValue(v) {\n    if (typeof v === \"number\") {\n      return parseNumberValue(v);\n    }\n    if (typeof v === \"string\") {\n      return parseStringValue(v);\n    }\n    if (typeof v === \"bigint\") {\n      return new NativeBigInt(v);\n    }\n    return v;\n  }\n  // Pre-define numbers in range [-999,999]\n  for (var i = 0; i < 1000; i++) {\n    Integer[i] = parseValue(i);\n    if (i > 0) Integer[-i] = parseValue(-i);\n  }\n  // Backwards compatibility\n  Integer.one = Integer[1];\n  Integer.zero = Integer[0];\n  Integer.minusOne = Integer[-1];\n  Integer.max = max;\n  Integer.min = min;\n  Integer.gcd = gcd;\n  Integer.lcm = lcm;\n  Integer.isInstance = function (x) {\n    return x instanceof BigInteger || x instanceof SmallInteger || x instanceof NativeBigInt;\n  };\n  Integer.randBetween = randBetween;\n  Integer.fromArray = function (digits, base, isNegative) {\n    return parseBaseFromArray(digits.map(parseValue), parseValue(base || 10), isNegative);\n  };\n  return Integer;\n}();\n\n// Node.js check\nif ( true && module.hasOwnProperty(\"exports\")) {\n  module.exports = bigInt;\n}\n\n//amd check\nif (true) {\n  !(__WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n    return bigInt;\n  }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/big-integer/BigInteger.js\n");

/***/ })

};
;