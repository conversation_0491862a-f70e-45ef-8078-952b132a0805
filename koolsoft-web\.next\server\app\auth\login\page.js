/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4184\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(rsc)/./src/components/providers/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/custom-toaster.tsx */ \"(rsc)/./src/components/ui/custom-toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDS29vbFNvZnQlNUMlNUNrb29sc29mdC13ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlc3Npb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJHJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNLb29sU29mdCU1QyU1Q2tvb2xzb2Z0LXdlYiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q2N1c3RvbS10b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkN1c3RvbVRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUErSjtBQUMvSjtBQUNBLHdMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2Vzc2lvblByb3ZpZGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxTZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDdXN0b21Ub2FzdGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXGN1c3RvbS10b2FzdGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\auth\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b3e87d01cb8a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImIzZTg3ZDAxY2I4YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _components_ui_custom_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/custom-toaster */ \"(rsc)/./src/components/ui/custom-toaster.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'KoolSoft',\n    description: 'KoolSoft Management System'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_custom_toaster__WEBPACK_IMPORTED_MODULE_3__.CustomToaster, {}, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDc0I7QUFDa0Q7QUFDVjtBQUV2RCxNQUFNRSxRQUFrQixHQUFHO0lBQ2hDQyxLQUFLLEVBQUUsVUFBVTtJQUNqQkMsV0FBVyxFQUFFO0FBQ2YsQ0FBQztBQUVjLFNBQVNDLFVBQVVBLENBQUMsRUFDakNDLFFBQUFBLEVBR0EsRUFBRTtJQUNGLHFCQUNFLDhEQUFDLElBQUk7UUFBQyxJQUFJLEVBQUMsSUFBSTtnQ0FDYiw4REFBQyxJQUFJO1lBQUMsU0FBUyxFQUFDLHVCQUF1Qjs7OEJBQ3JDLDhEQUFDLGtGQUFlLENBQUM7OEJBQUNBLFFBQVE7Ozs7Ozs4QkFDMUIsOERBQUMsd0VBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdEIiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyJztcbmltcG9ydCB7IEN1c3RvbVRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY3VzdG9tLXRvYXN0ZXInO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0tvb2xTb2Z0JyxcbiAgZGVzY3JpcHRpb246ICdLb29sU29mdCBNYW5hZ2VtZW50IFN5c3RlbScsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGFudGlhbGlhc2VkXCI+XG4gICAgICAgIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICA8Q3VzdG9tVG9hc3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJDdXN0b21Ub2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\providers\\SessionProvider.tsx",
"SessionProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/custom-toaster.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/custom-toaster.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomToaster: () => (/* binding */ CustomToaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CustomToaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CustomToaster() from the server but CustomToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\ui\\custom-toaster.tsx",
"CustomToaster",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/custom-toaster.tsx */ \"(ssr)/./src/components/ui/custom-toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDS29vbFNvZnQlNUMlNUNrb29sc29mdC13ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlc3Npb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJHJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNLb29sU29mdCU1QyU1Q2tvb2xzb2Z0LXdlYiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q2N1c3RvbS10b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkN1c3RvbVRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUErSjtBQUMvSjtBQUNBLHdMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2Vzc2lvblByb3ZpZGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxTZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDdXN0b21Ub2FzdGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXGN1c3RvbS10b2FzdGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(ssr)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Login form validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_6__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().email('Please enter a valid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(8, 'Password must be at least 8 characters'),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_6__.z.boolean().optional()\n});\n// Type for login form data\n/**\n * Login Page Component\n *\n * This page allows users to log in to the application using their email and password.\n */ function LoginPage() {\n    const { login, error: authError, isLoading } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';\n    // Initialize form with react-hook-form\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_7__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: '',\n            password: '',\n            rememberMe: false\n        }\n    });\n    // Handle form submission\n    const onSubmit = async (data)=>{\n        setIsSubmitting(true);\n        try {\n            await login(data.email, data.password, callbackUrl);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-center text-3xl font-extrabold text-[#0F52BA]\",\n                            children: \"KoolSoft\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-black\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-black\",\n                            children: [\n                                \"Or\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/\",\n                                    className: \"font-medium text-[#0F52BA] hover:text-blue-700\",\n                                    children: \"return to the homepage\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit(onSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md shadow-sm -space-y-px\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-black rounded-t-md focus:outline-none focus:ring-[#0F52BA] focus:border-[#0F52BA] focus:z-10 sm:text-sm\",\n                                            placeholder: \"Email address\",\n                                            ...register('email')\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-[#ef4444]\",\n                                            children: errors.email.message\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            className: \"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-black rounded-b-md focus:outline-none focus:ring-[#0F52BA] focus:border-[#0F52BA] focus:z-10 sm:text-sm\",\n                                            placeholder: \"Password\",\n                                            ...register('password')\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-[#ef4444]\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 35\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"remember-me\",\n                                            type: \"checkbox\",\n                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\",\n                                            ...register('rememberMe')\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"remember-me\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: \"Remember me\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/auth/forgot-password\",\n                                        className: \"font-medium text-[#0F52BA] hover:text-blue-700\",\n                                        children: \"Forgot your password?\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        authError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-[#ef4444] p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"Authentication Error\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-sm text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: authError\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#0F52BA] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F52BA] disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isSubmitting ? 'Signing in...' : 'Sign in'\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\n/**\n * Session Provider Component\n *\n * This component wraps the application with NextAuth.js session context.\n * It provides session state to all child components.\n *\n * @param children Child components\n * @returns Session provider component\n */ function SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        // Configure session refresh behavior\n        refetchInterval: 5 * 60,\n        refetchOnWindowFocus: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 16,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEU7QUFHNUU7Ozs7Ozs7O0NBUUEsR0FDTyxTQUFTQSxlQUFlQSxDQUFDLEVBQUVFLFFBQUFBLEVBQW1DLEVBQUU7SUFDckUscUJBQ0UsOERBQUMsNERBQXVCO1FBQ3RCO1FBQ0EsZUFBZSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUN4QixvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQztrQkFFMUJBLFFBQVE7Ozs7OztBQUdmIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxTZXNzaW9uUHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBTZXNzaW9uIFByb3ZpZGVyIENvbXBvbmVudFxuICpcbiAqIFRoaXMgY29tcG9uZW50IHdyYXBzIHRoZSBhcHBsaWNhdGlvbiB3aXRoIE5leHRBdXRoLmpzIHNlc3Npb24gY29udGV4dC5cbiAqIEl0IHByb3ZpZGVzIHNlc3Npb24gc3RhdGUgdG8gYWxsIGNoaWxkIGNvbXBvbmVudHMuXG4gKlxuICogQHBhcmFtIGNoaWxkcmVuIENoaWxkIGNvbXBvbmVudHNcbiAqIEByZXR1cm5zIFNlc3Npb24gcHJvdmlkZXIgY29tcG9uZW50XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlclxuICAgICAgLy8gQ29uZmlndXJlIHNlc3Npb24gcmVmcmVzaCBiZWhhdmlvclxuICAgICAgcmVmZXRjaEludGVydmFsPXs1ICogNjB9IC8vIFJlZnJlc2ggc2Vzc2lvbiBldmVyeSA1IG1pbnV0ZXNcbiAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzPXt0cnVlfSAvLyBSZWZyZXNoIHdoZW4gd2luZG93IHJlZ2FpbnMgZm9jdXNcbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9OZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJOZXh0QXV0aFNlc3Npb25Qcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/custom-toaster.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/custom-toaster.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomToaster: () => (/* binding */ CustomToaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./src/components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomToaster auto */ \n\n\nfunction CustomToaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    \"data-toast-id\": id,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 31\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 14\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n        lineNumber: 9,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/custom-toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 12,\n        columnNumber: 12\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border border-gray-200 bg-white text-black\",\n            success: \"border-primary bg-primary text-white\",\n            destructive: \"border-destructive bg-destructive text-white\",\n            warning: \"border-warning bg-warning text-black\",\n            info: \"border-info bg-info text-white\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, 'data-toast-id': dataToastId, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        \"data-toast-id\": dataToastId,\n        \"data-variant\": variant || 'default',\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 34,\n        columnNumber: 10\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.success]:border-primary/40 group-[.success]:hover:border-primary/30 group-[.success]:hover:bg-primary group-[.success]:hover:text-primary-foreground group-[.success]:focus:ring-primary group-[.destructive]:border-destructive/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive group-[.warning]:border-warning/40 group-[.warning]:hover:border-warning/30 group-[.warning]:hover:bg-warning group-[.warning]:hover:text-warning-foreground group-[.warning]:focus:ring-warning group-[.info]:border-info/40 group-[.info]:hover:border-info/30 group-[.info]:hover:bg-info group-[.info]:hover:text-info-foreground group-[.info]:focus:ring-info\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 42,\n        columnNumber: 12\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 opacity-70 transition-opacity hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100\", \"data-[variant=default]:text-gray-500 data-[variant=default]:hover:text-gray-900 data-[variant=default]:focus:ring-gray-400\", \"data-[variant=success]:text-white data-[variant=success]:focus:ring-primary-light\", \"data-[variant=destructive]:text-white data-[variant=destructive]:focus:ring-destructive-light\", \"data-[variant=warning]:text-black data-[variant=warning]:focus:ring-warning-light\", \"data-[variant=info]:text-white data-[variant=info]:focus:ring-info-light\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 12\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 54,\n        columnNumber: 12\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 59,\n        columnNumber: 12\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/use-toast.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/use-toast.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 5;\nconst TOAST_REMOVE_DELAY = 5000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_VALUE;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/use-toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/hooks/useAuth.ts":
/*!**********************************!*\
  !*** ./src/lib/hooks/useAuth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\n/**\n * Custom hook for authentication\n *\n * This hook provides:\n * - Authentication state (loading, authenticated, user data)\n * - Login and logout functions\n * - Role-based access control helpers\n */ function useAuth() {\n    const { data: session, status, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Log session data for debugging\n    console.log('useAuth Hook:', {\n        status,\n        sessionExists: !!session,\n        userExists: !!session?.user,\n        userRole: session?.user?.role,\n        isRefreshing\n    });\n    // Force refresh the session if it's taking too long to load\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            let timeoutId;\n            if (status === 'loading' && !isRefreshing) {\n                // If still loading after 2 seconds, try to refresh the session\n                timeoutId = setTimeout({\n                    \"useAuth.useEffect\": ()=>{\n                        console.log('useAuth: Session loading timeout, forcing refresh');\n                        setIsRefreshing(true);\n                        update() // Force refresh the session\n                        .then({\n                            \"useAuth.useEffect\": ()=>{\n                                console.log('useAuth: Session refreshed successfully');\n                            }\n                        }[\"useAuth.useEffect\"]).catch({\n                            \"useAuth.useEffect\": (err)=>{\n                                console.error('useAuth: Error refreshing session', err);\n                            }\n                        }[\"useAuth.useEffect\"]).finally({\n                            \"useAuth.useEffect\": ()=>{\n                                setIsRefreshing(false);\n                            }\n                        }[\"useAuth.useEffect\"]);\n                    }\n                }[\"useAuth.useEffect\"], 2000);\n            }\n            return ({\n                \"useAuth.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], [\n        status,\n        isRefreshing,\n        update\n    ]);\n    // Check if the user is authenticated\n    const isAuthenticated = status === 'authenticated' && !!session?.user;\n    // Check if the authentication state is loading\n    const isLoading = status === 'loading' || isRefreshing;\n    // Get the user's role (with fallback to empty string to avoid null errors)\n    const userRole = session?.user?.role || '';\n    // Log authentication state\n    console.log('useAuth State:', {\n        isAuthenticated,\n        isLoading,\n        userRole,\n        status\n    });\n    /**\n   * Login function\n   * @param email User email\n   * @param password User password\n   * @param callbackUrl URL to redirect to after successful login\n   * @returns Promise resolving to login success status\n   */ const login = async (email, password, callbackUrl)=>{\n        try {\n            setError(null);\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)('credentials', {\n                redirect: false,\n                email,\n                password\n            });\n            if (result?.error) {\n                setError('Invalid email or password');\n                return false;\n            }\n            if (result?.ok) {\n                router.push(callbackUrl || '/dashboard');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login error:', error);\n            setError('An error occurred during login');\n            return false;\n        }\n    };\n    /**\n   * Logout function\n   * @param callbackUrl URL to redirect to after logout\n   */ const logout = async (callbackUrl)=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signOut)({\n            redirect: false\n        });\n        router.push(callbackUrl || '/auth/login');\n    };\n    /**\n   * Check if the user has a specific role\n   * @param role Role to check\n   * @returns Boolean indicating if the user has the role\n   */ const hasRole = (role)=>{\n        if (!userRole) return false;\n        if (Array.isArray(role)) {\n            return role.includes(userRole);\n        }\n        return userRole === role;\n    };\n    /**\n   * Check if the user is an admin\n   * @returns Boolean indicating if the user is an admin\n   */ const isAdmin = ()=>userRole === 'ADMIN';\n    /**\n   * Check if the user is a manager\n   * @returns Boolean indicating if the user is a manager\n   */ const isManager = ()=>userRole === 'MANAGER';\n    /**\n   * Check if the user is an executive\n   * @returns Boolean indicating if the user is an executive\n   */ const isExecutive = ()=>userRole === 'EXECUTIVE';\n    return {\n        user: session?.user || null,\n        isAuthenticated,\n        isLoading,\n        userRole,\n        login,\n        logout,\n        error,\n        hasRole,\n        isAdmin,\n        isManager,\n        isExecutive\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeWords: () => (/* binding */ capitalizeWords),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combines class names with Tailwind CSS\n * @param inputs - Class names to combine\n * @returns Combined class names\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Formats a date string or Date object into a human-readable format\n * @param date - Date to format\n * @param format - Optional format (default: 'MMM dd, yyyy')\n * @returns Formatted date string\n */ function formatDate(date, format = 'MMM dd, yyyy') {\n    if (!date) return '';\n    try {\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) {\n            return '';\n        }\n        // Basic formatting options\n        const options = {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        };\n        // Add time if format includes time\n        if (format.includes('HH') || format.includes('hh') || format.includes('mm') || format.includes('ss')) {\n            options.hour = '2-digit';\n            options.minute = '2-digit';\n        }\n        return dateObj.toLocaleDateString('en-US', options);\n    } catch (error) {\n        console.error('Error formatting date:', error);\n        return '';\n    }\n}\n/**\n * Truncates a string to a specified length and adds ellipsis\n * @param str - String to truncate\n * @param length - Maximum length\n * @returns Truncated string\n */ function truncateString(str, length = 50) {\n    if (!str) return '';\n    if (str.length <= length) return str;\n    return str.substring(0, length) + '...';\n}\n/**\n * Formats a number as currency\n * @param value - Number to format\n * @param currency - Currency code (default: 'INR')\n * @returns Formatted currency string\n */ function formatCurrency(value, currency = 'INR') {\n    if (value === null || value === undefined) return '';\n    const numValue = typeof value === 'string' ? parseFloat(value) : value;\n    if (isNaN(numValue)) return '';\n    return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(numValue);\n}\n/**\n * Capitalizes the first letter of each word in a string\n * @param str - String to capitalize\n * @returns Capitalized string\n */ function capitalizeWords(str) {\n    if (!str) return '';\n    return str.split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ');\n}\n/**\n * Generates a random string of specified length\n * @param length - Length of the random string\n * @returns Random string\n */ function generateRandomString(length = 8) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Debounces a function\n * @param func - Function to debounce\n * @param wait - Wait time in milliseconds\n * @returns Debounced function\n */ function debounce(func, wait) {\n    let timeout = null;\n    return function(...args) {\n        const later = ()=>{\n            timeout = null;\n            func(...args);\n        };\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}\n/**\n * Validates an email address\n * @param email - Email to validate\n * @returns Whether the email is valid\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Formats a phone number\n * @param phone - Phone number to format\n * @returns Formatted phone number\n */ function formatPhoneNumber(phone) {\n    if (!phone) return '';\n    // Remove all non-numeric characters\n    const cleaned = phone.replace(/\\D/g, '');\n    // Format based on length\n    if (cleaned.length === 10) {\n        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n}\n/**\n * Converts a string to kebab-case\n * @param str - String to convert\n * @returns Kebab-case string\n */ function toKebabCase(str) {\n    if (!str) return '';\n    return str.replace(/([a-z])([A-Z])/g, '$1-$2').replace(/\\s+/g, '-').toLowerCase();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/zod","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();