import { getEmailService } from './email.service';
import { ReportRepository } from '@/lib/repositories/report.repository';
import { ReportEmailConfigRepository, ReportEmailDeliveryRepository } from '@/lib/repositories/email-distribution.repository';
import { generateReportFile } from '@/lib/utils/report-generator';
import { SendReportEmailInput } from '@/lib/validations/email-distribution.schema';
import { ExportFormat } from '@/lib/validations/scheduled-report.schema';
import path from 'path';
import fs from 'fs/promises';

export interface EmailDistributionResult {
  success: boolean;
  deliveryId: string;
  sentCount: number;
  failedCount: number;
  errors: string[];
}

export class EmailDistributionService {
  private emailService = getEmailService();
  private reportRepository = new ReportRepository();
  private configRepository = new ReportEmailConfigRepository();
  private deliveryRepository = new ReportEmailDeliveryRepository();

  /**
   * Send report via email using configuration or direct parameters
   */
  async sendReportEmail(
    input: SendReportEmailInput,
    userId: string
  ): Promise<EmailDistributionResult> {
    try {
      let config = null;
      let recipients: string[] = [];
      let emailSubject = '';
      let emailBody = '';
      let attachmentFormat = input.attachmentFormat;

      // Get configuration if configId provided
      if (input.configId) {
        config = await this.configRepository.findById(input.configId);
        if (!config) {
          throw new Error('Email configuration not found');
        }

        // Build recipient list from config
        recipients = [...config.individualRecipients];
        if (config.distributionListId) {
          // Note: distributionList relationship needs to be included in the query
          // recipients.push(...config.distributionList.emails);
        }

        emailSubject = config.emailSubject;
        emailBody = config.emailBody;
        attachmentFormat = config.attachmentFormat as 'PDF' | 'EXCEL' | 'CSV';
      } else {
        // Use direct parameters
        recipients = input.recipients || [];
        emailSubject = input.emailSubject || `${input.reportType} Report`;
        emailBody = input.emailBody || `Please find attached the ${input.reportType} report.`;
      }

      if (recipients.length === 0) {
        throw new Error('No recipients specified');
      }

      // Remove duplicates
      recipients = [...new Set(recipients)];

      // Create delivery record
      const deliveryData: any = {
        reportType: input.reportType,
        reportParameters: input.reportParameters,
        recipients,
        emailSubject,
        attachmentFormat,
        status: 'PENDING',
        createdBy: userId,
      };

      if (input.configId) {
        deliveryData.config = { connect: { id: input.configId } };
      }

      const delivery = await this.deliveryRepository.create(deliveryData);

      let attachmentPath: string | null = null;
      let sentCount = 0;
      let failedCount = 0;
      const errors: string[] = [];

      try {
        // Generate report data
        const reportData = await this.generateReportData(input.reportType, input.reportParameters);

        // Generate attachment if required
        if (input.includeAttachment) {
          attachmentPath = await this.generateReportAttachment(
            input.reportType,
            reportData,
            attachmentFormat,
            input.reportParameters
          );
        }

        // Send emails to all recipients
        const emailPromises = recipients.map(async (recipient) => {
          try {
            await this.emailService.sendEmail({
              to: recipient,
              subject: emailSubject,
              html: this.formatEmailBody(emailBody, {
                reportType: input.reportType,
                recipientEmail: recipient,
                reportDate: new Date().toLocaleDateString(),
                attachmentIncluded: input.includeAttachment,
              }),
              attachments: attachmentPath ? [{
                filename: `${input.reportType}_Report_${new Date().toISOString().split('T')[0]}.${attachmentFormat.toLowerCase()}`,
                content: require('fs').readFileSync(attachmentPath),
              }] : undefined,
            });
            sentCount++;
          } catch (error) {
            failedCount++;
            errors.push(`Failed to send to ${recipient}: ${error instanceof Error ? error.message : String(error)}`);
          }
        });

        await Promise.all(emailPromises);

        // Update delivery record
        await this.deliveryRepository.update(delivery.id, {
          status: failedCount === 0 ? 'SENT' : sentCount > 0 ? 'PARTIAL' : 'FAILED',
          sentCount,
          failedCount,
          errorMessages: errors,
          sentAt: sentCount > 0 ? new Date() : null,
          attachmentPath,
        });

        // Clean up attachment file if it was created
        if (attachmentPath) {
          try {
            await fs.unlink(attachmentPath);
          } catch (cleanupError) {
            console.warn('Failed to clean up attachment file:', cleanupError);
          }
        }

        return {
          success: sentCount > 0,
          deliveryId: delivery.id,
          sentCount,
          failedCount,
          errors,
        };

      } catch (error) {
        // Update delivery record with error
        await this.deliveryRepository.update(delivery.id, {
          status: 'FAILED',
          errorMessages: [error instanceof Error ? error.message : String(error)],
        });

        throw error;
      }

    } catch (error) {
      console.error('Email distribution error:', error);
      throw new Error(`Failed to send report email: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Retry failed email delivery
   */
  async retryEmailDelivery(
    deliveryId: string,
    specificRecipients?: string[]
  ): Promise<EmailDistributionResult> {
    const delivery = await this.deliveryRepository.findById(deliveryId);
    if (!delivery) {
      throw new Error('Email delivery not found');
    }

    // Determine recipients to retry
    let recipientsToRetry = specificRecipients || delivery.recipients;
    
    // If no specific recipients provided and delivery was partial, retry only failed ones
    if (!specificRecipients && delivery.status === 'PARTIAL') {
      // This would require more sophisticated tracking of which specific emails failed
      // For now, we'll retry all recipients
      recipientsToRetry = delivery.recipients;
    }

    // Create new delivery record for retry
    const retryInput: SendReportEmailInput = {
      reportType: delivery.reportType as any,
      reportParameters: delivery.reportParameters as Record<string, any>,
      recipients: recipientsToRetry,
      emailSubject: delivery.emailSubject,
      includeAttachment: !!delivery.attachmentFormat,
      attachmentFormat: (delivery.attachmentFormat as 'PDF' | 'EXCEL' | 'CSV') || 'PDF',
    };

    return this.sendReportEmail(retryInput, delivery.createdBy);
  }

  /**
   * Generate report data based on type and parameters
   */
  private async generateReportData(reportType: string, parameters: Record<string, any>) {
    switch (reportType) {
      case 'AMC':
        return this.reportRepository.getAMCReports({
          page: 1,
          limit: 10000,
          sortBy: 'contractDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'WARRANTY':
        return this.reportRepository.getWarrantyReports({
          page: 1,
          limit: 10000,
          sortBy: 'purchaseDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'SERVICE':
        return this.reportRepository.getServiceReports({
          page: 1,
          limit: 10000,
          sortBy: 'reportDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'SALES':
        return this.reportRepository.getSalesReports({
          page: 1,
          limit: 10000,
          sortBy: 'orderDate',
          sortOrder: 'desc',
          ...parameters,
        });

      case 'CUSTOMER':
        return this.reportRepository.getCustomerReports({
          page: 1,
          limit: 10000,
          sortBy: 'name',
          sortOrder: 'asc',
          ...parameters,
        });

      default:
        throw new Error(`Unsupported report type: ${reportType}`);
    }
  }

  /**
   * Generate report attachment file
   */
  private async generateReportAttachment(
    reportType: string,
    reportData: any,
    format: string,
    parameters: Record<string, any>
  ): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${reportType}_Report_${timestamp}.${format.toLowerCase()}`;
    const tempDir = path.join(process.cwd(), 'temp');
    const filePath = path.join(tempDir, filename);

    // Ensure temp directory exists
    await fs.mkdir(tempDir, { recursive: true });

    // Generate file using existing report generator
    return await generateReportFile(
      reportData,
      format as ExportFormat,
      `${reportType}_Report`
    );
  }

  /**
   * Format email body with placeholders
   */
  private formatEmailBody(template: string, data: Record<string, any>): string {
    let formatted = template;
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      formatted = formatted.replace(new RegExp(placeholder, 'g'), String(value));
    }

    return formatted;
  }

  /**
   * Get email delivery statistics
   */
  async getDeliveryStats(options?: {
    configId?: string;
    reportType?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    return this.deliveryRepository.getDeliveryStats(options);
  }
}
