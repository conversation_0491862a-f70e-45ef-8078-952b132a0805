"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fs-constants";
exports.ids = ["vendor-chunks/fs-constants"];
exports.modules = {

/***/ "(rsc)/./node_modules/fs-constants/index.js":
/*!********************************************!*\
  !*** ./node_modules/fs-constants/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = (__webpack_require__(/*! fs */ \"fs\").constants) || __webpack_require__(/*! constants */ \"constants\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnMtY29uc3RhbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7O0FBQUFBLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHQywrQ0FBdUIsSUFBSUEsbUJBQU8sQ0FBQyw0QkFBVyxDQUFDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcZnMtY29uc3RhbnRzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJ2ZzJykuY29uc3RhbnRzIHx8IHJlcXVpcmUoJ2NvbnN0YW50cycpXG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJjb25zdGFudHMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fs-constants/index.js\n");

/***/ })

};
;