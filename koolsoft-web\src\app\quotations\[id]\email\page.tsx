'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Mail,
  ArrowLeft,
  Send,
  FileText,
  User,
  Building,
} from 'lucide-react';

const emailQuotationSchema = z.object({
  to: z.string().email('Please enter a valid email address'),
  cc: z.string().optional(),
  bcc: z.string().optional(),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  includePDF: z.boolean().default(true),
  sendCopy: z.boolean().default(false),
});

type EmailQuotationForm = z.infer<typeof emailQuotationSchema>;

interface QuotationSummary {
  id: string;
  quotationNumber: string;
  quotationDate: string;
  status: string;
  totalAmount: number;
  customer: {
    name: string;
    email?: string;
  };
  executive: {
    name: string;
    email?: string;
  };
}

/**
 * Email Quotation Page
 *
 * This page allows users to send quotations via email with customizable
 * message content and recipient options.
 */
export default function EmailQuotationPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [quotation, setQuotation] = useState<QuotationSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  const quotationId = params?.id as string;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(emailQuotationSchema),
    defaultValues: {
      includePDF: true,
      sendCopy: false,
    },
  });

  // Fetch quotation summary
  const fetchQuotation = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/quotations/${quotationId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quotation');
      }

      const data = await response.json();
      if (data.success) {
        const quotationData = data.data;
        setQuotation(quotationData);
        
        // Pre-fill form with customer email and default subject
        if (quotationData.customer.email) {
          setValue('to', quotationData.customer.email);
        }
        setValue('subject', `Quotation ${quotationData.quotationNumber} - ${quotationData.customer.name}`);
        setValue('message', `Dear ${quotationData.customer.name},

Please find attached our quotation ${quotationData.quotationNumber} for your review.

Quotation Details:
- Quotation Number: ${quotationData.quotationNumber}
- Date: ${new Date(quotationData.quotationDate).toLocaleDateString()}
- Total Amount: ₹${quotationData.totalAmount.toLocaleString()}

Please feel free to contact us if you have any questions or require any clarification.

Thank you for your business.

Best regards,
${quotationData.executive.name}`);
      } else {
        throw new Error(data.error || 'Failed to fetch quotation');
      }
    } catch (error) {
      console.error('Error fetching quotation:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch quotation details. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (quotationId) {
      fetchQuotation();
    }
  }, [quotationId]);

  // Handle form submission
  const onSubmit = async (data: EmailQuotationForm) => {
    try {
      setSending(true);

      const response = await fetch(`/api/quotations/${quotationId}/email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to send email');
      }

      const result = await response.json();
      if (result.success) {
        toast({
          title: 'Success',
          description: 'Quotation email sent successfully.',
        });
        router.push(`/quotations/${quotationId}`);
      } else {
        throw new Error(result.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: 'Error',
        description: 'Failed to send email. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="Loading..." requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading quotation details...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!quotation) {
    return (
      <DashboardLayout title="Quotation Not Found" requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Quotation Not Found</h2>
          <p className="text-gray-600 mb-4">The quotation you're looking for doesn't exist or has been deleted.</p>
          <Button asChild>
            <Link href="/quotations">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quotations
            </Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title={`Email Quotation ${quotation.quotationNumber}`} requireAuth allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE']}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center space-x-2">
              <Mail className="h-5 w-5" />
              <span>Email Quotation {quotation.quotationNumber}</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Send quotation to customer via email
            </CardDescription>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Email Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="to">To *</Label>
                    <Input
                      type="email"
                      {...register('to')}
                      placeholder="<EMAIL>"
                    />
                    {errors.to && (
                      <p className="text-sm text-red-600">{errors.to.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="cc">CC</Label>
                    <Input
                      type="email"
                      {...register('cc')}
                      placeholder="<EMAIL> (optional)"
                    />
                    {errors.cc && (
                      <p className="text-sm text-red-600">{errors.cc.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="bcc">BCC</Label>
                    <Input
                      type="email"
                      {...register('bcc')}
                      placeholder="<EMAIL> (optional)"
                    />
                    {errors.bcc && (
                      <p className="text-sm text-red-600">{errors.bcc.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="subject">Subject *</Label>
                    <Input
                      {...register('subject')}
                      placeholder="Email subject"
                    />
                    {errors.subject && (
                      <p className="text-sm text-red-600">{errors.subject.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="message">Message *</Label>
                    <Textarea
                      {...register('message')}
                      rows={10}
                      placeholder="Email message content"
                    />
                    {errors.message && (
                      <p className="text-sm text-red-600">{errors.message.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includePDF"
                        {...register('includePDF')}
                      />
                      <Label htmlFor="includePDF">Include PDF attachment</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="sendCopy"
                        {...register('sendCopy')}
                      />
                      <Label htmlFor="sendCopy">Send a copy to myself</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" asChild>
                  <Link href={`/quotations/${quotationId}`}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Cancel
                  </Link>
                </Button>
                <Button type="submit" disabled={sending}>
                  <Send className="h-4 w-4 mr-2" />
                  {sending ? 'Sending...' : 'Send Email'}
                </Button>
              </div>
            </form>
          </div>

          {/* Quotation Summary */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Quotation Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">Quotation Number</Label>
                  <p className="font-medium">{quotation.quotationNumber}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">Customer</Label>
                  <div className="flex items-center">
                    <Building className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{quotation.customer.name}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">Executive</Label>
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{quotation.executive.name}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">Total Amount</Label>
                  <p className="text-lg font-semibold text-green-600">
                    ₹{quotation.totalAmount.toLocaleString()}
                  </p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">Date</Label>
                  <p>{new Date(quotation.quotationDate).toLocaleDateString()}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
