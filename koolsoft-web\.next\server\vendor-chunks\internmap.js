"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/internmap";
exports.ids = ["vendor-chunks/internmap"];
exports.modules = {

/***/ "(ssr)/./node_modules/internmap/src/index.js":
/*!*********************************************!*\
  !*** ./node_modules/internmap/src/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternMap: () => (/* binding */ InternMap),\n/* harmony export */   InternSet: () => (/* binding */ InternSet)\n/* harmony export */ });\nclass InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\nclass InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {\n      _intern: {\n        value: new Map()\n      },\n      _key: {\n        value: key\n      }\n    });\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\nfunction intern_get({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\nfunction intern_set({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\nfunction intern_delete({\n  _intern,\n  _key\n}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/internmap/src/index.js\n");

/***/ })

};
;