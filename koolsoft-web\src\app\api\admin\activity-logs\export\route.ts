import { NextRequest, NextResponse } from 'next/server';
import { getActivityLogService } from '@/lib/services/activity-log.service';
import { withActivityLogging } from '@/lib/middleware/activity-logger.middleware';
import { withAdminProtection } from '@/lib/auth/middleware';
import { format } from 'date-fns';

/**
 * GET /api/admin/activity-logs/export
 *
 * Export activity logs to CSV
 *
 * Query parameters:
 * - userId: Filter by user ID
 * - action: Filter by action
 * - entityType: Filter by entity type
 * - entityId: Filter by entity ID
 * - startDate: Filter by start date
 * - endDate: Filter by end date
 * - search: Search term
 * - format: Export format (csv)
 */
async function exportActivityLogs(req: NextRequest) {

  try {
    // Parse query parameters
    const url = new URL(req.url);

    // Parse filter parameters
    const userId = url.searchParams.get('userId') === 'all' ? undefined : url.searchParams.get('userId') || undefined;
    const action = url.searchParams.get('action') === 'all' ? undefined : url.searchParams.get('action') || undefined;
    const entityType = url.searchParams.get('entityType') === 'all' ? undefined : url.searchParams.get('entityType') || undefined;
    const entityId = url.searchParams.get('entityId') || undefined;
    const search = url.searchParams.get('search') || undefined;

    // Parse date parameters
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (url.searchParams.get('startDate')) {
      startDate = new Date(url.searchParams.get('startDate') as string);
    }

    if (url.searchParams.get('endDate')) {
      endDate = new Date(url.searchParams.get('endDate') as string);
      // Set end date to end of day
      endDate.setHours(23, 59, 59, 999);
    }

    // Get activity log service
    const activityLogService = getActivityLogService();

    // Fetch all logs with filters (no pagination)
    const { logs } = await activityLogService.findLogs(
      {
        userId,
        action,
        entityType,
        entityId,
        startDate,
        endDate,
        search,
      },
      0,
      10000 // Limit to 10,000 records for export
    );

    // Generate CSV
    const csvHeader = [
      'Timestamp',
      'Action',
      'User',
      'Entity Type',
      'Entity ID',
      'IP Address',
      'User Agent',
      'Details',
    ].join(',');

    const csvRows = logs.map(log => {
      // Format timestamp
      const timestamp = format(new Date(log.createdAt), 'yyyy-MM-dd HH:mm:ss');

      // Format user
      const user = (log as any).user?.name || 'System';

      // Format details
      let details = '';
      if (log.details) {
        try {
          details = typeof log.details === 'string'
            ? log.details
            : JSON.stringify(log.details);
        } catch (error) {
          details = String(log.details);
        }
        // Escape quotes and remove newlines for CSV
        details = details.replace(/"/g, '""').replace(/\n/g, ' ');
      }

      // Return CSV row
      return [
        `"${timestamp}"`,
        `"${log.action}"`,
        `"${user}"`,
        `"${log.entityType || ''}"`,
        `"${log.entityId || ''}"`,
        `"${log.ipAddress || ''}"`,
        `"${log.userAgent || ''}"`,
        `"${details}"`,
      ].join(',');
    });

    // Combine header and rows
    const csv = [csvHeader, ...csvRows].join('\n');

    // Generate filename
    const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
    const filename = `activity_logs_${now}.csv`;

    // Return CSV file
    return new NextResponse(csv, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error exporting activity logs:', error);
    return NextResponse.json(
      { error: 'Failed to export activity logs' },
      { status: 500 }
    );
  }
}

// Wrap the handler with admin protection and activity logging middleware
export const GET = withAdminProtection(
  withActivityLogging(exportActivityLogs, {
    action: 'export_activity_logs',
    entityType: 'activity_log',
  })
);
