"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlchars";
exports.ids = ["vendor-chunks/xmlchars"];
exports.modules = {

/***/ "(rsc)/./node_modules/xmlchars/xml/1.0/ed5.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlchars/xml/1.0/ed5.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n/**\n * Character classes and associated utilities for the 5th edition of XML 1.0.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n//\n// Fragments.\n//\nexports.CHAR = \"\\t\\n\\r -\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\";\nexports.S = \" \\t\\r\\n\";\n// tslint:disable-next-line:max-line-length\nexports.NAME_START_CHAR = \":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NAME_CHAR = \"-\" + exports.NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.CHAR_RE = new RegExp(\"^[\" + exports.CHAR + \"]$\", \"u\");\nexports.S_RE = new RegExp(\"^[\" + exports.S + \"]+$\", \"u\");\nexports.NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"]$\", \"u\");\nexports.NAME_CHAR_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]$\", \"u\");\nexports.NAME_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"][\" + exports.NAME_CHAR + \"]*$\", \"u\");\nexports.NMTOKEN_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]+$\", \"u\");\nvar TAB = 9;\nvar NL = 0xA;\nvar CR = 0xD;\nvar SPACE = 0x20;\n//\n// Lists.\n//\n/** All characters in the ``S`` production. */\nexports.S_LIST = [SPACE, NL, CR, TAB];\n/**\n * Determines whether a codepoint matches the ``CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR``.\n */\nfunction isChar(c) {\n  return c >= SPACE && c <= 0xD7FF || c === NL || c === CR || c === TAB || c >= 0xE000 && c <= 0xFFFD || c >= 0x10000 && c <= 0x10FFFF;\n}\nexports.isChar = isChar;\n/**\n * Determines whether a codepoint matches the ``S`` (space) production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``S``.\n */\nfunction isS(c) {\n  return c === SPACE || c === NL || c === CR || c === TAB;\n}\nexports.isS = isS;\n/**\n * Determines whether a codepoint matches the ``NAME_START_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_START_CHAR``.\n */\nfunction isNameStartChar(c) {\n  return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A || c === 0x3A || c === 0x5F || c === 0x200C || c === 0x200D || c >= 0xC0 && c <= 0xD6 || c >= 0xD8 && c <= 0xF6 || c >= 0x00F8 && c <= 0x02FF || c >= 0x0370 && c <= 0x037D || c >= 0x037F && c <= 0x1FFF || c >= 0x2070 && c <= 0x218F || c >= 0x2C00 && c <= 0x2FEF || c >= 0x3001 && c <= 0xD7FF || c >= 0xF900 && c <= 0xFDCF || c >= 0xFDF0 && c <= 0xFFFD || c >= 0x10000 && c <= 0xEFFFF;\n}\nexports.isNameStartChar = isNameStartChar;\n/**\n * Determines whether a codepoint matches the ``NAME_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_CHAR``.\n */\nfunction isNameChar(c) {\n  return isNameStartChar(c) || c >= 0x30 && c <= 0x39 || c === 0x2D || c === 0x2E || c === 0xB7 || c >= 0x0300 && c <= 0x036F || c >= 0x203F && c <= 0x2040;\n}\nexports.isNameChar = isNameChar;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlchars/xml/1.0/ed5.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlchars/xml/1.1/ed2.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlchars/xml/1.1/ed2.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n/**\n * Character classes and associated utilities for the 2nd edition of XML 1.1.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n//\n// Fragments.\n//\nexports.CHAR = \"\\u0001-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\";\nexports.RESTRICTED_CHAR = \"\\u0001-\\u0008\\u000B\\u000C\\u000E-\\u001F\\u007F-\\u0084\\u0086-\\u009F\";\nexports.S = \" \\t\\r\\n\";\n// tslint:disable-next-line:max-line-length\nexports.NAME_START_CHAR = \":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NAME_CHAR = \"-\" + exports.NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.CHAR_RE = new RegExp(\"^[\" + exports.CHAR + \"]$\", \"u\");\nexports.RESTRICTED_CHAR_RE = new RegExp(\"^[\" + exports.RESTRICTED_CHAR + \"]$\", \"u\");\nexports.S_RE = new RegExp(\"^[\" + exports.S + \"]+$\", \"u\");\nexports.NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"]$\", \"u\");\nexports.NAME_CHAR_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]$\", \"u\");\nexports.NAME_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"][\" + exports.NAME_CHAR + \"]*$\", \"u\");\nexports.NMTOKEN_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]+$\", \"u\");\nvar TAB = 9;\nvar NL = 0xA;\nvar CR = 0xD;\nvar SPACE = 0x20;\n//\n// Lists.\n//\n/** All characters in the ``S`` production. */\nexports.S_LIST = [SPACE, NL, CR, TAB];\n/**\n * Determines whether a codepoint matches the ``CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR``.\n */\nfunction isChar(c) {\n  return c >= 0x0001 && c <= 0xD7FF || c >= 0xE000 && c <= 0xFFFD || c >= 0x10000 && c <= 0x10FFFF;\n}\nexports.isChar = isChar;\n/**\n * Determines whether a codepoint matches the ``RESTRICTED_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``RESTRICTED_CHAR``.\n */\nfunction isRestrictedChar(c) {\n  return c >= 0x1 && c <= 0x8 || c === 0xB || c === 0xC || c >= 0xE && c <= 0x1F || c >= 0x7F && c <= 0x84 || c >= 0x86 && c <= 0x9F;\n}\nexports.isRestrictedChar = isRestrictedChar;\n/**\n * Determines whether a codepoint matches the ``CHAR`` production and does not\n * match the ``RESTRICTED_CHAR`` production. ``isCharAndNotRestricted(x)`` is\n * equivalent to ``isChar(x) && !isRestrictedChar(x)``. This function is faster\n * than running the two-call equivalent.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR`` and does not match\n * ``RESTRICTED_CHAR``.\n */\nfunction isCharAndNotRestricted(c) {\n  return c === 0x9 || c === 0xA || c === 0xD || c > 0x1F && c < 0x7F || c === 0x85 || c > 0x9F && c <= 0xD7FF || c >= 0xE000 && c <= 0xFFFD || c >= 0x10000 && c <= 0x10FFFF;\n}\nexports.isCharAndNotRestricted = isCharAndNotRestricted;\n/**\n * Determines whether a codepoint matches the ``S`` (space) production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``S``.\n */\nfunction isS(c) {\n  return c === SPACE || c === NL || c === CR || c === TAB;\n}\nexports.isS = isS;\n/**\n * Determines whether a codepoint matches the ``NAME_START_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_START_CHAR``.\n */\n// tslint:disable-next-line:cyclomatic-complexity\nfunction isNameStartChar(c) {\n  return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A || c === 0x3A || c === 0x5F || c === 0x200C || c === 0x200D || c >= 0xC0 && c <= 0xD6 || c >= 0xD8 && c <= 0xF6 || c >= 0x00F8 && c <= 0x02FF || c >= 0x0370 && c <= 0x037D || c >= 0x037F && c <= 0x1FFF || c >= 0x2070 && c <= 0x218F || c >= 0x2C00 && c <= 0x2FEF || c >= 0x3001 && c <= 0xD7FF || c >= 0xF900 && c <= 0xFDCF || c >= 0xFDF0 && c <= 0xFFFD || c >= 0x10000 && c <= 0xEFFFF;\n}\nexports.isNameStartChar = isNameStartChar;\n/**\n * Determines whether a codepoint matches the ``NAME_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_CHAR``.\n */\nfunction isNameChar(c) {\n  return isNameStartChar(c) || c >= 0x30 && c <= 0x39 || c === 0x2D || c === 0x2E || c === 0xB7 || c >= 0x0300 && c <= 0x036F || c >= 0x203F && c <= 0x2040;\n}\nexports.isNameChar = isNameChar;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlchars/xml/1.1/ed2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlchars/xmlns/1.0/ed3.js":
/*!************************************************!*\
  !*** ./node_modules/xmlchars/xmlns/1.0/ed3.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n/**\n * Character class utilities for XML NS 1.0 edition 3.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n//\n// Fragments.\n//\n// tslint:disable-next-line:max-line-length\nexports.NC_NAME_START_CHAR = \"A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NC_NAME_CHAR = \"-\" + exports.NC_NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.NC_NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NC_NAME_START_CHAR + \"]$\", \"u\");\nexports.NC_NAME_CHAR_RE = new RegExp(\"^[\" + exports.NC_NAME_CHAR + \"]$\", \"u\");\nexports.NC_NAME_RE = new RegExp(\"^[\" + exports.NC_NAME_START_CHAR + \"][\" + exports.NC_NAME_CHAR + \"]*$\", \"u\");\n/**\n * Determines whether a codepoint matches [[NC_NAME_START_CHAR]].\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches.\n */\n// tslint:disable-next-line:cyclomatic-complexity\nfunction isNCNameStartChar(c) {\n  return c >= 0x41 && c <= 0x5A || c === 0x5F || c >= 0x61 && c <= 0x7A || c >= 0xC0 && c <= 0xD6 || c >= 0xD8 && c <= 0xF6 || c >= 0x00F8 && c <= 0x02FF || c >= 0x0370 && c <= 0x037D || c >= 0x037F && c <= 0x1FFF || c >= 0x200C && c <= 0x200D || c >= 0x2070 && c <= 0x218F || c >= 0x2C00 && c <= 0x2FEF || c >= 0x3001 && c <= 0xD7FF || c >= 0xF900 && c <= 0xFDCF || c >= 0xFDF0 && c <= 0xFFFD || c >= 0x10000 && c <= 0xEFFFF;\n}\nexports.isNCNameStartChar = isNCNameStartChar;\n/**\n * Determines whether a codepoint matches [[NC_NAME_CHAR]].\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches.\n */\nfunction isNCNameChar(c) {\n  return isNCNameStartChar(c) || c === 0x2D || c === 0x2E || c >= 0x30 && c <= 0x39 || c === 0x00B7 || c >= 0x0300 && c <= 0x036F || c >= 0x203F && c <= 0x2040;\n}\nexports.isNCNameChar = isNCNameChar;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlchars/xmlns/1.0/ed3.js\n");

/***/ })

};
;