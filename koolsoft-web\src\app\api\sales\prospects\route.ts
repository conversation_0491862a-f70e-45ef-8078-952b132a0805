import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesProspectRepository } from '@/lib/repositories';
import { createSalesProspectSchema, salesFilterSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';

/**
 * GET /api/sales/prospects
 * Get sales prospects with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        status: searchParams.get('status') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
        search: searchParams.get('search') || undefined,
        skip: searchParams.get('skip') || undefined,
        take: searchParams.get('take') || undefined,
        sortBy: searchParams.get('sortBy') || undefined,
        sortOrder: searchParams.get('sortOrder') || undefined,
      };

      const validatedFilters = salesFilterSchema.parse(filters);

      const salesProspectRepository = getSalesProspectRepository();
      const result = await salesProspectRepository.findWithFilters(validatedFilters);

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error fetching sales prospects:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales prospects',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/sales/prospects
 * Create a new sales prospect
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createSalesProspectSchema.parse(body);

      const salesProspectRepository = getSalesProspectRepository();

      // Create sales prospect
      const salesProspect = await salesProspectRepository.create({
        customer: { connect: { id: validatedData.customerId } },
        executive: { connect: { id: validatedData.executiveId } },
        prospectDate: validatedData.prospectDate,
        contactPerson: validatedData.contactPerson,
        contactPhone: validatedData.contactPhone,
        status: validatedData.status,
        prospectPercentage: validatedData.prospectPercentage,
        followUpDate: validatedData.followUpDate,
        nextVisitDate: validatedData.nextVisitDate,
        lostReason: validatedData.lostReason,
        remarks: validatedData.remarks,
      });

      // Fetch the created prospect with relations
      const createdProspect = await salesProspectRepository.findById(salesProspect.id);

      return NextResponse.json({
        success: true,
        data: createdProspect,
        message: 'Sales prospect created successfully',
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating sales prospect:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create sales prospect',
        },
        { status: 500 }
      );
    }
  }
);
