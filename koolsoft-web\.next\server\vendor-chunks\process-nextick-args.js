"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/process-nextick-args";
exports.ids = ["vendor-chunks/process-nextick-args"];
exports.modules = {

/***/ "(rsc)/./node_modules/process-nextick-args/index.js":
/*!****************************************************!*\
  !*** ./node_modules/process-nextick-args/index.js ***!
  \****************************************************/
/***/ ((module) => {

eval("\n\nif (typeof process === 'undefined' || !process.version || process.version.indexOf('v0.') === 0 || process.version.indexOf('v1.') === 0 && process.version.indexOf('v1.8.') !== 0) {\n  module.exports = {\n    nextTick: nextTick\n  };\n} else {\n  module.exports = process;\n}\nfunction nextTick(fn, arg1, arg2, arg3) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('\"callback\" argument must be a function');\n  }\n  var len = arguments.length;\n  var args, i;\n  switch (len) {\n    case 0:\n    case 1:\n      return process.nextTick(fn);\n    case 2:\n      return process.nextTick(function afterTickOne() {\n        fn.call(null, arg1);\n      });\n    case 3:\n      return process.nextTick(function afterTickTwo() {\n        fn.call(null, arg1, arg2);\n      });\n    case 4:\n      return process.nextTick(function afterTickThree() {\n        fn.call(null, arg1, arg2, arg3);\n      });\n    default:\n      args = new Array(len - 1);\n      i = 0;\n      while (i < args.length) {\n        args[i++] = arguments[i];\n      }\n      return process.nextTick(function afterTick() {\n        fn.apply(null, args);\n      });\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/process-nextick-args/index.js\n");

/***/ })

};
;