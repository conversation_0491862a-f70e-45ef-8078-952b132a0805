'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Save, ArrowLeft, Eye } from 'lucide-react';
import { showSuccessToast, showErrorToast } from '@/lib/toast';
import { use } from 'react';

// Email Template interface
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  bodyHtml: string;
  bodyText: string;
  description?: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Email Template Edit Page
 *
 * This page allows administrators to edit email templates.
 */
export default function EmailTemplateEditPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const templateId = resolvedParams.id;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState<EmailTemplate | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    bodyHtml: '',
    bodyText: '',
    description: '',
    variables: [] as string[],
    isActive: true,
  });
  const router = useRouter();

  // Fetch template on component mount
  useEffect(() => {
    fetchTemplate();
  }, [templateId]);

  // Fetch template by ID
  const fetchTemplate = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/email/templates/${templateId}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}):`, errorText);
        showErrorToast(`Failed to fetch template: ${response.status} ${response.statusText}`);
        return;
      }

      const data = await response.json();
      console.log('Template data:', data);

      setTemplate(data);
      setFormData({
        name: data.name || '',
        subject: data.subject || '',
        bodyHtml: data.bodyHtml || '',
        bodyText: data.bodyText || '',
        description: data.description || '',
        variables: data.variables || [],
        isActive: data.isActive !== undefined ? data.isActive : true,
      });
    } catch (error) {
      console.error('Error fetching template:', error);
      showErrorToast(`Failed to fetch template: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Extract variables from template content
  const extractVariables = () => {
    const variableRegex = /{{([^{}]+)}}/g;
    const bodyVariables = [...formData.bodyHtml.matchAll(variableRegex)].map(match => match[1]);
    const subjectVariables = [...formData.subject.matchAll(variableRegex)].map(match => match[1]);
    const allVariables = [...new Set([...bodyVariables, ...subjectVariables])];

    setFormData(prev => ({ ...prev, variables: allVariables }));
  };

  // Save template
  const saveTemplate = async () => {
    try {
      setSaving(true);

      // Extract variables before saving
      extractVariables();

      const response = await fetch(`/api/email/templates/${templateId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error saving template:', errorData);
        showErrorToast(errorData.error || 'Failed to save template');
        return;
      }

      const updatedTemplate = await response.json();
      setTemplate(updatedTemplate);
      showSuccessToast('Template saved successfully');
    } catch (error) {
      console.error('Error saving template:', error);
      showErrorToast(`Failed to save template: ${(error as Error).message}`);
    } finally {
      setSaving(false);
    }
  };

  // Navigate to preview page
  const goToPreview = () => {
    router.push('/admin/email/preview');
  };

  // Go back to templates list
  const goBack = () => {
    router.push('/admin/email/templates');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-black">Edit Email Template</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button variant="outline" onClick={goToPreview}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : template ? (
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-black">Template Details</CardTitle>
              <CardDescription>
                Edit the email template details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-black">Template Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="text-black"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-black">Subject</Label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="text-black"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="text-black">Description</Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="text-black"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bodyHtml" className="text-black">HTML Content</Label>
                  <Textarea
                    id="bodyHtml"
                    name="bodyHtml"
                    value={formData.bodyHtml}
                    onChange={handleInputChange}
                    className="text-black min-h-[200px] font-mono"
                    rows={10}
                  />
                  <p className="text-sm text-gray-500">
                    Use {'{{'}<span>variableName</span>{'}}'} syntax for dynamic content
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bodyText" className="text-black">Plain Text Content</Label>
                  <Textarea
                    id="bodyText"
                    name="bodyText"
                    value={formData.bodyText}
                    onChange={handleInputChange}
                    className="text-black min-h-[150px] font-mono"
                    rows={6}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <Label htmlFor="isActive" className="text-black">Active</Label>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={saveTemplate}
                    disabled={saving}
                    className="w-full md:w-auto"
                  >
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    Save Template
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">Template not found</p>
          <Button variant="outline" onClick={goBack} className="mt-4">
            Go Back
          </Button>
        </div>
      )}
    </div>
  );
}
