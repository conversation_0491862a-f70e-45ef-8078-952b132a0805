'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  MoreHorizontal,
  Eye,
} from 'lucide-react';
import { formatReportDate, formatReportCurrency, formatReportNumber } from '@/lib/utils/report-utils';

interface ReportDataTableProps {
  data: any[];
  reportType: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
}

interface ColumnConfig {
  key: string;
  label: string;
  sortable?: boolean;
  type?: 'text' | 'date' | 'currency' | 'number' | 'status' | 'boolean';
  width?: string;
}

/**
 * Report Data Table Component
 * 
 * Displays report data in a sortable, paginated table format.
 * Automatically configures columns based on report type and data structure.
 */
export function ReportDataTable({
  data,
  reportType,
  pagination,
  onPageChange,
  onSortChange,
}: ReportDataTableProps) {
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Define column configurations for different report types
  const getColumnConfig = (reportType: string): ColumnConfig[] => {
    switch (reportType) {
      case 'AMC':
        return [
          { key: 'customer.name', label: 'Customer', sortable: true, type: 'text' },
          { key: 'startDate', label: 'Start Date', sortable: true, type: 'date' },
          { key: 'endDate', label: 'End Date', sortable: true, type: 'date' },
          { key: 'amount', label: 'Amount', sortable: true, type: 'currency' },
          { key: 'status', label: 'Status', sortable: true, type: 'status' },
          { key: 'users.name', label: 'Executive', sortable: true, type: 'text' },
          { key: 'actions', label: 'Actions', sortable: false, width: '100px' },
        ];

      case 'WARRANTY':
        return [
          { key: 'customer.name', label: 'Customer', sortable: true, type: 'text' },
          { key: 'installDate', label: 'Install Date', sortable: true, type: 'date' },
          { key: 'warrantyDate', label: 'Warranty Date', sortable: true, type: 'date' },
          { key: 'status', label: 'Status', sortable: true, type: 'status' },
          { key: 'users.name', label: 'Executive', sortable: true, type: 'text' },
          { key: 'actions', label: 'Actions', sortable: false, width: '100px' },
        ];

      case 'SERVICE':
        return [
          { key: 'customer.name', label: 'Customer', sortable: true, type: 'text' },
          { key: 'reportDate', label: 'Report Date', sortable: true, type: 'date' },
          { key: 'visitDate', label: 'Visit Date', sortable: true, type: 'date' },
          { key: 'natureOfService', label: 'Nature of Service', sortable: true, type: 'text' },
          { key: 'status', label: 'Status', sortable: true, type: 'status' },
          { key: 'executive.name', label: 'Executive', sortable: true, type: 'text' },
          { key: 'actions', label: 'Actions', sortable: false, width: '100px' },
        ];

      case 'SALES':
        return [
          { key: 'customer.name', label: 'Customer', sortable: true, type: 'text' },
          { key: 'orderDate', label: 'Order Date', sortable: true, type: 'date' },
          { key: 'amount', label: 'Amount', sortable: true, type: 'currency' },
          { key: 'status', label: 'Status', sortable: true, type: 'status' },
          { key: 'executive.name', label: 'Executive', sortable: true, type: 'text' },
          { key: 'actions', label: 'Actions', sortable: false, width: '100px' },
        ];

      case 'CUSTOMER':
        return [
          { key: 'name', label: 'Name', sortable: true, type: 'text' },
          { key: 'city', label: 'City', sortable: true, type: 'text' },
          { key: 'state', label: 'State', sortable: true, type: 'text' },
          { key: 'phone', label: 'Phone', sortable: false, type: 'text' },
          { key: 'isActive', label: 'Active', sortable: true, type: 'boolean' },
          { key: 'actions', label: 'Actions', sortable: false, width: '100px' },
        ];

      default:
        // Auto-generate columns from data if no specific config
        if (data.length > 0) {
          return Object.keys(data[0])
            .filter(key => !key.startsWith('_') && typeof data[0][key] !== 'object')
            .map(key => ({
              key,
              label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
              sortable: true,
              type: 'text' as const,
            }));
        }
        return [];
    }
  };

  const columns = getColumnConfig(reportType);

  // Get nested value from object using dot notation
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Format cell value based on type
  const formatCellValue = (value: any, type: string) => {
    if (value === null || value === undefined) return '-';

    switch (type) {
      case 'date':
        return formatReportDate(value);
      case 'currency':
        return formatReportCurrency(value);
      case 'number':
        return formatReportNumber(value);
      case 'boolean':
        return value ? 'Yes' : 'No';
      case 'status':
        return (
          <Badge 
            variant={
              value === 'ACTIVE' || value === 'COMPLETED' ? 'default' :
              value === 'PENDING' || value === 'IN_PROGRESS' ? 'secondary' :
              value === 'EXPIRED' || value === 'CANCELLED' ? 'destructive' :
              'outline'
            }
          >
            {String(value).replace('_', ' ')}
          </Badge>
        );
      default:
        return String(value);
    }
  };

  // Handle sort
  const handleSort = (columnKey: string) => {
    let newSortOrder: 'asc' | 'desc' = 'asc';
    
    if (sortBy === columnKey) {
      newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    }
    
    setSortBy(columnKey);
    setSortOrder(newSortOrder);
    onSortChange(columnKey, newSortOrder);
  };

  // Render sort icon
  const renderSortIcon = (columnKey: string) => {
    if (sortBy !== columnKey) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortOrder === 'asc' ? 
      <ArrowUp className="h-4 w-4" /> : 
      <ArrowDown className="h-4 w-4" />;
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      onPageChange(newPage);
    }
  };

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead 
                  key={column.key}
                  style={{ width: column.width }}
                  className={column.sortable ? 'cursor-pointer hover:bg-gray-50' : ''}
                  onClick={column.sortable ? () => handleSort(column.key) : undefined}
                >
                  <div className="flex items-center space-x-2">
                    <span>{column.label}</span>
                    {column.sortable && renderSortIcon(column.key)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={row.id || index}>
                {columns.map((column) => (
                  <TableCell key={column.key}>
                    {column.key === 'actions' ? (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : (
                      formatCellValue(
                        getNestedValue(row, column.key),
                        column.type || 'text'
                      )
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} results
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
              disabled={pagination.page === 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.totalPages)}
              disabled={pagination.page === pagination.totalPages}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
