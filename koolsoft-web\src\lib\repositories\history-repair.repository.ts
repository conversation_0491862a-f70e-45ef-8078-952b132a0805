/**
 * History Repair Repository
 *
 * This repository handles database operations for repair history records.
 * It provides methods for CRUD operations and specialized queries for repair tracking.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { PrismaRepository } from './prisma.repository';
import { HistoryRepair, HistoryFilterOptions } from '@/types/history.types';

export class HistoryRepairRepository extends PrismaRepository<
  Prisma.HistoryRepairGetPayload<{}>,
  string,
  Prisma.HistoryRepairCreateInput,
  Prisma.HistoryRepairUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('historyRepair');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  createTransactionRepository(tx: any): HistoryRepairRepository {
    return new HistoryRepairRepository(tx);
  }

  /**
   * Generic findMany method for flexible queries
   * @param options Query options
   * @returns Promise resolving to repairs
   */
  async findMany(options?: {
    where?: Prisma.HistoryRepairWhereInput;
    include?: Prisma.HistoryRepairInclude;
    orderBy?: Prisma.HistoryRepairOrderByWithRelationInput;
    skip?: number;
    take?: number;
  }): Promise<Prisma.HistoryRepairGetPayload<{ include: { historyCard: true } }>[]> {
    return this.model.findMany({
      ...options,
      include: {
        historyCard: {
          include: {
            customer: true,
          },
        },
        ...options?.include,
      },
    });
  }

  /**
   * Find repairs by history card ID
   * @param historyCardId History card ID
   * @param options Filter options
   * @returns Promise resolving to repairs
   */
  async findByHistoryCardId(
    historyCardId: string,
    options?: HistoryFilterOptions
  ): Promise<Prisma.HistoryRepairGetPayload<{ include: { historyCard: true } }>[]> {
    const where: Prisma.HistoryRepairWhereInput = {
      historyCardId,
      ...(options?.dateFrom && { repairDate: { gte: new Date(options.dateFrom) } }),
      ...(options?.dateTo && { repairDate: { lte: new Date(options.dateTo) } }),
      ...(options?.technicianId && { technicianId: options.technicianId }),
    };

    return this.model.findMany({
      where,
      include: {
        historyCard: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                address: true,
                city: true,
                state: true,
                phone: true,
                email: true,
              }
            }
          }
        }
      },
      orderBy: { repairDate: options?.sortOrder || 'desc' },
      skip: options?.page ? (options.page - 1) * (options.limit || 10) : undefined,
      take: options?.limit || 10,
    });
  }

  /**
   * Find repairs by customer ID
   * @param customerId Customer ID
   * @param options Filter options
   * @returns Promise resolving to repairs
   */
  async findByCustomerId(
    customerId: string, 
    options?: HistoryFilterOptions
  ): Promise<Prisma.HistoryRepairGetPayload<{ include: { technician: true, historyCard: true } }>[]> {
    const where: Prisma.HistoryRepairWhereInput = {
      historyCard: { customerId },
      ...(options?.dateFrom && { repairDate: { gte: new Date(options.dateFrom) } }),
      ...(options?.dateTo && { repairDate: { lte: new Date(options.dateTo) } }),
      ...(options?.status && { status: { in: options.status } }),
      ...(options?.priority && { priority: { in: options.priority } }),
      ...(options?.technicianId && { technicianId: options.technicianId }),
    };

    return this.model.findMany({
      where,
      include: {
        technician: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          }
        },
        historyCard: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                address: true,
                city: true,
                state: true,
                phone: true,
                email: true,
              }
            }
          }
        }
      },
      orderBy: { repairDate: options?.sortOrder || 'desc' },
      skip: options?.page ? (options.page - 1) * (options.limit || 10) : undefined,
      take: options?.limit || 10,
    });
  }

  /**
   * Find repairs by technician ID
   * @param technicianId Technician ID
   * @param options Filter options
   * @returns Promise resolving to repairs
   */
  async findByTechnicianId(
    technicianId: string, 
    options?: HistoryFilterOptions
  ): Promise<Prisma.HistoryRepairGetPayload<{ include: { technician: true, historyCard: true } }>[]> {
    const where: Prisma.HistoryRepairWhereInput = {
      technicianId,
      ...(options?.dateFrom && { repairDate: { gte: new Date(options.dateFrom) } }),
      ...(options?.dateTo && { repairDate: { lte: new Date(options.dateTo) } }),
      ...(options?.status && { status: { in: options.status } }),
      ...(options?.priority && { priority: { in: options.priority } }),
    };

    return this.model.findMany({
      where,
      include: {
        technician: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          }
        },
        historyCard: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                address: true,
                city: true,
                state: true,
                phone: true,
                email: true,
              }
            }
          }
        }
      },
      orderBy: { repairDate: options?.sortOrder || 'desc' },
      skip: options?.page ? (options.page - 1) * (options.limit || 10) : undefined,
      take: options?.limit || 10,
    });
  }

  /**
   * Get repair statistics for a customer
   * @param customerId Customer ID
   * @returns Promise resolving to repair statistics
   */
  async getRepairStatistics(customerId: string): Promise<{
    totalRepairs: number;
    completedRepairs: number;
    pendingRepairs: number;
    averageRepairTime: number;
    totalCost: number;
  }> {
    const [
      totalRepairs,
      costData
    ] = await Promise.all([
      this.model.count({
        where: { historyCard: { customerId } }
      }),
      this.model.aggregate({
        where: {
          historyCard: { customerId },
          cost: { not: null }
        },
        _sum: { cost: true }
      })
    ]);

    const totalCost = Number(costData._sum.cost || 0);

    return {
      totalRepairs,
      completedRepairs: totalRepairs, // Assume all are completed for now
      pendingRepairs: 0,
      averageRepairTime: 0, // Not available in current schema
      totalCost
    };
  }

  /**
   * Find recent repairs
   * @param options Filter options
   * @returns Promise resolving to recent repairs
   */
  async findRecentRepairs(
    options?: HistoryFilterOptions
  ): Promise<Prisma.HistoryRepairGetPayload<{ include: { historyCard: true } }>[]> {
    const where: Prisma.HistoryRepairWhereInput = {
      repairDate: { not: null },
      ...(options?.technicianId && { technicianId: options.technicianId }),
    };

    return this.model.findMany({
      where,
      include: {
        historyCard: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                address: true,
                city: true,
                state: true,
                phone: true,
                email: true,
              }
            }
          }
        }
      },
      orderBy: { repairDate: 'desc' },
      skip: options?.page ? (options.page - 1) * (options.limit || 10) : undefined,
      take: options?.limit || 10,
    });
  }
}
