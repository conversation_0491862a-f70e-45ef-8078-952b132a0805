-- Create Formula Engine Tables
-- This script creates the necessary tables for the Report Formula Engine

-- Create report_formulas table
CREATE TABLE IF NOT EXISTS report_formulas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    formula TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'CUSTOM' CHECK (category IN ('MATHEMATICAL', 'STATISTICAL', 'BUSINESS', 'CUSTOM')),
    is_template BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    variables JSONB,
    return_type VARCHAR(20) DEFAULT 'NUMBER' CHECK (return_type IN ('NUMBER', 'STRING', 'BOOLEAN', 'DATE')),
    complexity INTEGER DEFAULT 1 CHECK (complexity >= 1 AND complexity <= 100),
    usage_count INTEGER DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for report_formulas
CREATE INDEX IF NOT EXISTS idx_report_formulas_category ON report_formulas(category);
CREATE INDEX IF NOT EXISTS idx_report_formulas_is_template ON report_formulas(is_template);
CREATE INDEX IF NOT EXISTS idx_report_formulas_is_active ON report_formulas(is_active);
CREATE INDEX IF NOT EXISTS idx_report_formulas_created_by ON report_formulas(created_by);
CREATE INDEX IF NOT EXISTS idx_report_formulas_usage_count ON report_formulas(usage_count);

-- Create report_formula_fields table
CREATE TABLE IF NOT EXISTS report_formula_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type VARCHAR(20) NOT NULL CHECK (report_type IN ('AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER')),
    field_name VARCHAR(50) NOT NULL,
    field_label VARCHAR(100) NOT NULL,
    formula_id UUID NOT NULL REFERENCES report_formulas(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(report_type, field_name)
);

-- Create indexes for report_formula_fields
CREATE INDEX IF NOT EXISTS idx_report_formula_fields_report_type ON report_formula_fields(report_type);
CREATE INDEX IF NOT EXISTS idx_report_formula_fields_formula_id ON report_formula_fields(formula_id);
CREATE INDEX IF NOT EXISTS idx_report_formula_fields_is_active ON report_formula_fields(is_active);

-- Create formula_test_cases table
CREATE TABLE IF NOT EXISTS formula_test_cases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    formula_id UUID NOT NULL REFERENCES report_formulas(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    input_data JSONB NOT NULL,
    expected_result JSONB NOT NULL,
    actual_result JSONB,
    passed BOOLEAN,
    last_run_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for formula_test_cases
CREATE INDEX IF NOT EXISTS idx_formula_test_cases_formula_id ON formula_test_cases(formula_id);
CREATE INDEX IF NOT EXISTS idx_formula_test_cases_passed ON formula_test_cases(passed);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_report_formulas_updated_at 
    BEFORE UPDATE ON report_formulas 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_report_formula_fields_updated_at 
    BEFORE UPDATE ON report_formula_fields 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_formula_test_cases_updated_at 
    BEFORE UPDATE ON formula_test_cases 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default formula templates
INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
SELECT 
    'Percentage Calculation',
    'Calculate percentage of value relative to total',
    'PERCENTAGE(value, total)',
    'MATHEMATICAL',
    TRUE,
    '["value", "total"]'::jsonb,
    'NUMBER',
    (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE name = 'Percentage Calculation');

INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
SELECT 
    'Growth Rate',
    'Calculate growth percentage between current and previous values',
    'GROWTH(current, previous)',
    'MATHEMATICAL',
    TRUE,
    '["current", "previous"]'::jsonb,
    'NUMBER',
    (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE name = 'Growth Rate');

INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
SELECT 
    'Profit Margin',
    'Calculate profit margin percentage',
    'MARGIN(revenue, cost)',
    'BUSINESS',
    TRUE,
    '["revenue", "cost"]'::jsonb,
    'NUMBER',
    (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE name = 'Profit Margin');

INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
SELECT 
    'Tax Calculation',
    'Calculate tax amount',
    'TAX(amount, taxPercent)',
    'BUSINESS',
    TRUE,
    '["amount", "taxPercent"]'::jsonb,
    'NUMBER',
    (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE name = 'Tax Calculation');

INSERT INTO report_formulas (name, description, formula, category, is_template, variables, return_type, created_by)
SELECT 
    'Average Calculation',
    'Calculate average of multiple values',
    'AVERAGE(value1, value2, value3)',
    'STATISTICAL',
    TRUE,
    '["value1", "value2", "value3"]'::jsonb,
    'NUMBER',
    (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)
WHERE NOT EXISTS (SELECT 1 FROM report_formulas WHERE name = 'Average Calculation');

-- Verify tables were created
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('report_formulas', 'report_formula_fields', 'formula_test_cases')
ORDER BY table_name, ordinal_position;
