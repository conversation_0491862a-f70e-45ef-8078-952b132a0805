import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesNotificationService } from '@/lib/services/sales-notification.service';
import { updateNotificationPreferencesSchema } from '@/lib/validations/notification.schema';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';

/**
 * GET /api/notifications/preferences
 * Get notification preferences for the current user
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'User session not found' },
          { status: 401 }
        );
      }

      const salesNotificationService = getSalesNotificationService();
      const preferences = await salesNotificationService.getNotificationPreferences(session.user.id);

      return NextResponse.json({
        success: true,
        data: preferences,
      });
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch notification preferences',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/notifications/preferences
 * Update notification preferences for the current user
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'User session not found' },
          { status: 401 }
        );
      }

      const body = await request.json();
      
      // Validate request body
      const validatedData = updateNotificationPreferencesSchema.parse(body);

      const salesNotificationService = getSalesNotificationService();
      const updatedPreferences = await salesNotificationService.updateNotificationPreferences(
        session.user.id,
        validatedData
      );

      return NextResponse.json({
        success: true,
        data: updatedPreferences,
        message: 'Notification preferences updated successfully',
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update notification preferences',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/notifications/preferences/reset
 * Reset notification preferences to defaults based on user role
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'User session not found' },
          { status: 401 }
        );
      }

      const salesNotificationService = getSalesNotificationService();
      
      // Get user to determine role
      const { getUserRepository } = await import('@/lib/repositories');
      const userRepository = getUserRepository();
      const user = await userRepository.findById(session.user.id);
      
      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      // Get default preferences for user role
      const { getNotificationPreferenceRepository } = await import('@/lib/repositories');
      const notificationPreferenceRepository = getNotificationPreferenceRepository();
      const defaultPreferences = notificationPreferenceRepository.getDefaultPreferences(user.role);

      // Update preferences with defaults
      const updatedPreferences = await salesNotificationService.updateNotificationPreferences(
        session.user.id,
        defaultPreferences
      );

      return NextResponse.json({
        success: true,
        data: updatedPreferences,
        message: 'Notification preferences reset to defaults',
      });
    } catch (error) {
      console.error('Error resetting notification preferences:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to reset notification preferences',
        },
        { status: 500 }
      );
    }
  }
);
