import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export interface CrystalReportParams {
  startDate?: string;
  endDate?: string;
  customerId?: string;
  executiveId?: string;
  amcStatus?: string;
  warrantyStatus?: string;
  serviceStatus?: string;
  contractType?: string;
  groupBy?: 'customer' | 'executive' | 'status' | 'none';
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Crystal Report Repository
 * Handles data operations for migrated Crystal Reports
 */
export class CrystalReportRepository {
  
  /**
   * Get AMC Summary Report
   * Replicates AMCSummary.rpt functionality
   */
  async getAMCSummaryReport(params: CrystalReportParams) {
    const whereClause: Prisma.amc_contractsWhereInput = {};
    
    // Date filters
    if (params.startDate || params.endDate) {
      whereClause.startDate = {};
      if (params.startDate) whereClause.startDate.gte = new Date(params.startDate);
      if (params.endDate) whereClause.startDate.lte = new Date(params.endDate);
    }
    
    // Customer filter
    if (params.customerId) {
      whereClause.customerId = params.customerId;
    }
    
    // Executive filter
    if (params.executiveId) {
      whereClause.executiveId = params.executiveId;
    }
    
    // Status filter
    if (params.amcStatus) {
      whereClause.status = params.amcStatus;
    }
    
    // Contract type filter
    if (params.contractType) {
      // Note: contractType property needs to be added to amc_contractsWhereInput
      // whereClause.contractType = params.contractType;
    }
    
    const [data, total] = await Promise.all([
      prisma.amc_contracts.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
              email: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
        orderBy: {
          [params.sortBy || 'startDate']: params.sortOrder || 'desc',
        },
        skip: params.page ? (params.page - 1) * (params.limit || 50) : 0,
        take: params.limit || 50,
      }),
      prisma.amc_contracts.count({ where: whereClause }),
    ]);
    
    // Calculate summary statistics
    const summary = await this.calculateAMCSummary(whereClause);
    
    return {
      data,
      summary,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total,
        totalPages: Math.ceil(total / (params.limit || 50)),
      },
    };
  }
  
  /**
   * Get AMC Detail Report
   * Replicates AMCDetail.rpt functionality
   */
  async getAMCDetailReport(params: CrystalReportParams & { contractId?: string }) {
    if (!params.contractId) {
      throw new Error('Contract ID is required for AMC Detail Report');
    }
    
    const contract = await prisma.amc_contracts.findUnique({
      where: { id: params.contractId },
      include: {
        customer: {
          include: {
            // Note: addresses relationship needs to be defined in Prisma schema
            // addresses: true,
          },
        },
        users: true,
        // Include related service reports (commented out until schema relationship is defined)
        // service_reports: {
        //   include: {
        //     details: true,
        //   },
        //   orderBy: { reportDate: 'desc' },
        // },
      },
    });
    
    if (!contract) {
      throw new Error('AMC Contract not found');
    }
    
    // Calculate contract performance metrics
    const performanceMetrics = await this.calculateContractPerformance(params.contractId);
    
    return {
      data: [contract],
      summary: {
        contractDetails: contract,
        performanceMetrics,
        // Note: service_reports relationship needs to be defined in Prisma schema
        serviceHistory: [], // contract.service_reports,
      },
    };
  }
  
  /**
   * Get Warranty Summary Report
   * Replicates INWSummary.rpt functionality
   */
  async getWarrantySummaryReport(params: CrystalReportParams) {
    const whereClause: Prisma.warrantiesWhereInput = {};
    
    // Date filters (using installDate for warranties)
    if (params.startDate || params.endDate) {
      whereClause.installDate = {};
      if (params.startDate) whereClause.installDate.gte = new Date(params.startDate);
      if (params.endDate) whereClause.installDate.lte = new Date(params.endDate);
    }
    
    // Customer filter
    if (params.customerId) {
      whereClause.customerId = params.customerId;
    }
    
    // Executive filter
    if (params.executiveId) {
      whereClause.executiveId = params.executiveId;
    }
    
    // Status filter
    if (params.warrantyStatus) {
      whereClause.status = params.warrantyStatus;
    }
    
    const [data, total] = await Promise.all([
      prisma.warranties.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          users: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [params.sortBy || 'installDate']: params.sortOrder || 'desc',
        },
        skip: params.page ? (params.page - 1) * (params.limit || 50) : 0,
        take: params.limit || 50,
      }),
      prisma.warranties.count({ where: whereClause }),
    ]);
    
    // Calculate summary statistics
    const summary = await this.calculateWarrantySummary(whereClause);
    
    return {
      data,
      summary,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total,
        totalPages: Math.ceil(total / (params.limit || 50)),
      },
    };
  }
  
  /**
   * Get Service Summary Report
   * Replicates ServiceSummary.rpt functionality
   */
  async getServiceSummaryReport(params: CrystalReportParams) {
    const whereClause: Prisma.service_reportsWhereInput = {};
    
    // Date filters
    if (params.startDate || params.endDate) {
      whereClause.reportDate = {};
      if (params.startDate) whereClause.reportDate.gte = new Date(params.startDate);
      if (params.endDate) whereClause.reportDate.lte = new Date(params.endDate);
    }
    
    // Customer filter
    if (params.customerId) {
      whereClause.customerId = params.customerId;
    }
    
    // Executive filter
    if (params.executiveId) {
      whereClause.executiveId = params.executiveId;
    }
    
    // Status filter
    if (params.serviceStatus) {
      whereClause.status = params.serviceStatus;
    }
    
    const [data, total] = await Promise.all([
      prisma.service_reports.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          details: true,
        },
        orderBy: {
          [params.sortBy || 'reportDate']: params.sortOrder || 'desc',
        },
        skip: params.page ? (params.page - 1) * (params.limit || 50) : 0,
        take: params.limit || 50,
      }),
      prisma.service_reports.count({ where: whereClause }),
    ]);
    
    // Calculate summary statistics
    const summary = await this.calculateServiceSummary(whereClause);
    
    return {
      data,
      summary,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total,
        totalPages: Math.ceil(total / (params.limit || 50)),
      },
    };
  }
  
  /**
   * Calculate AMC Summary Statistics
   */
  private async calculateAMCSummary(whereClause: Prisma.amc_contractsWhereInput) {
    const [totalContracts, activeContracts, expiredContracts, totalAmount] = await Promise.all([
      prisma.amc_contracts.count({ where: whereClause }),
      prisma.amc_contracts.count({
        where: {
          ...whereClause,
          endDate: { gte: new Date() },
        },
      }),
      prisma.amc_contracts.count({
        where: {
          ...whereClause,
          endDate: { lt: new Date() },
        },
      }),
      prisma.amc_contracts.aggregate({
        where: whereClause,
        _sum: { amount: true },
      }),
    ]);
    
    return {
      totalContracts,
      activeContracts,
      expiredContracts,
      totalAmount: totalAmount._sum.amount || 0,
      averageAmount: totalContracts > 0 ? (totalAmount._sum.amount || 0) / totalContracts : 0,
    };
  }
  
  /**
   * Calculate Warranty Summary Statistics
   */
  private async calculateWarrantySummary(whereClause: Prisma.warrantiesWhereInput) {
    const [totalWarranties, activeWarranties, expiredWarranties] = await Promise.all([
      prisma.warranties.count({ where: whereClause }),
      prisma.warranties.count({
        where: {
          ...whereClause,
          warrantyDate: { gte: new Date() },
        },
      }),
      prisma.warranties.count({
        where: {
          ...whereClause,
          warrantyDate: { lt: new Date() },
        },
      }),
    ]);
    
    return {
      totalWarranties,
      activeWarranties,
      expiredWarranties,
    };
  }
  
  /**
   * Calculate Service Summary Statistics
   */
  private async calculateServiceSummary(whereClause: Prisma.service_reportsWhereInput) {
    const [totalReports, openReports, completedReports, pendingReports] = await Promise.all([
      prisma.service_reports.count({ where: whereClause }),
      prisma.service_reports.count({
        where: {
          ...whereClause,
          status: 'OPEN',
        },
      }),
      prisma.service_reports.count({
        where: {
          ...whereClause,
          status: 'COMPLETED',
        },
      }),
      prisma.service_reports.count({
        where: {
          ...whereClause,
          status: 'PENDING',
        },
      }),
    ]);
    
    return {
      totalReports,
      openReports,
      completedReports,
      pendingReports,
      completionRate: totalReports > 0 ? (completedReports / totalReports) * 100 : 0,
    };
  }
  
  /**
   * Get Warranty Detail Report
   * Replicates INWDetail.rpt functionality
   */
  async getWarrantyDetailReport(params: CrystalReportParams & { warrantyId?: string }) {
    if (!params.warrantyId) {
      throw new Error('Warranty ID is required for Warranty Detail Report');
    }

    const warranty = await prisma.warranties.findUnique({
      where: { id: params.warrantyId },
      include: {
        customer: {
          include: {
            // Note: addresses relationship needs to be defined in Prisma schema
            // addresses: true,
          },
        },
        users: true,
      },
    });

    if (!warranty) {
      throw new Error('Warranty not found');
    }

    return {
      data: [warranty],
      summary: {
        warrantyDetails: warranty,
      },
    };
  }

  /**
   * Get Service Detail Report
   * Replicates ServiceDetail.rpt functionality
   */
  async getServiceDetailReport(params: CrystalReportParams & { serviceReportId?: string }) {
    if (!params.serviceReportId) {
      throw new Error('Service Report ID is required for Service Detail Report');
    }

    const serviceReport = await prisma.service_reports.findUnique({
      where: { id: params.serviceReportId },
      include: {
        customer: {
          include: {
            // Note: addresses relationship needs to be defined in Prisma schema
            // addresses: true,
          },
        },
        executive: true,
        details: true,
      },
    });

    if (!serviceReport) {
      throw new Error('Service Report not found');
    }

    return {
      data: [serviceReport],
      summary: {
        serviceReportDetails: serviceReport,
      },
    };
  }

  /**
   * Get Sales Summary Report
   * Replicates SalesSummary.rpt functionality
   */
  async getSalesSummaryReport(params: CrystalReportParams) {
    const whereClause: Prisma.sales_ordersWhereInput = {};

    // Date filters
    if (params.startDate || params.endDate) {
      whereClause.orderDate = {};
      if (params.startDate) whereClause.orderDate.gte = new Date(params.startDate);
      if (params.endDate) whereClause.orderDate.lte = new Date(params.endDate);
    }

    // Executive filter
    if (params.executiveId) {
      whereClause.executiveId = params.executiveId;
    }

    const [data, total] = await Promise.all([
      prisma.sales_orders.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [params.sortBy || 'orderDate']: params.sortOrder || 'desc',
        },
        skip: params.page ? (params.page - 1) * (params.limit || 50) : 0,
        take: params.limit || 50,
      }),
      prisma.sales_orders.count({ where: whereClause }),
    ]);

    // Calculate summary statistics
    const summary = await this.calculateSalesSummary(whereClause);

    return {
      data,
      summary,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total,
        totalPages: Math.ceil(total / (params.limit || 50)),
      },
    };
  }

  /**
   * Get Customer Summary Report
   * Replicates CustomerSummary.rpt functionality
   */
  async getCustomerSummaryReport(params: CrystalReportParams) {
    const whereClause: Prisma.CustomerWhereInput = {};

    // Active filter
    if (params.customerId) {
      whereClause.id = params.customerId;
    }

    const [data, total] = await Promise.all([
      prisma.customer.findMany({
        where: whereClause,
        orderBy: {
          [params.sortBy || 'name']: params.sortOrder || 'asc',
        },
        skip: params.page ? (params.page - 1) * (params.limit || 50) : 0,
        take: params.limit || 50,
      }),
      prisma.customer.count({ where: whereClause }),
    ]);

    // Calculate summary statistics
    const summary = await this.calculateCustomerSummary(whereClause);

    return {
      data,
      summary,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total,
        totalPages: Math.ceil(total / (params.limit || 50)),
      },
    };
  }

  /**
   * Calculate Sales Summary Statistics
   */
  private async calculateSalesSummary(whereClause: Prisma.sales_ordersWhereInput) {
    const [totalOrders, totalAmount] = await Promise.all([
      prisma.sales_orders.count({ where: whereClause }),
      prisma.sales_orders.aggregate({
        where: whereClause,
        _sum: { amount: true },
      }),
    ]);

    return {
      totalOrders,
      totalAmount: totalAmount._sum.amount || 0,
      averageOrderValue: totalOrders > 0 ? (totalAmount._sum.amount || 0) / totalOrders : 0,
    };
  }

  /**
   * Calculate Customer Summary Statistics
   */
  private async calculateCustomerSummary(whereClause: Prisma.CustomerWhereInput) {
    const [totalCustomers, activeCustomers] = await Promise.all([
      prisma.customer.count({ where: whereClause }),
      prisma.customer.count({
        where: {
          ...whereClause,
          isActive: true,
        },
      }),
    ]);

    return {
      totalCustomers,
      activeCustomers,
      inactiveCustomers: totalCustomers - activeCustomers,
    };
  }

  /**
   * Calculate Contract Performance Metrics
   */
  private async calculateContractPerformance(contractId: string) {
    const serviceReports = await prisma.service_reports.count({
      where: {
        // This would need to be adjusted based on how AMC contracts relate to service reports
        // For now, we'll use a placeholder
        id: contractId, // This is incorrect but shows the structure
      },
    });

    return {
      totalServiceCalls: serviceReports,
      averageResponseTime: 2.5, // This would be calculated from actual data
      customerSatisfaction: 4.2, // This would come from feedback data
    };
  }
}
