import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import {
  ScheduledReportExecutionData,
  ListExecutionsParams,
  ExecutionStatus,
} from '@/lib/validations/scheduled-report.schema';

/**
 * Repository for managing scheduled report executions
 */
export class ScheduledReportExecutionRepository {
  /**
   * Create a new execution record
   */
  async create(data: ScheduledReportExecutionData) {
    return prisma.scheduled_report_executions.create({
      data,
      include: {
        scheduledReport: {
          select: {
            id: true,
            name: true,
            reportType: true,
            emailRecipients: true,
            exportFormat: true,
          },
        },
      },
    });
  }

  /**
   * Get execution by ID
   */
  async findById(id: string) {
    return prisma.scheduled_report_executions.findUnique({
      where: { id },
      include: {
        scheduledReport: {
          select: {
            id: true,
            name: true,
            reportType: true,
            emailRecipients: true,
            exportFormat: true,
            creator: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * List executions with filtering and pagination
   */
  async findMany(params: ListExecutionsParams) {
    const whereClause: Prisma.scheduled_report_executionsWhereInput = {};

    // Apply filters
    if (params.scheduledReportId) {
      whereClause.scheduledReportId = params.scheduledReportId;
    }

    if (params.status) {
      whereClause.status = params.status;
    }

    if (params.startDate || params.endDate) {
      whereClause.startedAt = {};
      if (params.startDate) whereClause.startedAt.gte = params.startDate;
      if (params.endDate) whereClause.startedAt.lte = params.endDate;
    }

    const [data, total] = await Promise.all([
      prisma.scheduled_report_executions.findMany({
        where: whereClause,
        include: {
          scheduledReport: {
            select: {
              id: true,
              name: true,
              reportType: true,
              emailRecipients: true,
              exportFormat: true,
            },
          },
        },
        orderBy: {
          [params.sortBy]: params.sortOrder,
        },
        skip: (params.page - 1) * params.limit,
        take: params.limit,
      }),
      prisma.scheduled_report_executions.count({ where: whereClause }),
    ]);

    return {
      data,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit),
      },
    };
  }

  /**
   * Update execution status and details
   */
  async updateStatus(
    id: string,
    status: ExecutionStatus,
    updates: Partial<{
      startedAt: Date;
      completedAt: Date;
      errorMessage: string;
      reportData: any;
      filePath: string;
      emailsSent: number;
      emailErrors: string[];
      executionTime: number;
      recordCount: number;
    }> = {}
  ) {
    return prisma.scheduled_report_executions.update({
      where: { id },
      data: {
        status,
        ...updates,
      },
      include: {
        scheduledReport: {
          select: {
            id: true,
            name: true,
            reportType: true,
            emailRecipients: true,
            exportFormat: true,
          },
        },
      },
    });
  }

  /**
   * Mark execution as started
   */
  async markAsStarted(id: string) {
    return this.updateStatus(id, 'RUNNING', {
      startedAt: new Date(),
    });
  }

  /**
   * Mark execution as completed
   */
  async markAsCompleted(
    id: string,
    data: {
      reportData?: any;
      filePath?: string;
      emailsSent?: number;
      recordCount?: number;
      executionTime?: number;
    }
  ) {
    const completedAt = new Date();
    return this.updateStatus(id, 'COMPLETED', {
      completedAt,
      ...data,
    });
  }

  /**
   * Mark execution as failed
   */
  async markAsFailed(
    id: string,
    errorMessage: string,
    emailErrors: string[] = []
  ) {
    return this.updateStatus(id, 'FAILED', {
      completedAt: new Date(),
      errorMessage,
      emailErrors,
    });
  }

  /**
   * Get execution statistics for a specific scheduled report
   */
  async getStatisticsForReport(scheduledReportId: string) {
    const [total, completed, failed, running, pending] = await Promise.all([
      prisma.scheduled_report_executions.count({
        where: { scheduledReportId },
      }),
      prisma.scheduled_report_executions.count({
        where: { scheduledReportId, status: 'COMPLETED' },
      }),
      prisma.scheduled_report_executions.count({
        where: { scheduledReportId, status: 'FAILED' },
      }),
      prisma.scheduled_report_executions.count({
        where: { scheduledReportId, status: 'RUNNING' },
      }),
      prisma.scheduled_report_executions.count({
        where: { scheduledReportId, status: 'PENDING' },
      }),
    ]);

    // Get average execution time for completed executions
    const avgExecutionTime = await prisma.scheduled_report_executions.aggregate({
      where: {
        scheduledReportId,
        status: 'COMPLETED',
        executionTime: { not: null },
      },
      _avg: {
        executionTime: true,
      },
    });

    // Get last execution
    const lastExecution = await prisma.scheduled_report_executions.findFirst({
      where: { scheduledReportId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        status: true,
        startedAt: true,
        completedAt: true,
        errorMessage: true,
        emailsSent: true,
        recordCount: true,
      },
    });

    return {
      total,
      completed,
      failed,
      running,
      pending,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      averageExecutionTime: avgExecutionTime._avg.executionTime || 0,
      lastExecution,
    };
  }

  /**
   * Get recent executions across all scheduled reports
   */
  async getRecentExecutions(limit: number = 10) {
    return prisma.scheduled_report_executions.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        scheduledReport: {
          select: {
            id: true,
            name: true,
            reportType: true,
          },
        },
      },
    });
  }

  /**
   * Clean up old execution records
   */
  async cleanupOldExecutions(daysToKeep: number = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await prisma.scheduled_report_executions.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
        status: {
          in: ['COMPLETED', 'FAILED'],
        },
      },
    });

    return result.count;
  }

  /**
   * Get execution summary for dashboard
   */
  async getExecutionSummary(days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [totalExecutions, completedExecutions, failedExecutions] = await Promise.all([
      prisma.scheduled_report_executions.count({
        where: {
          createdAt: { gte: startDate },
        },
      }),
      prisma.scheduled_report_executions.count({
        where: {
          createdAt: { gte: startDate },
          status: 'COMPLETED',
        },
      }),
      prisma.scheduled_report_executions.count({
        where: {
          createdAt: { gte: startDate },
          status: 'FAILED',
        },
      }),
    ]);

    // Get daily execution counts for the chart
    const dailyExecutions = await prisma.$queryRaw<Array<{
      date: string;
      total: number;
      completed: number;
      failed: number;
    }>>`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM scheduled_report_executions 
      WHERE created_at >= ${startDate}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `;

    return {
      totalExecutions,
      completedExecutions,
      failedExecutions,
      successRate: totalExecutions > 0 ? (completedExecutions / totalExecutions) * 100 : 0,
      dailyExecutions,
    };
  }
}
