/**
 * <PERSON><PERSON><PERSON> to set up quotation system database functions and triggers
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function setupQuotations() {
  try {
    console.log('Setting up quotation system...');

    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-quotations-tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL content by statements (simple approach)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await prisma.$executeRawUnsafe(statement + ';');
          console.log('✓ Executed SQL statement successfully');
        } catch (error) {
          // Skip errors for already existing objects
          if (error.message.includes('already exists')) {
            console.log('✓ Object already exists, skipping...');
          } else {
            console.error('Error executing statement:', error.message);
          }
        }
      }
    }

    console.log('✅ Quotation system setup completed successfully!');

    // Test the quotation number generation
    console.log('\nTesting quotation number generation...');
    const testNumber = await prisma.$queryRaw`SELECT generate_quotation_number() as quotation_number`;
    console.log('Generated quotation number:', testNumber[0].quotation_number);

  } catch (error) {
    console.error('❌ Error setting up quotation system:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the setup
setupQuotations()
  .then(() => {
    console.log('Setup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
