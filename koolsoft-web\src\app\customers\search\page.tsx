'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useCustomers } from '@/lib/hooks/useCustomers';
import { CustomerFilterForm } from '@/components/customers/customer-filter-form';
import { CustomerList } from '@/components/customers/customer-list';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Home, Search, ArrowLeft } from 'lucide-react';

/**
 * Advanced Customer Search Page
 *
 * This page provides a dedicated interface for searching customers with advanced filtering options.
 */
export default function CustomerSearchPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialQuery = searchParams?.get('q') || '';
  
  const {
    customers,
    isLoading,
    pagination,
    filters,
    setFilters,
    setPagination,
    refreshCustomers,
    deleteCustomer
  } = useCustomers();

  // Set initial search query if provided in URL
  useEffect(() => {
    if (initialQuery) {
      setFilters({ searchAll: initialQuery });
    }
  }, [initialQuery, setFilters]);

  // Handle filter change
  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  // Handle pagination change
  const handlePaginationChange = (skip: number, take: number) => {
    setPagination({ skip, take });
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb navigation */}
      <Breadcrumb className="mb-4">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">
              <Home className="h-4 w-4 mr-2" />
              Home
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/customers">
              Customers
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/customers/search">
              Advanced Search
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2 text-primary" />
              Advanced Customer Search
            </CardTitle>
            <CardDescription>
              Search for customers using multiple criteria
            </CardDescription>
          </div>
          <Button asChild variant="outline">
            <Link href="/customers">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customers
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <CustomerFilterForm
              onFilter={handleFilterChange}
              initialValues={{ ...filters, searchAll: initialQuery }}
            />
            <CustomerList
              customers={customers}
              isLoading={isLoading}
              pagination={pagination}
              onPaginationChange={handlePaginationChange}
              onRefresh={refreshCustomers}
              onDelete={deleteCustomer}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
