"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/util-deprecate";
exports.ids = ["vendor-chunks/util-deprecate"];
exports.modules = {

/***/ "(rsc)/./node_modules/util-deprecate/node.js":
/*!*********************************************!*\
  !*** ./node_modules/util-deprecate/node.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */\n\nmodule.exports = __webpack_require__(/*! util */ \"util\").deprecate;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdXRpbC1kZXByZWNhdGUvbm9kZS5qcyIsIm1hcHBpbmdzIjoiOztBQUNBO0FBQ0E7QUFDQTs7QUFFQUEsa0VBQTBDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcdXRpbC1kZXByZWNhdGVcXG5vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG4vKipcbiAqIEZvciBOb2RlLmpzLCBzaW1wbHkgcmUtZXhwb3J0IHRoZSBjb3JlIGB1dGlsLmRlcHJlY2F0ZWAgZnVuY3Rpb24uXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCd1dGlsJykuZGVwcmVjYXRlO1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIiwiZGVwcmVjYXRlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/util-deprecate/node.js\n");

/***/ })

};
;