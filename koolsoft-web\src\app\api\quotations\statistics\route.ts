import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository } from '@/lib/repositories';
import { quotationStatisticsSchema } from '@/lib/validations/quotation.schema';
import { z } from 'zod';

/**
 * GET /api/quotations/statistics
 * Get quotation statistics
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const queryParams = Object.fromEntries(searchParams.entries());
      const validatedParams = quotationStatisticsSchema.parse(queryParams);

      const quotationRepository = getQuotationRepository();

      // Get statistics
      const statistics = await quotationRepository.getStatistics(validatedParams);

      // Get additional metrics
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const lastMonth = new Date(currentMonth);
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      const currentYear = new Date();
      currentYear.setMonth(0, 1);
      currentYear.setHours(0, 0, 0, 0);

      const [
        currentMonthStats,
        lastMonthStats,
        currentYearStats,
      ] = await Promise.all([
        quotationRepository.getStatistics({
          ...validatedParams,
          startDate: currentMonth,
        }),
        quotationRepository.getStatistics({
          ...validatedParams,
          startDate: lastMonth,
          endDate: currentMonth,
        }),
        quotationRepository.getStatistics({
          ...validatedParams,
          startDate: currentYear,
        }),
      ]);

      // Calculate growth rates
      const monthlyGrowth = lastMonthStats.totalCount > 0
        ? ((currentMonthStats.totalCount - lastMonthStats.totalCount) / lastMonthStats.totalCount) * 100
        : 0;

      const valueGrowth = lastMonthStats.totalValue > 0
        ? ((currentMonthStats.totalValue - lastMonthStats.totalValue) / lastMonthStats.totalValue) * 100
        : 0;

      return NextResponse.json({
        success: true,
        data: {
          overall: statistics,
          currentMonth: currentMonthStats,
          lastMonth: lastMonthStats,
          currentYear: currentYearStats,
          growth: {
            monthly: {
              count: monthlyGrowth,
              value: valueGrowth,
            },
          },
          metrics: {
            averageQuotationValue: statistics.totalCount > 0
              ? statistics.totalValue / statistics.totalCount
              : 0,
            conversionRate: statistics.statusCounts.accepted > 0
              ? (statistics.statusCounts.accepted / statistics.totalCount) * 100
              : 0,
            rejectionRate: statistics.statusCounts.rejected > 0
              ? (statistics.statusCounts.rejected / statistics.totalCount) * 100
              : 0,
          },
        },
      });
    } catch (error) {
      console.error('Error fetching quotation statistics:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid query parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch quotation statistics',
        },
        { status: 500 }
      );
    }
  }
);
