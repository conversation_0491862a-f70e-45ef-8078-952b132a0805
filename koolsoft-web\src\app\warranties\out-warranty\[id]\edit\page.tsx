'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  XCircle, 
  Save, 
  X, 
  AlertTriangle,
  User,
  Calendar,
  DollarSign
} from 'lucide-react';

// Form validation schema
const editOutWarrantySchema = z.object({
  customerId: z.string().uuid('Customer is required'),
  executiveId: z.string().uuid('Executive is required'),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  amount: z.number().min(0, 'Amount must be non-negative'),
  source: z.string().max(20).default('NEW'),
  isActive: z.boolean().default(true),
}).refine(
  (data) => new Date(data.endDate) > new Date(data.startDate),
  {
    message: 'End date must be after start date',
    path: ['endDate'],
  }
);

type EditOutWarrantyForm = z.infer<typeof editOutWarrantySchema>;

interface OutWarrantyData {
  id: string;
  customerId: string;
  executiveId: string;
  contactPerson?: string;
  contactPhone?: string;
  startDate: string;
  endDate: string;
  amount: number;
  source: string;
  isActive: boolean;
  customer: {
    id: string;
    name: string;
  };
  executive: {
    id: string;
    name: string;
  };
}

export default function EditOutWarrantyPage() {
  const params = useParams();
  const router = useRouter();
  const outWarrantyId = params?.id as string;
  
  const [outWarranty, setOutWarranty] = useState<OutWarrantyData | null>(null);
  const [customers, setCustomers] = useState<Array<{ id: string; name: string }>>([]);
  const [executives, setExecutives] = useState<Array<{ id: string; name: string }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<EditOutWarrantyForm>({
    resolver: zodResolver(editOutWarrantySchema) as any,
  });

  // Load out-warranty data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Load out-warranty details
        const outWarrantyResponse = await fetch(`/api/out-warranties/${outWarrantyId}`, {
          credentials: 'include',
        });

        if (!outWarrantyResponse.ok) {
          throw new Error('Failed to fetch out-warranty details');
        }

        const outWarrantyData = await outWarrantyResponse.json();
        setOutWarranty(outWarrantyData);

        // Set form values
        setValue('customerId', outWarrantyData.customerId);
        setValue('executiveId', outWarrantyData.executiveId);
        setValue('contactPerson', outWarrantyData.contactPerson || '');
        setValue('contactPhone', outWarrantyData.contactPhone || '');
        setValue('startDate', outWarrantyData.startDate.split('T')[0]);
        setValue('endDate', outWarrantyData.endDate.split('T')[0]);
        setValue('amount', outWarrantyData.amount);
        setValue('source', outWarrantyData.source);
        setValue('isActive', outWarrantyData.isActive);

        // Load customers and executives
        const [customersResponse, executivesResponse] = await Promise.all([
          fetch('/api/customers?take=1000', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&take=1000', { credentials: 'include' })
        ]);

        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData.customers || []);
        }

        if (executivesResponse.ok) {
          const executivesData = await executivesResponse.json();
          setExecutives(executivesData.users || []);
        }

        setError(null);
      } catch (err: any) {
        console.error('Error loading data:', err);
        setError(err.message || 'Failed to load out-warranty details');
      } finally {
        setIsLoading(false);
      }
    };

    if (outWarrantyId) {
      loadData();
    }
  }, [outWarrantyId, setValue]);

  const onSubmit = async (data: EditOutWarrantyForm) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const response = await fetch(`/api/out-warranties/${outWarrantyId}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          startDate: new Date(data.startDate).toISOString(),
          endDate: new Date(data.endDate).toISOString(),
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to update out-warranty';
        try {
          const errorData = await response.json();
          if (errorData?.details && Array.isArray(errorData.details)) {
            // Handle Zod validation errors
            const validationErrors = errorData.details.map((err: any) =>
              `${err.path?.join('.')}: ${err.message}`
            ).join(', ');
            errorMessage = `Validation error: ${validationErrors}`;
          } else {
            errorMessage = errorData?.error || errorData?.message || errorMessage;
          }
        } catch {
          // If JSON parsing fails, use default message
        }
        throw new Error(errorMessage);
      }

      // Show success toast and redirect to detail page
      toast.success('Out-warranty updated successfully');
      router.push(`/warranties/out-warranty/${outWarrantyId}`);
    } catch (error: any) {
      console.error('Error updating out-warranty:', error);
      const errorMessage = error.message || 'Failed to update out-warranty. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <Skeleton className="h-6 w-48 bg-white/20" />
            <Skeleton className="h-4 w-64 bg-white/20" />
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
              <Skeleton className="h-20" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error && !outWarranty) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-black">{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <XCircle className="h-5 w-5" />
              <span>Edit Out-Warranty</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Update out-warranty information and settings
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" asChild>
              <Link href={`/warranties/out-warranty/${outWarrantyId}`}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Selection */}
                <div className="space-y-2">
                  <Label htmlFor="customerId" className="text-black">
                    Customer <span className="text-red-500">*</span>
                  </Label>
                  <Select value={watch('customerId')} onValueChange={(value) => setValue('customerId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.customerId && (
                    <p className="text-sm text-destructive">{errors.customerId.message}</p>
                  )}
                </div>

                {/* Executive Selection */}
                <div className="space-y-2">
                  <Label htmlFor="executiveId" className="text-black">
                    Executive <span className="text-red-500">*</span>
                  </Label>
                  <Select value={watch('executiveId')} onValueChange={(value) => setValue('executiveId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select executive" />
                    </SelectTrigger>
                    <SelectContent>
                      {executives.map((executive) => (
                        <SelectItem key={executive.id} value={executive.id}>
                          {executive.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.executiveId && (
                    <p className="text-sm text-destructive">{errors.executiveId.message}</p>
                  )}
                </div>

                {/* Contact Person */}
                <div className="space-y-2">
                  <Label htmlFor="contactPerson" className="text-black">Contact Person</Label>
                  <Input
                    id="contactPerson"
                    placeholder="Enter contact person name"
                    {...register('contactPerson')}
                  />
                  {errors.contactPerson && (
                    <p className="text-sm text-destructive">{errors.contactPerson.message}</p>
                  )}
                </div>

                {/* Contact Phone */}
                <div className="space-y-2">
                  <Label htmlFor="contactPhone" className="text-black">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    placeholder="Enter contact phone number"
                    {...register('contactPhone')}
                  />
                  {errors.contactPhone && (
                    <p className="text-sm text-destructive">{errors.contactPhone.message}</p>
                  )}
                </div>

                {/* Start Date */}
                <div className="space-y-2">
                  <Label htmlFor="startDate" className="text-black">
                    Start Date <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="startDate"
                    type="date"
                    {...register('startDate')}
                  />
                  {errors.startDate && (
                    <p className="text-sm text-destructive">{errors.startDate.message}</p>
                  )}
                </div>

                {/* End Date */}
                <div className="space-y-2">
                  <Label htmlFor="endDate" className="text-black">
                    End Date <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="endDate"
                    type="date"
                    {...register('endDate')}
                  />
                  {errors.endDate && (
                    <p className="text-sm text-destructive">{errors.endDate.message}</p>
                  )}
                </div>

                {/* Amount */}
                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-black">
                    Amount <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="Enter amount"
                    {...register('amount', { valueAsNumber: true })}
                  />
                  {errors.amount && (
                    <p className="text-sm text-destructive">{errors.amount.message}</p>
                  )}
                </div>

                {/* Source */}
                <div className="space-y-2">
                  <Label htmlFor="source" className="text-black">Source</Label>
                  <Select value={watch('source')} onValueChange={(value) => setValue('source', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NEW">New</SelectItem>
                      <SelectItem value="CONVERSION">Conversion</SelectItem>
                      <SelectItem value="RENEWAL">Renewal</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.source && (
                    <p className="text-sm text-destructive">{errors.source.message}</p>
                  )}
                </div>
            </div>

            {/* Error Display */}
            {error && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-black">{error}</AlertDescription>
              </Alert>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" asChild>
                <Link href={`/warranties/out-warranty/${outWarrantyId}`}>
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Out-Warranty
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
